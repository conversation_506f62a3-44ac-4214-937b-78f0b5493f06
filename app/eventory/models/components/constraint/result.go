package constraint

import "sync"

// Result is what constraint return, result has no FailedReasons is considered pass
type Result struct {
	FailedReasons map[string]*FailedReason `json:"FailedReasons"`
	deferRunOnce  sync.Once
	deferredFuncs []func() error
}

func NewResult(FailedReasons ...*FailedReason) *Result {
	newResult := &Result{
		FailedReasons: map[string]*FailedReason{},
	}

	for _, reason := range FailedReasons {
		newResult.FailedReasons[reason.String()] = reason
	}

	return newResult
}

func PassResult() *Result {
	return NewResult()
}

func (r *Result) Pass() bool {
	return len(r.FailedReasons) == 0
}

func (r *Result) HasReasonCode(code FailedCode) bool {
	for _, reason := range r.FailedReasons {
		if reason.Code == code {
			return true
		}
	}

	return false
}
