package stream

import (
	"github.com/17media/dig"
	subTopicModel "github.com/17media/gomodel/models/subTopic"

	"github.com/17media/api/app/concert-stream/domain/gift"
	"github.com/17media/api/app/concert-stream/domain/stream"
	"github.com/17media/api/service/queue"

	// register di manager
	_ "github.com/17media/api/app/concert-stream/app/stream/adapter"
	"github.com/17media/api/setup/dimanager"
)

func init() {
	Register(dimanager.DefaultManager)
	RegisterInfoPublisher(dimanager.DefaultManager)
	RegisterCommentPublisher(dimanager.DefaultManager)
}

func RegisterCommentPublisher(m *dimanager.Manager) {
	type params struct {
		dig.In
	}
	fn := func(p params) queue.Publisher {
		return queue.NewPubsubPublisher(subTopicModel.SubTopics[subTopicModel.KeyConcertComment].Topic)
	}
	m.ProvideConstructor(fn, `concertCommentPublisher`)
}

func RegisterInfoPublisher(m *dimanager.Manager) {
	type params struct {
		dig.In
	}
	fn := func(p params) queue.Publisher {
		return queue.NewPubsubPublisher(subTopicModel.SubTopics[subTopicModel.KeyAggregateConcertStream].Topic)
	}
	m.ProvideConstructor(fn, `concertInfoPublisher`)
}

func Register(m *dimanager.Manager) {
	type params struct {
		dig.In

		StreamRegion     *string           `name:"concert_stream_region"`
		StreamEtcdRepo   stream.Repository `name:"concertStreamEtcdRepo"`
		GiftEtcdRepo     gift.Repository   `name:"concertGiftEtcdRepo"`
		CommentPublisher queue.Publisher   `name:"concertCommentPublisher"`
		InfoPublisher    queue.Publisher   `name:"concertInfoPublisher"`
	}

	m.RegisterString("concert_stream_region", "", "region of concert-stream service")

	fn := func(p params) Stream {
		return New(
			p.StreamRegion,
			p.StreamEtcdRepo,
			p.GiftEtcdRepo,
			p.CommentPublisher,
			p.InfoPublisher,
		)
	}
	m.ProvideConstructor(fn, `concertStream`)
}

func GetStream(m *dimanager.Manager) Stream {
	var output Stream
	type params struct {
		dig.In
		Output Stream `name:"concertStream"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
