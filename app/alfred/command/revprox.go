package command

import (
	"flag"
	"fmt"
	"io/ioutil"
	"net"
	"strconv"
	"strings"
	"time"

	btime "github.com/17media/api/base/time"

	"github.com/google/go-github/github"
	"github.com/slack-go/slack"
	"golang.org/x/oauth2"
	"gopkg.in/yaml.v2"

	"github.com/17media/api/base/ctx"
	slackSrv "github.com/17media/api/service/slack"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/stores/user"
	"github.com/17media/logrus"
)

const (
	timeFormat      = "2006-01-02"
	defaultExpiry   = 7 // 7 Days
	blockDefaultStr = "default"
	blockForeverStr = "forever"
)

var (
	githubOwner          = flag.String("github_owner", "", "Github owner")
	githubConfigsRepo    = flag.String("github_configs_repo", "", "Github config repository")
	revproxBlacklistPath = flag.String("revprox_blacklist_path", "", "Blacklist yaml config")
	slackChannel         = flag.String("slack_channel", "", "Slack Channel")
)

type revproxCommand struct {
	slack  slackSrv.Slack
	github *github.Client
}

type blockedIP struct {
	IPAddress   net.IP    `yaml:"ip"`
	Description string    `yaml:"description"`
	ExpiredOn   time.Time `yaml:"expire"`
}

func (blkIp blockedIP) String() (s string) {
	s += fmt.Sprintf("Blocked IP Details:\n - IP: %s \n - ExpiredOn: %s \n - Description: %s \n", blkIp.IPAddress.String(), blkIp.ExpiredOn.Format(timeFormat), blkIp.Description)
	return s
}

type revproxIPBlacklistConf struct {
	IPBlacklist []blockedIP `yaml:"ipBlacklist"`
}

func (ipbklist *revproxIPBlacklistConf) String() (s string) {
	for _, ip := range ipbklist.IPBlacklist {
		s += fmt.Sprintf("- %s - %s - %s\n", ip.IPAddress.String(), ip.ExpiredOn.Format(timeFormat), ip.Description)
	}
	return s
}

func (ipbklist *revproxIPBlacklistConf) get(inputIP net.IP) *blockedIP {
	idx := ipbklist.existIdx(inputIP)
	if idx == -1 {
		return nil
	}
	targetIP := ipbklist.IPBlacklist[idx]
	return &targetIP
}

func (ipbklist *revproxIPBlacklistConf) add(targetIP blockedIP) error {
	idx := ipbklist.existIdx(targetIP.IPAddress)
	if idx != -1 {
		return fmt.Errorf("IP already blocked\n```%s```", targetIP.String())
	}
	ipbklist.IPBlacklist = append(ipbklist.IPBlacklist, targetIP)
	return nil
}

func (ipbklist *revproxIPBlacklistConf) remove(targetIP net.IP) (*blockedIP, error) {
	idx := ipbklist.existIdx(targetIP)
	if idx == -1 {
		text := fmt.Sprintf("IP did not exist in blacklist - %s", targetIP.String())
		return nil, fmt.Errorf(text)
	}
	removedIP := ipbklist.IPBlacklist[idx]
	ipbklist.IPBlacklist = append(ipbklist.IPBlacklist[:idx], ipbklist.IPBlacklist[idx+1:]...)
	return &removedIP, nil
}

func (ipbklist *revproxIPBlacklistConf) existIdx(targetIP net.IP) int {
	for i, blockedIP := range ipbklist.IPBlacklist {
		if targetIP.Equal(blockedIP.IPAddress) {
			return i
		}
	}
	return -1
}

func init() {
	rc := &revproxCommand{}
	register("revprox", rc.Create, []Command{
		Command{
			Name:            "getlist",
			CheckPermission: hasPerm(user.PermBatman),
			Help:            `Get Blacklist`,
			Func:            rc.getList,
			ArgsHelp:        []string{},
			ArgsOptHelp: []string{
				"\nExample:\n",
				"!revprox getlist",
			},
		},
		Command{
			Name:            "checkip",
			CheckPermission: hasPerm(user.PermBatman),
			Help:            `Check if IP blocked`,
			Func:            rc.checkIP,
			ArgsHelp:        []string{"<ip>"},
			ArgsOptHelp: []string{
				"\nExample:\n",
				"!revprox checkip *******",
			},
		},
		Command{
			Name:            "blockip",
			CheckPermission: hasPerm(user.PermBatman),
			Help:            `Block a IP`,
			Func:            rc.blockIP,
			ArgsHelp:        []string{"<ip>"},
			ArgsOptHelp: []string{
				"\nExample to block a ip '*******' with expire '5' days and description 'Testing Block':\n",
				"!revprox blockip ******* 5 Testing Block\n",
				"\n",
				"Or blocking with default period = 7 days\n",
				"!revprox blockip ******* default Testing Block",
				"Or blocking it forever\n",
				"!revprox blockip ******* forever Testing Block",
			},
		},
		Command{
			Name:            "unblockip",
			CheckPermission: hasPerm(user.PermBatman),
			Help:            `Unblock a IP`,
			Func:            rc.unblockIP,
			ArgsHelp:        []string{"<ip>"},
			ArgsOptHelp: []string{
				"\nExample:\n",
				"!revprox unblockip *******",
			},
		},
	})
}

func (rc *revproxCommand) Create() {
	rc.slack = slackSrv.GetSlack(dimanager.DefaultManager)

	ghts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: *gitHubAccessToken},
	)
	ghtc := oauth2.NewClient(oauth2.NoContext, ghts)
	rc.github = github.NewClient(ghtc)
}

func (rc *revproxCommand) getList(context ctx.CTX, slackUser *slack.User, args []string, extras ...string) (*slack.Attachment, *slack.Attachment) {
	blacklistConf := revproxIPBlacklistConf{}
	_, err := rc.getGithubFileAsStruct(context, *revproxBlacklistPath, &blacklistConf)
	if err != nil {
		return nil, ErrorRes(fmt.Errorf("Unable to get blacklist, error %+v", err))
	}

	text := blacklistConf.String()

	return nil, &slack.Attachment{
		Color:      "good",
		Title:      "Auto Blacklist",
		Text:       "```" + text + "```",
		MarkdownIn: []string{"text", "fields"},
	}
}

func (rc *revproxCommand) blockIP(context ctx.CTX, slackUser *slack.User, args []string, extras ...string) (*slack.Attachment, *slack.Attachment) {
	if len(args) < 1 {
		err := fmt.Errorf("\nExample: !revprox blockip ******* 5 Testing Block")
		logrus.Error(err)
		return nil, ErrorRes(err)
	}

	inputIP := net.ParseIP(args[0])
	if inputIP == nil {
		return nil, ErrorRes(fmt.Errorf("Cannot parse IP address: %s", args[0]))
	}

	expireTime := btime.TimeNow().AddDate(0, 0, defaultExpiry)
	if len(args) > 1 && args[1] == blockForeverStr {
		expireTime = time.Date(9999, time.December, 31, 0, 0, 0, 0, time.UTC)
	} else if len(args) > 1 && args[1] != blockDefaultStr {
		expireDays, err := strconv.Atoi(args[1])
		if err != nil {
			return nil, ErrorRes(fmt.Errorf("Unable to parse expire time, error %+v", err))
		}
		expireTime = btime.TimeNow().AddDate(0, 0, expireDays)
	}
	expireTime = expireTime.Truncate(24 * time.Hour) // Remove hours

	description := fmt.Sprintf("Blocked on %s", btime.TimeNow().Format(timeFormat))
	if len(args) > 2 {
		description = strings.Join(args[2:], " ")
	}

	targetIP := blockedIP{
		IPAddress:   inputIP,
		ExpiredOn:   expireTime,
		Description: description,
	}

	// Get current blacklist
	blacklistConf, fileSHA, err := rc.getBlacklistConf(context)
	if err != nil {
		return nil, ErrorRes(fmt.Errorf("Unable get blacklist, error %+v", err))
	}

	// Check if inputIP in blacklist and add
	err = blacklistConf.add(targetIP)
	if err != nil {
		return nil, ErrorRes(err)
	}

	// commit blacklist
	commitMsg := fmt.Sprintf("[Alfred] Block %s until %s by %s", targetIP.IPAddress.String(), targetIP.ExpiredOn.Format(timeFormat), slackUser.Name)
	commitURL, err := rc.commitBlacklistConf(context, slackUser, blacklistConf, fileSHA, commitMsg)
	if err != nil {
		return nil, ErrorRes(err)
	}

	// Prepare message
	text := fmt.Sprintf("Config:<%s|%s>\n", commitURL, *revproxBlacklistPath)
	text += targetIP.String()

	err = rc.postSlack(text, commitMsg)
	if err != nil {
		return nil, ErrorRes(err)
	}

	return nil, &slack.Attachment{
		Color:      "good",
		Title:      "Block IP success!",
		Text:       "```" + text + "```",
		MarkdownIn: []string{"text", "fields"},
	}
}

func (rc *revproxCommand) unblockIP(context ctx.CTX, slackUser *slack.User, args []string, extras ...string) (*slack.Attachment, *slack.Attachment) {
	if len(args) < 1 {
		err := fmt.Errorf("\nExample: !revprox unblockip *******")
		logrus.Error(err)
		return nil, ErrorRes(err)
	}

	inputIP := net.ParseIP(args[0])

	if inputIP == nil {
		return nil, ErrorRes(fmt.Errorf("Cannot parse IP address: %s", args[0]))
	}

	// Get current blacklist
	blacklistConf, fileSHA, err := rc.getBlacklistConf(context)
	if err != nil {
		return nil, ErrorRes(fmt.Errorf("Unable get blacklist, error %+v", err))
	}

	// Remove ip from list and commit
	unblockedIP, err := blacklistConf.remove(inputIP)
	if err != nil {
		return nil, ErrorRes(err)
	}

	commitMsg := fmt.Sprintf("[Alfred] Unblock %s by %s", unblockedIP.IPAddress.String(), slackUser.Name)
	commitURL, err := rc.commitBlacklistConf(context, slackUser, blacklistConf, fileSHA, commitMsg)
	if err != nil {
		return nil, ErrorRes(err)
	}

	// Prepare message
	text := fmt.Sprintf("Config:<%s|%s>\n", commitURL, *revproxBlacklistPath)
	text += unblockedIP.String()

	err = rc.postSlack(text, commitMsg)
	if err != nil {
		return nil, ErrorRes(err)
	}

	return nil, &slack.Attachment{
		Color:      "good",
		Title:      "Unblock IP Success!",
		Text:       "```" + text + "```",
		MarkdownIn: []string{"text", "fields"},
	}
}

func (rc *revproxCommand) checkIP(context ctx.CTX, slackUser *slack.User, args []string, extras ...string) (*slack.Attachment, *slack.Attachment) {
	if len(args) < 1 {
		err := fmt.Errorf("\nExample: !revprox checkip *******")
		logrus.Error(err)
		return nil, ErrorRes(err)
	}

	inputIP := net.ParseIP(args[0])
	if inputIP == nil {
		return nil, ErrorRes(fmt.Errorf("Cannot parse IP address: %s", args[0]))
	}

	// Get current blacklist
	blacklistConf, _, err := rc.getBlacklistConf(context)
	if err != nil {
		return nil, ErrorRes(fmt.Errorf("Unable get blacklist, error %+v", err))
	}

	// Check if targetIP is blacklist
	blockedIP := blacklistConf.get(inputIP)
	if blockedIP == nil {
		return nil, &slack.Attachment{
			Color:      "danger",
			Title:      "IP didn't exist in blacklist",
			Text:       "```" + inputIP.String() + "```",
			MarkdownIn: []string{"text", "fields"},
		}
	}

	return nil, &slack.Attachment{
		Color:      "good",
		Title:      "IP found in blacklist!",
		Text:       "```" + blockedIP.String() + "```",
		MarkdownIn: []string{"text", "fields"},
	}
}

func (rc *revproxCommand) getGithubFileAsStruct(context ctx.CTX, filePath string, fileStruct interface{}) (string, error) {
	fileContent, _, _, err := rc.github.Repositories.GetContents(context, *githubOwner, *githubConfigsRepo, filePath, nil)
	if err != nil {
		return "", err
	}

	contents, err := rc.github.Repositories.DownloadContents(context, *githubOwner, *githubConfigsRepo, filePath, nil)
	if err != nil {
		return "", err
	}

	buf, err := ioutil.ReadAll(contents)
	if err != nil {
		return "", err
	}

	if yaml.Unmarshal(buf, fileStruct) != nil {
		return "", err
	}

	return fileContent.GetSHA(), nil
}

func (rc *revproxCommand) getBlacklistConf(context ctx.CTX) (*revproxIPBlacklistConf, string, error) {
	blacklistConf := &revproxIPBlacklistConf{}
	oldFileSHA, err := rc.getGithubFileAsStruct(context, *revproxBlacklistPath, blacklistConf)
	if err != nil {
		return nil, "", err
	}
	return blacklistConf, oldFileSHA, nil
}

func (rc *revproxCommand) commitBlacklistConf(context ctx.CTX, slackUser *slack.User, blacklistConf *revproxIPBlacklistConf, oldFileSHA string, msg string) (string, error) {
	newFile, err := yaml.Marshal(blacklistConf)
	if err != nil {
		logrus.Error(err)
		return "", err
	}

	// Create Commit
	commitAuthur := github.CommitAuthor{
		Name:  &slackUser.Name,
		Email: &slackUser.Profile.Email,
	}
	githubFile := github.RepositoryContentFileOptions{
		Message:   &msg,
		Content:   newFile,
		SHA:       &oldFileSHA,
		Author:    &commitAuthur,
		Committer: &commitAuthur,
	}
	contentResp, _, err := rc.github.Repositories.UpdateFile(context, *githubOwner, *githubConfigsRepo, *revproxBlacklistPath, &githubFile)
	if err != nil {
		return "", err
	}

	return contentResp.GetHTMLURL(), nil
}

func (rc *revproxCommand) postSlack(msg, title string) error {
	if err := rc.slack.Message(*slackChannel, slack.MsgOptionText("Revprox", false), slack.MsgOptionAsUser(false), slack.MsgOptionAttachments(slack.Attachment{
		Color:      "good",
		Title:      title,
		Text:       "```" + msg + "```",
		MarkdownIn: []string{"text", "fields"},
	})); err != nil {
		return err
	}
	return nil
}
