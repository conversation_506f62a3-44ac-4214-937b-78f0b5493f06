// Package main is a shield server
//
// the purpose of this server is to provide notifications of
// emergent or scheduled maintenance eventis, by sending message with messenger providers
// to all clients to get the newest announcement.
package main

import (
	"flag"
	"fmt"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	errs "github.com/pkg/errors"
	_ "go.uber.org/automaxprocs" // this library will set GOMAXPROCS according to container engine cpu limit
	"gopkg.in/yaml.v2"

	"github.com/17media/logrus"

	"github.com/17media/api/app/shield/service/messengerv2"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/goroutine"
	"github.com/17media/api/base/metrics"
	btime "github.com/17media/api/base/time"
	"github.com/17media/api/models"
	messengerModel "github.com/17media/api/models/messenger"
	"github.com/17media/api/setup/dimanager"
)

const (
	// template defined the time layout for announcement.yaml
	template = "2006-01-02 15:04:05 (GMT-0700)"

	// configURLTemplate is the URL template of announcement YAML configs
	// first string is environment
	// second string is server
	// for example:
	//   - env: dev
	//   - server: 17app
	// the config URL will be: https://raw.githubusercontent.com/17media/announcement/master/dev/17app/announcement.yaml
	configURLTemplate = "https://raw.githubusercontent.com/17media/announcement/master/%s/%s/announcement.yaml"
)

var (
	port         = flag.Int("port", 8005, "api server port")
	env          = flag.String("env", "dev", "environment")
	servers      = flag.String("servers", "17app,event-server", "servers (split by comma)")
	allowHeaders = flag.String("allow_headers", "language", "allowed headers for client")

	met = metrics.New("shield")

	msger messengerv2.Service

	checkConfigPeriod = time.Second * 5
	serverConfigMap   = map[string]*config{}

	// for mocking function
	// sendMsg wraps sendGlobalMsg for testing purpose
	sendMsg = sendGlobalMsg
	// timeNow wraps btime.TimeNow for testing purpose
	timeNow = btime.TimeNow
	// getConfig wraps getConfigBodyInByte for testing purpose
	getConfigBody = getConfigBodyInByte
)

type announcement struct {
	Title       string `yaml:"title" json:"title"`
	Description string `yaml:"description" json:"description"`
	Pic         string `yaml:"pic" json:"pic"`
}

type response struct {
	StartTime   int64  `yaml:"startTime" json:"startTime"`
	EndTime     int64  `yaml:"endTime" json:"endTime"`
	Title       string `yaml:"title" json:"title"`
	Description string `yaml:"description" json:"description"`
	Pic         string `yaml:"pic" json:"pic"`
	Image       string `yaml:"image" json:"image"`
}

// Announcements maps reagion("EN", "TW", "JP"...) to announcement in specific language
type announcementConfig struct {
	NeedMaintenance bool                        `yaml:"needMaintenance" json:"needMaintenance"`
	ProvidersToUse  []messengerModel.ProviderID `yaml:"providersToUse" json:"providersToUse"`
	StartTime       time.Time                   `yaml:"startTime" json:"startTime"`
	EndTime         time.Time                   `yaml:"endTime" json:"endTime"`
	Image           string                      `yaml:"image" json:"image"`
	Announcements   map[string]announcement     `yaml:"announcements" json:"announcements"`
}

// UnmarshalYAML is the customized function that Unmarshal []byte into announcementConfig{}
func (ac *announcementConfig) UnmarshalYAML(unmarshal func(interface{}) error) error {
	type Alias announcementConfig
	aux := &struct {
		StartAt string `yaml:"startAt" json:"startAt"`
		EndAt   string `yaml:"endAt" json:"endAt"`
		Alias   `yaml:",inline"`
	}{
		Alias: (Alias)(*ac),
	}

	err := unmarshal(&aux)
	if err != nil {
		return err
	}

	// Parse start time
	startTime, err := time.Parse(template, aux.StartAt)
	if err != nil {
		return err
	}

	// Parse end time
	endTime, err := time.Parse(template, aux.EndAt)
	if err != nil {
		return err
	}

	// Update config only when there's no parsing error
	ac.NeedMaintenance = aux.Alias.NeedMaintenance
	ac.ProvidersToUse = aux.Alias.ProvidersToUse
	ac.StartTime = startTime
	ac.EndTime = endTime
	ac.Image = aux.Alias.Image
	ac.Announcements = map[string]announcement{}
	for k, v := range aux.Alias.Announcements {
		ac.Announcements[k] = v
	}

	return nil
}

type config struct {
	Server             string             `json:"server"`
	URL                string             `json:"URL"`
	BodyStr            string             `json:"bodyStr"`
	AnnouncementConfig announcementConfig `json:"announcementConfig"`
}

// update updates announcement stored in the config and send message when necessary
func (c *config) update(context ctx.CTX) {
	// get announcement.yaml
	body, err := getConfigBody(context, c.URL)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err": err,
			"url": c.URL,
		}).Error("getConfigBody failed")
		met.BumpSum("config.update.err", 1, "server", c.Server, "reason", "getconfigbody")
		return
	}

	// Unmarshal by customized UnmarshalYAML
	var announcement announcementConfig
	if err = yaml.Unmarshal(body, &announcement); err != nil {
		context.WithFields(logrus.Fields{
			"err":  err,
			"body": body,
		}).Error("Unmarshal body to config failed")
		met.BumpSum("config.update.err", 1, "server", c.Server, "reason", "unmarshal")
		return
	}
	c.AnnouncementConfig = announcement

	oriBodyStr := c.BodyStr
	newBodyStr := string(body)
	configChanged := (oriBodyStr != newBodyStr)
	if configChanged {
		c.BodyStr = newBodyStr
		met.BumpSum("config.update.changed", 1, "server", c.Server)
		context.WithField("config", *c).Info("config updated")

		// First time don't handle content
		if oriBodyStr == "" {
			return
		}
	}

	now := timeNow()
	duringMaintenance := now.After(c.AnnouncementConfig.StartTime) && now.Before(c.AnnouncementConfig.EndTime)
	meetStart := now.After(c.AnnouncementConfig.StartTime) && now.Sub(c.AnnouncementConfig.StartTime) <= checkConfigPeriod
	meetEnd := now.After(c.AnnouncementConfig.EndTime) && now.Sub(c.AnnouncementConfig.EndTime) <= checkConfigPeriod
	if len(c.AnnouncementConfig.ProvidersToUse) > 0 {
		if (duringMaintenance && configChanged) || (c.AnnouncementConfig.NeedMaintenance && (meetStart || meetEnd)) {
			// send maintenance message
			context.WithFields(logrus.Fields{
				"duringMaintenance": duringMaintenance,
				"configChanged":     configChanged,
				"meetStart":         meetStart,
				"meetEnd":           meetEnd,
				"config":            *c,
			}).Info("send maintenance message")

			if err := sendMsg(context, c.AnnouncementConfig.ProvidersToUse); err != nil {
				context.WithField("err", err).Error("maintenance message publish failed")
				met.BumpSum("config.update.sendmessage", 1, "server", c.Server, "reason", "fail")
			} else {
				met.BumpSum("config.update.sendmessage", 1, "server", c.Server, "reason", "success")
			}
		}
	}

	if c.AnnouncementConfig.NeedMaintenance {
		met.BumpSum("config.update.needmaintenance", 1, "server", c.Server, "reason", "true")
	} else {
		met.BumpSum("config.update.needMaintenance", 1, "server", c.Server, "reason", "false")
	}
}

func main() {
	m := dimanager.DefaultManager
	m.Compile()

	flag.Parse()
	msger = messengerv2.GetMessenger(m)

	initServerConfigMap()
	goroutine.Go(
		func() {
			for {
				context := ctx.Background()
				panicCh := goroutine.Go(func() {
					ticker := time.NewTicker(checkConfigPeriod)

					context.Info("start to update config")
					for range ticker.C {
						for _, config := range serverConfigMap {
							config.update(context)
						}
					}
				})

				p := <-panicCh
				if p != nil {
					context.WithFields(logrus.Fields{
						"panic":  p.Panic,
						"stack:": string(p.Stack),
					}).Error("update config panic")
				}
			}
		})

	r := gin.Default()
	r.Use(corsHeader)

	r.GET("/announcement", getAnnouncement)
	r.GET("/shield/config", getConfig)
	// health check for sre
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "healthy",
		})
	})

	r.Run(fmt.Sprintf(":%d", *port))
}

func initServerConfigMap() {
	for _, server := range strings.Split(*servers, ",") {
		serverConfigMap[server] = &config{
			Server:             server,
			URL:                fmt.Sprintf(configURLTemplate, *env, server),
			BodyStr:            "",
			AnnouncementConfig: announcementConfig{},
		}
	}
}

func corsHeader(c *gin.Context) {
	// cors for webapp
	w := c.Writer
	w.Header().Set("Access-Control-Allow-Origin", "*")
	if c.Request.Method == http.MethodOptions {
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PATCH, PUT, DELETE")
		w.Header().Set("Access-Control-Allow-Headers", *allowHeaders)
		// Set max age so browser doesn't need to request OPTION every time.
		// Chrome and Firefox's max possible age are 1 hour and 1 day.
		// Set as 1 day to meet the larger one.
		// Ref: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Max-Age
		w.Header().Set("Access-Control-Max-Age", "86400")
		c.Status(http.StatusOK)
		w.Write([]byte(""))
		return
	}

	c.Next()
}

// getAnnouncement returns 200 with announcement during maintenance, or 204 otherwise
func getAnnouncement(c *gin.Context) {
	server := c.DefaultQuery("server", "17app")
	config, ok := serverConfigMap[server]
	if !ok {
		met.BumpSum("getannouncement.on", 1, "reason", "unknown server")
		c.Status(http.StatusBadRequest)
		return
	}

	if !config.AnnouncementConfig.NeedMaintenance {
		met.BumpSum("getannouncement.on", 1, "server", server, "reason", "needmaintenance_false")
		c.Status(http.StatusNoContent)
		return
	}

	now := timeNow()
	if now.Before(config.AnnouncementConfig.StartTime) || now.After(config.AnnouncementConfig.EndTime) {
		met.BumpSum("getannouncement.on", 1, "server", server, "reason", "not_in_time")
		c.Status(http.StatusNoContent)
		return
	}

	language := c.Request.Header.Get("language")
	ann, ok := config.AnnouncementConfig.Announcements[language]
	if !ok {
		ann = config.AnnouncementConfig.Announcements["EN"]
	}

	resp := response{
		StartTime:   config.AnnouncementConfig.StartTime.Unix(),
		EndTime:     config.AnnouncementConfig.EndTime.Unix(),
		Title:       ann.Title,
		Description: ann.Description,
		Pic:         ann.Pic,
		Image:       config.AnnouncementConfig.Image,
	}

	met.BumpSum("getannouncement.on", 1, "server", server, "reason", "on")
	c.JSON(http.StatusOK, resp)
}

func getConfig(c *gin.Context) {
	var configs []*config
	for _, config := range serverConfigMap {
		configs = append(configs, config)
	}
	sort.Slice(configs, func(i, j int) bool {
		return configs[i].Server < configs[j].Server
	})
	c.JSON(http.StatusOK, configs)
}

// sendGlobalMsg send message with messenger providers to clients, remind them to call [GET]/announcement and get updated announcement
func sendGlobalMsg(context ctx.CTX, providers []messengerModel.ProviderID) error {
	if err := msger.Publish(context, messengerModel.GlobalAnnouncementChannel, &models.Message{
		Type: models.MsgType_GLOBAL_ANNOUNCEMENT,
		GlobalAnnouncementMsg: &models.GlobalAnnouncementMsg{
			Type: models.GlobalAnnouncementType_MAINTENANCE,
		},
	}, providers); err != nil {
		return errs.Wrap(err, "sendMessage")
	}
	return nil
}

// getConfigBodyInByte returns the content on configURL
// timestamp is used to prevent the response caching by github
func getConfigBodyInByte(context ctx.CTX, configURL string) ([]byte, error) {
	timestamp := timeNow().Unix()
	url := configURL + "?timestamp=" + strconv.FormatInt(timestamp, 10)
	resp, err := http.Get(url)
	if resp != nil {
		defer resp.Body.Close()
	}
	if err != nil {
		return nil, errs.Wrap(err, "httpGet")
	}
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, errs.Wrap(err, "ReadAll")
	}

	return body, nil
}
