package command

import (
	"database/sql"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/slack-go/slack"
	"google.golang.org/api/sheets/v4"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/encoding"
	"github.com/17media/api/base/env"
	"github.com/17media/api/models/keys"
	moneyModel "github.com/17media/api/models/money"
	loyaltyPointModel "github.com/17media/api/models/purchase/product/loyaltypoint"
	"github.com/17media/api/service/region"
	"github.com/17media/api/setup/dimanager"
	intraUser "github.com/17media/api/stores/intra/user"
	"github.com/17media/api/stores/loyaltypoint"
	loyaltyPointHelper "github.com/17media/api/stores/loyaltypoint/helper"
	loyaltyPointHelperCfg "github.com/17media/api/stores/loyaltypoint/helper/config"
	"github.com/17media/api/stores/money"
	"github.com/17media/api/stores/user"
)

const (
	// ref: https://developers.google.com/sheets/api/guides/concepts?hl=zh-tw
	// Single quotes are required for sheet names with spaces, special characters, or an alphanumeric combination.
	tabRedemption  = "'Redemption (Slackbot)'"
	tabPrize       = "'Prize List (Slackbot)'"
	tabUpdatePrize = "'Prize Update (Slackbot)'"

	rowRedemptionStart = 2
	rowPrizeStart      = 3

	redemptionCount         = 10000
	maxRedemptionsQueryDays = 90

	prizeColumnsCount  = 20
	optionColumnsCount = 4

	// queries below are copied from loyaltypoint shipper (stores/purchase/poroduct/loyaltypoint/impl.go)
	insertLog    = "INSERT INTO LoyaltyLog SET userID=:userID,execUserID=:execUserID,usedPoint=:usedPoint,action=:action,amount=:amount,region=:region,remark=:remark,tradeID=:tradeID,timestamp=:timestamp,migrated=:migrated"
	latestLogSQL = "SELECT id, userID, execUserID, usedPoint, region, remark, tradeID, timestamp FROM LoyaltyLog WHERE userID=? ORDER BY timestamp DESC, id DESC LIMIT 1 FOR UPDATE"

	timeNoLimit = "0"
	maxQuota    = 100000
)

var (
	loyaltyStoreSheets = map[string]string{
		// https://docs.google.com/spreadsheets/d/1PC5yTv4SANBvBSg5Bd-4Jb6D1N_tu87c0jlq8TW3jIw/edit
		"prod:TW": "1PC5yTv4SANBvBSg5Bd-4Jb6D1N_tu87c0jlq8TW3jIw",
		// https://docs.google.com/spreadsheets/d/1MwpU1oLr4eA4cib34ok-veUmTv1-MVjKNHOgjOin6Ug/edit
		"prod:JP": "1MwpU1oLr4eA4cib34ok-veUmTv1-MVjKNHOgjOin6Ug",
		// https://docs.google.com/spreadsheets/d/1MBcsXYedXlXaroB2xmLRC8IRAkr2mWf_gDARbiaVtTQ/edit
		"prod:US": "1MBcsXYedXlXaroB2xmLRC8IRAkr2mWf_gDARbiaVtTQ",
		// https://docs.google.com/spreadsheets/d/1d0rce6lmBfEQcmnXi14rggGTtifgjqmddjibpb_otuk/edit
		"prod:HK": "1d0rce6lmBfEQcmnXi14rggGTtifgjqmddjibpb_otuk",
		// https://docs.google.com/spreadsheets/d/1Go3nInLffh9yUWTaGNx2ux8sxaF35Hgo-KmoyI5SHvs/edit
		"prod:IN": "1Go3nInLffh9yUWTaGNx2ux8sxaF35Hgo-KmoyI5SHvs",
		// https://docs.google.com/spreadsheets/d/1hMhm71dgcOiBCLCp0anXDu0hodfVmbf1KW-m3z66XwE/edit
		"prod:MY": "1hMhm71dgcOiBCLCp0anXDu0hodfVmbf1KW-m3z66XwE",
		// https://docs.google.com/spreadsheets/d/1Xr3wVSgK6D5KKilGDp3zyW5RWz83KqMK49cvnOTR8zI/edit
		"prod:SG": "1Xr3wVSgK6D5KKilGDp3zyW5RWz83KqMK49cvnOTR8zI",
		// https://docs.google.com/spreadsheets/d/1oaTiqGNCePMOtUiUU1fWdD3uMQg888AnbiSzHRcoaWg/edit
		"prod:PH": "1oaTiqGNCePMOtUiUU1fWdD3uMQg888AnbiSzHRcoaWg",
		// https://docs.google.com/spreadsheets/d/1JtNd_7vGLn9TLa7QbfGWqo3eSO6UQcadsw3KpyEuLVo/edit
		"sta:TW": "1JtNd_7vGLn9TLa7QbfGWqo3eSO6UQcadsw3KpyEuLVo",
		// https://docs.google.com/spreadsheets/d/1B8p_ZZYgt8W21Tf5R3ex7Z5t_K_JdWZXr2RviCWr8I4/edit
		"sta:JP": "1B8p_ZZYgt8W21Tf5R3ex7Z5t_K_JdWZXr2RviCWr8I4",
		// https://docs.google.com/spreadsheets/d/16n0z-MJrZdTx15YCAtC1lszFYYOq8oho1RP9qgpN19E/edit
		"sta:US": "16n0z-MJrZdTx15YCAtC1lszFYYOq8oho1RP9qgpN19E",
		// https://docs.google.com/spreadsheets/d/19pilOwkBUKqc29YOXORSjo0dAc_pAw7JlzbGvE2g6p8/edit
		"sta:HK": "19pilOwkBUKqc29YOXORSjo0dAc_pAw7JlzbGvE2g6p8",
		// https://docs.google.com/spreadsheets/d/1AyZEf4kqPFLeM4P1rGjGbr34CiufNkB0oAzAqQWqwBM/edit
		"sta:IN": "1AyZEf4kqPFLeM4P1rGjGbr34CiufNkB0oAzAqQWqwBM",
		// https://docs.google.com/spreadsheets/d/1SI0HdwoLrNwxPzzD5laxnCXu2b1QkQdfJowH2SAK0W4/edit
		"sta:MY": "1SI0HdwoLrNwxPzzD5laxnCXu2b1QkQdfJowH2SAK0W4",
		// https://docs.google.com/spreadsheets/d/1KRTTmqALXQBW_KC9fJFbeFHaBYJ8UaC7mku_AYiZyJY/edit
		"sta:SG": "1KRTTmqALXQBW_KC9fJFbeFHaBYJ8UaC7mku_AYiZyJY",
		// https://docs.google.com/spreadsheets/d/1ApI8BdtB2OjiL8sR-5P75a5Rp8_ZvZyYjimnVWFCzi8/edit
		"sta:PH": "1ApI8BdtB2OjiL8sR-5P75a5Rp8_ZvZyYjimnVWFCzi8",
		// https://docs.google.com/spreadsheets/d/1-M9KfrNGN5wME_RujswAT7doFal75aSjEVgwElo0RZI/edit
		"uat:TW": "1-M9KfrNGN5wME_RujswAT7doFal75aSjEVgwElo0RZI",
		// https://docs.google.com/spreadsheets/d/1ffu6G4Hu7j42SOMrR35a14kfPiI709mKTTbIZGDQCig/edit
		"uat:JP": "1ffu6G4Hu7j42SOMrR35a14kfPiI709mKTTbIZGDQCig",
		// https://docs.google.com/spreadsheets/d/1beQ3Hn8ntBwjY5mv8qaoESaMBjEqY7g3VTIEAS7E56A/edit
		"uat:US": "1beQ3Hn8ntBwjY5mv8qaoESaMBjEqY7g3VTIEAS7E56A",
		// https://docs.google.com/spreadsheets/d/10AXVLQXCcf_PkXOYywoWEqIEv82gHos-PTxSpv_dT0g/edit
		"uat:HK": "10AXVLQXCcf_PkXOYywoWEqIEv82gHos-PTxSpv_dT0g",
		// https://docs.google.com/spreadsheets/d/1xfYJTVpnY-tKK5TVkYX879cyyFWLyyCppxIqfAt8Y9k/edit
		"uat:IN": "1xfYJTVpnY-tKK5TVkYX879cyyFWLyyCppxIqfAt8Y9k",
		// https://docs.google.com/spreadsheets/d/1QCuBj6zeq5MIBjsWlGRKpVy9hpjifWSqAxYlKafyIMs/edit
		"uat:MY": "1QCuBj6zeq5MIBjsWlGRKpVy9hpjifWSqAxYlKafyIMs",
		// https://docs.google.com/spreadsheets/d/1yjqty5vbimwf1gIz8-jpPx3dyi1hN7lSQnQRfWCHTGU/edit
		"uat:SG": "1yjqty5vbimwf1gIz8-jpPx3dyi1hN7lSQnQRfWCHTGU",
		// https://docs.google.com/spreadsheets/d/11qPceZBTsKuPc4i6lsjah4jmlL45d_tOVSHM85i8BdY/edit
		"uat:PH": "11qPceZBTsKuPc4i6lsjah4jmlL45d_tOVSHM85i8BdY",
	}

	secondsPerDay                          = int64(time.Hour.Seconds()) * 24
	isEnableMigrateExistsPointsSlackbotCmd = loyaltyPointHelperCfg.IsEnableMigrateExistsPointsSlackbotCmd
)

type loyaltyPointCommand struct {
	loyaltypoint       loyaltypoint.Store
	intraUser          intraUser.Store
	user               user.Store
	bank               money.Bank
	loyaltyPointHelper loyaltyPointHelper.Store
	region             region.Service
}

func init() {
	c := &loyaltyPointCommand{}

	register("loyaltypoint", c.Create, []Command{
		{
			Name:       "sync-setting",
			Permission: user.PermBackend,
			Help:       "sync loyalty point setting",
			Func:       c.syncSetting,
			FuncBlock:  nil,
			ArgsHelp:   []string{"<region>"},
			ArgsOptHelp: []string{
				"\nEXAMPLE: !loyaltypoint sync-setting TW",
			},
			DisableHelp: false,
		},
		{
			Name:       "migrate-exists-points",
			Permission: user.PermBackend,
			Help:       "migrate exists loyalty points",
			Func:       c.migrateExistsPoints,
			FuncBlock:  nil,
			ArgsHelp:   []string{"<region>"},
			ArgsOptHelp: []string{
				"\nEXAMPLE: !loyaltypoint migrate-exists-points TW",
			},
			DisableHelp: false,
		},
		{
			Name:       "reset-setting",
			Permission: user.PermBackend,
			Help:       "reset loyalty point setting to bonus ratio 100%",
			Func:       c.resetSetting,
			FuncBlock:  nil,
			ArgsHelp:   []string{"<region>"},
			ArgsOptHelp: []string{
				"\nEXAMPLE: !loyaltypoint reset-setting TW",
			},
			DisableHelp: true,
		},
		{
			Name:       "force-update-setting",
			Permission: user.PermBackend,
			Help:       "force to add settings with current loyalty point merchandises",
			Func:       c.forceUpdateSetting,
			FuncBlock:  nil,
			ArgsHelp:   []string{"<region>"},
			ArgsOptHelp: []string{
				"\nEXAMPLE: !loyaltypoint force-update-setting TW",
			},
			DisableHelp: true,
		},
	})

	register("loyaltypoint-store", c.Create, []Command{
		{
			Name:       "update-status",
			Permission: user.PermWriteLoyaltyPointRedemption,
			Help:       "update redemption status",
			Func:       c.redemptionUpdateStatus,
			FuncBlock:  nil,
			ArgsHelp:   []string{"<orderID>", "<status>"},
			ArgsOptHelp: []string{
				"\nEXAMPLE: !loyaltypoint-store update-status <orderID> WAITING",
				"\nEXAMPLE: !loyaltypoint-store update-status <orderID> REJECT",
				"\nEXAMPLE: !loyaltypoint-store update-status <orderID> SENT <receivedAt> <expiredAt>",
				"\n<receivedAt> <expiredAt> are optional for status SENT only",
				"\n<receivedAt> <expiredAt> should be input with format YYYY-MM-DDTHH:MM:SSZHH:MM or 0 for permanent prize",
			},
			DisableHelp: false,
		},
		{
			Name:       "redemption-list",
			Permission: user.PermReadLoyaltyPointRedemption,
			Help:       "list redemption",
			Func:       c.listRedemptions,
			FuncBlock:  nil,
			ArgsHelp:   []string{"<startTime>", "<endTime>"},
			ArgsOptHelp: []string{
				"\nEXAMPLE: !loyaltypoint-store redemption-list 2023-07-01T00:00:00+08:00 2023-08-01T00:00:00+08:00",
				"\nstartTime & endTime serve as filters to refine the creation timeframe for redemptions.",
			},
			DisableHelp: false,
		},
		{
			Name:       "update-prize",
			Permission: user.PermWriteLoyaltyPointPrize,
			Help:       "update prizes",
			Func:       c.updatePrizes,
			FuncBlock:  nil,
			ArgsHelp:   []string{},
			ArgsOptHelp: []string{
				"\nEXAMPLE: !loyaltypoint-store update-prize",
			},
			DisableHelp: false,
		},
		{
			Name:       "prize-list",
			Permission: user.PermReadLoyaltyPointPrize,
			Help:       "list prizes",
			Func:       c.listPrizes,
			FuncBlock:  nil,
			ArgsHelp:   []string{},
			ArgsOptHelp: []string{
				"\nEXAMPLE: !loyaltypoint-store prize-list",
			},
			DisableHelp: false,
		},
	})
}

func (c *loyaltyPointCommand) Create() {
	c.loyaltypoint = loyaltypoint.GetLoyaltyPoint(dimanager.DefaultManager)
	c.intraUser = intraUser.GetIntraUser(dimanager.DefaultManager)
	c.user = user.GetUser(dimanager.DefaultManager)
	c.bank = money.GetBank(dimanager.DefaultManager)
	c.loyaltyPointHelper = loyaltyPointHelper.GetLoyaltyPointHelper(dimanager.DefaultManager)
	c.region = region.GetRegion(dimanager.DefaultManager)
}

func (c *loyaltyPointCommand) syncSetting(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	region := strings.ToUpper(args[0])

	if err := c.loyaltypoint.SyncSetting(context, region); err != nil {
		return ErrorRes(fmt.Errorf("SyncSetting got err: %s", err.Error())), nil
	}

	return []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Text:       fmt.Sprintf("sync %v region done", region),
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (c *loyaltyPointCommand) migrateExistsPoints(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	if !isEnableMigrateExistsPointsSlackbotCmd() {
		return ErrorRes(fmt.Errorf("this command is not enabled")), nil
	}
	if len(args) != 1 {
		return ErrorRes(fmt.Errorf("not enough parameters")), nil
	}

	execUser, err := c.user.Get(context, email)
	if err != nil {
		return ErrorRes(err), nil
	}
	execUserID := execUser.UserID17
	if execUserID == "" {
		errMsg := "admin have no userID17"
		return ErrorRes(fmt.Errorf(errMsg)), nil
	}
	region := args[0]

	if _, _, _, err := c.bank.TradeTx(context, func(tx money.Tx) error {
		loyaltyPoints, err := c.loyaltypoint.GetLoyaltyPointsByRegion(context, tx.GetTx(), region)
		if err != nil {
			return err
		}

		for _, loyaltyPoint := range loyaltyPoints {
			// only update loyalty points greater than zero
			if loyaltyPoint.Point > 0 {
				err := c.updateLoyaltyPoint(context, tx, execUserID, loyaltyPoint)
				if err != nil {
					return err
				}
			}
		}

		return nil

	}); err != nil {
		context.WithField("err", err).Error("mdb.Transactx error")
		return ErrorRes(err), nil
	}

	return []*slack.Attachment{
		{
			Color:      "good",
			Text:       "Success!",
			MarkdownIn: []string{"text", "fields"},
		},
	}, nil
}

func (c *loyaltyPointCommand) resetSetting(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	region := strings.ToUpper(args[0])

	if err := c.loyaltypoint.ResetSettingPercentage(context, region); err != nil {
		return ErrorRes(fmt.Errorf("ResetSettingPercentage got err: %s", err.Error())), nil
	}

	return []*slack.Attachment{{
		Color:      "good",
		Text:       fmt.Sprintf("reset %v region settings done", region),
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (c *loyaltyPointCommand) forceUpdateSetting(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	region := strings.ToUpper(args[0])

	if err := c.loyaltypoint.ResetSettingByMerchandise(context, region); err != nil {
		return ErrorRes(fmt.Errorf("ResetSettingByMerchandise got err: %s", err.Error())), nil
	}

	return []*slack.Attachment{{
		Color:      "good",
		Text:       fmt.Sprintf("force update %v region settings done", region),
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

// updateLoyaltyPoint is used by migrateExistsPoints to update exists points
func (c *loyaltyPointCommand) updateLoyaltyPoint(context ctx.CTX, tx money.Tx, execUserID string, loyaltyPoint *loyaltyPointModel.LoyaltyPoint) error {
	userID := loyaltyPoint.UserID
	region := loyaltyPoint.Region
	points := loyaltyPoint.Point

	tx.AppendDealings(&moneyModel.Dealing{
		Category:     moneyModel.Category_FREE_LOYALTY_POINT,
		FromUserID:   env.OfficialPseudoUserID,
		ToUserID:     userID,
		FromCurrency: moneyModel.Currency_LOYALTY_POINT,
		ToCurrency:   moneyModel.Currency_LOYALTY_POINT,
		Amount:       points,
	})
	tx.AppendAfterFunc(func(tx *sqlx.Tx, tradeID string, _ []string, timeMs int64) error {
		if err := c.loyaltyPointHelper.UpdatePoint(context, tx, userID, region, points); err != nil {
			return err
		}

		latestLog := loyaltyPointModel.DBRow{}
		err := tx.Get(&latestLog, latestLogSQL, userID)
		if err != nil && err != sql.ErrNoRows {
			return err
		}

		res, err := tx.NamedExec(insertLog,
			loyaltyPointModel.DBRow{
				UserID:     userID,
				ExecUserID: execUserID,
				UsedPoint:  latestLog.UsedPoint,
				Action:     moneyModel.Action_INCREASE,
				Amount:     points,
				Region:     region,
				Remark:     "double points migration",
				TradeID:    tradeID,
				Timestamp:  timeMs / 1000,
				Migrated:   1,
			},
		)
		if err != nil {
			return err
		}
		_, err = res.LastInsertId()
		if err != nil {
			return err
		}

		return nil
	})

	return nil
}

func (c *loyaltyPointCommand) redemptionUpdateStatus(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	intraUser, err := c.intraUser.Get(context, email)
	if err != nil {
		return ErrorRes(err), nil
	}
	userInfo, err := c.user.GetByUserID(context, intraUser.UserID17)
	if err != nil {
		return ErrorRes(err), nil
	}
	region := c.user.GetRegion(context, userInfo)

	orderID := args[0]
	value, ok := loyaltyPointModel.RedemptionStatusValue[strings.ToUpper(args[1])]
	if !ok {
		return ErrorRes(fmt.Errorf("status %s not supported", args[1])), nil
	}
	status := loyaltyPointModel.RedemptionStatus(value)

	receivedAt := int64(0)
	expiredAt := int64(0)
	if len(args) == 4 {
		if args[2] != timeNoLimit {
			time, err := time.Parse(time.RFC3339, args[2])
			if err != nil {
				return ErrorRes(err), nil
			}
			receivedAt = time.Unix()
		}
		if args[3] != timeNoLimit {
			time, err := time.Parse(time.RFC3339, args[3])
			if err != nil {
				return ErrorRes(err), nil
			}
			expiredAt = time.Unix()
		}
	}

	if err := c.loyaltypoint.UpdateRedemptionStatus(context, orderID, intraUser, region, status, receivedAt, expiredAt); err != nil {
		return ErrorRes(fmt.Errorf("UpdateRedemptionStatus got err: %s", err.Error())), nil
	}

	return []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Text:       fmt.Sprintf("redemption [%s] is udpated to status [%s]", orderID, status.String()),
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (c *loyaltyPointCommand) listRedemptions(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	intraUser, err := c.intraUser.Get(context, email)
	if err != nil {
		return ErrorRes(err), nil
	}
	userInfo, err := c.user.GetByUserID(context, intraUser.UserID17)
	if err != nil {
		return ErrorRes(err), nil
	}
	region := c.user.GetRegion(context, userInfo)
	loc, err := c.region.GetGroupTimeLocationByRegion(context, region)
	if err != nil {
		context.WithField("err", err).Error("region.GetGroupTimeZoneByRegion failed")
		return ErrorRes(err), nil
	}

	startTime, err := time.Parse(time.RFC3339, args[0])
	if err != nil {
		return ErrorRes(err), nil
	}
	endTime, err := time.Parse(time.RFC3339, args[1])
	if err != nil {
		return ErrorRes(err), nil
	}
	if endTime.Unix()-startTime.Unix() > secondsPerDay*maxRedemptionsQueryDays {
		return ErrorRes(fmt.Errorf("query time period should be less than 90 days")), nil
	}

	redemptions, _, err := c.loyaltypoint.ListRedemptions(context, "", redemptionCount, loyaltyPointModel.RedeemLogSearchCond{
		Region:        region,
		CreatedAfter:  startTime.Unix(),
		CreatedBefore: endTime.Unix(),
	})
	if err != nil {
		return ErrorRes(err), nil
	}

	// get google sheet service
	if err := processFlag(); err != nil {
		return ErrorRes(err), nil
	}

	sheetService, err := getSheetService()
	if err != nil {
		return ErrorRes(err), nil
	}

	// write redemptions to the sheet
	sheetKey := getSheetKey(region)
	sheetID, ok := loyaltyStoreSheets[sheetKey]
	if !ok {
		return ErrorRes(fmt.Errorf("no loyalty point store sheet for region %s", region)), nil
	}
	readRange := fmt.Sprintf("%s!A%d", tabRedemption, rowRedemptionStart)
	if _, err := sheetService.Spreadsheets.Values.Update(sheetID, readRange, &sheets.ValueRange{
		Values: parseRedemptionsToSheets(redemptions, loc),
	}).ValueInputOption("RAW").Do(); err != nil {
		return ErrorRes(err), nil
	}

	return []*slack.Attachment{&slack.Attachment{
			Color:      "good",
			Text:       "Update complete",
			MarkdownIn: []string{"text", "fields"},
		}}, []*slack.Attachment{&slack.Attachment{
			Color: "good",
			Text:  "Here is your requested Info",
			Fields: []slack.AttachmentField{
				slack.AttachmentField{
					Title: fmt.Sprintf("Loyalty Point Store Management (%s)", region),
					Value: "https://docs.google.com/spreadsheets/d/" + sheetID,
				},
			},
			MarkdownIn: []string{"text", "fields"},
		}}
}

func parseRedemptionsToSheets(redemptions []*loyaltyPointModel.RedemptionRecord, loc *time.Location) [][]interface{} {
	rows := [][]interface{}{}
	for _, r := range redemptions {
		rows = append(rows, []interface{}{
			r.OrderID, r.RedeemerUserID, r.RedeemerName, r.PhoneNumber, r.MailingAddress, r.ReceiverUserID,
			r.PrizeID, r.SubPrizeID, r.Region,
			parseTimestampToString(r.CreatedAt, loc),
			parseTimestampToString(r.ReceivedAt, loc),
			parseTimestampToString(r.ExpiredAt, loc),
			r.Status.String(),
			r.Category.ItemID, r.Category.Type.String(), r.Category.SubType, r.TradeID, r.LastEditor,
			parseTimestampToString(r.LastUpdatedAt, loc),
		})
	}
	return rows
}

func parseTimestampToString(timestamp int64, loc *time.Location) string {
	if timestamp == int64(0) {
		return timeNoLimit
	}

	return time.Unix(timestamp, 0).In(loc).Format(time.RFC3339)
}

func (c *loyaltyPointCommand) updatePrizes(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	intraUser, err := c.intraUser.Get(context, email)
	if err != nil {
		return ErrorRes(err), nil
	}
	userInfo, err := c.user.GetByUserID(context, intraUser.UserID17)
	if err != nil {
		return ErrorRes(err), nil
	}
	region := c.user.GetRegion(context, userInfo)

	// get google sheet service
	if err := processFlag(); err != nil {
		return ErrorRes(err), nil
	}

	sheetService, err := getSheetService()
	if err != nil {
		return ErrorRes(err), nil
	}

	sheetKey := getSheetKey(region)
	sheetID, ok := loyaltyStoreSheets[sheetKey]
	if !ok {
		return ErrorRes(fmt.Errorf("no loyalty point store sheet for region %s", region)), nil
	}

	resp, err := getDataFromSpreadsheet(sheetService, sheetID, tabUpdatePrize, nil)
	if err != nil {
		return ErrorRes(err), nil
	}

	if len(resp.ValueRanges) == 0 {
		return ErrorRes(fmt.Errorf("no sheet data")), nil
	}

	attachFields := []slack.AttachmentField{}
	updateStatusColor := "good"
	publicResponse := "All rows update complete"

	for i, row := range resp.ValueRanges[0].Values {
		if i < rowPrizeStart-1 {
			continue
		}
		af := slack.AttachmentField{
			Title: fmt.Sprintf("Row [%d]: [%v]", i+1, row[0]),
			Value: "update success",
		}
		if errMsgs := c.processRowData(context, row, sheetService, sheetID, region); len(errMsgs) != 0 {
			af.Value = strings.Join(errMsgs, "\n")
			updateStatusColor = "warning"
			publicResponse = "some rows update failed"
		}
		attachFields = append(attachFields, af)
	}

	return []*slack.Attachment{&slack.Attachment{
			Color:      updateStatusColor,
			Text:       publicResponse,
			MarkdownIn: []string{"text", "fields"},
		}}, []*slack.Attachment{&slack.Attachment{
			Color:      updateStatusColor,
			Text:       "Update result of each row",
			Fields:     attachFields,
			MarkdownIn: []string{"text", "fields"},
		}}
}

func getSheetKey(region string) string {
	return keys.RedisKey(env.Namespace(), region)
}

func (c *loyaltyPointCommand) processRowData(context ctx.CTX, row []interface{}, sheetService *sheets.Service, sheetID, region string) (errMsgs []string) {
	// parse row data to prize
	prize, errMsgs := parseRowToPrize(row, region)
	if len(errMsgs) > 0 {
		return errMsgs
	}

	// check if prize is exist
	prizes, _, err := c.loyaltypoint.ListPrizes(context, 0, 1, loyaltyPointModel.PrizeSearchCond{
		PrizeID: prize.PrizeID,
	})
	if err == loyaltypoint.ErrPrizeNotFound {
		sequenceToSet := prize.Sequence
		if createErr := c.loyaltypoint.CreatePrize(context, prize); createErr != nil {
			errMsgs = append(errMsgs, createErr.Error())
		} else {
			// update sequence
			if sequenceErr := c.loyaltypoint.AssignSequence(context, prize.PrizeID, region, sequenceToSet); sequenceErr != nil {
				errMsgs = append(errMsgs, fmt.Sprintf("create success but set sequence failed: %s", sequenceErr.Error()))
			}
		}
		return errMsgs
	}
	if err != nil {
		return []string{
			fmt.Sprintf("loyaltypoint.ListPrizes failed with prizeID [%s] and region [%s]", prize.PrizeID, region),
			err.Error(),
		}
	}
	if len(prizes) != 1 {
		return []string{fmt.Sprintf("find %d prizes with prizeID [%s] and region [%s]", len(prizes), prize.PrizeID, region)}
	}
	p := prizes[0]
	if p.Region != region {
		return []string{"prizeID has been used in other region"}
	}

	// update prize
	updator := genPrizeUpdator(context, p, prize)
	errMsgs, err = c.loyaltypoint.UpdatePrize(context, prize.PrizeID, region, updator)
	if err != nil {
		errMsgs = append(errMsgs, err.Error())
	}

	// update sequence
	if prizes[0].Sequence != prize.Sequence {
		if err := c.loyaltypoint.AssignSequence(context, prize.PrizeID, region, prize.Sequence); err != nil {
			if len(errMsgs) == 0 {
				errMsgs = append(errMsgs, fmt.Sprintf("update success but set sequence failed: %s", err.Error()))
			} else {
				errMsgs = append(errMsgs, err.Error())
			}
		}
	}

	return errMsgs
}

// row data be like
// prizeID | prizeName | description | type | region | startTime | endTime | online | thumbnail | previewImage |
// custom | giftable | quota | quotaPerUser | category_itemID | category_type | category_subType | options | readOnlyURL |
// sequence
func parseRowToPrize(row []interface{}, region string) (*loyaltyPointModel.Prize, []string) {
	if len(row) != prizeColumnsCount {
		return nil, []string{"all columns except ReadOnlyURL are required"}
	}

	errMsgs := []string{}

	prizeType, ok := loyaltyPointModel.PrizeTypeValue[strings.ToUpper(row[3].(string))]
	if !ok {
		errMsgs = append(errMsgs, fmt.Sprintf("prize type [%v] of prize [%v] not exist", row[3], row[0]))
	}

	if strings.ToUpper(row[4].(string)) != region {
		errMsgs = append(errMsgs, fmt.Sprintf("wrong region of prize [%v]", row[0]))
	}

	startTime := int64(0)
	if row[5].(string) != timeNoLimit {
		time, err := time.Parse(time.RFC3339, row[5].(string))
		if err != nil {
			errMsgs = append(errMsgs, err.Error())
		} else {
			startTime = time.Unix()
		}
	}

	endTime := int64(0)
	if row[6].(string) != timeNoLimit {
		time, err := time.Parse(time.RFC3339, row[6].(string))
		if err != nil {
			errMsgs = append(errMsgs, err.Error())
		} else {
			endTime = time.Unix()
		}
	}

	online, err := strconv.ParseInt(row[7].(string), 10, 32)
	if err != nil {
		errMsgs = append(errMsgs, err.Error())
	}
	if online != int64(0) && online != int64(1) {
		errMsgs = append(errMsgs, fmt.Sprintf("online of prize [%v] should be 0 or 1", row[0]))
	}

	if row[8].(string) == "" {
		errMsgs = append(errMsgs, "thumbnail can't be empty")
	}

	if row[9].(string) == "" {
		errMsgs = append(errMsgs, "preview image can't be empty")
	}

	custom, err := strconv.ParseInt(row[10].(string), 10, 32)
	if err != nil {
		errMsgs = append(errMsgs, err.Error())
	}
	if custom != int64(0) && custom != int64(1) {
		errMsgs = append(errMsgs, fmt.Sprintf("custom of prize [%v] should be 0 or 1", row[0]))
	}

	giftable, err := strconv.ParseInt(row[11].(string), 10, 32)
	if err != nil {
		errMsgs = append(errMsgs, err.Error())
	}
	if giftable != int64(0) && giftable != int64(1) {
		errMsgs = append(errMsgs, fmt.Sprintf("giftable of prize [%v] should be 0 or 1", row[0]))
	}

	quota, err := strconv.ParseInt(row[12].(string), 10, 64)
	if err != nil {
		errMsgs = append(errMsgs, err.Error())
	}
	if quota > maxQuota {
		errMsgs = append(errMsgs, fmt.Sprintf("quota can't be more than %v", maxQuota))
	}

	quotaPerUser, err := strconv.ParseInt(row[13].(string), 10, 64)
	if err != nil {
		errMsgs = append(errMsgs, err.Error())
	}
	if quotaPerUser > maxQuota {
		errMsgs = append(errMsgs, fmt.Sprintf("quotaPerUser can't be more than %v", maxQuota))
	}

	categoryType, ok := loyaltyPointModel.PrizeCategoryTypeValue[strings.ToUpper(row[15].(string))]
	if !ok {
		errMsgs = append(errMsgs, fmt.Sprintf("category type [%v] of prize [%v] not exist", row[15], row[0]))
	}

	subType, err := strconv.ParseInt(row[16].(string), 10, 64)
	if err != nil {
		errMsgs = append(errMsgs, err.Error())
	}

	optionRows := strings.Split(row[17].(string), "\n")
	options := []loyaltyPointModel.PrizeOption{}

	if !(len(optionRows) == 1 && optionRows[0] == "") { // prevent from empty data in options field
		for _, or := range optionRows {
			data := strings.Split(or, ",")
			if len(data) != optionColumnsCount {
				errMsgs = append(errMsgs, fmt.Sprintf("option [%v] of prize [%v] should be like <subPrizeID>,<point>,<expireDay>,<online:0|1>", data[0], row[0]))
				continue
			}
			point, err := strconv.ParseInt(data[1], 10, 64)
			if err != nil {
				errMsgs = append(errMsgs, err.Error())
			}
			expireDay, err := strconv.ParseInt(data[2], 10, 64)
			if err != nil {
				errMsgs = append(errMsgs, err.Error())
			}
			online, err := strconv.ParseInt(data[3], 10, 64)
			if err != nil {
				errMsgs = append(errMsgs, err.Error())
			}
			if online != int64(0) && online != int64(1) {
				errMsgs = append(errMsgs, fmt.Sprintf("online of option [%v] prize [%v] should be 0 or 1", data[0], row[0]))
			}
			options = append(options, loyaltyPointModel.PrizeOption{
				SubPrizeID: data[0],
				Point:      point,
				ExpiryDay:  expireDay,
				Online:     int32(online),
			})
		}
	}

	readOnlyURL := row[18].(string)
	if encoding.EmptyOrNilString(readOnlyURL) {
		readOnlyURL = ""
	}

	sequence, err := strconv.ParseFloat(row[19].(string), 64)
	if err != nil {
		errMsgs = append(errMsgs, err.Error())
	}

	if len(errMsgs) > 0 {
		return nil, errMsgs
	}

	return &loyaltyPointModel.Prize{
		PrizeID:      row[0].(string),
		PrizeName:    row[1].(string),
		Description:  row[2].(string),
		Region:       region,
		Type:         loyaltyPointModel.PrizeType(prizeType),
		StartTime:    startTime,
		EndTime:      endTime,
		Online:       int32(online),
		Thumbnail:    row[8].(string),
		PreviewImage: row[9].(string),
		Custom:       int32(custom),
		Giftable:     int32(giftable),
		Quota:        quota,
		QuotaPerUser: quotaPerUser,
		Category: loyaltyPointModel.PrizeCategory{
			ItemID:  row[14].(string),
			Type:    loyaltyPointModel.PrizeCategoryType(categoryType),
			SubType: int32(subType),
		},
		Options:     options,
		ReadOnlyURL: readOnlyURL,
		Sequence:    sequence,
	}, errMsgs
}

func genPrizeUpdator(context ctx.CTX, originPrize, newPrize *loyaltyPointModel.Prize) *loyaltyPointModel.PrizeUpdator {
	updator := loyaltyPointModel.PrizeUpdator{}
	if newPrize.PrizeName != originPrize.PrizeName {
		updator.PrizeName = &newPrize.PrizeName
	}

	if newPrize.Description != originPrize.Description {
		updator.Description = &newPrize.Description
	}

	if newPrize.Type != originPrize.Type {
		updator.Type = &newPrize.Type
	}

	if newPrize.StartTime != originPrize.StartTime {
		updator.StartTime = &newPrize.StartTime
	}

	if newPrize.EndTime != originPrize.EndTime {
		updator.EndTime = &newPrize.EndTime
	}

	if newPrize.Online != originPrize.Online {
		updator.Online = &newPrize.Online
	}

	if newPrize.Thumbnail != originPrize.Thumbnail {
		updator.Thumbnail = &newPrize.Thumbnail
	}

	if newPrize.PreviewImage != originPrize.PreviewImage {
		updator.PreviewImage = &newPrize.PreviewImage
	}

	if newPrize.Custom != originPrize.Custom {
		updator.Custom = &newPrize.Custom
	}

	if newPrize.Giftable != originPrize.Giftable {
		updator.Giftable = &newPrize.Giftable
	}

	if newPrize.Quota != originPrize.Quota {
		updator.Quota = &newPrize.Quota
	}

	if newPrize.QuotaPerUser != originPrize.QuotaPerUser {
		updator.QuotaPerUser = &newPrize.QuotaPerUser
	}

	if newPrize.Category != originPrize.Category {
		updator.Category = loyaltyPointModel.PrizeCategoryUpdator{
			ItemID:  &newPrize.Category.ItemID,
			Type:    &newPrize.Category.Type,
			SubType: &newPrize.Category.SubType,
		}
	}

	originOptions := originPrize.Options.ToMap()
	for i, newOption := range newPrize.Options {
		if oldOption, exists := originOptions[newOption.SubPrizeID]; exists {
			// don't insert into updator if option is same
			// else an online prize will get option updator exist and can't modify to offline
			if newOption.Online == oldOption.Online &&
				newOption.Point == oldOption.Point &&
				newOption.ExpiryDay == oldOption.ExpiryDay {
				continue
			}

			u := loyaltyPointModel.PrizeOptionUpdator{
				SubPrizeID: newOption.SubPrizeID,
				Online:     &newPrize.Options[i].Online,
			}
			if originPrize.GetOnlineStatus() == loyaltyPointModel.PrizeOnlineStatusNeverOnline {
				u.Point = &newPrize.Options[i].Point
				u.ExpiryDay = &newPrize.Options[i].ExpiryDay
			}
			updator.UpdatedOptions = append(updator.UpdatedOptions, u)
		} else {
			updator.CreatedOptions = append(updator.CreatedOptions, newOption)
		}
	}

	if newPrize.ReadOnlyURL != originPrize.ReadOnlyURL {
		updator.ReadOnlyURL = &newPrize.ReadOnlyURL
	}

	return &updator
}

func (c *loyaltyPointCommand) listPrizes(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	intraUser, err := c.intraUser.Get(context, email)
	if err != nil {
		return ErrorRes(err), nil
	}
	userInfo, err := c.user.GetByUserID(context, intraUser.UserID17)
	if err != nil {
		return ErrorRes(err), nil
	}
	region := c.user.GetRegion(context, userInfo)

	loc, err := c.region.GetGroupTimeLocationByRegion(context, region)
	if err != nil {
		context.WithField("err", err).Error("region.GetGroupTimeZoneByRegion failed")
		return ErrorRes(err), nil
	}

	prizes, _, err := c.loyaltypoint.ListPrizes(context, 0, math.MaxInt, loyaltyPointModel.PrizeSearchCond{
		Region: region,
	})
	if err != nil {
		return ErrorRes(err), nil
	}

	// get google sheet service
	if err := processFlag(); err != nil {
		return ErrorRes(err), nil
	}

	sheetService, err := getSheetService()
	if err != nil {
		return ErrorRes(err), nil
	}

	// write redemptions to the sheet
	sheetKey := getSheetKey(region)
	sheetID, ok := loyaltyStoreSheets[sheetKey]
	if !ok {
		return ErrorRes(fmt.Errorf("no loyalty point store sheet for region %s", region)), nil
	}

	readRange := fmt.Sprintf("%s!A%d", tabPrize, rowPrizeStart)
	if _, err := sheetService.Spreadsheets.Values.Update(sheetID, readRange, &sheets.ValueRange{
		Values: parsePrizesToSheets(prizes, loc),
	}).ValueInputOption("RAW").Do(); err != nil {
		return ErrorRes(err), nil
	}

	return []*slack.Attachment{&slack.Attachment{
			Color:      "good",
			Text:       "Update complete",
			MarkdownIn: []string{"text", "fields"},
		}}, []*slack.Attachment{&slack.Attachment{
			Color: "good",
			Text:  "Here is your requested Info",
			Fields: []slack.AttachmentField{
				slack.AttachmentField{
					Title: fmt.Sprintf("Loyalty Point Store Management (%s)", region),
					Value: "https://docs.google.com/spreadsheets/d/" + sheetID,
				},
			},
			MarkdownIn: []string{"text", "fields"},
		}}
}

func parsePrizesToSheets(prizes []*loyaltyPointModel.Prize, loc *time.Location) [][]interface{} {
	rows := [][]interface{}{}
	for _, p := range prizes {
		rows = append(rows, []interface{}{
			p.PrizeID, p.PrizeName, p.Description, p.Type.String(), p.Region,
			parseTimestampToString(p.StartTime, loc), parseTimestampToString(p.EndTime, loc),
			p.Online, p.Thumbnail, p.PreviewImage, p.Custom, p.Giftable,
			p.Quota, p.QuotaPerUser, p.Category.ItemID, p.Category.Type.String(),
			p.Category.SubType, prizeOptionsToString(p.Options), p.ReadOnlyURL, p.Sequence,
		})
	}
	return rows
}

func prizeOptionsToString(options loyaltyPointModel.PrizeOptions) string {
	res := ""
	for _, o := range options {
		if res != "" {
			res += "\n"
		}
		res += fmt.Sprintf("%s,%d,%d,%d", o.SubPrizeID, o.Point, o.ExpiryDay, o.Online)
	}
	return res
}
