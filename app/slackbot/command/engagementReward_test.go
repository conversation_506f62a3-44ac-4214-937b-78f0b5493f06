package command

import (
	"fmt"
	"testing"
	"time"

	"github.com/slack-go/slack"
	"github.com/stretchr/testify/suite"

	model "github.com/17media/api/models/engagementreward"

	"github.com/17media/api/base/testutil"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/stores/engagementreward"
	mEngagementReward "github.com/17media/api/stores/engagementreward/mocks"
	mEngagementRewardRepo "github.com/17media/api/stores/engagementreward/repository/mocks"
	mGift "github.com/17media/api/stores/gift/mocks"
)

var (
	mockComboEngageID     = "gift-combo-10"
	mockMilestoneEngageID = "gift-milestone-20"

	mockComboEngagement = model.EngagementPhase{
		EngageID:        mockComboEngageID,
		Region:          testutil.MockRegionTW,
		Type:            model.EngageTypeCombo,
		CalculationType: model.CalculationTypeClient,
		Script:          `{"giftCombo":{"giftIDs":["giftID1"],"count":10}}`,
		StartTime:       time.Date(2024, 10, 1, 16, 0, 0, 0, time.UTC),
		EndTime:         time.Date(2024, 12, 1, 16, 0, 0, 0, time.UTC),
		I18nKey:         "gift-combo-key",
	}
	mockMilestoneEngagement = model.EngagementPhase{
		EngageID:        mockMilestoneEngageID,
		Region:          "ALL",
		Type:            model.EngageTypeMilestone,
		CalculationType: model.CalculationTypeClient,
		Script:          `{"giftMilestone":{"giftIDs":["giftID1"],"count":10}}`,
		StartTime:       time.Date(2024, 11, 1, 16, 0, 0, 0, time.UTC),
		EndTime:         time.Date(2024, 12, 1, 16, 0, 0, 0, time.UTC),
		CoolDownSec:     86400,
		I18nKey:         "gift-milestone-key",
	}
	mockEngagementPhases = []model.EngagementPhase{
		mockComboEngagement,
		mockMilestoneEngagement,
	}
	mockNonClientEngagement = model.EngagementPhase{
		EngageID:        mockMilestoneEngageID,
		Region:          "ALL",
		Type:            model.EngageTypeMilestone,
		CalculationType: model.CalculationTypeQuestHub,
		Script:          `{"giftMilestone":{"giftIDs":["giftID1"],"count":10}}`,
		StartTime:       time.Date(2024, 11, 1, 16, 0, 0, 0, time.UTC),
		EndTime:         time.Date(2024, 12, 1, 16, 0, 0, 0, time.UTC),
		CoolDownSec:     86400,
		I18nKey:         "gift-milestone-key",
	}
)

type engagementRewardSuite struct {
	suite.Suite

	cmd     *engagementRewardCommand
	manager *dimanager.Manager

	mockEngagementReward     *mEngagementReward.Store
	mockEngagementRewardRepo *mEngagementRewardRepo.Repository
	mockGift                 *mGift.Store
}

func (s *engagementRewardSuite) SetupSuite() {
	s.manager = dimanager.DefaultManager
}

func (s *engagementRewardSuite) SetupSubTest() {
	s.manager.ClearMock()
	s.mockEngagementReward = mEngagementReward.RegisterMock(s.manager, s.T())
	s.mockEngagementRewardRepo = mEngagementRewardRepo.RegisterMock(s.manager, s.T())
	s.mockGift = mGift.RegisterMock(s.manager)

	s.manager.Compile()

	s.cmd = &engagementRewardCommand{}
	s.cmd.Create()
}

func (s *engagementRewardSuite) TearDownSubTest() {
	s.mockEngagementReward.AssertExpectations(s.T())
	s.mockEngagementRewardRepo.AssertExpectations(s.T())
	s.mockGift.AssertExpectations(s.T())
}

func TestEngagementRewardSuite(t *testing.T) {
	suite.Run(t, new(engagementRewardSuite))
}

func (s *engagementRewardSuite) Test_createPhase() {
	tests := []struct {
		desc  string
		args  []string
		extra []string
		setup func()
		want  []*slack.Attachment
	}{
		{
			desc:  "normal case (combo)",
			args:  []string{string(model.EngageTypeCombo), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: []string{"gift-combo-10,reward-id,10,giftID1,giftID2\n"},
			setup: func() {
				mockParam := model.PhaseParam{
					EngageID:        mockComboEngageID,
					Region:          "ALL",
					Type:            model.EngageTypeCombo,
					CalculationType: model.CalculationTypeClient,
					Script: model.Script{
						GiftComboScript: &model.GiftComboScript{
							GiftCombo: model.GiftComboScriptCondition{
								GiftIDs: []string{"giftID1", "giftID2"},
								Count:   10,
							},
						},
					},
					CoolDownSec: 0,
					Rewards: []model.Reward{
						{
							Type:         model.EngagementRewardGift,
							ID:           "reward-id",
							ReceiverType: model.RewardReceiverTypeStreamRoom,
						},
					},
					I18nKey:   i18nKeyEngageCombo,
					StartTime: time.Date(2024, 11, 30, 16, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2024, 12, 31, 16, 0, 0, 0, time.UTC),
				}
				s.mockEngagementReward.On("Create", testutil.AnyCTX, mockParam).Return(nil).Once()
			},
			want: []*slack.Attachment{
				{
					Color:      "good",
					Text:       "Success:\n```\ngift-combo-10```\nFailed:\n```\n```",
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
		{
			desc:  "normal case (milestone)",
			args:  []string{string(model.EngageTypeMilestone), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: []string{"gift-milestone-20,reward-id,20,giftID1,giftID2\n"},
			setup: func() {
				mockParam := model.PhaseParam{
					EngageID:        mockMilestoneEngageID,
					Region:          "ALL",
					Type:            model.EngageTypeMilestone,
					CalculationType: model.CalculationTypeClient,
					Script: model.Script{
						GiftMilestoneScript: &model.GiftMilestoneScript{
							GiftMilestone: model.GiftMilestoneScriptCondition{
								GiftIDs: []string{"giftID1", "giftID2"},
								Count:   20,
							},
						},
					},
					CoolDownSec: 604800,
					Rewards: []model.Reward{
						{
							Type:         model.EngagementRewardGift,
							ID:           "reward-id",
							ReceiverType: model.RewardReceiverTypeStreamRoom,
						},
					},
					I18nKey:   i18nKeyEngageMilestone,
					StartTime: time.Date(2024, 11, 30, 16, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2024, 12, 31, 16, 0, 0, 0, time.UTC),
				}
				s.mockEngagementReward.On("Create", testutil.AnyCTX, mockParam).Return(nil).Once()
			},
			want: []*slack.Attachment{
				{
					Color:      "good",
					Text:       "Success:\n```\ngift-milestone-20```\nFailed:\n```\n```",
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
		{
			desc:  "command parameters are wrong",
			args:  []string{string(model.EngageTypeCombo)},
			extra: []string{"gift-combo-10,reward-id,10,giftID1,giftID2\n"},
			want: []*slack.Attachment{
				{
					Color:      "danger",
					Text:       "no enough field",
					MarkdownIn: []string{"text"},
				},
			},
		},
		{
			desc:  "missing command extra",
			args:  []string{string(model.EngageTypeCombo), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: []string{},
			want: []*slack.Attachment{
				{
					Color:      "danger",
					Text:       "Missing engage phase",
					MarkdownIn: []string{"text"},
				},
			},
		},
		{
			desc:  "compose phase param failed",
			args:  []string{"invalid type", "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: []string{"gift-combo-10,reward-id,10,giftID1,giftID2\n"},
			want: []*slack.Attachment{
				{
					Color:      "danger",
					Text:       "Invalid engage type: INVALID TYPE",
					MarkdownIn: []string{"text"},
				},
			},
		},
		{
			desc:  "engagementReward.Create failed",
			args:  []string{string(model.EngageTypeMilestone), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: []string{"gift-milestone-20,reward-id,20,giftID1,giftID2\n"},
			setup: func() {
				mockParam := model.PhaseParam{
					EngageID:        mockMilestoneEngageID,
					Region:          "ALL",
					Type:            model.EngageTypeMilestone,
					CalculationType: model.CalculationTypeClient,
					Script: model.Script{
						GiftMilestoneScript: &model.GiftMilestoneScript{
							GiftMilestone: model.GiftMilestoneScriptCondition{
								GiftIDs: []string{"giftID1", "giftID2"},
								Count:   20,
							},
						},
					},
					CoolDownSec: 604800,
					Rewards: []model.Reward{
						{
							Type:         model.EngagementRewardGift,
							ID:           "reward-id",
							ReceiverType: model.RewardReceiverTypeStreamRoom,
						},
					},
					I18nKey:   i18nKeyEngageMilestone,
					StartTime: time.Date(2024, 11, 30, 16, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2024, 12, 31, 16, 0, 0, 0, time.UTC),
				}
				s.mockEngagementReward.On("Create", testutil.AnyCTX, mockParam).Return(engagementreward.ErrEngageRewardEmpty).Once()
			},
			want: []*slack.Attachment{
				{
					Color:      "good",
					Text:       "Success:\n```\n```\nFailed:\n```\nEngageID: gift-milestone-20, Error: The reward can't be empty```",
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			if tt.setup != nil {
				tt.setup()
			}
			res, _ := s.cmd.createPhase(testutil.MockCTX, "", tt.args, tt.extra...)
			s.Require().Equal(tt.want, res)
		})
	}
}

func (s *engagementRewardSuite) Test_updatePhase() {
	tests := []struct {
		desc  string
		args  []string
		extra []string
		setup func()
		want  []*slack.Attachment
	}{
		{
			desc:  "normal case (combo)",
			args:  []string{string(model.EngageTypeCombo), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: []string{"gift-combo-10,reward-id,10,giftID1,giftID2\n"},
			setup: func() {
				mockParam := model.PhaseParam{
					EngageID:        mockComboEngageID,
					Region:          "ALL",
					Type:            model.EngageTypeCombo,
					CalculationType: model.CalculationTypeClient,
					Script: model.Script{
						GiftComboScript: &model.GiftComboScript{
							GiftCombo: model.GiftComboScriptCondition{
								GiftIDs: []string{"giftID1", "giftID2"},
								Count:   10,
							},
						},
					},
					CoolDownSec: 0,
					Rewards: []model.Reward{
						{
							Type:         model.EngagementRewardGift,
							ID:           "reward-id",
							ReceiverType: model.RewardReceiverTypeStreamRoom,
						},
					},
					I18nKey:   i18nKeyEngageCombo,
					StartTime: time.Date(2024, 11, 30, 16, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2024, 12, 31, 16, 0, 0, 0, time.UTC),
				}
				s.mockEngagementReward.On("Update", testutil.AnyCTX, mockParam).Return(nil).Once()
			},
			want: []*slack.Attachment{
				{
					Color:      "good",
					Text:       "Success:\n```\ngift-combo-10```\nFailed:\n```\n```",
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
		{
			desc:  "normal case (milestone)",
			args:  []string{string(model.EngageTypeMilestone), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: []string{"gift-milestone-20,reward-id,20,giftID1,giftID2\n"},
			setup: func() {
				mockParam := model.PhaseParam{
					EngageID:        mockMilestoneEngageID,
					Region:          "ALL",
					Type:            model.EngageTypeMilestone,
					CalculationType: model.CalculationTypeClient,
					Script: model.Script{
						GiftMilestoneScript: &model.GiftMilestoneScript{
							GiftMilestone: model.GiftMilestoneScriptCondition{
								GiftIDs: []string{"giftID1", "giftID2"},
								Count:   20,
							},
						},
					},
					CoolDownSec: 604800,
					Rewards: []model.Reward{
						{
							Type:         model.EngagementRewardGift,
							ID:           "reward-id",
							ReceiverType: model.RewardReceiverTypeStreamRoom,
						},
					},
					I18nKey:   i18nKeyEngageMilestone,
					StartTime: time.Date(2024, 11, 30, 16, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2024, 12, 31, 16, 0, 0, 0, time.UTC),
				}
				s.mockEngagementReward.On("Update", testutil.AnyCTX, mockParam).Return(nil).Once()
			},
			want: []*slack.Attachment{
				{
					Color:      "good",
					Text:       "Success:\n```\ngift-milestone-20```\nFailed:\n```\n```",
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
		{
			desc:  "command parameters are wrong",
			args:  []string{string(model.EngageTypeCombo)},
			extra: []string{"gift-combo-10,reward-id,10,giftID1,giftID2\n"},
			want: []*slack.Attachment{
				{
					Color:      "danger",
					Text:       "no enough field",
					MarkdownIn: []string{"text"},
				},
			},
		},
		{
			desc:  "missing command extra",
			args:  []string{string(model.EngageTypeCombo), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: []string{},
			want: []*slack.Attachment{
				{
					Color:      "danger",
					Text:       "Missing engage phase",
					MarkdownIn: []string{"text"},
				},
			},
		},
		{
			desc:  "compose phase param failed",
			args:  []string{"invalid type", "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: []string{"gift-combo-10,reward-id,10,giftID1,giftID2\n"},
			want: []*slack.Attachment{
				{
					Color:      "danger",
					Text:       "Invalid engage type: INVALID TYPE",
					MarkdownIn: []string{"text"},
				},
			},
		},
		{
			desc:  "engagementReward.Update failed",
			args:  []string{string(model.EngageTypeMilestone), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: []string{"gift-milestone-20,reward-id,20,giftID1,giftID2\n"},
			setup: func() {
				mockParam := model.PhaseParam{
					EngageID:        mockMilestoneEngageID,
					Region:          "ALL",
					Type:            model.EngageTypeMilestone,
					CalculationType: model.CalculationTypeClient,
					Script: model.Script{
						GiftMilestoneScript: &model.GiftMilestoneScript{
							GiftMilestone: model.GiftMilestoneScriptCondition{
								GiftIDs: []string{"giftID1", "giftID2"},
								Count:   20,
							},
						},
					},
					CoolDownSec: 604800,
					Rewards: []model.Reward{
						{
							Type:         model.EngagementRewardGift,
							ID:           "reward-id",
							ReceiverType: model.RewardReceiverTypeStreamRoom,
						},
					},
					I18nKey:   i18nKeyEngageMilestone,
					StartTime: time.Date(2024, 11, 30, 16, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2024, 12, 31, 16, 0, 0, 0, time.UTC),
				}
				s.mockEngagementReward.On("Update", testutil.AnyCTX, mockParam).Return(engagementreward.ErrEngageRewardEmpty).Once()
			},
			want: []*slack.Attachment{
				{
					Color:      "good",
					Text:       "Success:\n```\n```\nFailed:\n```\nEngageID: gift-milestone-20, Error: The reward can't be empty```",
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			if tt.setup != nil {
				tt.setup()
			}
			res, _ := s.cmd.updatePhase(testutil.MockCTX, "", tt.args, tt.extra...)
			s.Require().Equal(tt.want, res)
		})
	}
}

func (s *engagementRewardSuite) Test_composeEngageParam() {
	mockComboParams := []model.PhaseParam{
		{
			EngageID:        "gift-combo-10",
			Region:          "ALL",
			Type:            model.EngageTypeCombo,
			CalculationType: model.CalculationTypeClient,
			Script: model.Script{
				GiftComboScript: &model.GiftComboScript{
					GiftCombo: model.GiftComboScriptCondition{
						GiftIDs: []string{"giftID1", "giftID2"},
						Count:   10,
					},
				},
			},
			CoolDownSec: 0,
			Rewards: []model.Reward{
				{
					Type:         model.EngagementRewardGift,
					ID:           "reward-id",
					ReceiverType: model.RewardReceiverTypeStreamRoom,
				},
			},
			I18nKey:   i18nKeyEngageCombo,
			StartTime: time.Date(2024, 11, 30, 16, 0, 0, 0, time.UTC),
			EndTime:   time.Date(2024, 12, 31, 16, 0, 0, 0, time.UTC),
		},
		{
			EngageID:        "gift-combo-20",
			Region:          "ALL",
			Type:            model.EngageTypeCombo,
			CalculationType: model.CalculationTypeClient,
			Script: model.Script{
				GiftComboScript: &model.GiftComboScript{
					GiftCombo: model.GiftComboScriptCondition{
						GiftIDs: []string{"giftID2"},
						Count:   20,
					},
				},
			},
			CoolDownSec: 0,
			Rewards: []model.Reward{
				{
					Type:         model.EngagementRewardGift,
					ID:           "reward-id",
					ReceiverType: model.RewardReceiverTypeStreamRoom,
				},
			},
			I18nKey:   i18nKeyEngageCombo,
			StartTime: time.Date(2024, 11, 30, 16, 0, 0, 0, time.UTC),
			EndTime:   time.Date(2024, 12, 31, 16, 0, 0, 0, time.UTC),
		},
	}
	mockComboRow := "gift-combo-10,reward-id,10,giftID1,giftID2\ngift-combo-20,reward-id,20,giftID2\n"

	mockMilestoneParams := []model.PhaseParam{
		{
			EngageID:        "gift-milestone-10",
			Region:          "ALL",
			Type:            model.EngageTypeMilestone,
			CalculationType: model.CalculationTypeClient,
			Script: model.Script{
				GiftMilestoneScript: &model.GiftMilestoneScript{
					GiftMilestone: model.GiftMilestoneScriptCondition{
						GiftIDs: []string{"giftID1", "giftID2"},
						Count:   10,
					},
				},
			},
			CoolDownSec: 604800,
			Rewards: []model.Reward{
				{
					Type:         model.EngagementRewardGift,
					ID:           "reward-id",
					ReceiverType: model.RewardReceiverTypeStreamRoom,
				},
			},
			I18nKey:   i18nKeyEngageMilestone,
			StartTime: time.Date(2024, 11, 30, 16, 0, 0, 0, time.UTC),
			EndTime:   time.Date(2024, 12, 31, 16, 0, 0, 0, time.UTC),
		},
		{
			EngageID:        "gift-milestone-20",
			Region:          "ALL",
			Type:            model.EngageTypeMilestone,
			CalculationType: model.CalculationTypeClient,
			Script: model.Script{
				GiftMilestoneScript: &model.GiftMilestoneScript{
					GiftMilestone: model.GiftMilestoneScriptCondition{
						GiftIDs: []string{"giftID2"},
						Count:   20,
					},
				},
			},
			CoolDownSec: 604800,
			Rewards: []model.Reward{
				{
					Type:         model.EngagementRewardGift,
					ID:           "reward-id",
					ReceiverType: model.RewardReceiverTypeStreamRoom,
				},
			},
			I18nKey:   i18nKeyEngageMilestone,
			StartTime: time.Date(2024, 11, 30, 16, 0, 0, 0, time.UTC),
			EndTime:   time.Date(2024, 12, 31, 16, 0, 0, 0, time.UTC),
		},
	}
	mockMilestoneRow := "gift-milestone-10,reward-id,10,giftID1,giftID2\ngift-milestone-20,reward-id,20,giftID2\n"

	tests := []struct {
		desc   string
		args   []string
		extra  string
		assert func(params []model.PhaseParam, err error)
	}{
		{
			desc:  "normal case (combo)",
			args:  []string{string(model.EngageTypeCombo), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: mockComboRow,

			assert: func(params []model.PhaseParam, err error) {
				s.NoError(err)
				s.Equal(mockComboParams, params)
			},
		},
		{
			desc:  "normal case (milestone)",
			args:  []string{string(model.EngageTypeMilestone), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: mockMilestoneRow,

			assert: func(params []model.PhaseParam, err error) {
				s.NoError(err)
				s.Equal(mockMilestoneParams, params)
			},
		},
		{
			desc:  "exceed max row",
			args:  []string{string(model.EngageTypeMilestone), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: "1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n",
			assert: func(params []model.PhaseParam, err error) {
				s.EqualError(err, "Exceed max engagement command row: 20")
				s.Nil(params)
			},
		},
		{
			desc:  "engage type is invalid",
			args:  []string{"invalid type", "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: mockComboRow,
			assert: func(params []model.PhaseParam, err error) {
				s.EqualError(err, "Invalid engage type: INVALID TYPE")
				s.Nil(params)
			},
		},
		{
			desc:  "engage start time is invalid",
			args:  []string{string(model.EngageTypeCombo), "invalid start time", "2024-12-31T16:00:00"},
			extra: mockComboRow,
			assert: func(params []model.PhaseParam, err error) {
				s.EqualError(err, "Start time format is invalid: invalid start time")
				s.Nil(params)
			},
		},
		{
			desc:  "engage end time is invalid",
			args:  []string{string(model.EngageTypeCombo), "2024-11-30T16:00:00", "invalid end time"},
			extra: mockComboRow,
			assert: func(params []model.PhaseParam, err error) {
				s.EqualError(err, "End time format is invalid: invalid end time")
				s.Nil(params)
			},
		},
		{
			desc:  "engage phase param is invalid",
			args:  []string{string(model.EngageTypeCombo), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: "gift-combo-10,reward-id\n",
			assert: func(params []model.PhaseParam, err error) {
				s.EqualError(err, "Row: 1, Error: Engage phase parameters is invalid")
				s.Nil(params)
			},
		},
		{
			desc:  "engage script count is invalid",
			args:  []string{string(model.EngageTypeCombo), "2024-11-30T16:00:00", "2024-12-31T16:00:00"},
			extra: "gift-combo-10,reward-id,0,giftID1\n",
			assert: func(params []model.PhaseParam, err error) {
				s.EqualError(err, "Row: 1, Error: The count cannot be set to 0: 0")
				s.Nil(params)
			},
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			res, err := composeEngageParam(tt.args, tt.extra)
			tt.assert(res, err)
		})
	}
}

func (s *engagementRewardSuite) Test_listPhases() {
	tests := []struct {
		desc  string
		args  []string
		setup func()
		want  []*slack.Attachment
	}{
		{
			desc: "normal case (combo)",
			args: []string{testutil.MockRegionTW, string(model.EngageTypeCombo)},
			setup: func() {
				s.mockEngagementRewardRepo.EXPECT().GetEngagementPhasesByRegion(testutil.AnyCTX, testutil.MockRegionTW).Return(mockEngagementPhases, nil).Once()
			},
			want: []*slack.Attachment{
				{
					Color: "good",
					Text: fmt.Sprintf(
						"```" +
							"  ENGAGEID        REGION   TYPE    REWARDS   STARTTIME             ENDTIME               GIFTIDS   COUNT  \n" +
							"  gift-combo-10   TW       COMBO             2024-10-01T16:00:00   2024-12-01T16:00:00   giftID1   10     \n" +
							"```",
					),
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
		{
			desc: "normal case (milestone)",
			args: []string{testutil.MockRegionTW, string(model.EngageTypeMilestone)},
			setup: func() {
				s.mockEngagementRewardRepo.EXPECT().GetEngagementPhasesByRegion(testutil.AnyCTX, testutil.MockRegionTW).Return(mockEngagementPhases, nil).Once()
			},
			want: []*slack.Attachment{
				{
					Color: "good",
					Text: fmt.Sprintf(
						"```" +
							"  ENGAGEID            REGION   TYPE        REWARDS   STARTTIME             ENDTIME               GIFTIDS   COUNT  \n" +
							"  gift-milestone-20   ALL      MILESTONE             2024-11-01T16:00:00   2024-12-01T16:00:00   giftID1   10     \n" +
							"```",
					),
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
		{
			desc: "should filter non client type script",
			args: []string{testutil.MockRegionTW, string(model.EngageTypeMilestone)},
			setup: func() {
				s.mockEngagementRewardRepo.EXPECT().GetEngagementPhasesByRegion(testutil.AnyCTX, testutil.MockRegionTW).Return([]model.EngagementPhase{mockMilestoneEngagement, mockNonClientEngagement}, nil).Once()
			},
			want: []*slack.Attachment{
				{
					Color: "good",
					Text: fmt.Sprintf(
						"```" +
							"  ENGAGEID            REGION   TYPE        REWARDS   STARTTIME             ENDTIME               GIFTIDS   COUNT  \n" +
							"  gift-milestone-20   ALL      MILESTONE             2024-11-01T16:00:00   2024-12-01T16:00:00   giftID1   10     \n" +
							"```",
					),
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
		{
			desc: "engagement phases not found",
			args: []string{testutil.MockRegionTW, string(model.EngageTypeMilestone)},
			setup: func() {
				s.mockEngagementRewardRepo.EXPECT().GetEngagementPhasesByRegion(testutil.AnyCTX, testutil.MockRegionTW).Return([]model.EngagementPhase{}, nil).Once()
			},
			want: []*slack.Attachment{
				{
					Color:      "good",
					Text:       "no engagement phases",
					MarkdownIn: []string{"text"},
				},
			},
		},
		{
			desc: "engage type is invalid",
			args: []string{testutil.MockRegionTW, "invalid type"},
			want: []*slack.Attachment{
				{
					Color:      "danger",
					Text:       "invalid engage type: INVALID TYPE",
					MarkdownIn: []string{"text"},
				},
			},
		},
		{
			desc: "command parameters are wrong",
			args: []string{testutil.MockRegionTW},
			want: []*slack.Attachment{
				{
					Color:      "danger",
					Text:       "no enough field",
					MarkdownIn: []string{"text"},
				},
			},
		},
		{
			desc: "engageRepo.GetEngagementPhases failed",
			args: []string{testutil.MockRegionTW, string(model.EngageTypeCombo)},
			setup: func() {
				s.mockEngagementRewardRepo.EXPECT().GetEngagementPhasesByRegion(testutil.AnyCTX, testutil.MockRegionTW).Return([]model.EngagementPhase{}, testutil.MockError).Once()
			},
			want: []*slack.Attachment{
				{
					Color:      "danger",
					Text:       "something wrong",
					MarkdownIn: []string{"text"},
				},
			},
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			if tt.setup != nil {
				tt.setup()
			}
			res, _ := s.cmd.listPhases(testutil.MockCTX, "", tt.args)
			s.Require().Equal(tt.want, res)
		})
	}
}

func (s *engagementRewardSuite) Test_deletePhase() {
	tests := []struct {
		desc  string
		extra []string
		setup func()
		want  []*slack.Attachment
	}{
		{
			desc:  "successful case",
			extra: []string{"gift-combo-10\ngift-milestone-20\n"},
			setup: func() {
				s.mockEngagementRewardRepo.EXPECT().DeleteEngagementPhase(testutil.AnyCTX, "gift-combo-10").Return(nil).Once()
				s.mockEngagementRewardRepo.EXPECT().DeleteEngagementPhase(testutil.AnyCTX, "gift-milestone-20").Return(nil).Once()
			},
			want: []*slack.Attachment{
				{
					Color:      "good",
					Text:       "Success:\n```\ngift-combo-10\ngift-milestone-20```\nFailed:\n```\n```",
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
		{
			desc:  "exceed max row",
			extra: []string{"1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n"},
			want: []*slack.Attachment{
				{
					Color:      "danger",
					Text:       "Exceed max engagement command row: 20",
					MarkdownIn: []string{"text"},
				},
			},
		},
		{
			desc:  "engageRepo.DeleteEngagementPhase failed",
			extra: []string{"gift-combo-10\ngift-milestone-20\n"},
			setup: func() {
				s.mockEngagementRewardRepo.EXPECT().DeleteEngagementPhase(testutil.AnyCTX, "gift-combo-10").Return(testutil.MockError).Once()
				s.mockEngagementRewardRepo.EXPECT().DeleteEngagementPhase(testutil.AnyCTX, "gift-milestone-20").Return(testutil.MockError).Once()
			},
			want: []*slack.Attachment{
				{
					Color:      "good",
					Text:       "Success:\n```\n```\nFailed:\n```\nEngageID: gift-combo-10, Error: something wrong\nEngageID: gift-milestone-20, Error: something wrong```",
					MarkdownIn: []string{"text", "fields"},
				},
			},
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			if tt.setup != nil {
				tt.setup()
			}
			res, _ := s.cmd.deletePhase(testutil.MockCTX, "", []string{}, tt.extra...)
			s.Require().Equal(tt.want, res)
		})
	}
}
