package command

import (
	"fmt"
	"math"
	"strconv"

	"github.com/slack-go/slack"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/stores/streamer/overview"
	"github.com/17media/api/stores/user"
)

type erpCommand struct {
	overview overview.Store
}

func init() {
	erp := &erpCommand{}

	register("erp", erp.Create, []Command{

		{
			Name:        "sendRegionalSuppliers",
			Permission:  user.PermWriteERP,
			Help:        "send regional supplier records",
			Func:        erp.sendRegionalSuppliers,
			FuncBlock:   nil,
			ArgsHelp:    []string{"<yyyyMM>", "<region1>\n<region2>\n<region3>\n..."},
			ArgsOptHelp: []string{},
			DisableHelp: false,
		},
		{
			Name:        "sendStreamerSuppliers",
			Permission:  user.PermWriteERP,
			Help:        "send streamer supplier records",
			Func:        erp.sendStreamerSuppliers,
			FuncBlock:   nil,
			ArgsHelp:    []string{"<userID1>\n<userID2>\n<userID3>\n..."},
			ArgsOptHelp: []string{},
			DisableHelp: false,
		},
		{
			Name:        "sendAgencySuppliers",
			Permission:  user.PermWriteERP,
			Help:        "send agency supplier records",
			Func:        erp.sendAgencySuppliers,
			FuncBlock:   nil,
			ArgsHelp:    []string{"<ID1>\n<ID2>\n<ID3>\n..."},
			ArgsOptHelp: []string{},
			DisableHelp: false,
		},
		{
			Name:        "status",
			Permission:  user.PermWriteERP,
			Help:        "get send supplier records task status",
			Func:        erp.getStatus,
			FuncBlock:   nil,
			ArgsHelp:    []string{"<taskID>"},
			ArgsOptHelp: []string{},
			DisableHelp: false,
		},
	})
}

func (erp *erpCommand) Create() {
	erp.overview = overview.GetStreamerOverview(dimanager.DefaultManager)
}

func (erp *erpCommand) sendRegionalSuppliers(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	yyyyMM := args[0]
	regions := args[1:]

	taskID, err := erp.overview.SendRegionalSupplierRecordsToERP(context, yyyyMM, regions, overview.SendSupplierByAsync())
	if err != nil {
		return ErrorRes(err), nil
	}

	fields := []slack.AttachmentField{
		slack.AttachmentField{
			Title: "TaskID",
			Value: fmt.Sprintf("%s", taskID),
			Short: true,
		},
	}
	return []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Fields:     fields,
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (erp *erpCommand) sendStreamerSuppliers(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	userIDs := args
	taskID, err := erp.overview.SendStreamerSupplierRecordsToERP(context, userIDs, overview.SendSupplierByAsync())
	if err != nil {
		return ErrorRes(err), nil
	}

	fields := []slack.AttachmentField{
		slack.AttachmentField{
			Title: "TaskID",
			Value: fmt.Sprintf("%s", taskID),
			Short: true,
		},
	}
	return []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Fields:     fields,
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (erp *erpCommand) sendAgencySuppliers(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	agencyIDs := []int{}
	for _, row := range args {
		id, err := strconv.Atoi(row)
		if err != nil {
			return ErrorRes(err), nil
		}

		agencyIDs = append(agencyIDs, id)
	}

	taskID, err := erp.overview.SendAgencySupplierRecordsToERP(context, agencyIDs, overview.SendSupplierByAsync())
	if err != nil {
		return ErrorRes(err), nil
	}

	fields := []slack.AttachmentField{
		slack.AttachmentField{
			Title: "TaskID",
			Value: fmt.Sprintf("%s", taskID),
			Short: true,
		},
	}
	return []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Fields:     fields,
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (erp *erpCommand) getStatus(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	taskID := args[0]

	task, err := erp.overview.GetSendSupplierTask(context, taskID)
	if err != nil {
		return ErrorRes(err), nil
	}

	fields := []slack.AttachmentField{
		slack.AttachmentField{
			Title: "TaskID",
			Value: fmt.Sprintf("%s", task.TaskID),
		},
		slack.AttachmentField{
			Title: "Progress",
			Value: fmt.Sprintf("%d / %d", task.CompletedCount, task.TotalCount),
		},
		slack.AttachmentField{
			Title: "Failed Count",
			Value: fmt.Sprintf("%d", len(task.FailedUserIDs)),
		},
	}

	attachments := []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Fields:     fields,
		MarkdownIn: []string{"text", "fields"},
	}}

	titleBar := fmt.Sprintf(
		"Failed UserIDs:\n%2s  %-36s\n",
		"No", "UserID",
	)
	text := ""
	for i, userID := range task.FailedUserIDs {
		text = fmt.Sprintf(
			"%s%2d  %-36s\n",
			text, i+1, userID,
		)

		if math.Mod(float64(i+1), float64(resultPageSize)) > 0 && i < len(task.FailedUserIDs)-1 {
			continue
		}

		attachments = append(attachments, &slack.Attachment{
			Color:      "danger",
			Text:       "```" + titleBar + text + "```",
			MarkdownIn: []string{"text", "fields"},
		})
		text = ""
	}

	return attachments, nil
}
