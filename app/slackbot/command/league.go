package command

import (
	"fmt"
	"strings"
	"time"

	btime "github.com/17media/api/base/time"

	"github.com/slack-go/slack"

	"github.com/17media/logrus"

	"github.com/17media/api/base/ctx"
	lbModel "github.com/17media/api/models/leaderboard"
	"github.com/17media/api/service/redis"
	sregion "github.com/17media/api/service/region"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/stores/leaderboard"
	"github.com/17media/api/stores/league"
	"github.com/17media/api/stores/user"
)

var (
	loggedOffUserLimit = 20
)

type leagueCommand struct {
	leagueStore league.Store
	leaderboard leaderboard.Leaderboard
	userStore   user.Store
}

type leaguePerformance struct {
	score     int
	totalView int
	totalLive int
}

func init() {
	l := &leagueCommand{}

	register("league", l.Create, []Command{
		{
			Name:        "remove-member",
			Permission:  user.PermWriteLeague,
			Help:        "Remove a user from user's league, can't remove the owner of the league",
			Func:        l.RemoveMember,
			FuncBlock:   nil,
			ArgsHelp:    []string{"<userID>"},
			ArgsOptHelp: []string{},
			DisableHelp: false,
		},
		{
			Name:        "disband-league",
			Permission:  user.PermWriteLeague,
			Help:        "Disband a league using ownerID(userID)",
			Func:        l.DisbandLeague,
			FuncBlock:   nil,
			ArgsHelp:    []string{"<userID>"},
			ArgsOptHelp: []string{},
			DisableHelp: false,
		},
		{
			Name:        "restore-league",
			Permission:  user.PermWriteLeague,
			Help:        "Restore a league using ownerID(userID)",
			Func:        l.RestoreLeague,
			FuncBlock:   nil,
			ArgsHelp:    []string{"<userID>"},
			ArgsOptHelp: []string{},
			DisableHelp: false,
		},
		{
			Name:        "get-league-info",
			Permission:  user.PermWriteLeague,
			Help:        "Get a league's info using league's name",
			Func:        nil,
			FuncBlock:   l.GetLeagueInfo,
			ArgsHelp:    []string{"\n---\n<leagueName>"},
			ArgsOptHelp: []string{"\n*example*\n!league get-league-info\n---\nThis is a league name\n"},
			DisableHelp: false,
		},
		{
			Name:        "list-loggedoff-members",
			Permission:  user.PermWriteLeague,
			Help:        "List logged off users of the league by league owner's userID",
			Func:        l.GetLoggedOffUsers,
			FuncBlock:   nil,
			ArgsHelp:    []string{"<userID>"},
			ArgsOptHelp: []string{},
			DisableHelp: false,
		},
		{
			Name:        "cancel-loggedoff-invitation",
			Permission:  user.PermWriteLeague,
			Help:        "Cancel the invitations of logged off users by league owner's userID",
			Func:        l.CancelLoggedOffInvitation,
			FuncBlock:   nil,
			ArgsHelp:    []string{"<userID>"},
			ArgsOptHelp: []string{},
			DisableHelp: false,
		},
	})
}

func (lc *leagueCommand) Create() {
	lc.leagueStore = league.GetLeague(dimanager.DefaultManager)
	lc.leaderboard = leaderboard.GetLeaderboard(dimanager.DefaultManager)
	lc.userStore = user.GetUser(dimanager.DefaultManager)
}

func (lc *leagueCommand) RemoveMember(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	userID := args[0]

	leagueID, err := lc.leagueStore.GetLeagueIDByUser(context, userID)
	if err != nil {
		return ErrorRes(err), nil
	}

	// Get league's owner
	league, err := lc.leagueStore.Get(context, leagueID)
	if err != nil {
		return ErrorRes(err), nil
	}

	// Check if the user is owner
	if userID == league.OwnerID {
		return ErrorRes(fmt.Errorf("You can't remove owner")), nil
	}

	if err := lc.leagueStore.RemoveMember(context, leagueID, email, userID); err != nil {
		return ErrorRes(err), nil
	}

	return []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Text:       fmt.Sprintf("UserID `%s` has been removed from the league `%s`", userID, league.Name),
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (lc *leagueCommand) DisbandLeague(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	userID := args[0]

	leagueID, err := lc.leagueStore.GetLeagueIDByUser(context, userID)
	if err != nil {
		return ErrorRes(err), nil
	}

	// Get leagueInfo
	league, err := lc.leagueStore.Get(context, leagueID)
	if err != nil {
		return ErrorRes(err), nil
	}

	// Check if the user is owner
	if userID != league.OwnerID {
		return ErrorRes(fmt.Errorf("You can only disband league by owner's userID")), nil
	}

	if err := lc.leagueStore.Delete(context, leagueID, email); err != nil {
		if err == redis.ErrNotFound {
			return ErrorRes(fmt.Errorf("Disbanding league `%s` is already in progress", league.Name)), nil
		}
		return ErrorRes(err), nil
	}

	return []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Text:       fmt.Sprintf("League `%s` has been disbanded", league.Name),
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (lc *leagueCommand) RestoreLeague(context ctx.CTX, email string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	userID := args[0]
	leagueName := args[1]

	if err := lc.leagueStore.Restore(context, userID, leagueName, email); err != nil {
		return ErrorRes(err), nil
	}

	return []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Text:       fmt.Sprintf("League `%s` has been restored", leagueName),
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (lc *leagueCommand) GetLeagueInfo(context ctx.CTX, email string, args []string, extras ...string) ([]slack.Block, []slack.Block) {
	if len(extras) == 0 {
		return ErrorBlockRes(fmt.Errorf("Invalid league name format , please check your command again")), nil
	}
	name := extras[0]

	league, err := lc.leagueStore.GetByName(context, name)
	if err != nil {
		return ErrorBlockRes(err), nil
	}

	performances := []leaguePerformance{}
	// query for last 3 month
	for i := 0; i >= -2; i-- {
		score, err := lc.leaderboard.GetScore(context, lbModel.Type_LEAGUE_POINT_FROM, lbModel.Period_MONTH, btime.TimeNow(), league.ID, leaderboard.TablePlatform, sregion.GLOBAL, int(i))
		if err != nil {
			return ErrorBlockRes(err), nil
		}
		viewDuration, err := lc.leaderboard.GetScore(context, lbModel.Type_LEAGUE_VIEW_DURATION, lbModel.Period_MONTH, btime.TimeNow(), league.ID, leaderboard.TablePlatform, sregion.GLOBAL, int(i))
		if err != nil {
			return ErrorBlockRes(err), nil
		}
		liveDuration, err := lc.leaderboard.GetScore(context, lbModel.Type_LEAGUE_STREAM_DURATION, lbModel.Period_MONTH, btime.TimeNow(), league.ID, leaderboard.TablePlatform, sregion.GLOBAL, int(i))
		if err != nil {
			return ErrorBlockRes(err), nil
		}
		performances = append(performances, leaguePerformance{
			score:     score,
			totalLive: liveDuration,
			totalView: viewDuration,
		})

	}

	members, _, err := lc.leagueStore.GetMembers(context, league.ID)
	if err != nil {
		return ErrorBlockRes(err), nil
	}

	// Compose below part:
	// --- start of format ---
	// :white_check_mark:Information of leauge "xxx"
	// ------(divider)------
	// LeagueName:			LeagueID:
	// xxx					aaa-bbb-ccc
	// Owner:				OwnerID:
	// yyy					ddd-eee-fff
	// MemberCount:
	// 5566
	// ------(divider)------
	// --- end of format ---
	resp := []slack.Block{
		slack.NewSectionBlock(
			slack.NewTextBlockObject(slack.PlainTextType, fmt.Sprintf(":white_check_mark: Information of league \"%s\"", league.Name), true, false),
			nil,
			nil,
		),
		slack.NewDividerBlock(),
		slack.NewSectionBlock(
			nil,
			[]*slack.TextBlockObject{
				slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*LeagueName*:\n>%s", league.Name), false, false),
				slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*LeagueID*:\n>%s", league.ID), false, false),
				slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*Owner*:\n>%s", league.Owner.DisplayName), false, false),
				slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*OwnerID*:\n>%s", league.Owner.UserID), false, false),
				slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*MemberCount*:\n>%d", league.MemberCount), false, false),
			},
			nil,
		),
		slack.NewDividerBlock(),
	}

	// Compose performance with format:
	// --- start of format ---
	// ---League performance in 2019/8---
	// Total view duration		Total live duration
	// 3345678 secs				3345678 secs
	// League points
	// 3345678 points
	// --- start end format ---
	// PS. it'll list 3 months
	t := btime.TimeNow()
	// shift to UTC+8 because we use UTC+8 as the base time
	location, _ := time.LoadLocation("Asia/Taipei")
	t = t.In(location)
	shiftDay := (t.Day() - 1) * int(-1)
	t = t.AddDate(0, 0, shiftDay)
	for i, performance := range performances {
		t := t.AddDate(0, i*int(-1), 0)
		resp = append(resp, []slack.Block{
			slack.NewSectionBlock(
				slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*---League performance in %d/%d---*", t.Year(), t.Month()), false, false),
				[]*slack.TextBlockObject{
					slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*Total view duration*\n>%d secs", performance.totalView), false, false),
					slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*Total live duration*\n>%d secs", performance.totalLive), false, false),
					slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*League points*\n>%d points", performance.score), false, false),
				},
				nil,
			),
		}...)
	}

	// Compose member list's column name:
	// --- start of format ---
	// ------(divider)------
	// Members:
	// Name				UserID
	// --- end of format ---
	resp = append(resp, []slack.Block{
		slack.NewDividerBlock(),
		slack.NewSectionBlock(
			slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*Members*:"), false, false),
			[]*slack.TextBlockObject{
				slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*OpenID*"), false, false),
				slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf("*UserID*"), false, false),
			},
			nil,
		)}...,
	)

	// Compose member content:
	// --- start of format ---
	// DisplayName		UserID
	// --- end of format ---
	for _, member := range members {
		resp = append(resp, []slack.Block{
			slack.NewSectionBlock(
				nil,
				[]*slack.TextBlockObject{
					// DisplayName is OpenID, ref: stores/user/impl.go:GetPlainUsers
					slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf(">%s", member.UserInfo.DisplayName), false, false),
					slack.NewTextBlockObject(slack.MarkdownType, fmt.Sprintf(">%s", member.UserInfo.UserID), false, false),
				},
				nil,
			),
		}...)
	}

	return resp, nil
}

func (lc *leagueCommand) GetLoggedOffUsers(context ctx.CTX, _ string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	userID := args[0]

	leagueID, err := lc.leagueStore.GetLeagueIDByUser(context, userID)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "userID": userID}).Error("league.GetLeagueIDByUser failed")
		return ErrorRes(err), nil
	}

	members, err := lc.leagueStore.GetPlainMembers(context, leagueID)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "leagueID": leagueID}).Error("league.GetPlainMembers failed")
		return ErrorRes(err), nil
	}

	if len(members) == 0 {
		return []*slack.Attachment{&slack.Attachment{
			Color:      "good",
			Text:       SlackbotNoResult,
			MarkdownIn: []string{"text", "fields"},
		}}, nil
	}

	userIDs := []string{}
	for _, member := range members {
		userIDs = append(userIDs, member.UserID)
	}

	loggedOffUserIDs, err := lc.getLoggedOffUserIDs(context, userIDs...)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err}).Error("lc.getLoggedOffUserIDs failed")
		return ErrorRes(err), nil
	}

	if len(loggedOffUserIDs) == 0 {
		return []*slack.Attachment{&slack.Attachment{
			Color:      "good",
			Text:       SlackbotNoResult,
			MarkdownIn: []string{"text", "fields"},
		}}, nil
	}

	if len(loggedOffUserIDs) > loggedOffUserLimit {
		loggedOffUserIDs = loggedOffUserIDs[:loggedOffUserLimit]
	}

	return []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Text:       "```" + strings.Join(loggedOffUserIDs, "\n") + "```",
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (lc *leagueCommand) CancelLoggedOffInvitation(context ctx.CTX, _ string, args []string, extras ...string) ([]*slack.Attachment, []*slack.Attachment) {
	userID := args[0]

	leagueID, err := lc.leagueStore.GetLeagueIDByUser(context, userID)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "userID": userID}).Error("league.GetLeagueIDByUser failed")
		return ErrorRes(err), nil
	}

	invitations, err := lc.leagueStore.GetInvitationsByLeagueID(context, leagueID)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "userID": userID}).Error("league.GetInvitationsByLeagueID failed")
		return ErrorRes(err), nil
	}

	userIDs := []string{}
	for _, invitation := range invitations {
		userIDs = append(userIDs, invitation.UserID)
	}

	loggedOffUserIDs, err := lc.getLoggedOffUserIDs(context, userIDs...)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err}).Error("lc.getLoggedOffUserIDs failed")
		return ErrorRes(err), nil
	}

	if len(loggedOffUserIDs) == 0 {
		return []*slack.Attachment{&slack.Attachment{
			Color:      "good",
			Text:       SlackbotNoResult,
			MarkdownIn: []string{"text", "fields"},
		}}, nil
	}

	if len(loggedOffUserIDs) > loggedOffUserLimit {
		loggedOffUserIDs = loggedOffUserIDs[:loggedOffUserLimit]
	}

	var (
		cancelSuccessUserIDs []string
		cancelFailureUserIDs []string
	)
	for _, userID := range loggedOffUserIDs {
		if err := lc.leagueStore.CancelInvitation(context, leagueID, userID); err != nil {
			context.WithFields(logrus.Fields{"err": err, "userID": userID}).Warn("leagueStore.CancelInvitation failed")
			cancelFailureUserIDs = append(cancelFailureUserIDs, userID)
		}
		cancelSuccessUserIDs = append(cancelSuccessUserIDs, userID)
	}

	outputText := "```" + "These invitation have been canceled \n" + strings.Join(cancelSuccessUserIDs, "\n")
	if len(cancelFailureUserIDs) > 0 {
		outputText += "\n" + "These invitation canceled failed \n" + strings.Join(cancelFailureUserIDs, "\n")
	}
	outputText += "```"

	return []*slack.Attachment{&slack.Attachment{
		Color:      "good",
		Text:       outputText,
		MarkdownIn: []string{"text", "fields"},
	}}, nil
}

func (lc *leagueCommand) getLoggedOffUserIDs(context ctx.CTX, userIDs ...string) ([]string, error) {
	loggedOffUserIDs := []string{}

	users, err := lc.userStore.GetUsers(context, userIDs...)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "userIDs": userIDs}).Error("user.GetUsers failed")
		return loggedOffUserIDs, err
	}

	for _, user := range users {
		if user.IsRemoved == 1 {
			loggedOffUserIDs = append(loggedOffUserIDs, user.UserID)
		}
	}

	return loggedOffUserIDs, nil
}
