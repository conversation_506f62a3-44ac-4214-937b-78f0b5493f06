package urls

import (
	"flag"
	"net/url"

	"github.com/17media/logrus"

	"github.com/17media/api/app/revprox/util"
	"github.com/17media/api/base/ctx"
)

const (
	// GoURL URL
	GoURL = "go_url"
	// GoCloneURL URL
	GoCloneURL = "go_clone_url"
	// GoTradeURL is go trade url
	GoTradeURL = "go_trade_url"
	// GoLivesURL is go lives url
	GoLivesURL = "go_lives_url"
	// GoCellsURL is go cells url
	GoCellsURL = "go_cells_url"
	// GoPlatformURL is go platform url
	GoPlatformURL = "go_platform_url"
	// ApiUserSearchURL is isolated usersearch url
	ApiUserSearchURL = "api_usersearch_url"
	// GoIsoURL is go iso url
	GoIsoURL = "go_iso_url"
	// BBRichURL is bbrich url
	BBRichURL = "bbrich_url"
	// BBRichv2URL is bbrichv2 url
	BBRichv2URL = "bbrichv2_url"
	// TODO: remove gologs after api-logs is stable
	// ApiLogsURL is isolated logs url
	ApiLogsURL = "api_logs_url"
	// AggrGateURL is aggr-gateway url
	AggrGateURL = "aggr_gateway_url"

	// GroupCallURL is group call url
	GroupCallURL = "group_call_url"

	// ConcertStreamURL is the concert stream URL
	ConcertStreamURL = "concert_stream_url"
	// ConcertTradeURL is the concert trade URL
	ConcertTradeURL = "concert_trade_url"

	// GoLokaliseURL is golokalise url
	GoLokaliseURL = "golokalise_url"

	// PollURL is the poll url
	PollURL = "poll_url"
	// GoAuthURL is go auth url
	GoAuthURL = "go_auth_url"

	// CategoryURL is category svc url
	CategoryURL = "category_url"

	// PaymentNotifProxyURL is a service url that proxies payment notification from 3rd party to our server
	PaymentNotifProxyURL = "payment_notif_proxy_url"
)

var (
	prod = flag.Bool("isProd", false, "Check If the env is Prod")

	goURL                = flag.String("go_url", "", "URL of golang server")
	goCloneURL           = flag.String("go_clone_url", "", "URL of golang clone server")
	goTradeURL           = flag.String("go_trade_url", "", "URL of trade server")
	goLivesURL           = flag.String("go_lives_url", "", "URL of golang server")
	goCellsURL           = flag.String("go_cells_url", "", "URL of cells server")
	goPlatformURL        = flag.String("go_platform_url", "", "URL of platform test server")
	apiUserSearchURL     = flag.String("api_usersearch_url", "", "URL of isolated user search server")
	apiLogsURL           = flag.String("api_logs_url", "", "URL of isolated logs server")
	goIsoURL             = flag.String("go_iso_url", "", "URL of isolation server")
	bbrichURL            = flag.String("bbrich_url", "", "URL of bbrich server")
	bbrichv2URL          = flag.String("bbrichv2_url", "", "URL of bbrichv2 server")
	aggrGateURL          = flag.String("aggr_gateway_url", "", "URL of aggregation gateway server")
	groupCallURL         = flag.String("group_call_url", "", "URL of group call server")
	concertStreamURL     = flag.String("concert_stream_url", "", "URL of concert stream server")
	concertTradeURL      = flag.String("concert_trade_url", "", "URL of concert stream server")
	golokaliseURL        = flag.String("golokalise_url", "", "URL of golokalise server")
	pollURL              = flag.String("poll_url", "", "URL of isolated poll server")
	goAuthURL            = flag.String("go_auth_url", "", "URL of isolated auth server")
	categoryURL          = flag.String("category_url", "", "URL of category server")
	paymentNotifProxyURL = flag.String("payment_notif_proxy_url", "", "URL of payment notif proxy server")

	UrlMap = map[string]*url.URL{}
)

func ParseAndTest(context ctx.CTX) {
	// See if destination URLs are reachable
	context.WithFields(logrus.Fields{
		"gourl":                *goURL,
		"goClone_url":          *goCloneURL,
		"go_trade":             *goTradeURL,
		"goLivesurl":           *goLivesURL,
		"goCellsurl":           *goCellsURL,
		"goPlatformurl":        *goPlatformURL,
		"ApiUserSearchURL":     *apiUserSearchURL,
		"apiLogsURL":           *apiLogsURL,
		"goIsoURL":             *goIsoURL,
		"bbrichurl":            *bbrichURL,
		"bbrichv2url":          *bbrichv2URL,
		"aggrGateURL":          *aggrGateURL,
		"groupCallURL":         *groupCallURL,
		"concertStreamURL":     *concertStreamURL,
		"concertTradeURL":      *concertTradeURL,
		"golokaliseURL":        *golokaliseURL,
		"pollURL":              *pollURL,
		"goAuthURL":            *goAuthURL,
		"categoryURL":          *categoryURL,
		"paymentNotifProxyURL": *paymentNotifProxyURL,
	}).Info("destination urls")
	UrlMap[GoURL] = util.ParseURL(context, *goURL)
	UrlMap[GoCloneURL] = util.ParseURL(context, *goCloneURL)
	UrlMap[GoTradeURL] = util.ParseURL(context, *goTradeURL)
	UrlMap[GoLivesURL] = util.ParseURL(context, *goLivesURL)
	UrlMap[GoCellsURL] = util.ParseURL(context, *goCellsURL)
	UrlMap[GoPlatformURL] = util.ParseURL(context, *goPlatformURL)
	UrlMap[ApiUserSearchURL] = util.ParseURL(context, *apiUserSearchURL)
	UrlMap[ApiLogsURL] = util.ParseURL(context, *apiLogsURL)
	UrlMap[GoIsoURL] = util.ParseURL(context, *goIsoURL)
	UrlMap[BBRichURL] = util.ParseURL(context, *bbrichURL)
	UrlMap[BBRichv2URL] = util.ParseURL(context, *bbrichv2URL)
	UrlMap[AggrGateURL] = util.ParseURL(context, *aggrGateURL)
	UrlMap[GroupCallURL] = util.ParseURL(context, *groupCallURL)
	UrlMap[ConcertStreamURL] = util.ParseURL(context, *concertStreamURL)
	UrlMap[ConcertTradeURL] = util.ParseURL(context, *concertTradeURL)
	UrlMap[GoLokaliseURL] = util.ParseURL(context, *golokaliseURL)
	UrlMap[PollURL] = util.ParseURL(context, *pollURL)
	UrlMap[GoAuthURL] = util.ParseURL(context, *goAuthURL)
	UrlMap[CategoryURL] = util.ParseURL(context, *categoryURL)
	UrlMap[PaymentNotifProxyURL] = util.ParseURL(context, *paymentNotifProxyURL)
	util.TestURLs(context, UrlMap[GoURL])
	if *prod { // we would not test those url since we have only launched goapi in dev
		util.TestURLs(context, UrlMap[GoTradeURL])
	}

}
