package main

// Getting DI dependency graph
// install graphviz ( https://graphviz.org/download/ )
// exec `go test -generate_dot_file <filename>.dot` to get .dot file
// exec `dot -Tsvg <filename>.dot -O` to get .png file

import (
	"bytes"
	"flag"
	"io/ioutil"
	"testing"

	"github.com/17media/api/setup/dimanager"
)

var (
	generateDotFile = flag.String("generate_dot_file", "", "generates dependency tree .dot file")
)

func TestDependency(t *testing.T) {
	// Remarks: Compile() MUST be called before flag.Parse()
	// Please see setup/dimanager/dimanager.go for further information
	dimanager.DefaultManager.Compile()

	// check if all dependency is correctly set
	if err := dimanager.DefaultManager.VerifyDependency(); err != nil {
		panic(err)
	}

	if *generateDotFile != "" {
		var b bytes.Buffer
		if err := dimanager.DefaultManager.Visualize(&b); err != nil {
			panic(err)
		}
		if err := ioutil.WriteFile(*generateDotFile, b.Bytes(), 0644); err != nil {
			panic(err)
		}
	}
}
