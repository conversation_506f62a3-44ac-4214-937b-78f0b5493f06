package base

import (
	"errors"
	"fmt"
	"testing"

	"github.com/17media/api/base/e2e"
)

var (
	// ErrHTTPStatusCodeNotSuccess is the error returns when HTTP status code not success
	ErrHTTPStatusCodeNotSuccess = errors.New("HTTP status code not success")

	// HardcodedUserInfos provide specific user infos.
	HardcodedUserInfos = map[string]struct {
		UserID      string
		Name        string
		AccessToken string
	}{
		"e2euser0118": {
			UserID:      "30cfa38d-fb31-420c-8b15-3ec76aeca8bd",
			Name:        "e2euser0118",
			AccessToken: "40eaf245-e1bf-4b41-9ee3-f4541b150800",
		},
		"e2euser87": {
			UserID:      "304a38cd-ad43-4df1-b0bb-6e62f39248d5",
			Name:        "e2euser87",
			AccessToken: "c4dc1794-4a1e-4fff-abb9-07c622387e76",
		},
		"e2ebot1": {
			UserID:      "fdf8f01d-3d64-4fc3-5e31-6a292f5c4ef2",
			Name:        "e2ebot1",
			AccessToken: "09105393-2650-416e-7749-a31f74a85bdd",
		},
		"e2ebot2": {
			UserID:      "7ca0983b-3fa3-4e2c-9ebf-093ea4b92d2a",
			Name:        "e2ebot2",
			AccessToken: "37d0677e-47bf-4c85-93d9-4ecf3159578c",
		},
	}
)

// User simulates an user's behavior.
type User struct {
	e2e.HTTPWrapper
	Name       string
	appProfile AppProfile
	cfg        config
}

type config struct {
	appProfile AppProfile
	openID     string
	logger     e2e.Logger
}

// NewUser creates an http wrapper wrapping user information into header
func NewUser(options ...func(*config) error) (*User, error) {
	cfg := config{
		appProfile: MustNewAppProfile(),
	}
	for _, option := range options {
		err := option(&cfg)
		if err != nil {
			return nil, err
		}
	}

	if cfg.logger == nil {
		return nil, fmt.Errorf("nil logger")
	}

	w := e2e.HTTPWrapper{
		Logger: cfg.logger,
		Header: cfg.appProfile.MustToHeader(),
	}

	usr := &User{
		HTTPWrapper: w,
		Name:        cfg.openID,
		appProfile:  cfg.appProfile,
	}

	return usr, nil
}

// MustNewUser wraps NewUser function and panics when error returned
func MustNewUser(options ...func(*config) error) *User {
	usr, err := NewUser(options...)
	if err != nil {
		panic(err)
	}
	return usr
}

// Hardcode configs user with a hardcoded userID
func Hardcode(name string) func(*config) error {
	return func(cfg *config) error {
		userInfo, ok := HardcodedUserInfos[name]
		if !ok {
			panic(fmt.Errorf("Unable to find user info for name <%s>", name))
		}
		cfg.appProfile.AccessToken = userInfo.AccessToken
		cfg.appProfile.UserID = userInfo.UserID
		cfg.openID = userInfo.Name
		return nil
	}
}

// AccessToken configs user with a hardcoded access token
func AccessToken(accessToken string) func(*config) error {
	return func(cfg *config) error {
		cfg.appProfile.AccessToken = accessToken
		return nil
	}
}

// UserID configs user with a hardcoded userID
func UserID(userID string) func(*config) error {
	return func(cfg *config) error {
		cfg.appProfile.UserID = userID
		return nil
	}
}

// E2ELog sets logger for e2e test. It wraps testing.T to assert functions
func E2ELog(t *testing.T) func(*config) error {
	return func(cfg *config) error {
		cfg.logger = e2e.E2ELogger{t}
		return nil
	}
}

// StdoutLog sets logger to stdout.
func StdoutLog() func(*config) error {
	return func(cfg *config) error {
		cfg.logger = e2e.EmulatorLogger{}
		return nil
	}
}

// Signup with an token
func (u *User) Signup(token string) (string, int) {
	authRes := struct {
		Token string `json:"token"`
	}{}

	postBody := struct {
		AccessToken string `json:"facebookAccessToken"`
	}{
		AccessToken: token,
	}

	resp := u.Fire(e2e.HTTPPost("/auth/signup").Send(postBody), authRes)

	return authRes.Token, resp.StatusCode
}

// Login configs user with an token
func (u *User) Login(token string) (string, int) {
	authRes := struct {
		Token string `json:"token"`
	}{}

	postBody := struct {
		AccessToken string `json:"facebookAccessToken"`
	}{
		AccessToken: token,
	}

	resp := u.Fire(e2e.HTTPPost("/auth/login").Send(postBody), authRes)

	return authRes.Token, resp.StatusCode
}
