package utils

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"flag"
	"net/http"
	"net/url"
	"strings"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models/keys"
	regionSrv "github.com/17media/api/service/region"
	"github.com/17media/logrus"
)

var (
	urlMapJSON = flag.String("url_map_json", "[]", "URL mapping of servers")

	getIPRegion = regionSrv.GetIPRegion

	// ErrStatusNotOK indicates that the status is not `http.StatusOK`
	ErrStatusNotOK = errors.New("status not ok")
	// ErrStatusClientError indicates that the status is client error
	ErrStatusClientError = errors.New("status client Error")
	// ErrStatusServerError indicates that the status is server error
	ErrStatusServerError = errors.New("status server Error")
)

// EncodeURLCacheKey encode url cache key
// query is sorted and empty fields are removed
// region is injected into query according to ip address when region is not provided
func EncodeURLCacheKey(header http.Header, path string, query url.Values) string {
	for k, v := range query {
		if len(v) == 0 || v[0] == "" {
			query.Del(k)
		}
	}

	if query.Get("region") == "" {
		ipRegion, err := getIPRegion(header.Get("true-client-ip"))
		if err == nil {
			query.Add("region", ipRegion)
		}
	}

	b64Query := base64.StdEncoding.EncodeToString([]byte(query.Encode()))
	return keys.RedisKey(path, b64Query)
}

// DecodeURLCacheKey decode cache key to path and query string
func DecodeURLCacheKey(key string) (path, query string, err error) {
	parts := strings.Split(key, ":")
	if len(parts) != 2 {
		return "", "", errors.New("invalid key")
	}
	b, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		return "", "", err
	}
	return parts[0], string(b), nil
}

// SingleJoiningSlash joins two strings with a single slash
func SingleJoiningSlash(a, b string) string {
	aslash := strings.HasSuffix(a, "/")
	bslash := strings.HasPrefix(b, "/")
	switch {
	case aslash && bslash:
		return a + b[1:]
	case !aslash && !bslash:
		return a + "/" + b
	}
	return a + b
}

// GetURLMap generates the url map from the flag `url_map_json`
func GetURLMap(context ctx.CTX) map[string]*url.URL {
	var urlMappings []struct {
		PathPrefixes []string `json:"pathPrefixes"`
		URL          string   `json:"url"`
	}
	// replace ' in urlMapJSON with " beacuse go need this format to Unmarshal
	// can't use \\" in config.yaml because k8s can't recognize it and will append a escape before it
	urlMapJSONRe := strings.ReplaceAll(*urlMapJSON, "'", "\"")
	//trim head and tail quatation mark
	urlMapJSONRe = strings.Trim(urlMapJSONRe, "\"")

	if err := json.Unmarshal([]byte(urlMapJSONRe), &urlMappings); err != nil {
		context.WithFields(logrus.Fields{"err": err, "urlMapJSON": *urlMapJSON}).Panic("failed to unmarshal url map")
	}

	// log for debug on k8s
	context.WithFields(logrus.Fields{"urlMapJSON": *urlMapJSON, "urlMappings": urlMappings}).Info("result of json.Unmarshal url map")
	mp := make(map[string]*url.URL)
	for _, v := range urlMappings {
		for _, p := range v.PathPrefixes {
			mp[p] = ParseURL(context, v.URL)
		}
	}
	return mp
}

// ParseURL will generate an URL object from a URL string
func ParseURL(context ctx.CTX, u string) *url.URL {
	h, err := url.Parse(u)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err": err,
			"url": h,
		}).Panic("cannot parse url")
	}
	return h
}

// GeneratePathPrefixes generates all possible prefix combinations
// e.g., when the path is `/api/v1/cells`,
//
//	the combination will be `/`, `/api`, `/api/v1`, and `/api/v1/cells`
func GeneratePathPrefixes(path string) []string {
	prefixes := []string{}
	p := ""
	for _, s := range strings.Split(path, "/") {
		if p != "/" {
			p += "/"
		}
		p += s
		prefixes = append(prefixes, p)
	}
	return prefixes
}
