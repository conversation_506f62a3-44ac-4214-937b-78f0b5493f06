package main

// Getting DI dependency graph
// install graphviz ( https://graphviz.org/download/ )
// exec `go test -generate_dot_file <filename>.dot` to get .dot file
// exec `dot -Tsvg <filename>.dot -O` to get .png file

import (
	"bytes"
	"flag"
	"io/ioutil"
	"net/http"
	"net/url"
	"testing"

	"github.com/17media/api/setup/dimanager"
	"github.com/stretchr/testify/mock"
	"gotest.tools/assert"
)

var (
	generateDotFile = flag.String("generate_dot_file", "", "generates dependency tree .dot file")
)

func TestDependency(t *testing.T) {
	// Remarks: Compile() MUST be called before flag.Parse()
	// Please see setup/dimanager/dimanager.go for further information
	dimanager.DefaultManager.Compile()

	// check if all dependency is correctly set
	if err := dimanager.DefaultManager.VerifyDependency(); err != nil {
		panic(err)
	}
	if *generateDotFile != "" {
		var b bytes.Buffer
		if err := dimanager.DefaultManager.Visualize(&b); err != nil {
			panic(err)
		}
		if err := ioutil.WriteFile(*generateDotFile, b.Bytes(), 0644); err != nil {
			panic(err)
		}
	}
}

type mockFuncs struct {
	mock.Mock
}

func (m *mockFuncs) getQueryParamExclusions(apiPrefix string) []string {
	ret := m.Called(apiPrefix)
	return ret.Get(0).([]string)
}

func TestModifiesQuery(t *testing.T) {
	mockFunc := new(mockFuncs)
	getQueryParamExclusions = mockFunc.getQueryParamExclusions

	tests := []struct {
		desc        string
		path        string
		query       *url.Values
		header      http.Header
		setup       func()
		resultQuery *url.Values
	}{
		{
			desc: "default remove cursor",
			path: "/api/v1/cells",
			query: &url.Values{
				"region": []string{"TW"},
				"cursor": []string{"1234"},
				"count":  []string{"10"},
			},
			setup: func() {
				mockFunc.On("getQueryParamExclusions", "/").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/api").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/api/v1").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/api/v1/cells").Return([]string{}).Once()
			},
			resultQuery: &url.Values{
				"region": []string{"TW"},
				"count":  []string{"10"},
			},
		},
		{
			desc: "count as cache key is excluded by config",
			path: "/api/v1/cells",
			query: &url.Values{
				"region": []string{"TW"},
				"cursor": []string{"1234"},
				"count":  []string{"10"},
			},
			setup: func() {
				mockFunc.On("getQueryParamExclusions", "/").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/api").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/api/v1").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/api/v1/cells").Return([]string{"count"}).Once()
			},
			resultQuery: &url.Values{
				"region": []string{"TW"},
			},
		},
		{
			desc: "test region priority, UserSelectedRegion first",
			path: "/regionapi",
			query: &url.Values{
				"region": []string{"TW"},
			},
			header: http.Header{
				"UserSelectedRegion": []string{"US"},
				"Force-Region":       []string{"JP"},
				"userIpRegion":       []string{"HK"},
			},
			setup: func() {
				mockFunc.On("getQueryParamExclusions", "/").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/regionapi").Return([]string{}).Once()
			},
			resultQuery: &url.Values{
				"region": []string{"US"},
			},
		},
		{
			desc: "test region priority, Force-Region second",
			path: "/regionapi",
			query: &url.Values{
				"region": []string{"TW"},
			},
			header: http.Header{
				"Force-Region": []string{"JP"},
				"userIpRegion": []string{"HK"},
			},
			setup: func() {
				mockFunc.On("getQueryParamExclusions", "/").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/regionapi").Return([]string{}).Once()
			},
			resultQuery: &url.Values{
				"region": []string{"JP"},
			},
		},
		{
			desc: "test region priority, userIpRegion third",
			path: "/regionapi",
			query: &url.Values{
				"region": []string{"TW"},
			},
			header: http.Header{
				"userIpRegion": []string{"HK"},
			},
			setup: func() {
				mockFunc.On("getQueryParamExclusions", "/").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/regionapi").Return([]string{}).Once()
			},
			resultQuery: &url.Values{
				"region": []string{"HK"},
			},
		},
		{
			desc:  "test region priority with no region in query",
			path:  "/regionapi",
			query: &url.Values{},
			header: http.Header{
				"userIpRegion": []string{"HK"},
			},
			setup: func() {
				mockFunc.On("getQueryParamExclusions", "/").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/regionapi").Return([]string{}).Once()
			},
			resultQuery: &url.Values{
				"region": []string{"HK"},
			},
		},
		{
			desc:  "region not provided in requset query or header",
			path:  "/regionapi",
			query: &url.Values{},
			setup: func() {
				mockFunc.On("getQueryParamExclusions", "/").Return([]string{}).Once()
				mockFunc.On("getQueryParamExclusions", "/regionapi").Return([]string{}).Once()
			},
			resultQuery: &url.Values{},
		},
	}
	for _, test := range tests {
		if test.setup != nil {
			test.setup()
		}
		modifiesQuery(test.path, test.query, test.header)
		assert.Equal(t, test.resultQuery.Encode(), test.resultQuery.Encode())
	}
}
