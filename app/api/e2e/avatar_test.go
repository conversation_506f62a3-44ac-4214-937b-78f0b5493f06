package revprox

import (
	"testing"

	"github.com/stretchr/testify/suite"

	b "github.com/17media/api/app/api/e2e/base"
)

func TestAvatarTestSuite(t *testing.T) {
	suite.Run(t, new(avatarTestSuite))
}

type avatarTestSuite struct {
	suite.Suite
}

func (s *avatarTestSuite) SetupTest() {
	b.SetEnv(*b.Env, s.T().Name(), b.TypeE2e)
}

func (s *avatarTestSuite) TestAvatar() {

	user := b.MustNew<PERSON>ser(b.E2ELog(s.T()), b.Hard<PERSON>("e2euser87"))

	// set larger OS version to enable avatar
	user.Header["OSVersion"] = "20.0"

	// get grabbags
	list := user.GetGrabBags()
	if len(list.GrabBags) == 0 {
		return
	}

	bagID := list.GrabBags[0].ID
	s.Require().NotEmpty(list.GrabBags[0].Plans)
	planID := list.GrabBags[0].Plans[0].ID

	// draw
	drawRes := user.DrawGrabBag(bagID, planID)
	s.Require().NotEmpty(drawRes.ResultItems)
	itemID := drawRes.ResultItems[0].ItemID

	// read item
	user.ReadItem(itemID)

	// update avatar
	items := []string{
		"ap_body_regular_sienna_0",
		"ap_eyes_bubble_aqua_0",
		"ap_nose_dot_sienna_0",
		"ap_mouth_smile_def_0",
		"ap_ears_regular_sienna_0",
		"ap_hair_blunt_orange_0",
		"ap_eyebrow_thin_aqua_0",
		"cl_bottom_horn_blue_3",
		"cl_shirt_casual_dftblack_1",
		"cl_shoes_sport_white_1",
	}
	user.UpdateAvatar(items)

	// get avatar
	checkItems := []string{}
	info := user.GetAvatar(user.UserID())
	s.Require().NotEmpty(info.Parts)
	for _, part := range info.Parts {
		checkItems = append(checkItems, part.NodeInfo.ItemID)
	}
	s.ElementsMatch(items, checkItems)
}
