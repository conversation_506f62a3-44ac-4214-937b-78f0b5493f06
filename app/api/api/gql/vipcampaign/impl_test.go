package vipcampaign

import (
	"context"
	"fmt"
	"testing"
	"time"

	graphql "github.com/graph-gophers/graphql-go"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/17media/api/base/ctx"
	btime "github.com/17media/api/base/time"
	"github.com/17media/api/models"
	armyModel "github.com/17media/api/models/army"
	campaignModel "github.com/17media/api/models/campaignengine"
	legacyPayModel "github.com/17media/api/models/pay"
	orderModel "github.com/17media/api/models/purchase/order"
	mRegion "github.com/17media/api/service/region/mocks"
	ce "github.com/17media/api/stores/campaignengine"
	mCampaignEngine "github.com/17media/api/stores/campaignengine/mocks"
	cRule "github.com/17media/api/stores/campaignengine/rule"
	"github.com/17media/api/stores/user"
)

var (
	mockContext = ctx.Background()
	mockUserID  = "mockUser"

	locTW, _ = time.LoadLocation("Asia/Taipei")

	mockCTX = ctx.WithValues(mockContext, map[string]interface{}{
		"UserInfo": &models.User{
			UserID: mockUserID,
		},
		"ctx":        mockContext,
		"intraToken": "",
		"permissions": map[string]struct{}{
			user.PermWriteAccessVIPCampaign: {},
			user.PermReadAccessVIPCampaign:  {},
		},
	})

	mockCTX2 = ctx.WithValues(mockContext, map[string]interface{}{
		"UserInfo": &models.User{
			UserID: mockUserID,
			Region: "JP",
		},
		"ctx":        mockContext,
		"intraToken": "",
		"permissions": map[string]struct{}{
			user.PermWriteAccessVIPCampaign: {},
			user.PermReadAccessVIPCampaign:  {},
		},
	})

	mockCTXHKRegion = ctx.WithValues(mockContext, map[string]interface{}{
		"UserInfo": &models.User{
			UserID: mockUserID,
			Region: "HK",
		},
		"ctx":        mockContext,
		"intraToken": "",
		"permissions": map[string]struct{}{
			user.PermWriteAccessVIPCampaign: {},
			user.PermReadAccessVIPCampaign:  {},
		},
	})

	mockNoPermCTX = ctx.WithValues(mockContext, map[string]interface{}{
		"UserInfo": &models.User{
			UserID: mockUserID,
		},
		"ctx":         mockContext,
		"intraToken":  "",
		"permissions": map[string]struct{}{},
	})
)

type vipCampaignSuite struct {
	suite.Suite
	im *impl

	mCampaignEngine mCampaignEngine.Store
	mRegionService  mRegion.Service
}

func (s *vipCampaignSuite) SetupSuite() {
}

func (s *vipCampaignSuite) TearDownSuite() {
}

func (s *vipCampaignSuite) SetupTest() {
	s.mCampaignEngine = mCampaignEngine.Store{}
	s.mRegionService = mRegion.Service{}
	s.im = &impl{
		campaignEngine: &s.mCampaignEngine,
		regionService:  &s.mRegionService,
	}
}

func (s *vipCampaignSuite) TearDownTest() {
	s.mCampaignEngine.AssertExpectations(s.T())
}

func TestVIPCampaign(t *testing.T) {
	suite.Run(t, new(vipCampaignSuite))
}

func (s *vipCampaignSuite) TestCreateVIPCampaign() {

	type testCase struct {
		desc                   string
		input                  createVIPCampaignInput
		mockCTX                ctx.CTX
		mockExecutorRegion     string
		expCreateCampaignInput *ce.CreateCampaignInput
		expErr                 error
	}

	mockReferenceCode1 := "VIP_20220307_001"
	mockCampaignName1 := "VIP monthly sales"
	mockStartTime1 := time.Date(2022, time.March, 7, 0, 0, 0, 0, locTW)
	mockEndTime1 := time.Date(2022, time.March, 17, 0, 0, 0, 0, locTW)
	mockLimitTimes := int32(100)
	mockLimitCycleDays := int32(1)
	mockProductID := "ProductID1"
	mockBonus := int32(10000)
	mockVipUserType := "NEW_VIP"
	mockExtraBonusRate := float64(10)
	mockCurrencyAmount := float64(10.99)
	mockCurrency := "TWD"
	mockEquivalentPoint := 2000
	mockArmyRank := "SERGEANT"
	mockPeriod := "Period1M"
	mockDefaultVIPType := campaignModel.ALL
	mockVIPTypeStr := "VIP"
	mockVIPType := campaignModel.VIP

	tests := []testCase{
		{
			desc: "success - original price",
			input: createVIPCampaignInput{
				ReferenceCode: mockReferenceCode1,
				CampaignName:  mockCampaignName1,
				StartTime:     graphql.Time{Time: mockStartTime1},
				EndTime:       graphql.Time{Time: mockEndTime1},
				ConditionType: "VIP_ORIGINAL_PRICE",
				ConditionRewardInput: vipCampaignConditionRewardInput{
					VipOriginalPriceInput: &originalPriceInput{
						Condition: []*originalPriceCampaignCondition{
							{
								ProductID:     mockProductID,
								CampaignBonus: mockBonus,
								VipUserCondition: &[]*originalPriceVIPUserCondition{
									{
										VipUserType:    mockVipUserType,
										ExtraBonusRate: mockExtraBonusRate,
										StartDate:      &graphql.Time{Time: mockStartTime1},
										EndDate:        &graphql.Time{Time: mockEndTime1},
									},
								},
							},
						},
						RewardsSetting: &originalPriceRewardsSetting{
							LimitTimes:         mockLimitTimes,
							LimitResetCycleDay: nil,
							Reset:              false,
							HighestPlan:        false,
						},
					},
				},
				TargetUserRegion: nil,
				Draft:            true,
			},
			mockCTX: mockCTX,
			expCreateCampaignInput: &ce.CreateCampaignInput{
				ReferenceCode: mockReferenceCode1,
				CampaignName:  mockCampaignName1,
				StartTime:     mockStartTime1.Unix(),
				EndTime:       mockEndTime1.Unix(),
				Category:      campaignModel.VIPOriginalPrice,
				Rule: cRule.CampaignRule{
					VIPOriginalPrice: &cRule.CampaignRuleVIPOriginalPrice{
						RewardsSetting: cRule.VIPOriginalPriceRewardsSetting{
							LimitTimes:         int(mockLimitTimes),
							LimitResetCycleDay: 0,
							Reset:              false,
							HighestPlan:        false,
						},
						Condition: []cRule.VIPOriginalPriceCondition{
							{
								ProductID:     mockProductID,
								CampaignBonus: int(mockBonus),
								VIPUserCondition: []cRule.VIPUserCondition{
									{
										VIPUserType:    cRule.NewVIP,
										ExtraBonusRate: mockExtraBonusRate,
										StartTime:      mockStartTime1.Unix(),
										EndTime:        mockEndTime1.Unix(),
									},
								},
							},
						},
					},
				},
				TargetUserRegion: []string{},
				Status:           campaignModel.CampaignStatusDraft,
			},
			expErr: nil,
		},
		{
			desc: "success - specific amount without vip type",
			input: createVIPCampaignInput{
				ReferenceCode: mockReferenceCode1,
				CampaignName:  mockCampaignName1,
				StartTime:     graphql.Time{Time: mockStartTime1},
				EndTime:       graphql.Time{Time: mockEndTime1},
				ConditionType: "VIP_SPECIFIC_AMOUNT",
				ConditionRewardInput: vipCampaignConditionRewardInput{
					VipSpecificAmountInput: &specificAmountInput{
						Condition: []*specificAmountCampaignCondition{
							{
								SpecificAmount: currencyAmount{
									Amount:   mockCurrencyAmount,
									Currency: mockCurrency,
								},
								EquivalentPoint: int32(mockEquivalentPoint),
								CampaignBonus:   mockBonus,
							},
						},
						RewardsSetting: specificAmountRewardsSetting{
							RewardCountLimit: &specificAmountRewardCountLimit{
								LimitTimes:         mockLimitTimes,
								Reset:              true,
								LimitResetCycleDay: &mockLimitCycleDays,
							},
						},
					},
				},
				TargetUserRegion: nil,
				Draft:            true,
			},
			mockCTX:            mockCTX2,
			mockExecutorRegion: "JP",
			expCreateCampaignInput: &ce.CreateCampaignInput{
				ReferenceCode: mockReferenceCode1,
				CampaignName:  mockCampaignName1,
				StartTime:     mockStartTime1.Unix(),
				EndTime:       mockEndTime1.Unix(),
				Category:      campaignModel.VIPSpecificAmount,
				Rule: cRule.CampaignRule{
					VIPSpecificAmount: &cRule.CampaignRuleVIPSpecificAmount{
						RewardsSetting: cRule.VIPSpecificAmountRewardsSetting{
							RewardCountLimit: &cRule.VIPSpecificAmountRewardCountLimit{
								LimitTimes:         int(mockLimitTimes),
								Reset:              true,
								LimitResetCycleDay: int(mockLimitCycleDays),
							},
							VipType: &mockDefaultVIPType,
						},
						Condition: []cRule.VIPSpecificAmountCondition{
							{
								SpecificAmount: cRule.CurrencyAmount{
									Amount:   mockCurrencyAmount,
									Currency: mockCurrency,
								},
								EquivalentPoint: int(mockEquivalentPoint),
								CampaignBonus:   int(mockBonus),
							},
						},
					},
				},
				TargetUserRegion: []string{},
				Status:           campaignModel.CampaignStatusDraft,
			},
			expErr: nil,
		},
		{
			desc: "success - specific amount with vip type",
			input: createVIPCampaignInput{
				ReferenceCode: mockReferenceCode1,
				CampaignName:  mockCampaignName1,
				StartTime:     graphql.Time{Time: mockStartTime1},
				EndTime:       graphql.Time{Time: mockEndTime1},
				ConditionType: "VIP_SPECIFIC_AMOUNT",
				ConditionRewardInput: vipCampaignConditionRewardInput{
					VipSpecificAmountInput: &specificAmountInput{
						Condition: []*specificAmountCampaignCondition{
							{
								SpecificAmount: currencyAmount{
									Amount:   mockCurrencyAmount,
									Currency: mockCurrency,
								},
								EquivalentPoint: int32(mockEquivalentPoint),
								CampaignBonus:   mockBonus,
							},
						},
						RewardsSetting: specificAmountRewardsSetting{
							RewardCountLimit: &specificAmountRewardCountLimit{
								LimitTimes:         mockLimitTimes,
								Reset:              true,
								LimitResetCycleDay: &mockLimitCycleDays,
							},
							VipType: &mockVIPTypeStr,
						},
					},
				},
				TargetUserRegion: nil,
				Draft:            true,
			},
			mockCTX:            mockCTX2,
			mockExecutorRegion: "JP",
			expCreateCampaignInput: &ce.CreateCampaignInput{
				ReferenceCode: mockReferenceCode1,
				CampaignName:  mockCampaignName1,
				StartTime:     mockStartTime1.Unix(),
				EndTime:       mockEndTime1.Unix(),
				Category:      campaignModel.VIPSpecificAmount,
				Rule: cRule.CampaignRule{
					VIPSpecificAmount: &cRule.CampaignRuleVIPSpecificAmount{
						RewardsSetting: cRule.VIPSpecificAmountRewardsSetting{
							RewardCountLimit: &cRule.VIPSpecificAmountRewardCountLimit{
								LimitTimes:         int(mockLimitTimes),
								Reset:              true,
								LimitResetCycleDay: int(mockLimitCycleDays),
							},
							VipType: &mockVIPType,
						},
						Condition: []cRule.VIPSpecificAmountCondition{
							{
								SpecificAmount: cRule.CurrencyAmount{
									Amount:   mockCurrencyAmount,
									Currency: mockCurrency,
								},
								EquivalentPoint: int(mockEquivalentPoint),
								CampaignBonus:   int(mockBonus),
							},
						},
					},
				},
				TargetUserRegion: []string{},
				Status:           campaignModel.CampaignStatusDraft,
			},
			expErr: nil,
		},
		{
			desc: "success - army plan",
			input: createVIPCampaignInput{
				ReferenceCode: mockReferenceCode1,
				CampaignName:  mockCampaignName1,
				StartTime:     graphql.Time{Time: mockStartTime1},
				EndTime:       graphql.Time{Time: mockEndTime1},
				ConditionType: "ARMY_PLAN",
				ConditionRewardInput: vipCampaignConditionRewardInput{
					ArmyPlanInput: &armyPlanInput{
						Condition: []*armyPlanCampaignCondition{
							{
								ArmyRank:      mockArmyRank,
								Period:        mockPeriod,
								CampaignBonus: mockBonus,
							},
						},
						RewardsSetting: &armyPlanRewardsSetting{
							LimitTimes:         mockLimitTimes,
							LimitResetCycleDay: &mockLimitCycleDays,
							HighestPlan:        true,
						},
					},
				},
				TargetUserRegion: nil,
				Draft:            true,
			},
			mockCTX: mockCTX,
			expCreateCampaignInput: &ce.CreateCampaignInput{
				ReferenceCode: mockReferenceCode1,
				CampaignName:  mockCampaignName1,
				StartTime:     mockStartTime1.Unix(),
				EndTime:       mockEndTime1.Unix(),
				Category:      campaignModel.ArmyPlan,
				Rule: cRule.CampaignRule{
					ArmyPlan: &cRule.CampaignRuleArmyPlan{
						Condition: []cRule.ArmyPlanCampaignCondition{
							{
								ArmyRank:      armyModel.ArmyRank_RANK_1,
								Period:        campaignModel.Period1M,
								CampaignBonus: int(mockBonus),
							},
						},
						RewardsSetting: cRule.ArmyPlanRewardsSetting{
							LimitTimes:         int(mockLimitTimes),
							Reset:              false,
							LimitResetCycleDay: int(mockLimitCycleDays),
							HighestPlan:        true,
						},
					},
				},
				TargetUserRegion: []string{},
				Status:           campaignModel.CampaignStatusDraft,
			},
			expErr: nil,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		createCampaignInput, err := s.im.convertCreateCampaignInput(t.mockCTX, t.mockExecutorRegion, t.input)
		s.Equal(t.expErr, err, t.desc)
		s.NoError(err)
		s.Require().Equal(t.expCreateCampaignInput, createCampaignInput, t.desc)
	}
}

func (s *vipCampaignSuite) TestVIPCampaigns() {
	mockLimit := int32(100)
	mockCampaignID1 := int64(1)
	mockCampaignID2 := int64(2)
	mockCampaignID3 := int64(3)
	mockCampaignID4 := int64(4)
	mockCampaignID5 := int64(5)

	mockReferenceCode1 := "VIP_20220307_001"
	mockReferenceCode2 := "VIP_20220307_002"
	mockReferenceCode3 := "VIP_20220307_003"
	mockReferenceCode4 := "VIP_20220307_004"
	mockReferenceCode5 := "VIP_20220307_005"
	mockOperationRegion := "TW"

	mockCampaignName1 := "VIP monthly sales"
	mockCampaignName2 := "Army Campaign"
	mockCampaignName3 := "VIP monthly sales2"
	mockCampaignName4 := "VIP monthly sales3"
	mockCampaignName5 := "VIP monthly sales4"

	mockStartTime1 := time.Date(2022, time.March, 7, 0, 0, 0, 0, locTW).Unix()
	mockEndTime1 := time.Date(2022, time.March, 17, 0, 0, 0, 0, locTW).Unix()
	mockStartTime2 := time.Date(2022, time.March, 10, 0, 0, 0, 0, locTW).Unix()
	mockEndTime2 := time.Date(2022, time.April, 10, 0, 0, 0, 0, locTW).Unix()
	mockStartTime3 := time.Date(2022, time.March, 5, 0, 0, 0, 0, locTW).Unix()
	mockEndTime3 := time.Date(2022, time.April, 10, 0, 0, 0, 0, locTW).Unix()
	mockStartTime4 := time.Date(2022, time.February, 10, 0, 0, 0, 0, locTW).Unix()
	mockEndTime4 := time.Date(2022, time.March, 1, 0, 0, 0, 0, locTW).Unix()
	mockStartTime5 := time.Date(2022, time.March, 10, 0, 0, 0, 0, locTW).Unix()
	mockEndTime5 := time.Date(2022, time.April, 10, 0, 0, 0, 0, locTW).Unix()

	mockCampaign1 := &cRule.Campaign{
		ID:              mockCampaignID1,
		ReferenceCode:   mockReferenceCode1,
		CampaignName:    mockCampaignName1,
		OperationRegion: mockOperationRegion,
		StartTime:       mockStartTime1,
		EndTime:         mockEndTime1,
		CreateUserID:    mockUserID,
		Status:          campaignModel.CampaignStatusDraft,
	}
	mockCampaign2 := &cRule.Campaign{
		ID:              mockCampaignID2,
		ReferenceCode:   mockReferenceCode2,
		CampaignName:    mockCampaignName2,
		OperationRegion: mockOperationRegion,
		StartTime:       mockStartTime2,
		EndTime:         mockEndTime2,
		CreateUserID:    mockUserID,
		Status:          campaignModel.CampaignStatusScheduled,
	}
	mockCampaign3 := &cRule.Campaign{
		ID:              mockCampaignID3,
		ReferenceCode:   mockReferenceCode3,
		CampaignName:    mockCampaignName3,
		OperationRegion: mockOperationRegion,
		StartTime:       mockStartTime3,
		EndTime:         mockEndTime3,
		CreateUserID:    mockUserID,
		Status:          campaignModel.CampaignStatusScheduled,
	}
	mockCampaign4 := &cRule.Campaign{
		ID:              mockCampaignID4,
		ReferenceCode:   mockReferenceCode4,
		CampaignName:    mockCampaignName4,
		OperationRegion: mockOperationRegion,
		StartTime:       mockStartTime4,
		EndTime:         mockEndTime4,
		CreateUserID:    mockUserID,
		Status:          campaignModel.CampaignStatusDraft,
	}
	mockCampaign5 := &cRule.Campaign{
		ID:              mockCampaignID5,
		ReferenceCode:   mockReferenceCode5,
		CampaignName:    mockCampaignName5,
		OperationRegion: mockOperationRegion,
		StartTime:       mockStartTime5,
		EndTime:         mockEndTime5,
		CreateUserID:    mockUserID,
		Status:          campaignModel.CampaignStatusExpired,
	}

	type args struct {
		Cursor *string
		Limit  int32
		Filter vipCampaignsFilter
	}

	type testCase struct {
		desc          string
		args          args
		mockCTX       context.Context
		mockFunc      func(t testCase)
		mockCond      ce.GetCampaignsCond
		expCampaigns  []*cRule.Campaign
		expNextCursor string
		expErr        error
	}

	tests := []testCase{
		{
			desc: "succ show draft campaign",
			args: args{
				Cursor: nil,
				Limit:  mockLimit,
				Filter: vipCampaignsFilter{
					Status: "CAMPAIGN_DRAFT",
				},
			},
			mockCTX: mockCTX,
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaigns", mock.AnythingOfType("ctx.CTX"), "", int(t.args.Limit), t.mockCond).Return([]*cRule.Campaign{mockCampaign1}, "", nil).Once()
			},
			mockCond: ce.GetCampaignsCond{
				Statuses: []campaignModel.CampaignStatus{
					campaignModel.CampaignStatusDraft,
				},
			},
			expCampaigns:  []*cRule.Campaign{mockCampaign1},
			expNextCursor: "",
			expErr:        nil,
		},
		{
			desc: "succ show schedule campaign",
			args: args{
				Cursor: nil,
				Limit:  mockLimit,
				Filter: vipCampaignsFilter{
					Status: "CAMPAIGN_SCHEDULED",
				},
			},
			mockCTX: mockCTX,
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaigns", mock.AnythingOfType("ctx.CTX"), "", int(t.args.Limit), t.mockCond).Return([]*cRule.Campaign{mockCampaign2}, "", nil).Once()
			},
			mockCond: ce.GetCampaignsCond{
				Statuses: []campaignModel.CampaignStatus{
					campaignModel.CampaignStatusScheduled,
				},
			},
			expCampaigns:  []*cRule.Campaign{mockCampaign2},
			expNextCursor: "",
			expErr:        nil,
		},
		{
			desc: "succ show running campaign",
			args: args{
				Cursor: nil,
				Limit:  mockLimit,
				Filter: vipCampaignsFilter{
					Status: "CAMPAIGN_RUNNING",
				},
			},
			mockCTX: mockCTX,
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaigns", mock.AnythingOfType("ctx.CTX"), "", int(t.args.Limit), t.mockCond).Return([]*cRule.Campaign{mockCampaign3}, "", nil).Once()
			},
			mockCond: ce.GetCampaignsCond{
				Statuses: []campaignModel.CampaignStatus{
					campaignModel.CampaignStatusRunning,
				},
			},
			expCampaigns:  []*cRule.Campaign{mockCampaign3},
			expNextCursor: "",
			expErr:        nil,
		},
		{
			desc: "succ show expired campaign",
			args: args{
				Cursor: nil,
				Limit:  mockLimit,
				Filter: vipCampaignsFilter{
					Status: "CAMPAIGN_EXPIRED",
				},
			},
			mockCTX: mockCTX,
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaigns", mock.AnythingOfType("ctx.CTX"), "", int(t.args.Limit), t.mockCond).Return([]*cRule.Campaign{mockCampaign4, mockCampaign5}, "", nil).Once()
			},
			mockCond: ce.GetCampaignsCond{
				Statuses: []campaignModel.CampaignStatus{
					campaignModel.CampaignStatusExpired,
				},
			},
			expCampaigns:  []*cRule.Campaign{mockCampaign4, mockCampaign5},
			expNextCursor: "",
			expErr:        nil,
		},
		{
			desc: "failed, no permission",
			args: args{
				Cursor: nil,
				Limit:  mockLimit,
			},
			mockCTX:       mockNoPermCTX,
			mockFunc:      func(t testCase) {},
			expCampaigns:  nil,
			expNextCursor: "",
			expErr:        ErrNoPermission,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc(t)
		cResolver, err := s.im.VIPCampaigns(t.mockCTX, t.args)
		s.Equal(t.expErr, err, t.desc)
		if err == nil {
			s.Require().Equal(t.expCampaigns, cResolver.data, t.desc)
		}
	}
}

func (s *vipCampaignSuite) TestEditVIPCampaign() {
	mockCampaignID := "CPTW-000000001"
	mockeReferenceCode := "VIP_20220307_001"
	mockCampaignName1 := "VIP monthly sales"
	mockStartTime1 := time.Date(2022, time.March, 7, 0, 0, 0, 0, locTW)
	mockEndTime1 := time.Date(2022, time.March, 17, 0, 0, 0, 0, locTW)
	versionTime := time.Date(2022, time.March, 2, 0, 0, 0, 0, locTW)
	versionTimeMs := btime.MilliSecond(versionTime)
	mockLimitTimes := int32(100)
	mockProductID := "ProductID1"
	mockBonus := int32(10000)
	mockExtraBonusRate := float64(10)
	mockVipUserType := "NEW_VIP"
	status := campaignModel.CampaignStatusDraft
	editVIPTypeStr := "ALL"
	editVIPType := campaignModel.ALL

	mockEditCampaignInput := ce.EditCampaignInput{
		ReferenceCode: mockeReferenceCode,
		CampaignName:  mockCampaignName1,
		StartTime:     mockStartTime1.Unix(),
		EndTime:       mockEndTime1.Unix(),
		Category:      campaignModel.VIPSpecificAmount,
		Rule: cRule.CampaignRule{
			VIPSpecificAmount: &cRule.CampaignRuleVIPSpecificAmount{
				RewardsSetting: cRule.VIPSpecificAmountRewardsSetting{
					RewardCountLimit: &cRule.VIPSpecificAmountRewardCountLimit{
						LimitTimes:         int(mockLimitTimes),
						LimitResetCycleDay: 0,
						Reset:              false,
					},
					VipType: &editVIPType,
				},
				Condition: []cRule.VIPSpecificAmountCondition{
					{
						SpecificAmount: cRule.CurrencyAmount{
							Amount:   1000,
							Currency: "JPY",
						},
						EquivalentPoint: 500,
						CampaignBonus:   50,
					},
				},
			},
		},
		TargetUserRegion: []string{},
		Status:           &status,
		VersionTimeMs:    &versionTimeMs,
	}

	mockCampaign := cRule.Campaign{
		CampaignID:    mockCampaignID,
		ReferenceCode: mockeReferenceCode,
		CampaignName:  mockCampaignName1,
		StartTime:     mockStartTime1.Unix(),
		EndTime:       mockStartTime1.Unix(),
		CreateUserID:  mockUserID,
		UpdateUserID:  mockUserID,
		Status:        campaignModel.CampaignStatusDraft,
		Category:      campaignModel.VIPOriginalPrice,
		Rule: cRule.CampaignRule{
			VIPSpecificAmount: &cRule.CampaignRuleVIPSpecificAmount{
				RewardsSetting: cRule.VIPSpecificAmountRewardsSetting{
					RewardCountLimit: &cRule.VIPSpecificAmountRewardCountLimit{
						LimitTimes:         int(mockLimitTimes),
						LimitResetCycleDay: 0,
						Reset:              false,
					},
					VipType: &editVIPType,
				},
				Condition: []cRule.VIPSpecificAmountCondition{
					{
						SpecificAmount: cRule.CurrencyAmount{
							Amount:   1000,
							Currency: "JPY",
						},
						EquivalentPoint: 500,
						CampaignBonus:   50,
					},
				},
			},
		},
	}

	type args struct {
		Input editCampaignInput
	}

	type testCase struct {
		desc           string
		args           args
		mockCTX        ctx.CTX
		mockFunc       func(t testCase)
		expectCampaign cRule.Campaign
		expectError    error
	}

	tests := []testCase{
		{
			desc: "succ edit",
			args: args{
				Input: editCampaignInput{
					ID:            graphql.ID(mockCampaignID),
					ReferenceCode: mockeReferenceCode,
					CampaignName:  mockCampaignName1,
					StartTime:     graphql.Time{Time: mockStartTime1},
					EndTime:       graphql.Time{Time: mockEndTime1},
					ConditionType: "VIP_SPECIFIC_AMOUNT",
					ConditionRewardInput: vipCampaignConditionRewardInput{
						VipSpecificAmountInput: &specificAmountInput{
							Condition: []*specificAmountCampaignCondition{
								{
									SpecificAmount: currencyAmount{
										Amount:   1000,
										Currency: "JPY",
									},
									EquivalentPoint: int32(500),
									CampaignBonus:   int32(50),
								},
							},
							RewardsSetting: specificAmountRewardsSetting{
								RewardCountLimit: &specificAmountRewardCountLimit{
									LimitTimes:         int32(mockLimitTimes),
									LimitResetCycleDay: nil,
									Reset:              false,
								},
								VipType: &editVIPTypeStr,
							},
						},
					},
					TargetUserRegion: nil,
					Draft:            true,
					VersionTime:      graphql.Time{Time: versionTime},
				},
			},
			mockCTX: mockCTX2,
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("EditCampaign", mock.AnythingOfType("ctx.CTX"), mockUserID, mockCampaignID, mockEditCampaignInput).Return(&mockCampaign, nil).Once()
			},
			expectCampaign: mockCampaign,
			expectError:    nil,
		},
		{
			desc: "failed, no permission",
			args: args{
				Input: editCampaignInput{
					ID:            graphql.ID(mockCampaignID),
					ReferenceCode: mockeReferenceCode,
					CampaignName:  mockCampaignName1,
					StartTime:     graphql.Time{Time: mockStartTime1},
					EndTime:       graphql.Time{Time: mockEndTime1},
					ConditionType: "VIP_ORIGINAL_PRICE",
					ConditionRewardInput: vipCampaignConditionRewardInput{
						VipOriginalPriceInput: &originalPriceInput{
							Condition: []*originalPriceCampaignCondition{
								{
									ProductID:     mockProductID,
									CampaignBonus: mockBonus,
									VipUserCondition: &[]*originalPriceVIPUserCondition{
										{
											VipUserType:    mockVipUserType,
											ExtraBonusRate: mockExtraBonusRate,
											StartDate:      &graphql.Time{Time: mockStartTime1},
											EndDate:        &graphql.Time{Time: mockEndTime1},
										},
									},
								},
							},
							RewardsSetting: &originalPriceRewardsSetting{
								LimitTimes:         mockLimitTimes,
								LimitResetCycleDay: nil,
								Reset:              false,
								HighestPlan:        false,
							},
						},
					},
					TargetUserRegion: nil,
					Draft:            true,
					VersionTime:      graphql.Time{Time: versionTime},
				},
			},
			mockCTX:     mockNoPermCTX,
			mockFunc:    func(t testCase) {},
			expectError: ErrNoPermission,
		},
		{
			desc: "failed, invalid campaignID",
			args: args{
				Input: editCampaignInput{
					ID:            graphql.ID(""),
					ReferenceCode: mockeReferenceCode,
					CampaignName:  mockCampaignName1,
					StartTime:     graphql.Time{Time: mockStartTime1},
					EndTime:       graphql.Time{Time: mockEndTime1},
					ConditionType: "VIP_ORIGINAL_PRICE",
					ConditionRewardInput: vipCampaignConditionRewardInput{
						VipOriginalPriceInput: &originalPriceInput{
							Condition: []*originalPriceCampaignCondition{
								{
									ProductID:     mockProductID,
									CampaignBonus: mockBonus,
									VipUserCondition: &[]*originalPriceVIPUserCondition{
										{
											VipUserType:    mockVipUserType,
											ExtraBonusRate: mockExtraBonusRate,
											StartDate:      &graphql.Time{Time: mockStartTime1},
											EndDate:        &graphql.Time{Time: mockEndTime1},
										},
									},
								},
							},
							RewardsSetting: &originalPriceRewardsSetting{
								LimitTimes:         mockLimitTimes,
								LimitResetCycleDay: nil,
								Reset:              false,
								HighestPlan:        false,
							},
						},
					},
					TargetUserRegion: nil,
					Draft:            true,
					VersionTime:      graphql.Time{Time: versionTime},
				},
			},
			mockCTX:     mockCTX,
			mockFunc:    func(t testCase) {},
			expectError: ErrInvalidCampaignID,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc(t)

		campaignResolver, err := s.im.EditVIPCampaign(t.mockCTX, t.args)
		s.Equal(t.expectError, err, t.desc)
		if err == nil {
			s.Require().Equal(t.expectCampaign, campaignResolver.data, t.desc)
		}
	}
}

func (s *vipCampaignSuite) TestTargetUserRegionConfig() {
	regionInfoTW := campaignModel.RegionInfo{
		Region: "TW",
		Name:   "Taiwan",
	}
	regionInfoJP := campaignModel.RegionInfo{
		Region: "JP",
		Name:   "Japan",
	}
	regionInfoOther := campaignModel.RegionInfo{
		Region: "Other",
		Name:   "Other",
	}
	getTargetUserRegionInfos = func() []campaignModel.RegionInfo {
		return []campaignModel.RegionInfo{
			regionInfoTW, regionInfoJP, regionInfoOther,
		}
	}
	resolvers, _ := s.im.TargetUserRegionConfig(mockCTX)
	s.Require().Equal([]*RegionInfoResolver{
		{
			regionInfo: regionInfoTW,
		},
		{
			regionInfo: regionInfoJP,
		},
		{
			regionInfo: regionInfoOther,
		},
	}, resolvers)
}

func (s *vipCampaignSuite) TestVIPProductsList() {
	mockCurrency := "JPY"
	mockSellingCh := orderModel.SellingChannelWebJpBabyVip
	mockSellingCh2 := orderModel.SellingChannelWebJpSuperVipGMO
	mockProductType := legacyPayModel.ProductType_INTERNAL_ORDER_POINT

	mockProductsInfo1 := &campaignModel.VIPProductInfo{
		ProductID:    "mock_100_10",
		Price:        float64(100),
		Currency:     mockCurrency,
		BasicPoint:   100,
		RegularBonus: 10,
	}
	mockProductsInfo2 := &campaignModel.VIPProductInfo{
		ProductID:    "mock_200_20",
		Price:        float64(200),
		Currency:     mockCurrency,
		BasicPoint:   200,
		RegularBonus: 20,
	}
	mockProductsInfo3 := &campaignModel.VIPProductInfo{
		ProductID:    "mock_300_30",
		Price:        float64(300),
		Currency:     mockCurrency,
		BasicPoint:   300,
		RegularBonus: 30,
	}

	type testCase struct {
		desc                string
		mockCTX             context.Context
		mockSellingChannels []orderModel.SellingChannel
		mockCurrency        string
		mockProductType     legacyPayModel.ProductType
		mockFunc            func(t testCase)
		expectProductsList  []*campaignModel.VIPProductInfo
		expectErr           error
	}

	tests := []testCase{
		{
			desc:                "succ",
			mockCTX:             mockCTX2,
			mockSellingChannels: []orderModel.SellingChannel{mockSellingCh, mockSellingCh2},
			mockCurrency:        mockCurrency,
			mockProductType:     mockProductType,
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On(
					"GetProductsList",
					mock.AnythingOfType("ctx.CTX"), t.mockSellingChannels, t.mockCurrency, t.mockProductType,
				).Return([]*campaignModel.VIPProductInfo{
					mockProductsInfo1,
					mockProductsInfo2,
					mockProductsInfo3,
				}, nil).Once()
			},
			expectProductsList: []*campaignModel.VIPProductInfo{
				mockProductsInfo1,
				mockProductsInfo2,
				mockProductsInfo3,
			},
			expectErr: nil,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc(t)
		pResolver, err := s.im.VIPProductsList(t.mockCTX)
		s.Equal(t.expectErr, err, t.desc)
		if err == nil {
			s.Require().Equal(t.expectProductsList, pResolver.data, t.desc)
		}
	}
}

func (s *vipCampaignSuite) TestArmyProductsList() {
	mockCurrencyJPY := "JPY"
	mockCurrencyTWD := "TWD"
	mockProductType := legacyPayModel.ProductType_ARMY

	mockProductsInfo := []*campaignModel.ArmyProductInfo{
		{
			ArmyRank:        armyModel.ArmyRank_RANK_1,
			Price:           float64(100),
			Period:          campaignModel.Period1M,
			EquivalentPoint: 100,
		},
		{
			ArmyRank:        armyModel.ArmyRank_RANK_1,
			Price:           float64(200),
			Period:          campaignModel.Period3M,
			EquivalentPoint: 200,
		},
		{
			ArmyRank:        armyModel.ArmyRank_RANK_1,
			Price:           float64(300),
			Period:          campaignModel.Period1M,
			EquivalentPoint: 300,
		},
		{
			ArmyRank:        armyModel.ArmyRank_RANK_1,
			Price:           float64(400),
			Period:          campaignModel.Period3M,
			EquivalentPoint: 400,
		},
	}

	type testCase struct {
		desc                string
		mockCTX             context.Context
		mockSellingChannels []orderModel.SellingChannel
		mockCurrency        string
		mockProductType     legacyPayModel.ProductType
		mockFunc            func(t testCase)
		expectProductsList  []*campaignModel.ArmyProductInfo
		expectErr           error
	}

	tests := []testCase{
		{
			desc:                "success",
			mockCTX:             mockCTX2,
			mockSellingChannels: []orderModel.SellingChannel{orderModel.SellingChannelWebJpArmyGMO},
			mockCurrency:        mockCurrencyJPY,
			mockProductType:     mockProductType,
			mockFunc: func(t testCase) {
				for _, p := range mockProductsInfo {
					p.Currency = t.mockCurrency
				}

				s.mCampaignEngine.On(
					"GetArmyProductsList",
					mock.AnythingOfType("ctx.CTX"), t.mockSellingChannels, t.mockCurrency, t.mockProductType,
				).Return(mockProductsInfo, nil).Once()
			},
			expectProductsList: mockProductsInfo,
			expectErr:          nil,
		},
		{
			desc:                "HK region use TWD currency",
			mockCTX:             mockCTXHKRegion,
			mockSellingChannels: []orderModel.SellingChannel{orderModel.SellingChannelWebHkArmy},
			mockCurrency:        mockCurrencyTWD,
			mockProductType:     mockProductType,
			mockFunc: func(t testCase) {
				for _, p := range mockProductsInfo {
					p.Currency = t.mockCurrency
				}

				s.mCampaignEngine.On(
					"GetArmyProductsList",
					mock.AnythingOfType("ctx.CTX"), t.mockSellingChannels, t.mockCurrency, t.mockProductType,
				).Return(mockProductsInfo, nil).Once()
			},
			expectProductsList: mockProductsInfo,
			expectErr:          nil,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc(t)
		pResolver, err := s.im.ArmyProductsList(t.mockCTX)
		s.Equal(t.expectErr, err, t.desc)
		if err == nil {
			s.Require().Equal(t.expectProductsList, pResolver.data, t.desc)
		}
	}
}

func (s *vipCampaignSuite) TestRecallSendingTransaction() {
	mockTransactionID1 := int64(1)
	mockTransactionIDStr1 := "TS-000000001"
	mockTransactionID2 := int64(2)
	mockTransactionIDStr2 := "TS-000000002"
	mockTransactionID3 := int64(3)
	mockTransactionIDStr3 := "TS-000000003"
	mockTranstionIDList := []int64{mockTransactionID1, mockTransactionID2, mockTransactionID3}
	mockCampaignID := "CPTW-000000001"

	mockTransactions := []*ce.CampaignSendTransaction{
		{
			ID:         mockTransactionID1,
			CampaignID: mockCampaignID,
			UserID:     mockUserID,
			Status:     campaignModel.TransactionHistoryStatusRecall,
		},
		{
			ID:         mockTransactionID2,
			CampaignID: mockCampaignID,
			UserID:     mockUserID,
			Status:     campaignModel.TransactionHistoryStatusRecall,
		},
		{
			ID:         mockTransactionID3,
			CampaignID: mockCampaignID,
			UserID:     mockUserID,
			Status:     campaignModel.TransactionHistoryStatusRecall,
		},
	}

	type testCase struct {
		desc        string
		mockCTX     context.Context
		mockFunc    func()
		expectTxs   []*ce.CampaignSendTransaction
		expectError error
	}

	tests := []testCase{
		{
			desc:    "succ",
			mockCTX: mockCTX,
			mockFunc: func() {
				s.mCampaignEngine.On("RecallTransactions", mock.AnythingOfType("ctx.CTX"), mockUserID, mockTranstionIDList).Return(mockTransactions, nil).Once()
			},
			expectTxs:   mockTransactions,
			expectError: nil,
		},
		{
			desc:        "failed without recall permission",
			mockCTX:     mockNoPermCTX,
			mockFunc:    func() {},
			expectError: ErrNoPermission,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc()

		args := struct {
			Input recallInput
		}{
			Input: recallInput{IDs: []graphql.ID{
				graphql.ID(mockTransactionIDStr1),
				graphql.ID(mockTransactionIDStr2),
				graphql.ID(mockTransactionIDStr3),
			}},
		}

		tResolvers, err := s.im.RecallVIPCampaignTransaction(t.mockCTX, args)
		s.Require().Equal(t.expectError, err, t.desc)
		txDatas := make([]*ce.CampaignSendTransaction, len(tResolvers))
		for i, tResolver := range tResolvers {
			txDatas[i] = tResolver.data
		}
		if err == nil {
			s.Require().Equal(t.expectTxs, txDatas, t.desc)
		}
	}
}

func (s *vipCampaignSuite) TestSubmitVIPCampaign() {
	type args struct {
		Input submitCampaignInput
	}

	type testCase struct {
		desc      string
		mockCTX   context.Context
		mockArgs  args
		mockFunc  func(t testCase)
		expErr    error
		expOutput bool
	}

	mockCampaignID := "VIP_20220316_001"

	tests := []testCase{
		{
			desc:    "success",
			mockCTX: mockCTX,
			mockArgs: args{
				Input: submitCampaignInput{
					ID: graphql.ID(mockCampaignID),
				},
			},
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("UpdateCampaignStatus", mock.AnythingOfType("ctx.CTX"), mockUserID, mockCampaignID, campaignModel.CampaignStatusScheduled).Return(nil).Once()
			},
			expErr:    nil,
			expOutput: true,
		},
		{
			desc:    "no perm",
			mockCTX: mockNoPermCTX,
			mockArgs: args{
				Input: submitCampaignInput{
					ID: graphql.ID(mockCampaignID),
				},
			},
			mockFunc:  func(t testCase) {},
			expErr:    ErrNoPermission,
			expOutput: false,
		},
		{
			desc:    "empty campaignID string",
			mockCTX: mockCTX,
			mockArgs: args{
				Input: submitCampaignInput{
					ID: graphql.ID(""),
				},
			},
			mockFunc:  func(t testCase) {},
			expErr:    fmt.Errorf("campaignID is empty"),
			expOutput: false,
		},
		{
			desc:    "UpdateCampaignStatus fail",
			mockCTX: mockCTX,
			mockArgs: args{
				Input: submitCampaignInput{
					ID: graphql.ID(mockCampaignID),
				},
			},
			mockFunc: func(t testCase) {
				err := fmt.Errorf("UpdateCampaignStatus fail")
				s.mCampaignEngine.On("UpdateCampaignStatus", mock.AnythingOfType("ctx.CTX"), mockUserID, mockCampaignID, campaignModel.CampaignStatusScheduled).Return(err).Once()
			},
			expErr:    fmt.Errorf("UpdateCampaignStatus fail"),
			expOutput: false,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc(t)
		success, err := s.im.SubmitVIPCampaign(t.mockCTX, t.mockArgs)
		s.Equal(t.expErr, err, t.desc)
		s.Equal(t.expOutput, success, t.desc)
	}
}

func (s *vipCampaignSuite) TestDeleteVIPCampaign() {
	type args struct {
		Input deleteCampaignInput
	}

	type testCase struct {
		desc         string
		mockCTX      context.Context
		args         args
		mockFunc     func(t testCase)
		expectResult bool
		expectError  error
	}

	mockCampaignID := "VIP_20220321_001"

	tests := []testCase{
		{
			desc:    "success",
			mockCTX: mockCTX,
			args: args{
				Input: deleteCampaignInput{
					graphql.ID(mockCampaignID),
				},
			},
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("SoftDeleteCampaign", mock.AnythingOfType("ctx.CTX"), mockUserID, mockCampaignID).Return(nil).Once()
			},
			expectResult: true,
			expectError:  nil,
		},
		{
			desc:    "failed without permission",
			mockCTX: mockNoPermCTX,
			args: args{
				Input: deleteCampaignInput{
					ID: graphql.ID(mockCampaignID),
				},
			},
			mockFunc:     func(t testCase) {},
			expectResult: false,
			expectError:  ErrNoPermission,
		},
		{
			desc:    "empty campaignID string",
			mockCTX: mockCTX,
			args: args{
				Input: deleteCampaignInput{
					ID: graphql.ID(""),
				},
			},
			mockFunc:     func(t testCase) {},
			expectResult: false,
			expectError:  fmt.Errorf("campaignID is empty"),
		},
		{
			desc:    "CampaignEngine SoftDeleteCampaign Failed",
			mockCTX: mockCTX,
			args: args{
				Input: deleteCampaignInput{
					graphql.ID(mockCampaignID),
				},
			},
			mockFunc: func(t testCase) {
				err := fmt.Errorf("SoftDeleteCampaign fail")
				s.mCampaignEngine.On("SoftDeleteCampaign", mock.AnythingOfType("ctx.CTX"), mockUserID, mockCampaignID).Return(err).Once()
			},
			expectResult: false,
			expectError:  fmt.Errorf("SoftDeleteCampaign fail"),
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc(t)
		result, err := s.im.DeleteVIPCampaign(t.mockCTX, t.args)
		s.Equal(t.expectError, err, t.desc)
		if err == nil {
			s.Equal(t.expectResult, result, t.desc)
		}
	}
}

func (s *vipCampaignSuite) TestMoveToDraftCampaign() {
	type args struct {
		Input moveToDraftCampaignInput
	}

	type testCase struct {
		desc      string
		mockCTX   context.Context
		mockArgs  args
		mockFunc  func(t testCase)
		expErr    error
		expOutput bool
	}

	mockCampaignID := "VIP_20220316_001"

	tests := []testCase{
		{
			desc:    "success",
			mockCTX: mockCTX,
			mockArgs: args{
				Input: moveToDraftCampaignInput{
					ID: graphql.ID(mockCampaignID),
				},
			},
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("UpdateCampaignStatus", mock.AnythingOfType("ctx.CTX"), mockUserID, mockCampaignID, campaignModel.CampaignStatusDraft).Return(nil).Once()
			},
			expErr:    nil,
			expOutput: true,
		},
		{
			desc:    "no perm",
			mockCTX: mockNoPermCTX,
			mockArgs: args{
				Input: moveToDraftCampaignInput{
					ID: graphql.ID(mockCampaignID),
				},
			},
			mockFunc:  func(t testCase) {},
			expErr:    ErrNoPermission,
			expOutput: false,
		},
		{
			desc:    "empty campaignID string",
			mockCTX: mockCTX,
			mockArgs: args{
				Input: moveToDraftCampaignInput{
					ID: graphql.ID(""),
				},
			},
			mockFunc:  func(t testCase) {},
			expErr:    fmt.Errorf("campaignID is empty"),
			expOutput: false,
		},
		{
			desc:    "UpdateCampaignStatus fail",
			mockCTX: mockCTX,
			mockArgs: args{
				Input: moveToDraftCampaignInput{
					ID: graphql.ID(mockCampaignID),
				},
			},
			mockFunc: func(t testCase) {
				err := fmt.Errorf("UpdateCampaignStatus fail")
				s.mCampaignEngine.On("UpdateCampaignStatus", mock.AnythingOfType("ctx.CTX"), mockUserID, mockCampaignID, campaignModel.CampaignStatusDraft).Return(err).Once()
			},
			expErr:    fmt.Errorf("UpdateCampaignStatus fail"),
			expOutput: false,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc(t)
		success, err := s.im.MoveToDraftVIPCampaign(t.mockCTX, t.mockArgs)
		s.Equal(t.expErr, err, t.desc)
		s.Equal(t.expOutput, success, t.desc)
	}
}

func (s *vipCampaignSuite) TestForceStopCampaign() {
	type args struct {
		Input forceStopCampaignInput
	}

	type testCase struct {
		desc      string
		mockCTX   context.Context
		mockArgs  args
		mockFunc  func(t testCase)
		expErr    error
		expOutput bool
	}

	mockCampaignID := "VIP_20220316_001"

	tests := []testCase{
		{
			desc:    "success",
			mockCTX: mockCTX,
			mockArgs: args{
				Input: forceStopCampaignInput{
					ID: graphql.ID(mockCampaignID),
				},
			},
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("UpdateCampaignStatus", mock.AnythingOfType("ctx.CTX"), mockUserID, mockCampaignID, campaignModel.CampaignStatusExpired).Return(nil).Once()
			},
			expErr:    nil,
			expOutput: true,
		},
		{
			desc:    "no perm",
			mockCTX: mockNoPermCTX,
			mockArgs: args{
				Input: forceStopCampaignInput{
					ID: graphql.ID(mockCampaignID),
				},
			},
			mockFunc:  func(t testCase) {},
			expErr:    ErrNoPermission,
			expOutput: false,
		},
		{
			desc:    "empty campaignID string",
			mockCTX: mockCTX,
			mockArgs: args{
				Input: forceStopCampaignInput{
					ID: graphql.ID(""),
				},
			},
			mockFunc:  func(t testCase) {},
			expErr:    fmt.Errorf("campaignID is empty"),
			expOutput: false,
		},
		{
			desc:    "UpdateCampaignStatus fail",
			mockCTX: mockCTX,
			mockArgs: args{
				Input: forceStopCampaignInput{
					ID: graphql.ID(mockCampaignID),
				},
			},
			mockFunc: func(t testCase) {
				err := fmt.Errorf("UpdateCampaignStatus fail")
				s.mCampaignEngine.On("UpdateCampaignStatus", mock.AnythingOfType("ctx.CTX"), mockUserID, mockCampaignID, campaignModel.CampaignStatusExpired).Return(err).Once()
			},
			expErr:    fmt.Errorf("UpdateCampaignStatus fail"),
			expOutput: false,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc(t)
		success, err := s.im.ForceStopVIPCampaign(t.mockCTX, t.mockArgs)
		s.Equal(t.expErr, err, t.desc)
		s.Equal(t.expOutput, success, t.desc)
	}
}

func (s *vipCampaignSuite) TestRetrySendingRecall() {
	mockRecallID1 := int64(1)
	mockRecallIDStr1 := "RT-000000001"
	mockRecallID2 := int64(2)
	mockRecallIDStr2 := "RT-000000002"
	mockRecallID3 := int64(3)
	mockRecallIDStr3 := "RT-000000003"
	mockRecallIDList := []int64{mockRecallID1, mockRecallID2, mockRecallID3}
	mockCampaignID := "CPTW-000000001"

	mockTransactions := []*ce.CampaignSendTransaction{
		{
			ID:         mockRecallID1,
			CampaignID: mockCampaignID,
			UserID:     mockUserID,
			Status:     campaignModel.TransactionHistoryStatusRecall,
		},
		{
			ID:         mockRecallID2,
			CampaignID: mockCampaignID,
			UserID:     mockUserID,
			Status:     campaignModel.TransactionHistoryStatusRecall,
		},
		{
			ID:         mockRecallID3,
			CampaignID: mockCampaignID,
			UserID:     mockUserID,
			Status:     campaignModel.TransactionHistoryStatusRecall,
		},
	}

	type testCase struct {
		desc        string
		mockCTX     context.Context
		mockFunc    func()
		expectTxs   []*ce.CampaignSendTransaction
		expectError error
	}

	tests := []testCase{
		{
			desc:    "succ",
			mockCTX: mockCTX,
			mockFunc: func() {
				s.mCampaignEngine.On("RetryRecalls", mock.AnythingOfType("ctx.CTX"), mockUserID, mockRecallIDList).Return(mockTransactions, nil).Once()
			},
			expectTxs:   mockTransactions,
			expectError: nil,
		},
		{
			desc:        "failed without retry recall permission",
			mockCTX:     mockNoPermCTX,
			mockFunc:    func() {},
			expectError: ErrNoPermission,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc()

		args := struct {
			Input retryRecallInput
		}{
			Input: retryRecallInput{IDs: []graphql.ID{
				graphql.ID(mockRecallIDStr1),
				graphql.ID(mockRecallIDStr2),
				graphql.ID(mockRecallIDStr3),
			}},
		}

		tResolvers, err := s.im.RetryRecallVIPCampaignTransaction(t.mockCTX, args)
		s.Require().Equal(t.expectError, err, t.desc)
		txDatas := make([]*ce.CampaignSendTransaction, len(tResolvers))
		for i, tResolver := range tResolvers {
			txDatas[i] = tResolver.data
		}
		if err == nil {
			s.Require().Equal(t.expectTxs, txDatas, t.desc)
		}
	}
}

func (s *vipCampaignSuite) TestVIPCampaignTransactions() {
	mockLimit := int32(100)
	mockCampaignID := "mockCampaignID1"
	mockUserIDs := &[]string{"mockuser1", "mockuser2"}
	mockStartTime := time.Date(2022, time.March, 7, 0, 0, 0, 0, locTW)
	mockEndTime := time.Date(2022, time.March, 17, 0, 0, 0, 0, locTW)
	mockCampaignSendTransaction := &ce.CampaignSendTransaction{}

	type args struct {
		Cursor *string
		Limit  int32
		Filter *campaignTransactionsFilter
	}

	type testCase struct {
		desc          string
		args          args
		mockCTX       context.Context
		mockFunc      func(t testCase)
		mockCond      ce.GetCampaignsTransactionCond
		expCampaigns  []*ce.CampaignSendTransaction
		expNextCursor string
		expErr        error
	}

	tests := []testCase{
		{
			desc: "success, no filter",
			args: args{
				Cursor: nil,
				Limit:  mockLimit,
				Filter: nil,
			},
			mockCTX: mockCTX2,
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaignTransactionList", mock.AnythingOfType("ctx.CTX"), "", int(t.args.Limit), t.mockCond).Return(
					[]*ce.CampaignSendTransaction{
						mockCampaignSendTransaction,
					}, "", nil).Once()
			},
			mockCond: ce.GetCampaignsTransactionCond{
				OperatorRegion: "JP",
			},
			expCampaigns: []*ce.CampaignSendTransaction{
				mockCampaignSendTransaction,
			},
			expNextCursor: "",
			expErr:        nil,
		},
		{
			desc: "success, with update time filter",
			args: args{
				Cursor: nil,
				Limit:  mockLimit,
				Filter: &campaignTransactionsFilter{
					UpdateTime: &timeFilter{
						From: graphql.Time{Time: mockStartTime},
						To:   graphql.Time{Time: mockEndTime},
					},
				},
			},
			mockCTX: mockCTX2,
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaignTransactionList", mock.AnythingOfType("ctx.CTX"), "", int(t.args.Limit), t.mockCond).Return(
					[]*ce.CampaignSendTransaction{
						mockCampaignSendTransaction,
					}, "", nil).Once()
			},
			mockCond: ce.GetCampaignsTransactionCond{
				UpdateTime: &ce.TimeFilter{
					From: mockStartTime,
					To:   mockEndTime,
				},
				OperatorRegion: "JP",
			},
			expCampaigns: []*ce.CampaignSendTransaction{
				mockCampaignSendTransaction,
			},
			expNextCursor: "",
			expErr:        nil,
		},
		{
			desc: "success, with userIDs filter",
			args: args{
				Cursor: nil,
				Limit:  mockLimit,
				Filter: &campaignTransactionsFilter{
					UserIDs: mockUserIDs,
				},
			},
			mockCTX: mockCTX2,
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaignTransactionList", mock.AnythingOfType("ctx.CTX"), "", int(t.args.Limit), t.mockCond).Return(
					[]*ce.CampaignSendTransaction{
						mockCampaignSendTransaction,
					}, "", nil).Once()
			},
			mockCond: ce.GetCampaignsTransactionCond{
				UserIDs:        *mockUserIDs,
				OperatorRegion: "JP",
			},
			expCampaigns: []*ce.CampaignSendTransaction{
				mockCampaignSendTransaction,
			},
			expNextCursor: "",
			expErr:        nil,
		},
		{
			desc: "success, with CampaignIDs filter",
			args: args{
				Cursor: nil,
				Limit:  mockLimit,
				Filter: &campaignTransactionsFilter{
					CampaignIDs: &[]graphql.ID{graphql.ID(mockCampaignID)},
				},
			},
			mockCTX: mockCTX2,
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaignTransactionList", mock.AnythingOfType("ctx.CTX"), "", int(t.args.Limit), t.mockCond).Return(
					[]*ce.CampaignSendTransaction{
						mockCampaignSendTransaction,
					}, "", nil).Once()
			},
			mockCond: ce.GetCampaignsTransactionCond{
				CampaignIDs:    []string{mockCampaignID},
				OperatorRegion: "JP",
			},
			expCampaigns: []*ce.CampaignSendTransaction{
				mockCampaignSendTransaction,
			},
			expNextCursor: "",
			expErr:        nil,
		},
		{
			desc: "failed, no permission",
			args: args{
				Cursor: nil,
				Limit:  mockLimit,
			},
			mockCTX:       mockNoPermCTX,
			mockFunc:      func(t testCase) {},
			expCampaigns:  nil,
			expNextCursor: "",
			expErr:        ErrNoPermission,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc(t)
		cResolver, err := s.im.VIPCampaignTransactions(t.mockCTX, t.args)
		s.Equal(t.expErr, err, t.desc)
		if err == nil {
			s.Require().Equal(t.expCampaigns, cResolver.data, t.desc)
		}
	}
}

func (s *vipCampaignSuite) TestVIPCampaignDetail() {
	mockCampaignID1 := "CPTW-000000001"
	mockCampaignID2 := "CPTW-000000002"
	mockCampaignID3 := "CPTW-000000003"

	mockReferenceCode1 := "VIP_20220307_001"
	mockReferenceCode2 := "VIP_20220307_002"
	mockReferenceCode3 := "VIP_20220307_003"

	mockOperationRegion := "TW"

	mockCampaignName1 := "VIP monthly sales"
	mockCampaignName2 := "VIP monthly sales2"
	mockCampaignName3 := "army sale"

	mockStartTime1 := time.Date(2022, time.March, 7, 0, 0, 0, 0, locTW).Unix()
	mockEndTime1 := time.Date(2022, time.March, 17, 0, 0, 0, 0, locTW).Unix()
	mockStartTime2 := time.Date(2022, time.March, 10, 0, 0, 0, 0, locTW).Unix()
	mockEndTime2 := time.Date(2022, time.April, 10, 0, 0, 0, 0, locTW).Unix()
	mockStartTime3 := time.Date(2022, time.March, 5, 0, 0, 0, 0, locTW).Unix()
	mockEndTime3 := time.Date(2022, time.April, 10, 0, 0, 0, 0, locTW).Unix()
	mockDistributionTime := time.Date(2022, time.April, 12, 0, 0, 0, 0, locTW).Unix()

	mockDefaultVIPType := campaignModel.ALL
	mockVIPType := campaignModel.VIP

	mockCampaign1 := &cRule.Campaign{
		ID:              1,
		ReferenceCode:   mockReferenceCode1,
		CampaignID:      mockCampaignID1,
		CampaignName:    mockCampaignName1,
		OperationRegion: mockOperationRegion,
		StartTime:       mockStartTime1,
		EndTime:         mockEndTime1,
		CreateUserID:    mockUserID,
		Status:          campaignModel.CampaignStatusDraft,
		Category:        campaignModel.VIPOriginalPrice,
		Rule: cRule.CampaignRule{
			VIPOriginalPrice: &cRule.CampaignRuleVIPOriginalPrice{
				RewardsSetting: cRule.VIPOriginalPriceRewardsSetting{
					LimitTimes:         100,
					Reset:              false,
					LimitResetCycleDay: 0,
					HighestPlan:        true,
				},
				Condition: []cRule.VIPOriginalPriceCondition{
					{
						ProductID:     "mock_300_100",
						CampaignBonus: 50,
						VIPUserCondition: []cRule.VIPUserCondition{
							{
								VIPUserType:    cRule.NewVIP,
								ExtraBonusRate: 0.12,
							},
							{
								VIPUserType:    cRule.RepeatVIP,
								ExtraBonusRate: 0.15,
							},
							{
								VIPUserType:    cRule.RepeatVIP,
								ExtraBonusRate: 0.15,
							},
						},
					},
				},
			},
		},
	}

	mockCampaign2 := &cRule.Campaign{
		ID:              2,
		ReferenceCode:   mockReferenceCode2,
		CampaignID:      mockCampaignID2,
		CampaignName:    mockCampaignName2,
		OperationRegion: "JP",
		StartTime:       mockStartTime2,
		EndTime:         mockEndTime2,
		CreateUserID:    mockUserID,
		Status:          campaignModel.CampaignStatusDraft,
		Category:        campaignModel.VIPSpecificAmount,
		Rule: cRule.CampaignRule{
			VIPSpecificAmount: &cRule.CampaignRuleVIPSpecificAmount{
				RewardsSetting: cRule.VIPSpecificAmountRewardsSetting{
					Lottery: &cRule.VIPSpecificAmountLottery{
						TotalUsers:       100,
						DistributionTime: mockDistributionTime,
					},
					VipType: &mockDefaultVIPType,
				},
				Condition: []cRule.VIPSpecificAmountCondition{
					{
						SpecificAmount: cRule.CurrencyAmount{
							Amount:   float64(2000),
							Currency: "NTD",
						},
						EquivalentPoint: 10000,
						CampaignBonus:   200,
					},
					{
						SpecificAmount: cRule.CurrencyAmount{
							Amount:   float64(4000),
							Currency: "NTD",
						},
						EquivalentPoint: 20000,
						CampaignBonus:   400,
					},
				},
			},
		},
	}

	mockCampaign2GivenVIPType := &cRule.Campaign{
		ID:              2,
		ReferenceCode:   mockReferenceCode2,
		CampaignID:      mockCampaignID2,
		CampaignName:    mockCampaignName2,
		OperationRegion: "JP",
		StartTime:       mockStartTime2,
		EndTime:         mockEndTime2,
		CreateUserID:    mockUserID,
		Status:          campaignModel.CampaignStatusDraft,
		Category:        campaignModel.VIPSpecificAmount,
		Rule: cRule.CampaignRule{
			VIPSpecificAmount: &cRule.CampaignRuleVIPSpecificAmount{
				RewardsSetting: cRule.VIPSpecificAmountRewardsSetting{
					Lottery: &cRule.VIPSpecificAmountLottery{
						TotalUsers:       100,
						DistributionTime: mockDistributionTime,
					},
					VipType: &mockVIPType,
				},
				Condition: []cRule.VIPSpecificAmountCondition{
					{
						SpecificAmount: cRule.CurrencyAmount{
							Amount:   float64(2000),
							Currency: "NTD",
						},
						EquivalentPoint: 10000,
						CampaignBonus:   200,
					},
					{
						SpecificAmount: cRule.CurrencyAmount{
							Amount:   float64(4000),
							Currency: "NTD",
						},
						EquivalentPoint: 20000,
						CampaignBonus:   400,
					},
				},
			},
		},
	}

	mockCampaign3 := &cRule.Campaign{
		ID:              3,
		ReferenceCode:   mockReferenceCode3,
		CampaignID:      mockCampaignID3,
		CampaignName:    mockCampaignName3,
		OperationRegion: mockOperationRegion,
		StartTime:       mockStartTime3,
		EndTime:         mockEndTime3,
		CreateUserID:    mockUserID,
		Status:          campaignModel.CampaignStatusDraft,
		Category:        campaignModel.ArmyPlan,
		Rule: cRule.CampaignRule{
			ArmyPlan: &cRule.CampaignRuleArmyPlan{
				Condition: []cRule.ArmyPlanCampaignCondition{
					{
						ArmyRank:      armyModel.ArmyRank_RANK_1,
						Period:        campaignModel.Period1M,
						CampaignBonus: 100,
					},
					{
						ArmyRank:      armyModel.ArmyRank_RANK_1,
						Period:        campaignModel.Period3M,
						CampaignBonus: 500,
					},
				},
				RewardsSetting: cRule.ArmyPlanRewardsSetting{
					LimitTimes:         100,
					Reset:              false,
					LimitResetCycleDay: 0,
					HighestPlan:        true,
				},
			},
		},
	}

	type args struct {
		Filter vipCampaignDetailFilter
	}

	type testCase struct {
		desc           string
		mockCTX        context.Context
		args           args
		mockFunc       func(t testCase)
		expectCampaign cRule.Campaign
		expectError    error
	}

	tests := []testCase{
		{
			desc:    "success, vip original price",
			mockCTX: mockCTX,
			args: args{
				Filter: vipCampaignDetailFilter{
					ID: graphql.ID(mockCampaignID1),
				},
			},
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaigns", mock.AnythingOfType("ctx.CTX"), "", 1, ce.GetCampaignsCond{
					CampaignIDs: []string{mockCampaignID1},
				}).Return([]*cRule.Campaign{mockCampaign1}, "", nil).Once()
			},
			expectCampaign: *mockCampaign1,
			expectError:    nil,
		},
		{
			desc:    "success, vip specific amount with default VIP type",
			mockCTX: mockCTX2,
			args: args{
				Filter: vipCampaignDetailFilter{
					ID: graphql.ID(mockCampaignID2),
				},
			},
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaigns", mock.AnythingOfType("ctx.CTX"), "", 1, ce.GetCampaignsCond{
					CampaignIDs:    []string{mockCampaignID2},
					OperatorRegion: "JP",
				}).Return([]*cRule.Campaign{mockCampaign2}, "", nil).Once()
			},
			expectCampaign: *mockCampaign2,
			expectError:    nil,
		},
		{
			desc:    "success, vip specific amount with given VIP type ",
			mockCTX: mockCTX2,
			args: args{
				Filter: vipCampaignDetailFilter{
					ID: graphql.ID(mockCampaignID2),
				},
			},
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaigns", mock.AnythingOfType("ctx.CTX"), "", 1, ce.GetCampaignsCond{
					CampaignIDs:    []string{mockCampaignID2},
					OperatorRegion: "JP",
				}).Return([]*cRule.Campaign{mockCampaign2GivenVIPType}, "", nil).Once()
			},
			expectCampaign: *mockCampaign2GivenVIPType,
			expectError:    nil,
		},
		{
			desc:    "success, army plan",
			mockCTX: mockCTX,
			args: args{
				Filter: vipCampaignDetailFilter{
					ID: graphql.ID(mockCampaignID3),
				},
			},
			mockFunc: func(t testCase) {
				s.mCampaignEngine.On("GetCampaigns", mock.AnythingOfType("ctx.CTX"), "", 1, ce.GetCampaignsCond{
					CampaignIDs: []string{mockCampaignID3},
				}).Return([]*cRule.Campaign{mockCampaign3}, "", nil).Once()
			},
			expectCampaign: *mockCampaign3,
			expectError:    nil,
		},
		{
			desc:    "failed without permission",
			mockCTX: mockNoPermCTX,
			args: args{
				Filter: vipCampaignDetailFilter{
					ID: graphql.ID(mockCampaignID3),
				},
			},
			mockFunc:    func(t testCase) {},
			expectError: ErrNoPermission,
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc(t)
		cResolver, err := s.im.VIPCampaignDetail(t.mockCTX, t.args)
		s.Equal(t.expectError, err, t.desc)
		if err == nil {
			s.Equal(t.expectCampaign, cResolver.data, t.desc)
		}
	}
}

func (s *vipCampaignSuite) TestConfig() {
	regionInfoTW := campaignModel.RegionInfo{
		Region: "TW",
		Name:   "Taiwan",
	}
	regionInfoJP := campaignModel.RegionInfo{
		Region: "JP",
		Name:   "Japan",
	}
	regionInfoOther := campaignModel.RegionInfo{
		Region: "Other",
		Name:   "Other",
	}
	getTargetUserRegionInfos = func() []campaignModel.RegionInfo {
		return []campaignModel.RegionInfo{
			regionInfoTW, regionInfoJP, regionInfoOther,
		}
	}
	getBonusLimit = func(string) float64 {
		return 0.04
	}
	getRegionCurrency = func(string) (string, error) {
		return "JPY", nil
	}

	resolvers, _ := s.im.Config(mockCTX)
	s.Require().Equal(&ConfigResolver{
		bonusLimit: 0.04,
		targetUserRegionInfos: []campaignModel.RegionInfo{
			regionInfoTW, regionInfoJP, regionInfoOther,
		},
		regionService: &s.mRegionService,
		currency:      "JPY",
	}, resolvers)
}
