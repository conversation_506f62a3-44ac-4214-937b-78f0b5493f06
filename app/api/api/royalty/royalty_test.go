package royalty

import (
	"errors"
	"flag"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/17media/api/apis/stats"
	"github.com/17media/api/app/api/api"
	"github.com/17media/api/models"
	mRoyalty "github.com/17media/api/stores/royalty/mocks"
	mUser "github.com/17media/api/stores/user/mocks"
)

var (
	mockCTX = mock.AnythingOfType("ctx.CTX")
)

type royaltyTestSuite struct {
	suite.Suite
	router   *gin.Engine
	mUser    *mUser.Store
	mRoyalty *mRoyalty.Store
}

func (s *royaltyTestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)
}

func (s *royaltyTestSuite) SetupTest() {
	flag.Set("enable_mock", "true") // so we can test mock result

	s.router = gin.Default()
	s.router.Use(stats.AddContext(), stats.Log())
	s.mUser = &mUser.Store{}
	s.mUser.On("GetByToken", mockCTX, api.MockAccessToken).Return(api.MockUser, nil).Maybe()
	s.mUser.On("MigratePureOpenID", mockCTX, mock.AnythingOfType("*models.User")).Return(nil).Maybe()
	s.mUser.On("GetRegionByUserID", mockCTX, api.MockUserID).Return(api.MockRegionInfo.Region).Maybe()
	s.mRoyalty = &mRoyalty.Store{}

	NewHandler(s.router.Group("/api/v1/loyalty"), s.mUser, s.mRoyalty)
}

func (s *royaltyTestSuite) TearDownTest() {
	s.mUser.AssertExpectations(s.T())
	s.mRoyalty.AssertExpectations(s.T())
}

func TestSuite(t *testing.T) {
	suite.Run(t, new(royaltyTestSuite))
}

func (s *royaltyTestSuite) TestRead() {
	tests := []struct {
		desc   string
		mock   func()
		assert func(code int, body string)
	}{
		{
			desc: "no error, return 204",
			mock: func() {
				s.mRoyalty.On("MarkTitleReadTime", mockCTX, api.MockUser).Return(nil)
			},
			assert: func(code int, body string) {
				s.Equal(http.StatusNoContent, code)
				s.Empty(body)
			},
		},
		{
			desc: "server error",
			mock: func() {
				s.mRoyalty.On("MarkTitleReadTime", mockCTX, api.MockUser).Return(errors.New("server error"))
			},
			assert: func(code int, body string) {
				s.Equal(models.StatusServerError, code)
				s.JSONEq(`{"errorCode":0, "errorMessage":"server error", "errorTitle":""}`, body)
			},
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			defer s.TearDownTest()
			s.SetupTest()

			if tt.mock != nil {
				tt.mock()
			}

			path := "/api/v1/loyalty/title/read"
			req := api.MockHTTPRequest("PATCH", path, nil)
			w := api.NewRecorder()
			s.router.ServeHTTP(w, req)

			tt.assert(w.Code, w.Body.String())
		})
	}
}
