package arcade

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/17media/api/apis"
	"github.com/17media/api/apis/common"
	"github.com/17media/api/app/api/api/aggregator"
	userMW "github.com/17media/api/app/api/api/middlewares/user"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/encoding"
	btime "github.com/17media/api/base/time"
	"github.com/17media/api/models"
	model "github.com/17media/api/models/arcade"
	lbModel "github.com/17media/api/models/leaderboard"
	"github.com/17media/api/service/region"
	"github.com/17media/api/stores/arcade"
	"github.com/17media/api/stores/live"
	"github.com/17media/api/stores/schedule"
	"github.com/17media/api/stores/user"
)

var (
	timeNow = btime.TimeNow
)

type Handler struct {
	user          user.Store
	arcade        arcade.Store
	scheduleStore schedule.Store
	lvStore       live.Live
}

func NewHandler(rg *gin.RouterGroup, us user.Store, ar arcade.Store, ss schedule.Store, lvStore live.Live) Handler {
	h := Handler{
		user:          us,
		arcade:        ar,
		scheduleStore: ss,
		lvStore:       lvStore,
	}

	rg.Use(common.GetToken())
	rg.Use(userMW.GetUser(us))
	apis.Handle(rg, "PUT", "/newGame", h.createGame)
	apis.Handle(rg, "PATCH", "/:gameID/start", apis.MockByGetValue(mockResStartGame), h.startGame)
	apis.Handle(rg, "PATCH", "/:gameID/end", h.endGame)
	apis.Handle(rg, "GET", "/leaderboard", h.getLeaderboard)
	apis.Handle(rg, "PATCH", "/:gameID/keepalive", h.keepAlive)
	return h
}

// swagger:route PATCH /arcade/:gameID/keepalive arcade arcadeEndGameParam
//
// # Keeps game alive
//
// Responses:
//
//	200:
//	420:
//	520: validationError
func (h *Handler) keepAlive(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	userInfo := c.MustGet("UserInfo").(*models.User)
	gameID := c.Param("gameID")

	p := &endGameParams{}
	if err := encoding.BindJSON(c, p); err != nil {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}

	if err := h.arcade.KeepAlive(context, userInfo, gameID, p.RoomID, p.Result); err != nil {
		context.WithField("err", err).Error("keepAlive failed")
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}
	apis.JSON(c, http.StatusOK, gin.H{})
}

// swagger:model arcadeNewGameResp
type newGameResp struct {
	// TODO: this field is for backward compatible, deprecate after 2020/04/30
	LegacyGameID string `json:"GameID"`
	GameID       string `json:"gameID"`
}

// swagger:model arcadeNewGameParam
type newGameParams struct {
	Type model.Type `json:"type"`
}

// swagger:route PUT /arcade/newGame arcade
//
// # Game start
//
// Responses:
//
//	200: arcadeNewGameResp
//	520: validationError
func (h *Handler) createGame(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	userInfo := c.MustGet("UserInfo").(*models.User)

	p := newGameParams{}
	if err := encoding.BindJSON(c, &p, encoding.WithoutFillNil()); err != nil {
		// this is for backward compatible
		// old version apps won't call this API with request body
		// so we need to handle such case
		p = newGameParams{
			Type: model.TypeShirleyBird,
		}
	}

	gameType := p.Type
	// fallback. this is for backward compatible as well
	if encoding.Zero32OrNilInt(int32(p.Type)) {
		gameType = model.TypeShirleyBird
	}

	gameID, err := h.arcade.CreateFreeGame(context, userInfo.UserID, gameType)
	if err != nil {
		context.WithField("err", err).Error("CreateGame failed")
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode: models.Error_UNKNOWN,
		})
		return
	}

	apis.JSON(c, http.StatusOK, newGameResp{
		LegacyGameID: gameID,
		GameID:       gameID,
	})
}

// swagger:route PATCH /arcade/:ID/start arcade
//
// # Game start
//
// Responses:
//
//	200:
//	520: validationError
func (h *Handler) startGame(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	userInfo := c.MustGet("UserInfo").(*models.User)
	gameID := c.Param("gameID")

	resp, err := h.arcade.StartGame(context, userInfo.UserID, gameID)
	if err != nil {
		context.WithField("err", err).Error("StartGame failed")
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}

	apis.JSON(c, http.StatusOK, resp)
}

// swagger:model arcadeEndGameParam
type endGameParams struct {
	Result string `json:"result"`
	RoomID string `json:"roomID"`
}

// swagger:route PATCH /arcade/:ID/end arcade arcadeEndGameParam
//
// # Game end
//
// Responses:
//
//	200:
//	520: validationError
func (h *Handler) endGame(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	userInfo := c.MustGet("UserInfo").(*models.User)
	gameID := c.Param("gameID")

	p := &endGameParams{}
	if err := encoding.BindJSON(c, p); err != nil {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}

	rank, err := h.arcade.EndGame(context, userInfo.UserID, gameID, p.Result, p.RoomID)
	if err != nil {
		context.WithField("err", err).Error("EndGame failed")
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}

	apis.JSON(c, http.StatusOK, model.ResEndGame{Rank: int32(rank)})
}

type getLeaderboardParams struct {
	// | Type | Value |
	// | --- | --- |
	// | ARCADE_SHIRLEYBIRD | 36 |
	// | CrazyStairsEasyMode | 44 |
	// | CrazyStairsProMode | 45 |
	// | CrazyStairsFunnyMode | 46 |
	// | CrazyStairsGeneralMode | 47 |
	//
	// in: query
	Type int32 `form:"type" validate:"min=36"`
	// in: query
	Region string `form:"region"`
	// in: query
	TA model.TA `form:"ta" validate:"min=0,max=2"`
	// Inject streaming info: 1
	//
	// in: query
	GetLiveStreamID int `form:"getLiveStreamID" validate:"min=0,max=1"`
	// Inject onLive info: 1
	//
	// in: query
	GetOnLiveInfo int `form:"getOnliveInfo" validate:"min=0,max=1"`
	// Inject watchLive info: 1
	//
	// in: query
	GetWatchLiveInfo int `form:"getWatchLiveInfo" validate:"min=0,max=1"`
}

func getLeaderboardQuery(c *gin.Context) (*getLeaderboardParams, error) {
	var query getLeaderboardParams
	if err := encoding.BindJSON(c, &query); err != nil {
		return nil, err
	}

	if _, ok := lbModel.Type_name[query.Type]; !ok {
		return nil, fmt.Errorf("wrong type")
	}

	return &query, nil
}

// swagger:route GET /arcade/leaderboard arcade getLeaderboard
//
// # Get leaderboard of arcade
//
// Responses:
//
//	200:
//	420: validationError
//	520: validationError
func (h *Handler) getLeaderboard(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)

	query, err := getLeaderboardQuery(c)
	if err != nil {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}
	_, getLiveStreamID := c.GetQuery("getLiveStreamID")

	userInfo, _ := c.Get("UserInfo")
	user := userInfo.(*models.User)
	userID := user.UserID

	// for old version's apps requesting for shirleyBird's data, they won't bring `Region`
	// so we will set its value to region.GLOBAL to fit its original logic
	if len(query.Region) == 0 {
		query.Region = region.GLOBAL
	}
	lbType := lbModel.Type(query.Type)

	ranks, myRank, err := h.arcade.GetRank(context, userID, query.Region, query.TA, lbType)
	if err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_DEFAULT,
			ErrorMessage: err.Error(),
		})
		return
	}

	userRanks := aggregator.InitUserRanks(ranks)
	// Inject user info
	if err := aggregator.InjectUserInfo(context, h.user, h.scheduleStore, userID, userRanks); err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_DEFAULT,
			ErrorMessage: err.Error(),
		})
		return
	}

	if getLiveStreamID {
		// Inject liveStreamID
		if err := aggregator.InjectLiveStreamID(context, h.lvStore, userRanks); err != nil {
			apis.JSON(c, models.StatusServerError, models.Error{
				ErrorCode:    models.Error_DEFAULT,
				ErrorMessage: err.Error(),
			})
			return
		}
	}
	if query.GetOnLiveInfo != 0 {
		// Inject onliveInfo
		if err := aggregator.InjectOnLiveInfo(context, h.lvStore, userRanks); err != nil {
			apis.JSON(c, models.StatusServerError, models.Error{
				ErrorCode:    models.Error_DEFAULT,
				ErrorMessage: err.Error(),
			})
			return
		}
	}
	if query.GetWatchLiveInfo != 0 {
		// Inject watchLiveInfo
		if err := aggregator.InjectWatchLiveInfo(context, h.lvStore, userRanks); err != nil {
			apis.JSON(c, models.StatusServerError, models.Error{
				ErrorCode:    models.Error_DEFAULT,
				ErrorMessage: err.Error(),
			})
			return
		}
	}
	apis.JSON(c, http.StatusOK, &model.ResLeaderboard{
		ScoreRank: userRanks,
		Type:      lbType.String(),
		Region:    query.Region,
		MyRank:    &myRank,
	})
	return
}
