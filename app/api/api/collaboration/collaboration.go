package collaboration

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/17media/api/apis"
	"github.com/17media/api/apis/common"
	userMW "github.com/17media/api/app/api/api/middlewares/user"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/encoding"
	"github.com/17media/api/models"
	model "github.com/17media/api/models/collaboration"
	"github.com/17media/api/stores/collaboration"
	liveHelper "github.com/17media/api/stores/live/helper"
	"github.com/17media/api/stores/user"
)

type Handler struct {
	user          user.Store
	liveHelper    liveHelper.Helper
	collaboration collaboration.Store
}

func NewHandler(
	rg *gin.RouterGroup,
	user user.Store,
	liveHelper liveHelper.Helper,
	collaboration collaboration.Store,
) Handler {
	h := Handler{
		user:          user,
		liveHelper:    liveHelper,
		collaboration: collaboration,
	}

	urg := rg.Group("")
	urg.Use(common.GetToken())
	urg.Use(userMW.GetUser(user))

	apis.Handle(urg, "POST", "/streamers/:streamerID/gift-mission/actions", h.executeAction)
	apis.Handle(urg, "GET", "/streamers/:streamerID/gift-mission/status", h.getMissionDisplayStatus)
	apis.Handle(urg, "GET", "/streamers/:streamerID/gift-mission", h.getMission)
	apis.Handle(urg, "GET", "/streamers/:streamerID/gift-mission/rewards", h.getMissionRewards)

	return h
}

type pathParam struct {
	StreamerID string `uri:"streamerID" binding:"required"`
}

type executeActionParam struct {
	Action model.MissionAction `json:"action" validate:"required"`
}

func (h *Handler) executeAction(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	requester := c.MustGet("UserInfo").(*models.User)

	// Check if requester is streamer
	pathParam := pathParam{}
	if err := c.ShouldBindUri(&pathParam); err != nil {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	} else if pathParam.StreamerID != requester.UserID {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_AUTHORIZATION_FAIL,
			ErrorMessage: models.Error_AUTHORIZATION_FAIL.String(),
		})
		return
	}

	// Parse request body
	param := executeActionParam{}
	if err := encoding.BindJSON(c, &param, encoding.WithoutFillNil()); err != nil {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get streamID
	streamID, err := h.liveHelper.MappingLiveIDByUserID(context, pathParam.StreamerID)
	if errors.Is(err, liveHelper.ErrNotBroadcasting) {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	} else if err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_UNKNOWN,
			ErrorMessage: err.Error(),
		})
		return
	}
	streamerID := pathParam.StreamerID
	roomID := strconv.Itoa(int(requester.RoomID))

	switch param.Action {
	case model.ActionStart:
		err = h.collaboration.StartMissionByStreamer(context, streamerID, roomID, streamID)
	case model.ActionStop:
		err = h.collaboration.StopMissionByStreamer(context, streamerID, roomID, streamID)
	default:
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: "invalid action",
		})
		return
	}
	if err != nil {
		switch err {
		case collaboration.ErrDeviceIDNotMatched:
			apis.JSON(c, models.StatusClientError, models.Error{
				ErrorCode:    models.Error_COLLABORATION_DEVICE_NOT_MATCHED,
				ErrorMessage: err.Error(),
			})
			return
		case collaboration.ErrMissionNotFound, collaboration.ErrNotEligible, collaboration.ErrStartMissionInWrongStatus:
			apis.JSON(c, models.StatusClientError, models.Error{
				ErrorCode:    models.Error_PARAMETER_ERROR,
				ErrorMessage: err.Error(),
			})
			return
		default:
			apis.JSON(c, models.StatusServerError, models.Error{
				ErrorCode:    models.Error_UNKNOWN,
				ErrorMessage: err.Error(),
			})
			return
		}
	}

	apis.JSON(c, http.StatusNoContent, gin.H{})
}

func (h *Handler) getMissionDisplayStatus(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)

	// Get streamerID
	pathParam := pathParam{}
	if err := c.ShouldBindUri(&pathParam); err != nil {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get streamID
	streamID, err := h.liveHelper.MappingLiveIDByUserID(context, pathParam.StreamerID)
	if errors.Is(err, liveHelper.ErrNotBroadcasting) {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	} else if err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_UNKNOWN,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get display status resp
	displayStatusResp, err := h.collaboration.GetMissionDisplayStatus(context, pathParam.StreamerID, streamID)
	if err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_UNKNOWN,
			ErrorMessage: err.Error(),
		})
		return
	}

	apis.JSON(c, http.StatusOK, displayStatusResp)
}

func (h *Handler) getMission(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	requester := c.MustGet("UserInfo").(*models.User)

	// Get streamerID
	pathParam := pathParam{}
	if err := c.ShouldBindUri(&pathParam); err != nil {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get streamID
	streamID, err := h.liveHelper.MappingLiveIDByUserID(context, pathParam.StreamerID)
	if errors.Is(err, liveHelper.ErrNotBroadcasting) {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	} else if err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_UNKNOWN,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get mission resp
	missionResp, err := h.collaboration.GetSpecialModeMission(context, requester.UserID, pathParam.StreamerID, streamID)
	if err != nil {
		switch err {
		case collaboration.ErrMissionNotFound, collaboration.ErrNotEligible, collaboration.ErrNotInSpecialMode:
			apis.JSON(c, models.StatusClientError, models.Error{
				ErrorCode:    models.Error_PARAMETER_ERROR,
				ErrorMessage: err.Error(),
			})
			return
		default:
			apis.JSON(c, models.StatusServerError, models.Error{
				ErrorCode:    models.Error_UNKNOWN,
				ErrorMessage: err.Error(),
			})
			return
		}
	}

	apis.JSON(c, http.StatusOK, missionResp)
}

type getMissionRewardsQueryParam struct {
	UserID string `form:"userID" validate:"required"`
}

type getMissionRewardsResp struct {
	Rewards []model.Reward `json:"rewards"`
}

func (h *Handler) getMissionRewards(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	requester := c.MustGet("UserInfo").(*models.User)

	// Parse path and query param
	pathParam := pathParam{}
	if err := c.ShouldBindUri(&pathParam); err != nil {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}
	queryParam := getMissionRewardsQueryParam{}
	if err := encoding.BindJSON(c, &queryParam, encoding.WithoutFillNil()); err != nil {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	} else if queryParam.UserID != requester.UserID {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_AUTHORIZATION_FAIL,
			ErrorMessage: models.Error_AUTHORIZATION_FAIL.String(),
		})
		return
	}

	// Get streamID
	streamID, err := h.liveHelper.MappingLiveIDByUserID(context, pathParam.StreamerID)
	if errors.Is(err, liveHelper.ErrNotBroadcasting) {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	} else if err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_UNKNOWN,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get rewards
	rewards, err := h.collaboration.GetMissionRewards(context, queryParam.UserID, pathParam.StreamerID, streamID)
	if err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_UNKNOWN,
			ErrorMessage: err.Error(),
		})
		return
	}

	apis.JSON(c, http.StatusOK, getMissionRewardsResp{
		Rewards: rewards,
	})
}
