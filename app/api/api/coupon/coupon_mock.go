package coupon

import (
	"github.com/gin-gonic/gin"

	model "github.com/17media/api/models/coupon"
)

func mockGetHistories(c *gin.Context) interface{} {
	return resGetHistories{
		Cursor: "",
		Histories: []*model.History{
			&model.History{
				CampaignName:    "55688-123456",
				CampaignContent: "Thy days are done, thy fame begun;Thy country's strains record The triumphs of her chosen Son,The slaughter of his sword!The deeds he did, the fields he won,The freedom he restored!",
				CampaignURL:     "https://i.ytimg.com/vi/yXSOUa95G6Q/hqdefault.jpg",
				RewardMsg:       "NTD1000",
				RedeemTime:      1541811600,
				RewardCode:      "55688",
			},
			&model.History{
				CampaignName:    "55688-654321",
				CampaignContent: "Though thou art fall'n, while we are free,Thou shalt not taste of death!The generous blood that flow'd from thee Disdain'd to sink beneath: Within our veins its currents be,Thy spirit on our breath!",
				CampaignURL:     "http://img.vlook.cn/static/pic/snap_17/fHu8.jpg",
				RewardMsg:       "NTD2000",
				RedeemTime:      1541671200,
			},
			&model.History{
				CampaignName:    "55688-941794",
				CampaignContent: "Thy name, our charging hosts along,Shall be the battle-word!Thy fall, the theme of choral song From virgin voices pour'd!To weep would do thy glory wrong:Thou shalt not be deplored.",
				CampaignURL:     "http://p9.p.pixnet.net/albums/userpics/9/6/804396/4913190b372a8.jpg",
				RewardMsg:       "NTD3000",
				RedeemTime:      1541576996,
				RewardCode:      "55699",
			},
			&model.History{
				CampaignName:    "55688-520131",
				CampaignContent: "You may write me down in history With your bitter, twisted lies,You may trod me in the very dirt But still, like dust, I'll rise.",
				CampaignURL:     "https://jakewqj.files.wordpress.com/2013/03/4177404_1364360532z.jpg?w=660",
				RewardMsg:       "NTD100",
				RedeemTime:      1541462400,
			},
			&model.History{
				CampaignName:    "55688-999999",
				CampaignContent: "Does my sassiness upset you? Why are you beset with gloom? 'Cause I walk like I've got oil wells Pumping in my living room.",
				CampaignURL:     "http://s2.sinaimg.cn/mw690/004lDiFCgy6Pk7G91jXd1&690",
				RewardMsg:       "NTD200",
				RedeemTime:      1541030400,
				RewardCode:      "55700",
			},
		},
	}
}

func mockApplyCoupon(c *gin.Context) interface{} {
	code := c.Param("code")

	if code == "55688" {
		return &model.ResUseCoupon{
			RewardCode:     "",
			RedeemImageURL: "http://i.imgur.com/xPRX1p7.jpg",
			RedeemMsg:      "I BRING you with reverent hands The books of my numberless dreams,White woman that passion has worn As the tide wears the dove-grey sands,And with heart more old than the horn That is brimmed from the pale fire of time:White woman with numberless dreams,I bring you my passionate rhyme.",
			CampaignURL:    "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSVuzHvQQE7j8awMrcyQlhFT5bRpBUgAIu8aND-YIbIUusTSs2T",
		}
	}

	return &model.ResUseCoupon{
		RewardCode:     "941794",
		RedeemImageURL: "https://scontent.ftpe8-1.fna.fbcdn.net/v/t1.0-1/18034156_1722906901340198_2584382753984003365_n.jpg?_nc_cat=109&_nc_ht=scontent.ftpe8-1.fna&oh=c47c5f6c80c0852f9b1327d02cfea747&oe=5C7715B7",
		RedeemMsg:      "Sophocles long ago Heard it on the Agean, and it brought Into his mind the turbid ebb and flow Of human misery; we Find also in the sound a thought, Hearing it by this distant northern sea.",
		CampaignURL:    "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS_sd-Pq4OIpLbXWNR9sgZf-PEEZ6gQnBEJMcdNvzZw0CiEmnyt",
	}
}
