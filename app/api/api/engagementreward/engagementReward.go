package engagementreward

import (
	"encoding/json"
	"errors"
	"net/http"

	"github.com/17media/logrus"
	"github.com/gin-gonic/gin"

	"github.com/17media/api/apis/common"
	userMW "github.com/17media/api/app/api/api/middlewares/user"

	"github.com/17media/api/apis"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/encoding"
	"github.com/17media/api/models"
	cfgModel "github.com/17media/api/models/config"
	model "github.com/17media/api/models/engagementreward"
	featureModel "github.com/17media/api/models/feature"
	regionModel "github.com/17media/api/models/region"
	"github.com/17media/api/stores/engagementreward"
	"github.com/17media/api/stores/feature"
	"github.com/17media/api/stores/live"
	"github.com/17media/api/stores/user"
)

var (
	isFeatureOn = feature.IsFeatureOn
)

// NewHandler creates a new handler for engagementReward routing
func NewHandler(rg *gin.RouterGroup, engagementRewardStore engagementreward.Store, userStore user.Store, liveStore live.Live) {
	eh := engagementRewardHandler{
		engagementRewardStore: engagementRewardStore,
		userStore:             userStore,
		liveStore:             liveStore,
	}

	urg := rg.Group("")
	urg.Use(common.GetToken())
	urg.Use(userMW.GetUserOrGuest(userStore))
	urg.Use(userMW.GetRegionInfo(userStore))

	// Get active engagement phases
	apis.Handle(urg, "GET", "/phases", eh.getPhases)

	// Trigger engagement phases
	apis.Handle(urg, "POST", "/phases/trigger", eh.triggerPhases)
}

// engagementRewardHandler is program api handler
type engagementRewardHandler struct {
	engagementRewardStore engagementreward.Store
	userStore             user.Store
	liveStore             live.Live
}

func (eh *engagementRewardHandler) getPhases(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)

	regionInfo := c.MustGet("RegionInfo").(*regionModel.RegionInfo)

	activePhases, err := eh.engagementRewardStore.ListActiveScriptsByRegion(context, regionInfo.Region)
	if err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_UNKNOWN,
			ErrorMessage: err.Error(),
		})
		return
	}

	resp := composeGetPhaseResp(context, activePhases)

	apis.JSON(c, http.StatusOK, resp)
}

func composeGetPhaseResp(context ctx.CTX, phases []model.EngagementPhase) model.EngagementPhaseResp {
	engages := []model.EngagementPhaseRespItem{}

	for _, phase := range phases {
		if phase.CalculationType != model.CalculationTypeClient {
			continue
		}

		var parsedScript interface{}
		if err := json.Unmarshal([]byte(phase.Script), &parsedScript); err != nil {
			context.WithFields(logrus.Fields{"err": err}).Error("json.Unmarshal failed")
			continue
		}

		item := model.EngagementPhaseRespItem{
			EngageID:   phase.EngageID,
			Type:       phase.Type,
			Replayable: phase.IsReplayable(),
			Script:     parsedScript,
		}
		engages = append(engages, item)
	}

	return model.EngagementPhaseResp{
		Engages: engages,
	}
}

func (eh *engagementRewardHandler) triggerPhases(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)

	body := TriggerRequest{}
	if err := encoding.BindJSON(c, &body, encoding.WithoutFillNil()); err != nil {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	}

	streamInfo, err := eh.liveStore.GetStreamInfo(context, body.StreamID)
	if errors.Is(err, live.ErrStreamNotFound) {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	} else if err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_UNKNOWN,
			ErrorMessage: err.Error(),
		})
		return
	}

	if !isFeatureOn(featureModel.FeatureEngagementReward, &regionModel.RegionInfo{Region: streamInfo.Region}, &cfgModel.DeviceInfo{}) {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: engagementreward.ErrEngagementNotAvailable.Error(),
		})
		return
	}

	triggerParam := model.TriggerParam{
		StreamID:       body.StreamID,
		UserID:         body.UserID,
		EngageIDs:      []string{body.EngageID},
		Region:         streamInfo.Region,
		Action:         model.TriggerActionMeetCondition,
		TriggerDetails: body.TriggerDetails,
	}

	err = eh.engagementRewardStore.Trigger(context, triggerParam)
	if errors.Is(err, engagementreward.ErrEngageNotExist) || errors.Is(err, engagementreward.ErrEngageInCoolDown) {
		apis.JSON(c, models.StatusClientError, models.Error{
			ErrorCode:    models.Error_PARAMETER_ERROR,
			ErrorMessage: err.Error(),
		})
		return
	} else if err != nil {
		apis.JSON(c, models.StatusServerError, models.Error{
			ErrorCode:    models.Error_UNKNOWN,
			ErrorMessage: err.Error(),
		})
		return
	}

	apis.JSON(c, http.StatusNoContent, nil)
}
