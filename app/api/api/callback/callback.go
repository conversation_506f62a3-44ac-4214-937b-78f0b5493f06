package callback

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"github.com/17media/api/base/metrics"
	btime "github.com/17media/api/base/time"
	"github.com/17media/logrus"
	"github.com/gin-gonic/gin"

	"github.com/17media/api/apis"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models"
	model "github.com/17media/api/models/rtmp"
	"github.com/17media/api/service/rtmp"
	"github.com/17media/api/stores/live"
	lhConfig "github.com/17media/api/stores/live/helper/config"
)

var (
	met = metrics.New("callback")

	timeNow          = btime.TimeNow
	getPullRateLimit = lhConfig.GetPullRateLimitCfg
)

type callbackHDL struct {
	rtmp      rtmp.RTMP
	liveStore live.Live
}

// NewHandler initialize the callback endpoint for other services
func NewHandler(rg *gin.RouterGroup, rtmp rtmp.RTMP, liveStore live.Live) error {
	hdl := callbackHDL{
		rtmp:      rtmp,
		liveStore: liveStore,
	}

	// rtmp providers callback
	apis.Handle(rg, "POST", "/rtmp/:provider", hdl.rtmpCallback)
	apis.Handle(rg, "POST", "/rtmp/:provider/:action", hdl.rtmpCallbackWithAction)
	apis.Handle(rg, "GET", "/rtmp/:provider/:action", hdl.rtmpCallbackWithAction)

	return nil
}

func (h *callbackHDL) rtmpCallback(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)

	// bind resquest body
	var req map[string]interface{}
	c.Bind(&req)

	// handle this callback
	provider := c.Param("provider")
	resp, err := h.rtmp.HandleCallback(context, provider, "", c.Request.URL.Query(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{})
		context.WithFields(logrus.Fields{
			"provider": provider,
			"err":      err,
		})
		return
	}
	c.JSON(http.StatusOK, resp)
}

type WansuStreamStatusEventParam struct {
	IP      string `form:"ip"`
	ID      string `form:"id"`
	Node    string `form:"node"`
	App     string `form:"app"`
	Appname string `form:"appname"`
	Time    string `form:"time"`
	Sign    string `form:"sign"`
}

type TencentStreamStatusEventParam struct {
	Appid        int    `json:"appid"`         // 用户 APPID
	App          string `json:"app"`           // 推流域名
	Appname      string `json:"appname"`       // 推流路径
	StreamID     string `json:"stream_id"`     // 直播流名称
	ChannelID    string `json:"channel_id"`    // 同直播流名称
	EventTime    int64  `json:"event_time"`    // 事件消息产生的 UNIX 时间戳
	Sequence     string `json:"sequence"`      // 消息序列号，标识一次推流活动，一次推流活动会产生相同序列号的推流和断流消息
	Node         string `json:"node"`          // 直播接入点的 IP
	UserIP       string `json:"user_ip"`       // 用户推流 IP
	StreamParam  string `json:"stream_param"`  // 用户推流 URL 所带参数
	PushDuration string `json:"push_duration"` // 断流事件通知推流时长，单位毫秒
	Errcode      int    `json:"errcode"`       // 推断流错误码
	Errmsg       string `json:"errmsg"`        // 推断流错误描述
	SetID        int    `json:"set_id"`        // 判断是否为国内外推流。1-6为国内，7-200为国外
	Width        int    `json:"width"`         // 视频宽度，最开始推流回调的时候若视频头部信息缺失，可能为0
	Height       int    `json:"height"`        // 视频高度，最开始推流回调的时候若视频头部信息缺失，可能为0
}

func (h *callbackHDL) rtmpCallbackWithAction(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	provider := c.Param("provider")
	action := c.Param("action")

	switch action {
	case model.ActionAuth:
		// bind resquest body
		req := model.SourceAuth{}
		if err := c.Bind(&req); err != nil {
			// Bind failure will add status code 400 automatically
			context.WithField("err", err).Error("bind failed")
			return
		}

		context = ctx.WithValues(context, map[string]interface{}{
			"randomString": req.RandomString,
			"liveStreamID": req.LiveStreamID,
			"userID":       req.UserID,
			"deviceID":     req.DeviceID,
			"ip":           req.IP,
			"timestamp":    req.Timestamp,
			"md5":          req.MD5,
		})
		md5s := map[string]interface{}{}
		verifyMD5, err := req.ToMD5()
		if err != nil {
			context.Error("can't produce md5 for verification")
			c.JSON(models.StatusServerError, gin.H{})
			return
		}

		md5s["request"] = req.MD5
		md5s["verify"] = verifyMD5
		ttl := getPullRateLimit().PullURLTTL
		now := timeNow().Unix()

		context = ctx.WithValues(context, map[string]interface{}{
			"verifyMD5": verifyMD5,
			"now":       now,
			"ttl":       ttl,
		})

		if _, err = h.rtmp.HandleCallback(context, provider, action, c.Request.URL.Query(), md5s); err != nil {
			c.JSON(models.StatusServerError, gin.H{})
			context.WithFields(logrus.Fields{
				"provider": provider,
				"err":      err,
			}).Error("rtmp.HandleCallback failed")
			return
		}

		timestamp, err := strconv.ParseInt(req.Timestamp, 10, 64)
		if err != nil {
			c.JSON(models.StatusServerError, gin.H{})
			context.WithFields(logrus.Fields{
				"provider": provider,
				"err":      err,
			}).Error("strconv.ParseInt failed")
			return
		}

		if (now - timestamp) > int64(ttl) {
			context.Error("exceed ttl")
			c.JSON(models.StatusServerError, gin.H{})
			return
		}

		// NOTED: we couldn't move live store into provider, so this is the place we can handle business logic
		streamInfo, err := h.liveStore.GetInfo(context, req.LiveStreamID)
		if err != nil {
			context.WithFields(logrus.Fields{
				"provider": provider,
				"err":      err,
			}).Error("stream.GetInfo failed")
			c.JSON(models.StatusServerError, gin.H{})
			return
		}

		// we shouldn't allow user to pull stream if stream is closed
		if len(streamInfo.CloseBy) > 0 {
			context.WithField("reason", streamInfo.CloseBy).Error("stream closed")
			c.JSON(models.StatusServerError, gin.H{})
			return
		}

		if err := h.liveStore.CheckSourceAuth(context, req, streamInfo); err != nil {
			context.WithFields(logrus.Fields{
				"provider": provider,
				"err":      err,
			}).Error("live.CheckSourceAuth failed")
			// TODO: adjust response case if needs
			c.JSON(models.StatusServerError, gin.H{})
			return
		}

		c.JSON(http.StatusOK, gin.H{})

	case model.ActionStreamOnLive:
		met.BumpSum("provider", 1, "action", action)

		p, ok := models.RtmpProviderValue[strings.ToUpper(provider)]
		if !ok || rtmp.IsUnsupportedProvider(int32(p)) {
			context.WithFields(logrus.Fields{
				"provider": provider,
			}).Error("provider not supported")

			c.JSON(models.StatusServerError, gin.H{})
			return
		}

		pro := models.RtmpProvider(p)
		switch pro {
		case models.RtmpProviderWansu, models.RtmpProviderWansuLowLatency, models.RtmpProviderWansuAuth:
			req := WansuStreamStatusEventParam{}
			if err := c.ShouldBindQuery(&req); err != nil {
				c.JSON(models.StatusClientError, gin.H{})
				context.WithField("err", err).Error("ShouldBindQuery failed")
				return
			}

			// FIXME: verify correctness of sign

			reqbody, _ := json.Marshal(req)
			context.WithFields(logrus.Fields{
				"reqbody": string(reqbody),
			}).Info("wansu stream on live event notification")

			c.JSON(http.StatusOK, gin.H{})
			return

		case models.RtmpProviderTencent:
			req := TencentStreamStatusEventParam{}
			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(models.StatusClientError, gin.H{})
				context.WithField("err", err).Error("ShouldBindJSON failed")
				return
			}

			// FIXME:
			// 1. add callback key in tencent console
			// 2. verify correctness of sign

			reqbody, _ := json.Marshal(req)
			context.WithFields(logrus.Fields{
				"reqbody": string(reqbody),
			}).Info("tencent stream on live event notification")

			c.JSON(http.StatusOK, gin.H{})
			return

		default:
			context.WithFields(logrus.Fields{
				"provider": pro.String(),
			}).Error("provider not supported")

			c.JSON(models.StatusServerError, gin.H{})
			return
		}

	case model.ActionStreamEnd:
		met.BumpSum("provider", 1, "action", action)

		p, ok := models.RtmpProviderValue[strings.ToUpper(provider)]
		if !ok || rtmp.IsUnsupportedProvider(int32(p)) {
			context.WithFields(logrus.Fields{
				"provider": provider,
			}).Error("provider not supported")

			c.JSON(models.StatusServerError, gin.H{})
			return
		}

		pro := models.RtmpProvider(p)
		switch pro {
		case models.RtmpProviderWansu, models.RtmpProviderWansuLowLatency, models.RtmpProviderWansuAuth:
			req := WansuStreamStatusEventParam{}
			if err := c.ShouldBindQuery(&req); err != nil {
				c.JSON(models.StatusClientError, gin.H{})
				context.WithField("err", err).Error("ShouldBindQuery failed")
				return
			}

			// FIXME: verify correctness of sign

			reqbody, _ := json.Marshal(req)
			context.WithFields(logrus.Fields{
				"reqbody": string(reqbody),
			}).Info("wansu stream end event notification")

			c.JSON(http.StatusOK, gin.H{})
			return

		case models.RtmpProviderTencent:
			req := TencentStreamStatusEventParam{}
			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(models.StatusClientError, gin.H{})
				context.WithField("err", err).Error("ShouldBindJSON failed")
				return
			}

			// FIXME:
			// 1. add callback key in tencent console
			// 2. verify correctness of sign

			reqbody, _ := json.Marshal(req)
			context.WithFields(logrus.Fields{
				"reqbody": string(reqbody),
			}).Info("tencent stream end event notification")

			c.JSON(http.StatusOK, gin.H{})
			return

		default:
			context.WithFields(logrus.Fields{
				"provider": pro.String(),
			}).Error("provider not supported")

			c.JSON(models.StatusServerError, gin.H{})
			return
		}

	default:
		c.JSON(http.StatusOK, gin.H{})
	}
}
