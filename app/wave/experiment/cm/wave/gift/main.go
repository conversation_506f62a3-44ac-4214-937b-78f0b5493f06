package main

import (
	"flag"
	"fmt"

	"github.com/jmoiron/sqlx"

	"github.com/17media/api/app/wave/models"
	mdb "github.com/17media/api/base/db"
	btime "github.com/17media/api/base/time"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/setup/mysql/mysqlgiftwriter"
)

var (
	start, _ = btime.Parse("2021-05-31 00:00:00 (GMT+0700)")
	end, _   = btime.Parse("2021-06-13 23:59:59 (GMT+0700)")
	tag      = "eventgift_superstarid"
	tab      = "event_superstarid"
	gifts    = []*models.Gift{
		&models.Gift{
			DisplayType:          models.GiftDisplayTypeNoEffect,
			GiftID:               "event_gift_superstarid_0",
			Point:                10,
			IconURL:              "https://cdn.wave.com.tw/v1/bot/1607061611696482_5930172641243476_82610418207050714312892031385422_re.png",
			I18n<PERSON><PERSON>:              "event_gift_superstarid_0",
			MessageSuffixIconURL: "https://cdn.wave.com.tw/v1/bot/1600830425352664_4034442562792722_24808213911828014549443501972415_pm.png",
			AnimationID:          "event_gift_superstarid_0",
			AnimationCount:       1,
			Sequence:             1,
			AnimationURLsStr:     "[]",
		},
		&models.Gift{
			DisplayType:          models.GiftDisplayTypeAtmosphere,
			GiftID:               "event_gift_superstarid_1",
			Point:                300,
			IconURL:              "https://cdn.wave.com.tw/v1/bot/1619680641516700_0931809999663317_54574813798119354088862732532983_zp.png",
			I18nKey:              "event_gift_superstarid_1",
			MessageSuffixIconURL: "https://cdn.wave.com.tw/v1/bot/1619680641516700_0931809999663317_54574813798119354088862732532983_zp.png",
			AnimationID:          "event_gift_superstarid_1",
			AnimationCount:       1,
			Sequence:             2,
			Label:                "giftpanel_gifttag_atmosphere",
			LabelColor:           "#F05057",
			AnimationHeight:      12,
			AnimationWidth:       12,
			AnimationDuration:    30,
			AnimationURLsStr:     "[\"https://cdn.wave.com.tw/v1/bot/1619680647569549_6681223148753183_05951794049295587701087020418822_fk.png\",\"https://cdn.wave.com.tw/v1/bot/1619680652881127_5668776136802153_98714479946286778200624326208919_hs.png\",\"https://cdn.wave.com.tw/v1/bot/1619680657468630_9711828895409388_70501112670402367258209015189531_xf.png\"]",
		},
		&models.Gift{
			DisplayType:          models.GiftDisplayTypeCollision,
			GiftID:               "event_gift_superstarid_2",
			Point:                1500,
			IconURL:              "https://cdn.wave.com.tw/v1/bot/1619681561701026_2849166263230425_17322591586167479461437920899201_wc.png",
			I18nKey:              "event_gift_superstarid_2",
			MessageSuffixIconURL: "https://cdn.wave.com.tw/v1/bot/1619681568538754_1858725755579078_32776361358176230153670492227563_wr.png",
			AnimationID:          "event_gift_superstarid_2",
			AnimationCount:       1,
			Sequence:             3,
			AnimationHeight:      45,
			AnimationWidth:       45,
			AnimationShape:       models.GiftShapeCircle,
			Label:                "giftpanel_gifttag_drop",
			LabelColor:           "#EEEEEE",
			ExistDurationInSec:   180,
			AnimationURLsStr:     "[]",
		},
		&models.Gift{
			DisplayType:          models.GiftDisplayTypeCollision,
			GiftID:               "event_gift_superstarid_3",
			Point:                3000,
			IconURL:              "https://cdn.wave.com.tw/v1/bot/1619681734664367_5317596380086764_61657132694478418951888607715121_rl.png",
			I18nKey:              "event_gift_superstarid_3",
			MessageSuffixIconURL: "https://cdn.wave.com.tw/v1/bot/1619681741637929_6535806217962341_97755761626673431821102601872418_zi.png",
			AnimationID:          "event_gift_superstarid_3",
			AnimationCount:       1,
			Sequence:             4,
			AnimationHeight:      85,
			AnimationWidth:       85,
			AnimationShape:       models.GiftShapeCircle,
			Label:                "giftpanel_gifttag_drop",
			LabelColor:           "#EEEEEE",
			ExistDurationInSec:   180,
			AnimationURLsStr:     "[]",
		},
		&models.Gift{
			DisplayType:          models.GiftDisplayTypeFullScreen,
			GiftID:               "event_gift_superstarid_4",
			Point:                5000,
			IconURL:              "https://cdn.wave.com.tw/v1/bot/1619682031175881_4565350274655880_95164685477125988914991379398090_gp.png",
			I18nKey:              "event_gift_superstarid_4",
			MessageSuffixIconURL: "https://cdn.wave.com.tw/v1/bot/1619681999864537_7489953609651119_05834571111630133101738955244727_pp.png",
			AnimationID:          "event_gift_superstarid_4",
			AnimationCount:       1,
			Sequence:             5,
			AnimationHeight:      1136,
			AnimationWidth:       640,
			AnimationDynamicURL:  "https://cdn.wave.com.tw/v1/bot/1619682011947253_2883802116046458_26216645974389242405222410356799_ft.webp",
			AnimationDuration:    3,
			Label:                "giftpanel_gifttag_full",
			LabelColor:           "#F9E335",
			AnimationURLsStr:     "[]",
		},
		&models.Gift{
			DisplayType:          models.GiftDisplayTypeCollision,
			GiftID:               "event_gift_superstarid_5",
			Point:                20000,
			IconURL:              "https://cdn.wave.com.tw/v1/bot/1619682170649595_2631412184100897_48401188311397882958094332159732_ax.png",
			I18nKey:              "event_gift_superstarid_5",
			MessageSuffixIconURL: "https://cdn.wave.com.tw/v1/bot/1619682175560840_1265794491417198_78043703714011449175322665972540_ua.png",
			AnimationID:          "event_gift_superstarid_5",
			AnimationCount:       1,
			Sequence:             6,
			AnimationHeight:      170,
			AnimationWidth:       170,
			AnimationShape:       models.GiftShapeCircle,
			AnimationDynamicURL:  "https://cdn.wave.com.tw/v1/bot/1619682397488390_0050998359298670_03401495748412494254231855733201_qv.webp",
			Label:                "giftpanel_gifttag_anime",
			LabelColor:           "#00C99C",
			ExistDurationInSec:   1200,
			AnimationURLsStr:     "[]",
		},
		&models.Gift{
			DisplayType:          models.GiftDisplayTypeFullScreen,
			GiftID:               "event_gift_superstarid_6",
			Point:                35000,
			IconURL:              "https://cdn.wave.com.tw/v1/bot/1619682563159683_1487856870882343_98857019458404309569696417934764_pb.png",
			I18nKey:              "event_gift_superstarid_6",
			MessageSuffixIconURL: "https://cdn.wave.com.tw/v1/bot/1619682569159760_4204407133445962_20575607733103210132604113855082_gc.png",
			AnimationID:          "event_gift_superstarid_6",
			AnimationCount:       1,
			Sequence:             7,
			AnimationHeight:      1136,
			AnimationWidth:       640,
			AnimationDynamicURL:  "https://cdn.wave.com.tw/v1/bot/1619682576548274_7223422411111119_60251579905700381325717155970236_xi.webp",
			AnimationDuration:    5,
			Label:                "giftpanel_gifttag_full",
			LabelColor:           "#F9E335",
			AnimationURLsStr:     "[]",
		},
		&models.Gift{
			DisplayType:               models.GiftDisplayTypeFullScreen,
			GiftID:                    "event_gift_superstarid_7",
			Point:                     100000,
			IconURL:                   "https://cdn.wave.com.tw/v1/bot/1607061609573093_5268025923365253_77804674358930297669450236215757_im.png",
			I18nKey:                   "event_gift_superstarid_7",
			MessageSuffixIconURL:      "https://cdn.wave.com.tw/v1/bot/1600839226442836_7767683476321299_84432118337175668723907818978617_ac.png",
			AnimationID:               "event_gift_superstarid_7",
			AnimationCount:            1,
			Sequence:                  8,
			AnimationHeight:           1136,
			AnimationWidth:            640,
			AnimationDynamicURL:       "https://cdn.wave.com.tw/v1/bot/1600839323980786_7945111111056446_59674570702052990965112879125166_uf.webp",
			AnimationDynamicLowEndURL: "https://cdn.wave.com.tw/v1/bot/1600839323980786_7945111111056446_59674570702052990965112879125166_uf.webp",
			AnimationDuration:         10,
			Label:                     "giftpanel_gifttag_full",
			LabelColor:                "#F9E335",
			AnimationURLsStr:          "[]",
		},
	}
)

func upsertGift(dbx *sqlx.DB, gift *models.Gift) error {
	s := `
INSERT INTO Gift(giftID, point, iconURL, i18nKey, isOnline, isHidden, isArchived, sequence, regionMode, regions, type, messageSuffixIconURL, animationID, animationCount, tag, tab, validStartTime, validEndTime, animationHeight, animationWidth, animationDuration, animationUrls, animationShape, label, labelColor, existDurationInSec, animationDynamicUrl, displayType, animationDynamicLowEndUrl)
	VALUES(:giftID, :point, :iconURL, :i18nKey, :isOnline, :isHidden, :isArchived, :sequence, :regionMode, '[]', :type, :messageSuffixIconURL, :animationID, :animationCount, :tag, :tab, :validStartTime, :validEndTime, :animationHeight, :animationWidth, :animationDuration, :animationUrls, :animationShape, :label, :labelColor, :existDurationInSec, :animationDynamicUrl, :displayType, :animationDynamicLowEndUrl)
	ON DUPLICATE KEY UPDATE
	point=:point, iconURL=:iconURL, sequence=:sequence, messageSuffixIconURL=:messageSuffixIconURL, validStartTime=:validStartTime, validEndTime=:validEndTime, animationHeight=:animationHeight, animationWidth=:animationWidth, animationDuration=:animationDuration, animationUrls=:animationUrls, animationShape=:animationShape, label=:label, labelColor=:labelColor, existDurationInSec=:existDurationInSec, animationDynamicUrl=:animationDynamicUrl, displayType=:displayType, animationDynamicLowEndUrl=:animationDynamicLowEndUrl;
`

	res, err := dbx.NamedExec(s, gift)
	if err != nil {
		return err
	}
	_, err = res.RowsAffected()
	if err != nil {
		return err
	}

	return nil
}

func main() {
	m := dimanager.DefaultManager
	m.Compile()
	flag.Parse()

	db := mysqlgiftwriter.GetMySQLGiftWriter(m)
	dbx := sqlx.NewDb(db, mdb.SQLDriver)

	// set general fields
	for _, gift := range gifts {
		gift.Type = models.GiftTypeNormal
		gift.IsOnline = 1
		gift.IsHidden = 0
		gift.IsArchived = 0
		gift.RegionMode = models.RegionModeAll
		gift.ValidStartTime = start.Unix()
		gift.ValidEndTime = end.Unix()
		gift.Tab = tab
		gift.Tag = tag

		if err := upsertGift(dbx, gift); err != nil {
			fmt.Println(gift)
			panic(err)
		}
	}
}
