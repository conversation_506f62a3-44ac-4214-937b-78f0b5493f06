package main

import (
	"flag"
	"fmt"

	compute "google.golang.org/api/compute/v1"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/service/gcp"
)

const (
	instanceGroup = "ondemand-service"
	//templateName  = "ondemand-service-template"
	templateName = "ondemand-service-dev"
)

func main() {
	ctx := ctx.Background()
	flag.Parse()

	cs := gcp.New()

	//addInstanceTempalte(ctx, cs)
	createGroup(ctx, cs)
	//addInstance(ctx, cs)

	//is, err := cs.ListInstances(ctx, instanceGroup)
	//if err != nil {
	//	panic(err)
	//}
	//for _, i := range is {
	//	id, err := cs.GetInstance(ctx, strings.Split(i.Instance, "instances/")[1])
	//	if err != nil {
	//		panic(err)
	//	}
	//	fmt.Printf("%+v\n", id.NetworkInterfaces[0].AccessConfigs[0].NatIP)
	//	fmt.Println(strings.Split(i.Instance, "instances/")[1])
	//	fmt.Println(i.CurrentAction, i.Id, i.Instance, i.InstanceStatus)

	//	// cmd := "workspace/go/src/github.com/17media/api/experiment/cm/gcp/dev.sh > deploy.log 2>&1 &"
	//	cmd := "echo $USER"
	//	r, err := cs.SSHInstance(ctx, strings.Split(i.Instance, "instances/")[1], cmd)
	//	if err != nil {
	//		panic(err)
	//	}
	//	fmt.Printf("=== Output ===\n%s\n=============", r)

	//}

	//err = cs.DeleteInstancesFromGroup(ctx, instanceGroup, is[0].Instance)
	//if err != nil {
	//	panic(err)
	//}
}

func deleteInstanceTempalte(context ctx.CTX, cs gcp.Service) {
	err := cs.DeleteInstanceTemplate(context, "ondemand-template")
	if err != nil {
		panic(err)
	}
}

func addInstanceTempalte(context ctx.CTX, cs gcp.Service) {
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
	sshScript := "ubuntu:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDYG05UiDUGZsdh349FpbtP01jf6VeOhcDmykZ+dn8ZoUCTuNHXPxeYIQmrVq8G42gcNIFG9wmCS2gqGffOdlLQ6Wu+Sdsa4IlBSQvpOrZZIiRvVNrZqg6AcjWKZY4M6bI4mgH/N6vIUjjB1M+oBfZYwx6vBlR7LcpnMzBl+qNG4b42Vb9QKEE6Qp2eOlOt2q3KPDUgRU7JlOMEEeyEgPBT/Z0Zn/TQ8SpzgsqFm3rOtqnhaDToh+ItGDG8gvUfD8lwYpSukrhrhLCwpSVkhH5E6JP4/LZqa+wyHC1hnmsdz+lSQQ+0TUEQrnqf7TH1Jtyyt4f8qkyHcOtDXbRhCPmL ubuntu"
	autoStart := true
	it := &compute.InstanceTemplate{
		Name:        "ondemand-service-template",
		Description: "ondemand service template",
		Properties: &compute.InstanceProperties{
			MachineType: "n1-standard-2",
			Metadata: &compute.Metadata{
				Items: []*compute.MetadataItems{
					&compute.MetadataItems{
						Key:   "startup-script",
						Value: &startScript,
					},
					&compute.MetadataItems{
						Key:   "ssh-keys",
						Value: &sshScript,
					},
				},
			},
			Tags: &compute.Tags{
				Items: []string{"http-server"},
			},
			Disks: []*compute.AttachedDisk{
				&compute.AttachedDisk{
					Mode:       "READ_WRITE",
					Type:       "PERSISTENT",
					AutoDelete: true,
					Boot:       true,
					DeviceName: "ondemand-service-template",
					InitializeParams: &compute.AttachedDiskInitializeParams{
						SourceImage: "projects/ubuntu-os-cloud/global/images/ubuntu-1604-xenial-v20180522",
						DiskSizeGb:  100,
						DiskType:    "pd-standard",
					},
				},
			},
			CanIpForward: false,
			NetworkInterfaces: []*compute.NetworkInterface{
				&compute.NetworkInterface{
					Network: "projects/media17-dev/global/networks/ondemand-service",
					AccessConfigs: []*compute.AccessConfig{
						&compute.AccessConfig{
							Name: "External NAT",
							//"networkTier": "PREMIUM"
							Type: "ONE_TO_ONE_NAT",
						},
					},
					AliasIpRanges: []*compute.AliasIpRange{},
				},
			},
			Scheduling: &compute.Scheduling{
				Preemptible:       false,
				OnHostMaintenance: "MIGRATE",
				AutomaticRestart:  &autoStart,
			},
			ServiceAccounts: []*compute.ServiceAccount{
				&compute.ServiceAccount{
					Email: "<EMAIL>",
					Scopes: []string{
						"https://www.googleapis.com/auth/devstorage.read_only",
						"https://www.googleapis.com/auth/logging.write",
						"https://www.googleapis.com/auth/monitoring.write",
						"https://www.googleapis.com/auth/servicecontrol",
						"https://www.googleapis.com/auth/service.management.readonly",
						"https://www.googleapis.com/auth/trace.append",
					},
				},
			},
		},
	}
	err := cs.AddInstanceTemplate(context, it)
	if err != nil {
		panic(err)
	}
}

func createGroup(context ctx.CTX, cs gcp.Service) {
	tn := fmt.Sprintf("https://www.googleapis.com/compute/v1/projects/media17-dev/global/instanceTemplates/%s", templateName)
	err := cs.CreateGroup(context, instanceGroup, tn, 1)
	if err != nil {
		panic(err)
	}
}

func addInstance(context ctx.CTX, cs gcp.Service) {
	err := cs.AddInstanceToGroup(context, instanceGroup)
	if err != nil {
		panic(err)
	}
}
