#!/bin/bash

go run main.go \
    -mongo_db="wave-test" \
    -mongo_auth_db="admin" \
    -mongo_uri="mongodb://localhost:27017" \
    -mongo_enableSSL=false \
    -mongo_set_safe=false \
    -mongo_pool_size_multiplier=10 \
    -rds_gift_uri="wave:wave@tcp(localhost:3306)/wave?charset=utf8mb4&timeout=10s&&&wave:wave@tcp(localhost:3306)/wave?charset=utf8mb4&timeout=10s&&&wave:wave@tcp(localhost:3306)/wave?charset=utf8mb4&timeout=10s" \
    -es_protocol=http \
    -es_username="" \
    -es_password="" \
    -es_uri=http://localhost:9200 \
    -es_ping_uri=http://localhost:9200 \
    -es_index=wave \
    -es_client_timeout_second=5 \
    -es_client_max_retries=1 \
    -etcd_hosts="http://localhost:2379" \
    -pubsub_emulator_host="localhost:8538" \
    -redis_cache_uri="localhost:6379" \
    -redis_cache_username="" \
    -redis_cache_pwd="" \
    -redis_cache_paging_uri="localhost:6379" \
    -redis_cache_paging_username="" \
    -redis_cache_paging_pwd="" \
    -redis_persist_uri="localhost:6379" \
    -redis_persist_username="" \
    -redis_persist_pwd="" \
    -redis_cluster_cache_addr="" \
    -redis_cluster_cache_user="" \
    -redis_cluster_cache_pwd=""

  
