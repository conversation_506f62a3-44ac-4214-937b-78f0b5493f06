package main

import (
	"flag"
	"fmt"
	"time"

	"github.com/17media/api/app/wave/base/script"
	"github.com/17media/api/app/wave/stores/user"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/setup/dimanager"
)

const (
	batchSize    = 10000
	region       = "TW"
	lastOnlineAt = int64(1697472000) // 2023/10/17 00:00:00 GMT+08:00
)

func main() {
	m := dimanager.DefaultManager
	m.Compile()

	flag.Parse()

	script.LoadDependency(m)
	userStore := user.GetUser(m)
	context := ctx.Background()

	// get total users
	_, total, err := userStore.List(context, 0, 1, user.WithRegion(region), user.WithLastOnlineAt(lastOnlineAt))
	if err != nil {
		panic(err)
	}

	startTime := time.Now()
	requests := 0
	for offset := 0; offset < total; offset += batchSize {
		if _, _, err := userStore.List(context, offset, batchSize, user.WithRegion(region), user.WithLastOnlineAt(lastOnlineAt)); err != nil {
			panic(err)
		}
		requests += 1
	}

	elapsed := time.Since(startTime)

	fmt.Println("total:", total)
	fmt.Println("batchSize:", batchSize)
	fmt.Printf("Process time (s): %f\n", elapsed.Seconds())
	fmt.Printf("Latency per request (s): %f\n", elapsed.Seconds()/float64(requests))
}
