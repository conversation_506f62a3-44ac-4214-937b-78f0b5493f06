package gift

import (
	"fmt"

	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/app/wave/stores/gift/factory"
)

// MetaFactory is a container of multiple actual factory object
type MetaFactory struct {
	Factories map[models.GiftType]factory.Factory
}

// Get return product for a specific gift type
func (mf *MetaFactory) Get(resources factory.Resources, typ models.GiftType) (factory.Product, error) {
	fac, ok := mf.Factories[typ]
	if !ok {
		return nil, fmt.Errorf("no factory for type: %v", typ)
	}
	product := fac.GenerateProduct(resources)

	return product, nil
}
