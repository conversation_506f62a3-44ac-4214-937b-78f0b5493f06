package taiwan

import (
	"errors"

	"github.com/17media/api/app/wave/models"
)

var (
	errInvalidInvoiceProvider   = errors.New("invalid invoice provider")
	errEmptyInvoicePayload      = errors.New("empty invoice payload")
	errEmptyInvoiceNo           = errors.New("empty invoice number")
	errInvalidInvoiceIssuedTime = errors.New("invalid invoice issued time")
	errEmptyInvoiceRandomNumber = errors.New("empty invoice random number")
)

type issuedParams struct {
	Provider        models.TaiwanInvoiceProvider
	ProviderTraceID string
	Payload         string
	InvoiceNo       string
	IssuedTime      int64
	RandomNumber    string
}

func (p issuedParams) Validate() error {
	if p.Provider == models.TaiwanInvoiceProviderDummy {
		return errInvalidInvoiceProvider
	}
	if p.Payload == "" {
		return errEmptyInvoicePayload
	}
	if p.InvoiceNo == "" {
		return errEmptyInvoiceNo
	}
	if p.IssuedTime == 0 {
		return errInvalidInvoiceIssuedTime
	}
	if p.RandomNumber == "" {
		return errEmptyInvoiceRandomNumber
	}
	return nil
}
