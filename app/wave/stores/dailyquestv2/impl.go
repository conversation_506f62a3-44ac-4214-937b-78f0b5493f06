package dailyquestv2

import (
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"

	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/app/wave/stores"
	baggageHelper "github.com/17media/api/app/wave/stores/baggage/helper"
	"github.com/17media/api/app/wave/stores/decocenter"
	giftHelper "github.com/17media/api/app/wave/stores/gift/helper"
	"github.com/17media/api/app/wave/stores/money"
	"github.com/17media/api/app/wave/stores/user"
	"github.com/17media/api/base/ctx"
	mdb "github.com/17media/api/base/db"
	"github.com/17media/api/base/env"
	"github.com/17media/api/service/kv"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/service/redis/lock"
	"github.com/17media/logrus"
)

var (
	timeNow = time.Now

	errExceedRewardLimit = errors.New("checked-in reward exceeds the limit")
)

const (
	timeMachineTTL         = 24 * time.Hour
	dailyCheckInLockTTL    = 5 * time.Second
	dailyCheckInWaitPeriod = 5 * time.Second

	insertDailyQuestSQL = `INSERT INTO DailyQuestTransaction SET userID=?, day=?, rewardType=?, rewardID=?, tradeID=?, dealingID=?, createdAt=?, checkedInAt=?, migration=?`
	selectDailyQuestSQL = `
SELECT id, userID, day, rewardType, rewardID, tradeID, dealingID, createdAt, checkedInAt, migration
FROM DailyQuestTransaction
;`
	migrationVersion = 2

	daysOfWeek = 7
)

type impl struct {
	dbx        *sqlx.DB
	kv         kv.KV
	cache      redis.Service
	persist    redis.Service
	giftHelper giftHelper.Worker
	factory    Factory
}

func (im *impl) GetDailyCheckInStatus(context ctx.CTX, user *models.User) (*models.DailyCheckInStatus, error) {
	region := user.GetViewerRegion()
	now, err := im.getTimeNow(context, user.ID, region)
	if err != nil {
		context.WithFields(logrus.Fields{
			"userID": user.ID,
			"region": region,
			"err":    err,
		}).Error("im.getTimeNow failed")
		return nil, err
	}
	// get rewards
	rewards, err := getWeeklyRewardsByRegion(context, region, now)
	if err != nil {
		context.WithFields(logrus.Fields{
			"region": region,
			"now":    now.Unix(),
			"err":    err,
		}).Error("getWeeklyRewardsByRegion failed")
		return nil, err
	}
	// get DailyCheckInData from kv
	key, err := getCheckInDataKey(user.ID, region, now)
	if err != nil {
		context.WithFields(logrus.Fields{
			"userID": user.ID,
			"region": region,
			"now":    now.Unix(),
			"err":    err,
		}).Error("getCheckInDataKey failed")
		return nil, err
	}

	data := &models.DailyCheckInData{}
	if err := im.kv.Get(context, models.TabWaveDailyQuestCheckInData, key, data); err != nil && !errors.Is(err, kv.ErrNotFound) {
		context.WithFields(logrus.Fields{
			"table": models.TabWaveDailyQuestCheckInData,
			"k":     key,
			"err":   err,
		}).Error("kv.Get failed")
		return nil, err
	} else if errors.Is(err, kv.ErrNotFound) {
		checkedInDays := int64(0)
		rs, err := im.toDailyCheckInRewards(context, rewards, checkedInDays)
		if err != nil {
			context.WithFields(logrus.Fields{
				"checkInDays": checkedInDays,
				"err":         err,
			}).Error("im.toDailyCheckInRewards failed")
			return nil, err
		}
		return &models.DailyCheckInStatus{
			CheckInToday:  false,
			Rewards:       rs,
			CheckedInDays: checkedInDays,
		}, nil
	}
	// CheckInData is found
	rs, err := im.toDailyCheckInRewards(context, rewards, data.CheckedInDays)
	if err != nil {
		context.WithFields(logrus.Fields{
			"checkInDays": data.CheckedInDays,
			"err":         err,
		}).Error("im.toDailyCheckInRewards failed")
		return nil, err
	}
	ret := &models.DailyCheckInStatus{
		CheckInToday:  data.CheckedInHistory[models.GetIthDay(models.DailyQuestStartOfWeek, now.Weekday())] != 0,
		Rewards:       rs,
		CheckedInDays: data.CheckedInDays,
	}
	return ret, nil
}

func (im *impl) getTimeNow(context ctx.CTX, userID string, region string) (time.Time, error) {
	loc, err := util.GetLocationByRegion(region)
	if err != nil {
		context.WithFields(logrus.Fields{
			"region": region,
			"err":    err,
		}).Error("util.GetLocationByRegion failed")
		return time.Time{}, err
	}

	defaultNow := timeNow().In(loc)
	// prod can not adopt time machine
	if env.Namespace() == env.PROD {
		return defaultNow, nil
	}
	// get time machine from cache
	val, err := im.cache.Get(context, getTimeMachineRedisKey(userID))
	if err != nil && !errors.Is(err, redis.ErrNotFound) {
		context.WithFields(logrus.Fields{
			"key": getTimeMachineRedisKey(userID),
			"err": err,
		}).Warn("cache.Get failed")
		return defaultNow, nil
	} else if errors.Is(err, redis.ErrNotFound) {
		// user doesn't set time machine
		return defaultNow, nil
	}
	// user sets time machine
	ts, err := strconv.Atoi(string(val))
	if err != nil {
		context.WithFields(logrus.Fields{
			"s":   string(val),
			"err": err,
		}).Warn("strconv.Atoi failed")
		return defaultNow, nil
	}
	return time.Unix(int64(ts), 0).In(loc), nil
}

// getCheckInDataKey returns {userID}_{now.year}_{now.week}
// weekday of [start, end] is [Monday, Sunday]
func getCheckInDataKey(userID string, region string, now time.Time) (string, error) {
	loc, err := util.GetLocationByRegion(region)
	if err != nil {
		return "", err
	}
	year, week := now.In(loc).ISOWeek()
	return fmt.Sprintf("%s_%d_%d", userID, year, week), nil
}

func (im *impl) DailyCheckIn(context ctx.CTX, user *models.User) (*models.DailyCheckInRes, error) {
	var res *models.DailyCheckInRes
	var err error
	region := user.GetViewerRegion()
	now, err := im.getTimeNow(context, user.ID, region)
	if err != nil {
		context.WithFields(logrus.Fields{
			"userID": user.ID,
			"region": region,
			"err":    err,
		}).Error("im.getTimeNow failed")
		return nil, err
	}

	key, err := getCheckInDataKey(user.ID, region, now)
	if err != nil {
		context.WithFields(logrus.Fields{
			"userID": user.ID,
			"region": region,
			"now":    now.Unix(),
			"err":    err,
		}).Error("getCheckInDataKey failed")
		return nil, err
	}

	// get lock
	lockKey := getDailyCheckInRedisLockKey(user.ID)
	ok, lockToken, err := lock.GetLock(context, im.persist, lockKey, dailyCheckInLockTTL, dailyCheckInWaitPeriod)
	if err != nil {
		context.WithField("err", err).Error("lock.GetLock failed")
		return nil, err
	} else if !ok {
		context.Error("lock.GetLock wait lock failed")
		return nil, fmt.Errorf("lock.GetLock wait lock failed")
	}
	defer lock.ReleaseLock(context, im.persist, lockKey, lockToken)

	rewards, err := getWeeklyRewardsByRegion(context, region, now)
	if err != nil {
		context.WithFields(logrus.Fields{
			"region": region,
			"now":    now.Unix(),
			"err":    err,
		}).Error("getWeeklyRewardsByRegion failed")
		return nil, err
	}

	data := &models.DailyCheckInData{}
	if err := im.kv.Get(context, models.TabWaveDailyQuestCheckInData, key, data); err != nil && !errors.Is(err, kv.ErrNotFound) {
		context.WithFields(logrus.Fields{
			"table": models.TabWaveDailyQuestCheckInData,
			"k":     key,
			"err":   err,
		}).Error("kv.Get failed")
		return nil, err
	} else if errors.Is(err, kv.ErrNotFound) {
		// has not checked-in this week
		year, week := now.ISOWeek()
		initData := &models.DailyCheckInData{
			ID:               key,
			UserID:           user.ID,
			Year:             int64(year),
			Week:             int64(week),
			CheckedInHistory: make([]int64, daysOfWeek), // fill 0 to all values by default, means all days haven't been checked-in
			CheckedInDays:    0,
		}
		// give first reward
		res, err = im.reward(context, rewards[0], initData, now)
		if err != nil {
			context.WithFields(logrus.Fields{
				"userID": user.ID,
				"reward": rewards[0],
				"err":    err,
			}).Error("im.reward failed")
			return nil, err
		}
	} else {
		// has checked-in this week
		loc, err := util.GetLocationByRegion(region)
		if err != nil {
			context.WithFields(logrus.Fields{
				"region": region,
				"err":    err,
			}).Error("util.GetLocationByRegion failed")
			return nil, err
		}
		if data.HasCheckedInToday(now, loc) {
			return nil, ErrCheckInAlreadyDone
		}
		if int(data.CheckedInDays) >= len(rewards) {
			return nil, errExceedRewardLimit
		}
		res, err = im.reward(context, rewards[data.CheckedInDays], data, now)
		if err != nil {
			context.WithFields(logrus.Fields{
				"userID": user.ID,
				"reward": rewards[data.CheckedInDays],
				"err":    err,
			}).Error("im.reward failed")
			return nil, err
		}
	}
	return res, nil
}

func (im *impl) GetMaxConsecutiveDays(context ctx.CTX, user *models.User) (int, error) {
	return 0, nil
}

func (im *impl) SetTimeMachine(context ctx.CTX, userID string, t time.Time) error {
	if env.Namespace() == env.PROD {
		return ErrNotSupportTimeMachine
	}
	val := []byte(strconv.Itoa(int(t.Unix())))
	if err := im.cache.Set(context, getTimeMachineRedisKey(userID), val, timeMachineTTL); err != nil {
		context.WithFields(logrus.Fields{
			"key": getTimeMachineRedisKey(userID),
			"val": val,
			"ttl": timeMachineTTL,
			"err": err,
		}).Error("cache.Set failed")
		return err
	}
	return nil
}

func (im *impl) ResetTimeMachine(context ctx.CTX, userID string) error {
	if env.Namespace() == env.PROD {
		return ErrNotSupportTimeMachine
	}
	if _, err := im.cache.Del(context, getTimeMachineRedisKey(userID)); err != nil {
		context.WithFields(logrus.Fields{
			"key": getTimeMachineRedisKey(userID),
			"err": err,
		}).Error("cache.Del failed")
		return err
	}
	return nil
}

func (im *impl) reward(context ctx.CTX, reward dailyRewardDetail, currentData *models.DailyCheckInData, checkInTime time.Time) (*models.DailyCheckInRes, error) {
	checkInDays := currentData.CheckedInDays + 1
	res := &models.DailyCheckInRes{
		CheckedInDone:     checkInDays == daysOfWeek,
		RewardType:        reward.Type,
		RewardIconURL:     reward.RewardIconURL,
		RewardDescription: reward.RewardDescription,
	}
	now := timeNow()
	if err := reward.Validate(); err != nil {
		context.WithFields(logrus.Fields{
			"reward": reward,
			"err":    err,
		}).Error("reward.Validate failed")
		return nil, err
	}

	// copy checked-in history and replace new value
	newHistory := make([]int64, len(currentData.CheckedInHistory))
	copy(newHistory, currentData.CheckedInHistory)
	newHistory[models.GetIthDay(models.DailyQuestStartOfWeek, checkInTime.Weekday())] = checkInTime.Unix()

	// update kv
	newData := &models.DailyCheckInData{
		ID:               currentData.ID,
		UserID:           currentData.UserID,
		Year:             currentData.Year,
		Week:             currentData.Week,
		CheckedInHistory: newHistory,
		CheckedInDays:    checkInDays,
	}
	if err := im.kv.Set(context, models.TabWaveDailyQuestCheckInData, currentData.ID, newData); err != nil {
		context.WithFields(logrus.Fields{
			"table": models.TabWaveDailyQuestCheckInData,
			"k":     currentData.ID,
			"err":   err,
		}).Error("kv.Set failed")
		return nil, err
	}
	// dispatch reward
	userID := newData.UserID
	day := newData.CheckedInDays
	rewardSender, err := im.factory.GetRewardSender(reward.Type)
	if err != nil {
		context.WithFields(logrus.Fields{
			"rewardType": reward.Type,
			"err":        err,
		}).Error("factory.GetRewardSender failed")
		return nil, err
	}
	param := sendParam{
		reward:          reward,
		day:             day,
		now:             now,
		checkInTime:     checkInTime,
		dailyCheckInRes: *res,
	}
	sendRes, err := rewardSender.Send(context, userID, param)
	if err != nil {
		context.WithFields(logrus.Fields{
			"userID": userID,
			"param":  param,
			"err":    err,
		}).Error("rewardSender.Send failed")
		return nil, err
	}
	return &sendRes.dailyCheckInRes, nil
}

// toDailyCheckInRewards if checkedInDays = 2 means rewards has been received 2 days,
// the method will set Received to being true depending on checkedInDays
func (im *impl) toDailyCheckInRewards(context ctx.CTX, d dailyRewardDetails, checkedInDays int64) ([]models.DailyCheckInReward, error) {
	var ret []models.DailyCheckInReward
	for i, detail := range d {
		tmpIconURL := detail.IconURL
		tmpDesc := detail.Description
		if detail.Type == models.DailyQuestRewardTypeGiftRandomCapsule ||
			detail.Type == models.DailyQuestRewardTypeGift {
			g, err := im.giftHelper.GetGiftInfo(context, detail.GiftID)
			if err != nil {
				context.WithFields(logrus.Fields{
					"giftID": detail.GiftID,
					"err":    err,
				}).Error("giftHelper.GetGiftInf failed")
				return nil, err
			}
			tmpIconURL = g.IconURL
			tmpDesc = g.I18nKey
		}
		ret = append(ret, models.DailyCheckInReward{
			IconURL:           tmpIconURL,
			Description:       tmpDesc,
			RewardIconURL:     detail.RewardIconURL,
			RewardDescription: detail.RewardDescription,
			Received:          i < int(checkedInDays),
		})
	}
	return ret, nil
}

func GetMigrationVersion() int {
	return migrationVersion
}

func GetInsertDailyQuestSQL() string {
	return insertDailyQuestSQL
}

func New(
	db *sql.DB, kv kv.KV, cache redis.Service,
	persist redis.Service, bank money.Bank,
	decoCenter decocenter.Store, giftHelper giftHelper.Worker,
	baggageHelper baggageHelper.Helper, user user.Store,
) Store {
	dbx := sqlx.NewDb(db, mdb.SQLDriver)

	im := &impl{
		dbx:        dbx,
		kv:         kv,
		cache:      cache,
		persist:    persist,
		giftHelper: giftHelper,
		factory:    newFactory(bank, decoCenter, dbx, giftHelper, baggageHelper, user),
	}

	return im
}
