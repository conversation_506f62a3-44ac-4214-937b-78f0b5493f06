package observer

import (
	queueModel "github.com/17media/api/app/wave/models/queue"
	"github.com/17media/api/app/wave/service/queue"
	"github.com/17media/api/app/wave/stores/missionv2"
	"github.com/17media/api/app/wave/stores/user"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/dig"
)

func init() {
	Register(dimanager.DefaultManager)
}

type publisher struct {
	DoneEvent queue.Publisher
}

func initPublishers() *publisher {
	return &publisher{
		DoneEvent: queue.NewPubsubPublisher(queueModel.SubTopics[queueModel.KeyMissionDoneEvent].Topic),
	}
}

func Register(m *dimanager.Manager) {
	type params struct {
		dig.In

		User      user.Store      `name:"waveUser"`
		MissionV2 missionv2.Store `name:"waveMissionV2"`
	}
	fn := func(p params) Store {
		return New(p.User, p.MissionV2, initPublishers())
	}
	m.ProvideConstructor(fn, `waveMissionV2EventObserver`)
}

func Get(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"waveMissionV2EventObserver"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
