package missionv2

import (
	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/base/ctx"
)

type Store interface {
	// Gets returns all missions for the user
	Gets(context ctx.CTX, user *models.User) (models.UserMissionsV2Rel, error)
	// Do increases the count of the mission and returns true if the mission is done
	// each mission will be done only once, if <PERSON> is called again, it will return false
	Do(context ctx.CTX, user *models.User, missionID string, count int64, timeMS int64) (done bool, err error)
}
