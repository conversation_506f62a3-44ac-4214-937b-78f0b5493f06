// Package appstore implement Verifier interface for Apple App Store
package appstore

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/17media/logrus"
	"github.com/dogenzaka/go-iap/appstore"

	"github.com/17media/api/app/wave/models"
	payVerifier "github.com/17media/api/app/wave/stores/purchase/pay/verifier"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/metrics"
)

const (
	maxVerifyRetryCnt = 5
	bundleID          = "com.wave.waveradio"
)

var (
	met       = metrics.New("appstore")
	passwords = map[models.PayMethod]string{
		models.PayMethodIOS: "e001355b306b4ef3b97f41bc9dd43fec",
	}
)

func New() map[models.PayMethod]payVerifier.IAPVerifier {
	ret := map[models.PayMethod]payVerifier.IAPVerifier{}

	for payMethod, pwd := range passwords {
		ret[payMethod] = &verifier{
			client: appstore.NewWithConfig(appstore.Config{
				IsProduction: true,
				TimeOut:      time.Second * 5,
			}),
			sandboxClient: appstore.NewWithConfig(appstore.Config{
				IsProduction: false,
				TimeOut:      time.Second * 60,
			}),
			password:  pwd,
			payMethod: payMethod,
		}
	}

	return ret
}

type verifier struct {
	client        appstore.Client
	sandboxClient appstore.Client
	password      string
	payMethod     models.PayMethod
}

// verify wraps SDK's `Verify` with retry and error handle
func (v *verifier) verify(context ctx.CTX, sandbox bool, payload string) (*models.IAPResponse, error) {
	const (
		maxRetryProdCnt    = 5
		maxRetrySandboxCnt = 5
	)
	client := v.client
	maxRetryCnt := maxRetryProdCnt
	env := "prod"
	if sandbox {
		client = v.sandboxClient
		maxRetryCnt = maxRetrySandboxCnt
		env = "sandbox"
	}
	req := appstore.IAPRequest{
		ReceiptData: payload,
		Password:    v.password,
	}
	resp := &models.IAPResponse{}
	for i := 0; i < maxRetryCnt; i++ {
		if err := client.Verify(req, resp); err != nil {
			context.WithFields(logrus.Fields{
				"err":      err,
				"retryCnt": i,
				"sandbox":  sandbox,
			}).Error("decode json from appstore response")
			if i == maxRetryCnt-1 {
				// the lib expect not return nil response even in error case
				return resp, err
			}
		} else {
			met.BumpSum("appstore.client.verify.try.cnt", 1, "env", env, "retryCnt", strconv.Itoa(i))
			context.WithFields(logrus.Fields{
				"retryCnt": i,
				"sandbox":  sandbox,
			}).Info("client.Verify with appstore success")
			break
		}
	}
	err := appstore.HandleError(resp.Status)
	if err == nil {
		if !sandbox && resp.Receipt.BundleID != bundleID {
			return resp, errors.New("The bundle id of the receipt doesn't match")
		}
	}
	return resp, err
}

func (v *verifier) Verify(context ctx.CTX, payload string) ([]models.RawReceipt, error) {
	var err error
	sandbox := false
	env := "prod"
	resp := &models.IAPResponse{}

	// verify with retry
	for i := 0; i < maxVerifyRetryCnt; i++ {
		resp, err = v.verify(context, sandbox, payload)

		// Check returned status code
		if resp.Status == 21007 {
			sandbox = true
			env = "sandbox"
			resp, err = v.verify(context, sandbox, payload)
		}

		// Check if error occurs
		if err != nil {
			context.WithFields(logrus.Fields{
				"err":     err,
				"sandbox": sandbox,
			}).Error("appstore verify failed")
		} else {
			met.BumpSum("appstore.verify.try.cnt", 1, "env", env, "retryCnt", strconv.Itoa(i))
			context.WithFields(logrus.Fields{
				"retryCnt": i,
				"sandbox":  sandbox,
			}).Info("appstore verify success")
			break
		}
	}
	if err != nil {
		context.WithField("err", err).Error("abort to verify payload")
		return nil, err
	}

	// Parse this receipt in this payload
	transactionIDMap := map[string]bool{}
	rs := []models.RawReceipt{}
	appleReceipts := resp.Receipt.InApp

	// For receipts containing auto-renewable subscriptions,
	// check the value of the latest_receipt_info key to get the status of the most recent renewal.
	// [Ref] https://developer.apple.com/library/content/releasenotes/General/ValidateAppStoreReceipt/Chapters/ValidateRemotely.html
	// https://developer.apple.com/library/content/releasenotes/General/ValidateAppStoreReceipt/Chapters/ReceiptFields.html
	appleReceipts = append(appleReceipts, resp.LatestReceiptInfo...)
	AppAccountTokenMap := map[string]string{}
	for _, latestReceipt := range resp.LatestReceiptInfo {
		AppAccountTokenMap[latestReceipt.TransactionID] = latestReceipt.AppAccountToken
	}
	// NOTE: Sometimes app may sends receipts with empty in_app field,
	// We guess that maybe the transaction is still in processing.
	// So we return an error here and let app retry until the receipt contains something
	if len(appleReceipts) == 0 {
		context.WithField("err", err).Error("empty receipt")
		return nil, fmt.Errorf("empty receipt")
	}

	for _, appleReceipt := range appleReceipts {
		if _, exist := transactionIDMap[appleReceipt.TransactionID]; exist {
			continue
		}

		q, _ := strconv.Atoi(appleReceipt.Quantity)
		pT := time.Time{}
		eT := time.Time{}
		if pt, _ := strconv.ParseInt(appleReceipt.PurchaseDate.PurchaseDateMS, 10, 64); pt > 0 {
			pT = time.Unix(0, pt*int64(time.Millisecond/time.Nanosecond))
		}
		if et, _ := strconv.ParseInt(appleReceipt.ExpiresDate.ExpiresDateMS, 10, 64); et > 0 {
			eT = time.Unix(0, et*int64(time.Millisecond/time.Nanosecond))
		}
		subscribe := true
		if appleReceipt.WebOrderLineItemID == "" {
			subscribe = false
		}

		rs = append(rs, models.RawReceipt{
			AcquiringByWave:   false,
			Sandbox:           sandbox,
			PayMethod:         v.payMethod,
			ID:                appleReceipt.TransactionID,
			Quantity:          q,
			ProductID:         appleReceipt.ProductID,
			OriginalReceiptID: appleReceipt.OriginalTransactionID,
			Subscribe:         subscribe,
			PurchasedTime:     pT,
			ExpiredTime:       eT,
			UserID:            AppAccountTokenMap[appleReceipt.TransactionID],
		})
		transactionIDMap[appleReceipt.TransactionID] = true
	}
	return rs, nil
}

func (v *verifier) SupportSandboxOnProd() bool {
	return true
}
