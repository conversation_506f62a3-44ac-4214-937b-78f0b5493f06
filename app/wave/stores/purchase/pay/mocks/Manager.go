// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	mock "github.com/stretchr/testify/mock"

	models "github.com/17media/api/app/wave/models"

	pay "github.com/17media/api/app/wave/stores/purchase/pay"

	sqlx "github.com/jmoiron/sqlx"
)

// Manager is an autogenerated mock type for the Manager type
type Manager struct {
	mock.Mock
}

// AbandonPayments provides a mock function with given fields: context, tx, execUserID, paymentIDs, reason, timeMs
func (_m *Manager) AbandonPayments(context ctx.CTX, tx *sqlx.Tx, execUserID string, paymentIDs []string, reason string, timeMs int64) error {
	ret := _m.Called(context, tx, execUserID, paymentIDs, reason, timeMs)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string, []string, string, int64) error); ok {
		r0 = rf(context, tx, execUserID, paymentIDs, reason, timeMs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CheckIAPLimit provides a mock function with given fields: context, userID, startedAtMs, endedAtMs, conditionType, limit
func (_m *Manager) CheckIAPLimit(context ctx.CTX, userID string, startedAtMs int64, endedAtMs int64, conditionType models.IAPLimitConditionType, limit int64) error {
	ret := _m.Called(context, userID, startedAtMs, endedAtMs, conditionType, limit)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, int64, int64, models.IAPLimitConditionType, int64) error); ok {
		r0 = rf(context, userID, startedAtMs, endedAtMs, conditionType, limit)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CheckReceipt provides a mock function with given fields: context, receiptID, payMethod
func (_m *Manager) CheckReceipt(context ctx.CTX, receiptID string, payMethod models.PayMethod) error {
	ret := _m.Called(context, receiptID, payMethod)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, models.PayMethod) error); ok {
		r0 = rf(context, receiptID, payMethod)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateMakeUpReceipt provides a mock function with given fields: context, tx, payMethod, receiptID, execUserID, userID, merchandiseID, value, timeMs
func (_m *Manager) CreateMakeUpReceipt(context ctx.CTX, tx *sqlx.Tx, payMethod models.PayMethod, receiptID string, execUserID string, userID string, merchandiseID string, value models.PaymentValue, timeMs int64) (*models.Receipt, error) {
	ret := _m.Called(context, tx, payMethod, receiptID, execUserID, userID, merchandiseID, value, timeMs)

	var r0 *models.Receipt
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, models.PayMethod, string, string, string, string, models.PaymentValue, int64) *models.Receipt); ok {
		r0 = rf(context, tx, payMethod, receiptID, execUserID, userID, merchandiseID, value, timeMs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Receipt)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, models.PayMethod, string, string, string, string, models.PaymentValue, int64) error); ok {
		r1 = rf(context, tx, payMethod, receiptID, execUserID, userID, merchandiseID, value, timeMs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreatePayments provides a mock function with given fields: context, tx, payMethod, execUserID, userID, values, orderID, paycode, remark, timeMs, optFuncs
func (_m *Manager) CreatePayments(context ctx.CTX, tx *sqlx.Tx, payMethod models.PayMethod, execUserID string, userID string, values []models.PaymentValue, orderID string, paycode string, remark string, timeMs int64, optFuncs ...pay.OptFunc) ([]*models.Payment, error) {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, tx, payMethod, execUserID, userID, values, orderID, paycode, remark, timeMs)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*models.Payment
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, models.PayMethod, string, string, []models.PaymentValue, string, string, string, int64, ...pay.OptFunc) []*models.Payment); ok {
		r0 = rf(context, tx, payMethod, execUserID, userID, values, orderID, paycode, remark, timeMs, optFuncs...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Payment)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, models.PayMethod, string, string, []models.PaymentValue, string, string, string, int64, ...pay.OptFunc) error); ok {
		r1 = rf(context, tx, payMethod, execUserID, userID, values, orderID, paycode, remark, timeMs, optFuncs...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreatePreReceipt provides a mock function with given fields: context, tx, payMethod, user, merchandiseID, sellingChannel, returnURL, clientBackURL, timeMs, vendorAttrs, options
func (_m *Manager) CreatePreReceipt(context ctx.CTX, tx *sqlx.Tx, payMethod models.PayMethod, user *models.User, merchandiseID string, sellingChannel models.SellingChannel, returnURL string, clientBackURL string, timeMs int64, vendorAttrs map[string]string, options ...pay.CreatePreReceiptOption) (string, string, error) {
	_va := make([]interface{}, len(options))
	for _i := range options {
		_va[_i] = options[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, tx, payMethod, user, merchandiseID, sellingChannel, returnURL, clientBackURL, timeMs, vendorAttrs)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 string
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, models.PayMethod, *models.User, string, models.SellingChannel, string, string, int64, map[string]string, ...pay.CreatePreReceiptOption) string); ok {
		r0 = rf(context, tx, payMethod, user, merchandiseID, sellingChannel, returnURL, clientBackURL, timeMs, vendorAttrs, options...)
	} else {
		r0 = ret.Get(0).(string)
	}

	var r1 string
	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, models.PayMethod, *models.User, string, models.SellingChannel, string, string, int64, map[string]string, ...pay.CreatePreReceiptOption) string); ok {
		r1 = rf(context, tx, payMethod, user, merchandiseID, sellingChannel, returnURL, clientBackURL, timeMs, vendorAttrs, options...)
	} else {
		r1 = ret.Get(1).(string)
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, *sqlx.Tx, models.PayMethod, *models.User, string, models.SellingChannel, string, string, int64, map[string]string, ...pay.CreatePreReceiptOption) error); ok {
		r2 = rf(context, tx, payMethod, user, merchandiseID, sellingChannel, returnURL, clientBackURL, timeMs, vendorAttrs, options...)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GetOrderUserID provides a mock function with given fields: context, orderID
func (_m *Manager) GetOrderUserID(context ctx.CTX, orderID string) (string, error) {
	ret := _m.Called(context, orderID)

	var r0 string
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) string); ok {
		r0 = rf(context, orderID)
	} else {
		r0 = ret.Get(0).(string)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPayment provides a mock function with given fields: context, tx, paymentID
func (_m *Manager) GetPayment(context ctx.CTX, tx *sqlx.Tx, paymentID string) (*models.Payment, error) {
	ret := _m.Called(context, tx, paymentID)

	var r0 *models.Payment
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string) *models.Payment); ok {
		r0 = rf(context, tx, paymentID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Payment)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, string) error); ok {
		r1 = rf(context, tx, paymentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPaymentID provides a mock function with given fields: context, tx, displayPaymentID
func (_m *Manager) GetPaymentID(context ctx.CTX, tx *sqlx.Tx, displayPaymentID int64) (string, error) {
	ret := _m.Called(context, tx, displayPaymentID)

	var r0 string
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, int64) string); ok {
		r0 = rf(context, tx, displayPaymentID)
	} else {
		r0 = ret.Get(0).(string)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, int64) error); ok {
		r1 = rf(context, tx, displayPaymentID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPaymentsByOrderID provides a mock function with given fields: context, tx, orderID
func (_m *Manager) GetPaymentsByOrderID(context ctx.CTX, tx *sqlx.Tx, orderID string) ([]*models.Payment, error) {
	ret := _m.Called(context, tx, orderID)

	var r0 []*models.Payment
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string) []*models.Payment); ok {
		r0 = rf(context, tx, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Payment)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, string) error); ok {
		r1 = rf(context, tx, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPaymentsByReceiptID provides a mock function with given fields: context, tx, receiptID, payMethod
func (_m *Manager) GetPaymentsByReceiptID(context ctx.CTX, tx *sqlx.Tx, receiptID string, payMethod models.PayMethod) ([]*models.Payment, error) {
	ret := _m.Called(context, tx, receiptID, payMethod)

	var r0 []*models.Payment
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string, models.PayMethod) []*models.Payment); ok {
		r0 = rf(context, tx, receiptID, payMethod)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Payment)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, string, models.PayMethod) error); ok {
		r1 = rf(context, tx, receiptID, payMethod)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPreReceiptInfo provides a mock function with given fields: context, preReceiptID, optFuncs
func (_m *Manager) GetPreReceiptInfo(context ctx.CTX, preReceiptID string, optFuncs ...pay.OptFunc) (*models.PreReceiptInfo, error) {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, preReceiptID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *models.PreReceiptInfo
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, ...pay.OptFunc) *models.PreReceiptInfo); ok {
		r0 = rf(context, preReceiptID, optFuncs...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PreReceiptInfo)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, ...pay.OptFunc) error); ok {
		r1 = rf(context, preReceiptID, optFuncs...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPreReceiptMetadata provides a mock function with given fields: context, preReceiptID
func (_m *Manager) GetPreReceiptMetadata(context ctx.CTX, preReceiptID string) (map[string]string, error) {
	ret := _m.Called(context, preReceiptID)

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) map[string]string); ok {
		r0 = rf(context, preReceiptID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, preReceiptID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetReceiptByIDTx provides a mock function with given fields: context, tx, receiptID, payMethod
func (_m *Manager) GetReceiptByIDTx(context ctx.CTX, tx *sqlx.Tx, receiptID string, payMethod models.PayMethod) (*models.Receipt, error) {
	ret := _m.Called(context, tx, receiptID, payMethod)

	var r0 *models.Receipt
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string, models.PayMethod) *models.Receipt); ok {
		r0 = rf(context, tx, receiptID, payMethod)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Receipt)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, string, models.PayMethod) error); ok {
		r1 = rf(context, tx, receiptID, payMethod)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HandleIAPNotifPayload provides a mock function with given fields: context, payload, payMethod
func (_m *Manager) HandleIAPNotifPayload(context ctx.CTX, payload string, payMethod models.PayMethod) (*models.ServerNotification, error) {
	ret := _m.Called(context, payload, payMethod)

	var r0 *models.ServerNotification
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, models.PayMethod) *models.ServerNotification); ok {
		r0 = rf(context, payload, payMethod)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.ServerNotification)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, models.PayMethod) error); ok {
		r1 = rf(context, payload, payMethod)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RefundReceipt provides a mock function with given fields: context, tx, execUserID, receiptIDs, payMethod, timeMs
func (_m *Manager) RefundReceipt(context ctx.CTX, tx *sqlx.Tx, execUserID string, receiptIDs []string, payMethod models.PayMethod, timeMs int64) error {
	ret := _m.Called(context, tx, execUserID, receiptIDs, payMethod, timeMs)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string, []string, models.PayMethod, int64) error); ok {
		r0 = rf(context, tx, execUserID, receiptIDs, payMethod, timeMs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdatePreReceiptPayload provides a mock function with given fields: context, tx, preReceiptID, payload, timeMs
func (_m *Manager) UpdatePreReceiptPayload(context ctx.CTX, tx *sqlx.Tx, preReceiptID string, payload string, timeMs int64) error {
	ret := _m.Called(context, tx, preReceiptID, payload, timeMs)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string, string, int64) error); ok {
		r0 = rf(context, tx, preReceiptID, payload, timeMs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpsertReceipt provides a mock function with given fields: context, isMakeup, timeMs, receipt
func (_m *Manager) UpsertReceipt(context ctx.CTX, isMakeup bool, timeMs int64, receipt *models.Receipt) error {
	ret := _m.Called(context, isMakeup, timeMs, receipt)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, bool, int64, *models.Receipt) error); ok {
		r0 = rf(context, isMakeup, timeMs, receipt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// VerifyIAPPayload provides a mock function with given fields: context, payload, payMethod, isMakeup, timeMs, requestUserID
func (_m *Manager) VerifyIAPPayload(context ctx.CTX, payload string, payMethod models.PayMethod, isMakeup bool, timeMs int64, requestUserID string) ([]*models.Receipt, []models.SellingChannel, error) {
	ret := _m.Called(context, payload, payMethod, isMakeup, timeMs, requestUserID)

	var r0 []*models.Receipt
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, models.PayMethod, bool, int64, string) []*models.Receipt); ok {
		r0 = rf(context, payload, payMethod, isMakeup, timeMs, requestUserID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*models.Receipt)
		}
	}

	var r1 []models.SellingChannel
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, models.PayMethod, bool, int64, string) []models.SellingChannel); ok {
		r1 = rf(context, payload, payMethod, isMakeup, timeMs, requestUserID)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]models.SellingChannel)
		}
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, string, models.PayMethod, bool, int64, string) error); ok {
		r2 = rf(context, payload, payMethod, isMakeup, timeMs, requestUserID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// VerifyNotifPayload provides a mock function with given fields: context, payload, payMethod, isMakeup, timeMs, requestUserID
func (_m *Manager) VerifyNotifPayload(context ctx.CTX, payload string, payMethod models.PayMethod, isMakeup bool, timeMs int64, requestUserID string) (*models.Receipt, models.SellingChannel, error) {
	ret := _m.Called(context, payload, payMethod, isMakeup, timeMs, requestUserID)

	var r0 *models.Receipt
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, models.PayMethod, bool, int64, string) *models.Receipt); ok {
		r0 = rf(context, payload, payMethod, isMakeup, timeMs, requestUserID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.Receipt)
		}
	}

	var r1 models.SellingChannel
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, models.PayMethod, bool, int64, string) models.SellingChannel); ok {
		r1 = rf(context, payload, payMethod, isMakeup, timeMs, requestUserID)
	} else {
		r1 = ret.Get(1).(models.SellingChannel)
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, string, models.PayMethod, bool, int64, string) error); ok {
		r2 = rf(context, payload, payMethod, isMakeup, timeMs, requestUserID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}
