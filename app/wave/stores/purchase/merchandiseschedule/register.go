package merchandiseschedule

import (
	"github.com/17media/api/app/wave/stores/bonusschedule"
	"github.com/17media/api/app/wave/stores/purchase/merchandise"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/dig"
)

func init() {
	Register(dimanager.DefaultManager)
}

func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		MerchandiseManager merchandise.Manager `name:"waveMerchandiseManager"`
		BonusSchedule      bonusschedule.Store `name:"waveBonusSchedule"`
	}
	fn := func(p params) Store {
		return New(p.MerchandiseManager, p.BonusSchedule)
	}
	m.ProvideConstructor(fn, `waveMerchandiseSchedule`)
}

func Get(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"waveMerchandiseSchedule"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
