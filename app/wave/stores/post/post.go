package post

import (
	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/base/ctx"
)

const (
	// DefaultNotificationCount is the default post notification count
	DefaultNotificationCount = 20
)

var (
	// ErrNotPostAuthor represents the error that an user tries to delete a post that not belong to him/her
	ErrNotPostAuthor = models.BadRequestErr{Code: "NOT_POST_AUTHOR"}
	// ErrPostNotFound represents the error that post not found
	ErrPostNotFound = models.NotFoundErr{Code: "POST_NOT_FOUND"}
	// ErrDirtyWord represents the error that content contains dirty word
	ErrDirtyWord = models.BadRequestErr{Code: "DIRTY_WORD_DETECTED"}
	// ErrImageInvalidExt represents the error that img has invalid ext
	ErrImageInvalidExt = models.BadRequestErr{Code: "POST_IMAGE_INVALID_EXT"}
)

// OptionFunc alters post List optional args
type OptionFunc func(*Option) error

// Option defines filters for post List
type Option struct {
	authorID *string
	region   *string
}

// WithAuthorID searches post for specific author
func WithAuthorID(authorID string) OptionFunc {
	return func(opt *Option) error {
		opt.authorID = &authorID
		return nil
	}
}

// WithRegion searches post for specific region
func WithRegion(r string) OptionFunc {
	return func(opt *Option) error {
		opt.region = &r
		return nil
	}
}

type pinnedPost struct {
	PostID  string `json:"post_id"`
	EndedAt int64  `json:"ended_at"`
}

// Store represents the post store
type Store interface {
	// List returns latest posts
	List(context ctx.CTX, user *models.User, cursor string, count int, opt ...OptionFunc) ([]*models.Post, string, error)

	// ListTrending returns the trending posts
	ListTrending(context ctx.CTX, user *models.User, cursor string, count int) ([]*models.Post, string, error)

	// ListFollowed returns the post posted by user's followings
	ListFollowed(context ctx.CTX, user *models.User, cursor string, count int) ([]*models.Post, string, error)

	// Get returns the post
	Get(context ctx.CTX, user *models.User, postID string) (*models.Post, error)

	// GetPlainPost returns the post without request user
	GetPlainPost(context ctx.CTX, postID string) (*models.Post, error)

	// Create creates a post in forum
	Create(context ctx.CTX, user *models.User, param models.PostParams) (*models.Post, error)

	// Patch patches a post in forum
	Patch(context ctx.CTX, id string, param models.PatchPostParams) (*models.Post, error)

	Incr(context ctx.CTX, id string, param models.IncrPostParams) (*models.Post, error)

	// Delete deletes the post and only author is able to delete it
	Delete(context ctx.CTX, user *models.User, postID string) error

	// ListComments returns the comments in the post
	ListComments(context ctx.CTX, user *models.User, postID, cursor string, count int) ([]*models.PostComment, string, error)

	// CreateComment creates a comment in the post
	CreateComment(context ctx.CTX, user *models.User, postID string, param models.PostCommentParams) (*models.PostComment, error)

	// ListNotifications list post notifications
	ListNotifications(context ctx.CTX, user *models.User, cursor string, count int) ([]*models.PostNotification, string, error)

	// HasNotificationBadge check that has notification unread or not
	HasNotificationBadge(context ctx.CTX, userID string, badge models.PostNotificationBadge) (bool, error)

	// AddNotificationBadges set notifcation badges of the user and return the count on app notification badge
	AddNotificationBadges(context ctx.CTX, userID string) int64

	// DeleteNotificationBadge delete the notification badge
	DeleteNotificationBadge(context ctx.CTX, userID string, badge models.PostNotificationBadge)

	// Pin a post by id
	Pin(context ctx.CTX, postID string, endedAt int64) error

	// Unpin a post by id
	Unpin(context ctx.CTX, postID string) error

	// Like a post by id
	Like(context ctx.CTX, user *models.User, postID string) error

	// Unlike a post by id
	Unlike(context ctx.CTX, user *models.User, postID string) error
}
