package notification

import (
	"time"

	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/service/notifier"
)

var (
	ErrNotificaionNotFound = models.NotFoundErr{Code: "NOTIFICATION_NOT_FOUND"}
	ErrInvalidTimeRange    = models.Err{Code: "INVALID_TIME_RANGE"}
)

// Store is the notification interface
type Store interface {
	// InsertRecords insert notification records into bigquery
	InsertRecords(context ctx.CTX, records []*models.BQOneSignalWriterItem) error

	// GetByID get notification by ID
	GetByID(context ctx.CTX, notificationID string) (*models.NotificationResult, error)

	// GetByTime get notification by time range
	GetByTime(context ctx.CTX, user *models.User, start, end time.Time) ([]*models.NotificationResult, error)

	// GetBySrc get notification by source ID and type
	GetBySrc(context ctx.CTX, srcType models.NotifierType, srcID string) (*models.NotificationResult, error)

	// SendToFollowers:
	//   step 1: SendToFollowers
	//     -> async call to avoid high latency
	//   step 2: SendToFollowersHelper
	//     -> Break down the follower into offsets and then pass them to the subsequent workers for execution.
	//   step 3: SendToFollowersByOffset
	//     -> Send notif by specify offset
	// SendToFollowers send notification to followers
	SendToFollowers(context ctx.CTX, streamer *models.User, msgType models.OneSignalLiveMsgType, message notifier.Message) error
	SendToFollowersHelper(context ctx.CTX, data []byte, t time.Time) error
	SendToFollowersByOffset(context ctx.CTX, data []byte, t time.Time) error
}
