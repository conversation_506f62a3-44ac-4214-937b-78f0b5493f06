package notification

import (
	"fmt"
	"time"

	"cloud.google.com/go/bigquery"
	"github.com/17media/logrus"
	"google.golang.org/api/iterator"

	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/app/wave/stores/user"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/metrics"
	onesignal "github.com/17media/api/service/notifier"
	"github.com/17media/api/service/queryv2"
)

const (
	batchSize = 1000

	defaultDataset = "notification"
	defaultTable   = "onesignal_events"

	getRecordByNotifIDSQL = `
SELECT notification_id, receiver_user_id, status, type, sub_type, src_id, reason, received_at, additional_data
FROM %s.%s
WHERE notification_id=@notification_id
LIMIT 1
`
	getRecordByTimeSQL = `
SELECT notification_id, receiver_user_id, status, type, sub_type, src_id, reason, received_at, additional_data
FROM %s.%s
WHERE received_at BETWEEN @start_time AND @end_time
`
	getRecordBySrcSQL = `
SELECT notification_id, receiver_user_id, status, type, sub_type, src_id, reason, received_at, additional_data
FROM %s.%s
WHERE type=@type
AND src_id=@src_id
LIMIT 1
`

	fieldNotificationID = "notification_id"
	fieldNotifierType   = "type"
	fieldReceivedAt     = "received_at"
	fieldReason         = "reason"
	fieldSrcID          = "src_id"
	fieldReceiverUserID = "receiver_user_id"

	paramStartTime = "start_time"
	paramEndTime   = "end_time"

	validTimeRange = 7 * 86400

	// because there're 99 or entity + 100 users entity= total 199 entity, max of block size equal 100.
	blockSize = 100
)

var (
	timeNow = time.Now

	sendToBQ  = writeToBigQuery
	getFromBQ = readFromBigQuery

	met    = metrics.New("notification")
	ocSend = onesignal.SendOneSignalByFilters
)

type impl struct {
	bqWriter *bigquery.Client
	bqReader *bigquery.Client

	publisher *publisher
	user      user.Store
	queryv2   queryv2.Mongo
}

// New Creates store implementation
func New(
	bqWriter *bigquery.Client,
	bqReader *bigquery.Client,
	publisher *publisher,
	queryv2 queryv2.Mongo,
	userStore user.Store,
) Store {
	im := &impl{
		bqWriter:  bqWriter,
		bqReader:  bqReader,
		publisher: publisher,
		queryv2:   queryv2,
		user:      userStore,
	}

	return im
}

func (im *impl) InsertRecords(context ctx.CTX, records []*models.BQOneSignalWriterItem) error {
	return sendToBQ(context, im.bqWriter, records)
}

func (im *impl) GetByID(context ctx.CTX, notificationID string) (*models.NotificationResult, error) {
	sql := fmt.Sprintf(getRecordByNotifIDSQL, defaultDataset, defaultTable)
	params := []bigquery.QueryParameter{
		{Name: fieldNotificationID, Value: notificationID},
	}

	items, err := getFromBQ(context, im.bqReader, sql, params)
	if err != nil {
		context.WithField("err", err).Error("getFromBQ failed")
		return nil, err
	}

	// 1 because BigQuery will return null value default
	if len(items) <= 1 {
		return nil, ErrNotificaionNotFound
	}

	return im.setDataToResult(context, items[0]), nil
}

func (im *impl) GetByTime(context ctx.CTX, user *models.User, start, end time.Time) ([]*models.NotificationResult, error) {
	if start.Unix() > end.Unix() || end.Unix()-start.Unix() > validTimeRange {
		return nil, ErrInvalidTimeRange
	}

	sql := fmt.Sprintf(getRecordByTimeSQL, defaultDataset, defaultTable)
	params := []bigquery.QueryParameter{
		{Name: paramStartTime, Value: start},
		{Name: paramEndTime, Value: end},
	}

	if user != nil {
		sql += fmt.Sprintf(" AND receiver_user_id=@receiver_user_id")
		params = append(params, bigquery.QueryParameter{Name: fieldReceiverUserID, Value: user.ID})
	}

	sql += " ORDER BY received_at DESC"

	items, err := getFromBQ(context, im.bqReader, sql, params)
	if err != nil {
		context.WithField("err", err).Error("getFromBQ failed")
		return nil, err
	}

	// 1 because BigQuery will return null value default
	if len(items) <= 1 {
		return nil, ErrNotificaionNotFound
	}

	res := []*models.NotificationResult{}
	for i := range items {
		// don't return last default value
		if i == len(items)-1 {
			break
		}

		res = append(res, im.setDataToResult(context, items[i]))
	}

	return res, nil
}

func (im *impl) GetBySrc(context ctx.CTX, srcType models.NotifierType, srcID string) (*models.NotificationResult, error) {
	sql := fmt.Sprintf(getRecordBySrcSQL, defaultDataset, defaultTable)
	params := []bigquery.QueryParameter{
		{Name: fieldNotifierType, Value: srcType},
		{Name: fieldSrcID, Value: srcID},
	}

	items, err := getFromBQ(context, im.bqReader, sql, params)
	if err != nil {
		context.WithField("err", err).Error("getFromBQ failed")
		return nil, err
	}

	// 1 because BigQuery will return null value default
	if len(items) <= 1 {
		return nil, ErrNotificaionNotFound
	}

	return im.setDataToResult(context, items[0]), nil
}

func (im *impl) setDataToResult(context ctx.CTX, data *models.BQOneSignalReaderItem) *models.NotificationResult {
	res := &models.NotificationResult{}

	if data.NotifierType.Valid {
		res.NotifierType = models.NotifierType(data.NotifierType.Int64)
	} else {
		context.WithFields(logrus.Fields{
			"field": fieldNotifierType,
			"data":  data.NotifierType,
		}).Errorf("%s field data is invalid", fieldNotifierType)
	}

	if data.ReceivedAt.Valid {
		res.ReceivedAt = data.ReceivedAt.Timestamp.Unix()
	} else {
		context.WithFields(logrus.Fields{
			"field": fieldReceivedAt,
			"data":  data.ReceivedAt,
		}).Errorf("%s field data is invalid", fieldReceivedAt)
	}

	if data.NotifierReason.Valid {
		res.Reason = models.NotifierReason(data.NotifierReason.Int64)
	} else {
		context.WithFields(logrus.Fields{
			"field": fieldReason,
			"data":  data.NotifierReason,
		}).Errorf("%s field data is invalid", fieldReason)
	}

	return res
}

func writeToBigQuery(context ctx.CTX, bq *bigquery.Client, records []*models.BQOneSignalWriterItem) error {
	bqInserter := bq.Dataset(defaultDataset).Table(defaultTable).Inserter()

	for i := 0; i < len(records); i = i + batchSize {
		shift := i + batchSize
		if shift > len(records) {
			shift = len(records)
		}

		if err := bqInserter.Put(context, records[i:shift]); err != nil {
			context.WithFields(logrus.Fields{
				"dataset": defaultDataset,
				"table":   defaultTable,
				"shift":   shift,
				"err":     err,
			}).Error("bqInserter.Put failed")
			return err
		}
	}

	return nil
}

func readFromBigQuery(context ctx.CTX, bq *bigquery.Client, query string, params []bigquery.QueryParameter) ([]*models.BQOneSignalReaderItem, error) {
	output := []*models.BQOneSignalReaderItem{}

	bqQuery := bq.Query(query)
	bqQuery.Parameters = params

	result, err := bqQuery.Read(context)
	if err != nil {
		context.WithField("err", err).Error("bqQuery.Read failed")
		return nil, err
	}

	for {
		tmp := models.BQOneSignalReaderItem{}
		if err := result.Next(&tmp); err == iterator.Done {
			output = append(output, &tmp)
			break
		} else if err != nil {
			context.WithField("err", err).Error("Iterator.Next failed")
			return nil, err
		}

		output = append(output, &tmp)
	}

	return output, nil
}

func (im *impl) SendToFollowers(context ctx.CTX, streamer *models.User, msgType models.OneSignalLiveMsgType, message onesignal.Message) error {
	if msgType != models.OneSignalLiveMsgTypeNewPost {
		return fmt.Errorf("not support msg type")
	}

	// NOTE if user is invisible, do not send pn
	if streamer.FollowersCount <= 0 || streamer.RoleInfo.Role == models.UserRoleInvisible {
		return nil
	}

	params := &SendToFollowersHelperJobParam{
		User:    streamer,
		Message: message,
		MsgType: msgType,
	}

	if err := im.publisher.SendToFollowersHelper.Publish(
		context,
		params,
	); err != nil {
		context.WithFields(logrus.Fields{
			"err":    err,
			"params": *params,
		}).Error("SendToFollowersHelper publish failed")
		return err
	}

	return nil
}
