package notifypreference

import (
	"github.com/17media/api/service/kv"
	"github.com/17media/api/service/queryv2"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/dig"

	// trigger init
	_ "github.com/17media/api/app/wave/service/kv"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of notify preference object ot the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		Kv    kv.KV         `name:"waveKv"`
		Mongo queryv2.Mongo `name:"queryv2WithoutFillNil"`
	}

	fn := func(p params) Store {
		return New(
			p.Kv,
			p.Mongo,
		)
	}

	m.ProvideConstructor(fn, `waveNotifyPreference`)
}

// GetNotifyPreference returns the object
func GetNotifyPreference(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"waveNotifyPreference"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}

	return output
}
