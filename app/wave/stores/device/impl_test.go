package device

import (
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/17media/api/app/wave/models"
	waveKv "github.com/17media/api/app/wave/service/kv"
	madmin "github.com/17media/api/app/wave/stores/admin/mocks"
	"github.com/17media/api/base/ctx"
	bdocker "github.com/17media/api/base/docker"
	"github.com/17media/api/service/kv"
	"github.com/17media/api/setup/dimanager"
)

var (
	mockCTX       = ctx.Background()
	mockTimestamp = int64(1573727288) // 2019/11/14 18:28:08(+08:00)

	mockAdminUser = &models.User{
		ID:    "mock-admin-id",
		Name:  "mock-admin-name",
		Admin: 1,
	}
)

type deviceSuite struct {
	suite.Suite
	deviceStore *impl
	redisPort   string
	mongoPort   string
	mockAdmin   *madmin.Store
	kv          kv.KV

	manager *dimanager.Manager
}

func (s *deviceSuite) SetupSuite() {
	localhost, ports, err := bdocker.RunExtDockers([]string{"redis", "mongo"})
	s.Require().NoError(err)
	s.redisPort = ports[0]
	s.mongoPort = ports[1]

	s.manager = dimanager.DefaultManager
	s.manager.ProvideString("redis_cache_uri", localhost+":"+ports[0])
	s.manager.ProvideString("redis_persist_uri", localhost+":"+ports[0])
	s.manager.ProvideString("mongo_uri", localhost+":"+ports[1])
	s.manager.ProvideString("mongo_db", "17media")
}

func (s *deviceSuite) TearDownSuite() {
	s.NoError(bdocker.RemExtDockers())
}

func (s *deviceSuite) SetupTest() {
	s.manager.ClearMock()
	s.mockAdmin = madmin.RegisterMock(s.manager)
	s.manager.Compile()

	s.kv = waveKv.GetKv(s.manager)
	s.deviceStore = GetDevice(s.manager).(*impl)
}

func (s *deviceSuite) TearDownTest() {
	s.mockAdmin.AssertExpectations(s.T())
	s.NoError(bdocker.ClearRedis(s.redisPort))
	s.NoError(bdocker.ClearMongo(s.mongoPort))
}

func TestDeviceSuite(t *testing.T) {
	suite.Run(t, new(deviceSuite))
}

func (s *deviceSuite) TestBan() {
	isBanned, err := s.deviceStore.IsBanned(mockCTX, "ios", "1")
	s.Require().NoError(err)
	s.Require().Equal(false, isBanned)

	s.mockAdmin.On(
		"RecordAction", mock.AnythingOfType("ctx.CTX"), mockAdminUser, models.ActionTypeBanDevice,
		"user1", "ios:1", models.ParamTypeDeviceID,
	).Return(nil).Once()
	err = s.deviceStore.Ban(mockCTX, mockAdminUser, "ios", "1", "user1", "user1name")
	s.Require().NoError(err)

	isBanned, err = s.deviceStore.IsBanned(mockCTX, "ios", "1")
	s.Require().NoError(err)
	s.Require().Equal(true, isBanned)

	s.mockAdmin.On(
		"RecordAction", mock.AnythingOfType("ctx.CTX"), mockAdminUser, models.ActionTypeUnbanDevice,
		"user1", "ios:1", models.ParamTypeDeviceID,
	).Return(nil).Once()
	err = s.deviceStore.Unban(mockCTX, mockAdminUser, "user1", "ios", "1")
	s.Require().NoError(err)

	isBanned, err = s.deviceStore.IsBanned(mockCTX, "ios", "1")
	s.Require().NoError(err)
	s.Require().Equal(false, isBanned)
}

func (s *deviceSuite) TestIsNewDevice() {
	timeNow = func() time.Time {
		return time.Unix(mockTimestamp, 0)
	}

	mockDeviceID := "mock-device-id"
	// no device id in the cache case
	isNewDevice, err := s.deviceStore.IsNewDevice(mockCTX, mockDeviceID)
	s.Require().NoError(err)
	s.Require().Equal(true, isNewDevice)

	// has device id in the cache case but CreatedAt is too early (more than 30 days ago)
	err = s.kv.Set(mockCTX, models.TabWaveDeviceRegistry, mockDeviceID, &models.DeviceRegistry{
		ID:        mockDeviceID,
		Type:      "iOS",
		CreatedAt: mockTimestamp - 31*86400,
	})
	s.Require().NoError(err)
	isNewDevice, err = s.deviceStore.IsNewDevice(mockCTX, mockDeviceID)
	s.Require().NoError(err)
	s.Require().Equal(false, isNewDevice)

	// has device id in the cache case and the device has currently been created
	err = s.kv.Set(mockCTX, models.TabWaveDeviceRegistry, mockDeviceID, &models.DeviceRegistry{
		ID:        mockDeviceID,
		Type:      "iOS",
		CreatedAt: mockTimestamp - 29*86400,
	})
	s.Require().NoError(err)
	isNewDevice, err = s.deviceStore.IsNewDevice(mockCTX, mockDeviceID)
	s.Require().NoError(err)
	s.Require().Equal(true, isNewDevice)
}

func (s *deviceSuite) TestReset() {
	timeNow = func() time.Time {
		return time.Unix(mockTimestamp, 0)
	}

	mockDeviceID := "mock-device-id"
	// no device id in the cache case
	err := s.deviceStore.Reset(mockCTX, mockDeviceID)
	s.Require().Equal(ErrDeviceNotFound, err)

	// has device id in the cache case but CreatedAt is too early (more than 30 days ago)
	err = s.kv.Set(mockCTX, models.TabWaveDeviceRegistry, mockDeviceID, &models.DeviceRegistry{
		ID:        mockDeviceID,
		Type:      "iOS",
		CreatedAt: mockTimestamp - 31*86400,
	})
	s.Require().NoError(err)

	// make sure the device data has been correctly set
	d := &models.DeviceRegistry{}
	err = s.kv.Get(mockCTX, models.TabWaveDeviceRegistry, mockDeviceID, d)
	s.Require().NoError(err)
	s.Require().Equal(&models.DeviceRegistry{
		ID:        mockDeviceID,
		Type:      "iOS",
		CreatedAt: mockTimestamp - 31*86400,
	}, d)

	// apply Reset function
	err = s.deviceStore.Reset(mockCTX, mockDeviceID)
	s.Require().NoError(err)

	err = s.kv.Get(mockCTX, models.TabWaveDeviceRegistry, mockDeviceID, d)
	s.Require().NoError(err)
	s.Require().Equal(mockTimestamp, d.CreatedAt)
}

func (s *deviceSuite) TestGetRegistry() {
	timeNow = func() time.Time {
		return time.Unix(mockTimestamp, 0)
	}

	mockDeviceID := "mock-device-id"
	// no device id in the cache case
	_, err := s.deviceStore.GetRegistry(mockCTX, mockDeviceID)
	s.Require().Equal(ErrDeviceRegistryNotFound, err)

	// set device registry
	mockRegistry := &models.DeviceRegistry{
		ID:        mockDeviceID,
		Type:      "iOS",
		CreatedAt: mockTimestamp - 31*86400,
	}
	err = s.kv.Set(mockCTX, models.TabWaveDeviceRegistry, mockDeviceID, mockRegistry)
	s.Require().NoError(err)

	// apply GetRegistry function
	registry, err := s.deviceStore.GetRegistry(mockCTX, mockDeviceID)
	s.Require().NoError(err)
	s.Require().Equal(mockRegistry, registry)
}

func (s *deviceSuite) TestHasMarkerType() {
	timeNow = func() time.Time {
		return time.Unix(mockTimestamp, 0)
	}

	mockDeviceID := "mock-device-id"
	// no device id in the cache case
	hasMarkerType, err := s.deviceStore.HasMarkerType(mockCTX, mockDeviceID, models.DeviceMarkerTypeNewbieRose)
	s.Require().NoError(err)
	s.Require().Equal(false, hasMarkerType)

	// set device registry
	mockRegistry := &models.DeviceRegistry{
		ID:        mockDeviceID,
		Type:      "iOS",
		CreatedAt: mockTimestamp - 31*86400,
		Marker:    uint64(1),
	}
	err = s.kv.Set(mockCTX, models.TabWaveDeviceRegistry, mockDeviceID, mockRegistry)
	s.Require().NoError(err)

	// apply GetRegistry function
	hasMarkerType, err = s.deviceStore.HasMarkerType(mockCTX, mockDeviceID, models.DeviceMarkerTypeNewbieRose)
	s.Require().NoError(err)
	s.Require().Equal(true, hasMarkerType)
}

func (s *deviceSuite) TestUpdateMarkerType() {
	timeNow = func() time.Time {
		return time.Unix(mockTimestamp, 0)
	}

	mockDeviceID := "mock-device-id"

	// set device registry
	mockRegistry := &models.DeviceRegistry{
		ID:        mockDeviceID,
		Type:      "iOS",
		CreatedAt: mockTimestamp - 31*86400,
		Marker:    uint64(0),
	}
	err := s.kv.Set(mockCTX, models.TabWaveDeviceRegistry, mockDeviceID, mockRegistry)
	s.Require().NoError(err)

	// has no marker case
	hasMarkerType, err := s.deviceStore.HasMarkerType(mockCTX, mockDeviceID, models.DeviceMarkerTypeNewbieRose)
	s.Require().NoError(err)
	s.Require().Equal(false, hasMarkerType)

	// update marker status
	err = s.deviceStore.UpdateMarkerType(mockCTX, mockDeviceID, models.DeviceMarkerTypeNewbieRose, true)
	s.Require().NoError(err)

	// has marker after update
	hasMarkerType, err = s.deviceStore.HasMarkerType(mockCTX, mockDeviceID, models.DeviceMarkerTypeNewbieRose)
	s.Require().NoError(err)
	s.Require().Equal(true, hasMarkerType)
}
