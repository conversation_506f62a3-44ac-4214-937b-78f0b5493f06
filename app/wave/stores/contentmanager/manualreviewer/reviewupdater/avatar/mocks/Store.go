// Code generated by mockery v1.1.3-0.20250311022958-4653efadba67. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	mock "github.com/stretchr/testify/mock"
)

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// Update provides a mock function with given fields: context, userID, srcID, url
func (_m *Store) Update(context ctx.CTX, userID string, srcID string, url string) error {
	ret := _m.Called(context, userID, srcID, url)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string) error); ok {
		r0 = rf(context, userID, srcID, url)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewStore creates a new instance of Store. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *Store {
	mock := &Store{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
