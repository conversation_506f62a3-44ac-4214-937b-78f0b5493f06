package contentmanager

import (
	slackService "github.com/17media/api/app/wave/service/slack"
	"github.com/17media/api/app/wave/stores/contentmanager/processor"
	_ "github.com/17media/api/app/wave/stores/contentmanager/processor/audio"
	_ "github.com/17media/api/app/wave/stores/contentmanager/processor/image"
	"github.com/17media/api/service/queryv2"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/dig"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of contract object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		Image   processor.Store      `name:"waveImageContentProcessor"`
		Audio   processor.Store      `name:"waveAudioContentProcessor"`
		Slack   slackService.Service `name:"waveSlack"`
		Queryv2 queryv2.Mongo        `name:"queryv2WithoutFillNil"`
	}

	fn := func(p params) Store {
		return New(
			p.Image,
			p.Audio,
			p.Slack,
			p.Queryv2,
		)
	}
	m.ProvideConstructor(fn, `waveContentManager`)
}

// GetContentManager returns the object
func GetContentManager(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"waveContentManager"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
