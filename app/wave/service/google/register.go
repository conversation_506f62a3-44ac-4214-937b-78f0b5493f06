package google

import (
	"github.com/17media/api/service/redis"
	_ "github.com/17media/api/service/redis/rediscache"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/dig"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of googleSvc object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		Cache        redis.Service `name:"redisCache"`
		ClientID     *string       `name:"google_client_id"`
		ClientSecret *string       `name:"google_client_secret"`
	}
	m.RegisterString("google_client_id", "", "google oauth client_id")
	m.RegisterString("google_client_secret", "", "google oauth client_secret")
	fn := func(p params) Service {
		return New(
			p.Cache,
			*p.ClientID,
			*p.ClientSecret,
		)
	}
	m.ProvideConstructor(fn, `waveGoogleAPI`)
}

// GetGoogleSvc returns the google service object
func GetGoogleSvc(m *dimanager.Manager) Service {
	var output Service
	type params struct {
		dig.In
		Output Service `name:"waveGoogleAPI"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
