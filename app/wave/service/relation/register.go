package relation

import (
	"github.com/17media/dig"

	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/models/keys"
	"github.com/17media/api/service/localcache"
	"github.com/17media/api/service/queryv2"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/service/relation"
	"github.com/17media/api/setup/dimanager"

	// trigger init
	_ "github.com/17media/api/service/localcache/primitive"
	_ "github.com/17media/api/service/redis/rediscache"
)

var (
	relMongo = map[keys.Table]*relation.TableConf{
		// Wave's table
		models.TabWaveTopicDiscoverPlaylist: {
			FromField: "topic_id",
			ToField:   "playlist_id",
			SortField: "weight",
			TwoWay:    true,
		},
		models.TabWaveTopicPopularPlaylist: {
			FromField: "topic_id",
			ToField:   "playlist_id",
			SortField: "weight",
			TwoWay:    true,
		},
		models.TabWaveUserFollow: {
			FromField:   "user_id",
			ToField:     "following_user_id",
			Sort<PERSON>ield:   "created_at",
			TwoWay:      true,
			ForceReload: true,
		},
		models.TabWaveUserBlock: {
			FromField:   "user_id",
			ToField:     "blocking_user_id",
			SortField:   "created_at",
			TwoWay:      true,
			ForceReload: true,
		},
		models.TabWaveChatroomPrivateMember: {
			FromField: "from_user_id",
			ToField:   "to_user_id",
			SortField: "created_at",
			TwoWay:    true,
		},
		models.TabWaveChatroomMember: {
			FromField: "chatroom_id",
			ToField:   "user_id",
			SortField: "updated_at",
			TwoWay:    true,
		},
		models.TabWaveChatroomMessage: {
			FromField: "chatroom_id",
			ToField:   "id",
			SortField: "created_nano_at",
			TwoWay:    true,
		},
		models.TabWavePostComment: {
			FromField: "post_id",
			ToField:   "id",
			SortField: "created_nano_at",
			TwoWay:    true,
		},
		models.TabWavePostNotification: {
			FromField: "user_id",
			ToField:   "post_id",
			SortField: "updated_at",
			TwoWay:    true,
		},
		models.TabWavePostLike: {
			FromField: "user_id",
			ToField:   "post_id",
			SortField: "updated_at",
			TwoWay:    true,
		},
		models.TabWaveUserPhoto: {
			FromField: "user_id",
			ToField:   "id",
			SortField: "created_at",
			TwoWay:    true,
		},
		models.TabWaveUserGroupGiftCount: {
			FromField: "user_id",
			ToField:   "gift_group_id",
			SortField: "count",
			TwoWay:    false,
		},
		models.TabWaveMerchandiseHiddenIAPBonus: {
			FromField: "user_id",
			ToField:   "merchandise_id",
			SortField: "created_at",
			TwoWay:    false,
		},
		models.TabWaveRedEnvelope: {
			FromField: "live_id",
			ToField:   "id",
			SortField: "created_at",
			TwoWay:    false,
		},
		models.TabWaveChatroomInvitation: {
			FromField: "chatroom_id",
			ToField:   "to_user_id",
			SortField: "created_at",
			TwoWay:    true,
		},
		models.TabWaveLiveAdmin: {
			FromField: "streamer_id",
			ToField:   "admin_user_id",
			SortField: "created_at",
			TwoWay:    true,
		},
		models.TabWaveUserMission: {
			FromField: "user_id",
			ToField:   "mission_id",
			SortField: "created_at",
			TwoWay:    false,
		},
		models.TabWaveUserRedBlueGames: {
			FromField: "user_id",
			ToField:   "game_id",
			SortField: "created_at",
			TwoWay:    true,
		},
		models.TabWaveUserRacingGames: {
			FromField: "user_id",
			ToField:   "game_id",
			SortField: "created_at",
			TwoWay:    true,
		},
		models.TabWaveCircleMembers: {
			FromField: "circle_room_id",
			ToField:   "user_id",
			SortField: "created_at",
			TwoWay:    true,
		},
		models.TabWaveCircleMessages: {
			FromField: "circle_room_id",
			ToField:   "id",
			SortField: "created_nano_at",
			TwoWay:    false,
		},
		models.TabWaveUserPets: {
			FromField: "user_id",
			ToField:   "pet_id",
			SortField: "weight",
			TwoWay:    true,
		},
		models.TabWaveCollectionUserGiftsReceived: {
			FromField: "from_key",
			ToField:   "gift_id",
			SortField: "count",
			TwoWay:    false,
		},
		models.TabWaveCollectionUserGiftsSent: {
			FromField: "from_key",
			ToField:   "gift_id",
			SortField: "count",
			TwoWay:    false,
		},
		models.TabWaveCollectionUserGiftsLeaderboard: {
			FromField: "from_key",
			ToField:   "target_user_id",
			SortField: "count",
			TwoWay:    false,
		},
		models.TabWavePetV2UserPet: {
			FromField: "user_id",
			ToField:   "user_pet_id",
			SortField: "created_at",
			TwoWay:    true,
		},
		models.TabWaveUserMissionV2: {
			FromField: "from_key",
			ToField:   "mission_id",
			SortField: "count",
			TwoWay:    true,
		},
		models.TabWaveDailyMissionDoneMissions: {
			FromField: "from_key",
			ToField:   "mission_id",
			SortField: "created_at_ms",
			TwoWay:    true,
		},
		// NOTE: Remember to add the table/indexes in prod/staging before you add table here

	}

	// relationMgoTables would be used by mgoDB that is not adopting cache
	relationMgoTables = map[keys.Table]*relation.TableConf{
		models.TabWaveCollectionCampaignGifts: {
			FromField: "from_key",
			ToField:   "to_key",
			SortField: "created_at_ms",
			TwoWay:    true,
		},
		// NOTE: Remember to add the table/indexes in prod/staging before you add table here
	}
)

func init() {
	Register(dimanager.DefaultManager)
	RegisterRelationMongo(dimanager.DefaultManager)
}

// Register registers the constructor of relation object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In

		LocalcachePrimitive localcache.Service `name:"localcachePrimitive"`
		RedisCache          redis.Service      `name:"redisCache"`
		Query               queryv2.Mongo      `name:"queryv2WithoutFillNil"`
	}

	fn2 := func(p params) relation.Service {
		return relation.New(p.LocalcachePrimitive, p.RedisCache, p.Query, relMongo)
	}
	m.ProvideConstructor(fn2, `waveRelation`)
}

// GetRelation returns the relation without v2 object
func GetRelation(m *dimanager.Manager) relation.Service {
	var output relation.Service
	type params struct {
		dig.In
		Output relation.Service `name:"waveRelation"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}

func RegisterRelationMongo(m *dimanager.Manager) {
	type params struct {
		dig.In

		Query queryv2.Mongo `name:"queryv2WithoutFillNil"`
	}

	fn := func(p params) relation.Service {
		return NewRelationMongo(p.Query, relationMgoTables)
	}
	m.ProvideConstructor(fn, `waveRelationMongo`)
}

// GetRelationMongo returns the relation
func GetRelationMongo(m *dimanager.Manager) relation.Service {
	var output relation.Service
	type params struct {
		dig.In
		Output relation.Service `name:"waveRelationMongo"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
