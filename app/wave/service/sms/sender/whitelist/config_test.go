package whitelist

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

var (
	mockValidWhitelistConfRAW = []byte(`
white_lists:
  - country_code: "1"
    phone_number: "1234567890"
    verification_code: "1234"
`)
	mockWhitelistConf = whitelistCfg{
		Whitelists: Whitelists{
			{
				CountryCode:      "1",
				PhoneNumber:      "1234567890",
				VerificationCode: "1234",
			},
		},
	}
)

type confSuite struct {
	suite.Suite
}

func TestConfigSuite(t *testing.T) {
	suite.Run(t, new(confSuite))
}

func (s *confSuite) TestWhitelistConf() {
	c := whitelistCfg{}
	intf, _, err := c.Check(mockValidWhitelistConfRAW)
	s.Require().NoError(err)
	c.Apply(intf)
	s.Require().Equal(mockWhitelistConf, c)
}
