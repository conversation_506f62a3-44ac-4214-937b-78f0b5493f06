// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import ctx "github.com/17media/api/base/ctx"
import libphonenumber "github.com/ttacon/libphonenumber"
import mock "github.com/stretchr/testify/mock"
import models "github.com/17media/api/app/wave/models"

// Service is an autogenerated mock type for the Service type
type Service struct {
	mock.Mock
}

// Reset provides a mock function with given fields: context, user
func (_m *Service) Reset(context ctx.CTX, user *models.User) error {
	ret := _m.Called(context, user)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User) error); ok {
		r0 = rf(context, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Send provides a mock function with given fields: context, senderType, to, region
func (_m *Service) Send(context ctx.CTX, senderType models.SMSSenderType, to *libphonenumber.PhoneNumber, region string) (*models.SMSInfo, error) {
	ret := _m.Called(context, senderType, to, region)

	var r0 *models.SMSInfo
	if rf, ok := ret.Get(0).(func(ctx.CTX, models.SMSSenderType, *libphonenumber.PhoneNumber, string) *models.SMSInfo); ok {
		r0 = rf(context, senderType, to, region)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.SMSInfo)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, models.SMSSenderType, *libphonenumber.PhoneNumber, string) error); ok {
		r1 = rf(context, senderType, to, region)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Verify provides a mock function with given fields: context, senderType, requestID, pinCode
func (_m *Service) Verify(context ctx.CTX, senderType models.SMSSenderType, requestID string, pinCode string) (*models.PhoneRequest, error) {
	ret := _m.Called(context, senderType, requestID, pinCode)

	var r0 *models.PhoneRequest
	if rf, ok := ret.Get(0).(func(ctx.CTX, models.SMSSenderType, string, string) *models.PhoneRequest); ok {
		r0 = rf(context, senderType, requestID, pinCode)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.PhoneRequest)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, models.SMSSenderType, string, string) error); ok {
		r1 = rf(context, senderType, requestID, pinCode)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}
