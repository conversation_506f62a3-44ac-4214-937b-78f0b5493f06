package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/17media/api/app/wave/api/apis"
	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/app/wave/stores/chat"
	"github.com/17media/api/app/wave/stores/eventlog"
	"github.com/17media/api/app/wave/stores/gift"
	"github.com/17media/api/app/wave/stores/gift/helper"
	"github.com/17media/api/app/wave/stores/pet"
	"github.com/17media/api/app/wave/stores/royalcenter"
	"github.com/17media/api/app/wave/stores/user"
	"github.com/17media/api/base/ctx"
	bversion "github.com/17media/api/base/version"
	"github.com/17media/api/service/i18n"
	"github.com/17media/api/service/storage/gcs"
	"github.com/17media/logrus"
)

const (
	i18nKeyMeetPhotoOldVersion = "meet_photo_oldversion" // NOTE: backend config
)

type chatHandler struct {
	user        user.Store
	chat        chat.Store
	gcsHelper   *gcs.Helper
	giftStore   gift.Store
	giftHelper  helper.Worker
	pet         pet.Store
	eventlog    eventlog.Store
	royalCenter royalcenter.Store
}

// NewChatHandler creates request handlers for chat
func NewChatHandler(
	rg *gin.RouterGroup,
	user user.Store,
	chat chat.Store,
	gcsHelper *gcs.Helper,
	giftStore gift.Store,
	giftHelper helper.Worker,
	pet pet.Store,
	eventlog eventlog.Store,
	royalCenter royalcenter.Store,
) {
	ch := chatHandler{
		user:        user,
		chat:        chat,
		gcsHelper:   gcsHelper,
		giftStore:   giftStore,
		giftHelper:  giftHelper,
		pet:         pet,
		eventlog:    eventlog,
		royalCenter: royalCenter,
	}

	apis.Handle(rg, "GET", "/rooms", authenticated(user), ch.list)
	apis.Handle(rg, "GET", "/rooms/:chatroomID", authenticated(user), ch.get)
	apis.Handle(rg, "GET", "/rooms/:chatroomID/messages", authenticated(user), ch.listMessages)
	apis.Handle(rg, "POST", "/rooms/:chatroomID/messages", authenticated(user), ch.createMessage)
	apis.Handle(rg, "PUT", "/rooms/:chatroomID/read", authenticated(user), ch.readChatroom)
	apis.Handle(rg, "POST", "/rooms/:chatroomID/block", authenticated(user), ch.block)
	apis.Handle(rg, "DELETE", "/rooms/:chatroomID/block", authenticated(user), ch.unblock)
	apis.Handle(rg, "DELETE", "/rooms/:chatroomID", authenticated(user), ch.deleteChatroom)

	// invitations
	apis.Handle(rg, "GET", "/invitations", authenticated(user), ch.listInvitation)
	apis.Handle(rg, "POST", "/rooms/:chatroomID/invitation", authenticated(user), ch.acceptInvitation)
	apis.Handle(rg, "DELETE", "/rooms/:chatroomID/invitation", authenticated(user), ch.declineInvitation)

	// notification
	apis.Handle(rg, "GET", "/notification", authenticated(user), ch.getNotification)

	// gifts
	apis.Handle(rg, "POST", "/rooms/:chatroomID/gifts", authenticated(user, needCompleteRegister()), ch.sendGift)
	apis.Handle(rg, "GET", "/rooms/:chatroomID/gift_tabs", authenticated(user, needCompleteRegister()), ch.getGiftTabs)

	// groupchat
	apis.Handle(rg, "POST", "/rooms", authenticated(user), ch.createGroupchat)
	apis.Handle(rg, "PATCH", "/rooms/:chatroomID", authenticated(user), ch.updateGroupchat)
	apis.Handle(rg, "GET", "/users", authenticated(user), ch.getUserList)
	apis.Handle(rg, "POST", "/rooms/:chatroomID/members", authenticated(user), ch.addMembers)
	apis.Handle(rg, "DELETE", "/rooms/:chatroomID/members/:userID", authenticated(user), ch.deleteMember)
	apis.Handle(rg, "GET", "/rooms/:chatroomID/members", authenticated(user), ch.getMemberList)
	apis.Handle(rg, "GET", "/users/recommend", authenticated(user), ch.getRecommendList)
	apis.Handle(rg, "POST", "/rooms/:chatroomID/leave", authenticated(user), ch.leaveGroupchat)
}

// swagger:model chatCursorRes
type chatCursorRes struct {
	Cursor string                    `json:"cursor"`
	Data   []*models.DisplayChatroom `json:"data"`
	Meta   chatMetaData              `json:"meta"`
}

type chatMetaData struct {
	InvitationNum int `json:"invitation_num"`
}

// swagger:route GET /chat/rooms chat list
//
// # List chatrooms
//
// Responses:
//
//	200: chatCursorRes
//	400: error
//	500: error
func (ch *chatHandler) list(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	lang := c.GetHeader("language")
	di := extractDeviceInfo(c)

	var p CursorParams
	if err := c.Bind(&p); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	crs, nextCursor, _, err := ch.chat.GetRooms(
		context, user, p.Cursor, 30)
	if err != nil {
		Error(c, err)
		return
	}

	dcrs := []*models.DisplayChatroom{}
	for _, cr := range crs {
		// hide group chatroom if user use old version
		if bversion.CompareVersion(di.Version, "5.14.0") == -1 && cr.Type == models.ChatroomTypeGroup {
			continue
		}

		// TODO: angie, remove version checker after Oct 21, 2021
		// Switch type pic to type text
		if bversion.CompareVersion(di.Version, "5.3.0") == -1 && cr.LastMessage != nil && cr.LastMessage.Type == models.ContentTypePicture {
			announcement, err := i18n.Translate(lang, i18nKeyMeetPhotoOldVersion)
			if err != nil {
				Error(c, err)
				return
			}

			cr.LastMessage.Type = models.ContentTypeText
			cr.LastMessage.Content = announcement
		}

		// TODO: leon, remove version checker after Dec 17, 2021
		// Switch type gift to type i18n
		if bversion.CompareVersion(di.Version, "5.9.0") == -1 && cr.LastMessage != nil && cr.LastMessage.Type == models.ContentTypeGift {
			announcement, err := i18n.Translate(lang, models.I18nKeyChatGiftOldVersion)
			if err != nil {
				Error(c, err)
				return
			}

			cr.LastMessage.Type = models.ContentTypeText
			cr.LastMessage.Content = announcement
		}

		tmp := cr.Parse(ch.gcsHelper, lang, user.ID)
		dcrs = append(dcrs, tmp)

		if tmp.ID == "" {
			context.WithFields(logrus.Fields{
				"param":    p,
				"chatroom": tmp,
			}).Error("chatroom.ID should not be empty")
		}
	}

	invitationCount, err := ch.chat.GetInvitationCount(context, user)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, chatCursorRes{
		Cursor: nextCursor,
		Data:   dcrs,
		Meta: chatMetaData{
			InvitationNum: invitationCount,
		},
	})
}

// swagger:route GET chat/rooms/:chatroomID chat get
//
// # Get chatroom by id
//
// Responses:
//
//	200: displayChatroom
//	500: error
func (ch *chatHandler) get(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	lang := c.GetHeader("language")
	chatroomID := c.Param("chatroomID")
	di := extractDeviceInfo(c)

	cr, err := ch.chat.GetRoomByID(
		context, user, chatroomID)
	if err != nil {
		Error(c, err)
		return
	}

	dm := cr.Parse(ch.gcsHelper, lang, user.ID)
	// TODO: leon, remove version checker after Dec 17, 2021
	// Switch type gift to type i18n
	if bversion.CompareVersion(di.Version, "5.9.0") == -1 && dm.LastMessage != nil && dm.LastMessage.Type == models.ContentTypeGift {
		announcement, err := i18n.Translate(lang, models.I18nKeyChatGiftOldVersion)
		if err != nil {
			Error(c, err)
			return
		}

		dm.LastMessage.Type = models.ContentTypeText
		dm.LastMessage.Content = announcement
	}

	JSON(c, http.StatusOK, dm)
}

// swagger:model chatMessageCursorRes
type chatMessageCursorRes struct {
	Cursor string                       `json:"cursor"`
	Data   []*models.DisplayChatMessage `json:"data"`
}

// swagger:route GET /chat/rooms/:chatroomID/messages chat listMessages
//
// # List chatroom messages
//
// Responses:
//
//	200: chatMessageCursorRes
//	400: error
//	500: error
func (ch *chatHandler) listMessages(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	lang := c.GetHeader("language")
	chatroomID := c.Param("chatroomID")
	di := extractDeviceInfo(c)

	var p CursorParams
	if err := c.Bind(&p); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	msgs, nextCur, err := ch.chat.GetMessages(
		context, user.ID, chatroomID,
		p.Cursor, defaultLimit,
	)
	if err != nil {
		Error(c, err)
		return
	}

	dmsgs := []*models.DisplayChatMessage{}
	for _, msg := range msgs {
		// TODO: angie, remove version checker after Oct 21, 2021
		// Switch type pic to type text and set content to announcment msg
		if bversion.CompareVersion(di.Version, "5.3.0") == -1 && msg.Type == models.ContentTypePicture {
			announcement, err := i18n.Translate(lang, i18nKeyMeetPhotoOldVersion)
			if err != nil {
				Error(c, err)
				return
			}

			msg.Type = models.ContentTypeText
			msg.Content = announcement
		}

		// TODO: leon, remove version checker after Dec 17, 2021
		// Switch type gift to type i18n and set content to i18n key
		if bversion.CompareVersion(di.Version, "5.9.0") == -1 && msg.Type == models.ContentTypeGift {
			announcement, err := i18n.Translate(lang, models.I18nKeyChatGiftOldVersion)
			if err != nil {
				Error(c, err)
				return
			}

			msg.Type = models.ContentTypeText
			msg.Content = announcement
		}

		dmsgs = append(dmsgs, msg.Parse(ch.gcsHelper, lang, user.ID))
	}
	JSON(c, http.StatusOK, chatMessageCursorRes{
		Cursor: nextCur,
		Data:   dmsgs,
	})
}

// swagger:route POST /chat/rooms/:chatroomID/messages chat createMessage
//
// send a meesage to the chatroom
//
// Responses:
//
//	200: displayMessage
//	400: error
//	500: error
func (ch *chatHandler) createMessage(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	lang := c.GetHeader("language")
	chatroomID := c.Param("chatroomID")

	var p models.ChatMessageParams
	if err := c.Bind(&p); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	msg, err := ch.chat.SendToChatroom(
		context, user, chatroomID, p,
	)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, msg.Parse(ch.gcsHelper, lang, user.ID))
}

// swagger:route PUT /chat/rooms/:chatroomID/read chat readChatroom
//
// read chatroom messages
//
// Responses:
//
//	204:
//	500: error
func (ch *chatHandler) readChatroom(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")

	if err := ch.chat.Read(context, user.ID, chatroomID); err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusNoContent, gin.H{})
}

// swagger:route POST /chat/rooms/:chatroomID/block chat block
//
// block the chatroom
//
// Responses:
//
//	204:
//	500: error
func (ch *chatHandler) block(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")

	cr, err := ch.chat.GetRoomByID(
		context, user, chatroomID)
	if err != nil {
		Error(c, err)
		return
	}

	// NOTE block chatroom is same as block user now
	//   but we separate to 2 api to make flexible in the future
	for _, member := range cr.Members {
		if err := ch.user.Block(context, user, member.ID); err != nil {
			Error(c, err)
			return
		}
	}

	JSON(c, http.StatusNoContent, gin.H{})
}

// swagger:route DELETE /chat/rooms/:chatroomID/block chat unblock
//
// unblock the chatroom
//
// Responses:
//
//	204:
//	500: error
func (ch *chatHandler) unblock(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")

	cr, err := ch.chat.GetRoomByID(
		context, user, chatroomID)
	if err != nil {
		Error(c, err)
		return
	}

	// NOTE unblock chatroom is same as unblock user now
	//   but we separate to 2 api to make flexible in the future
	for _, member := range cr.Members {
		if err := ch.user.Unblock(context, user, member.ID); err != nil {
			Error(c, err)
			return
		}
	}

	JSON(c, http.StatusNoContent, gin.H{})
}

// swagger:route DELETE /chat/rooms/:chatroomID chat deleteChatroom
//
// delete the chatroom
//
// Responses:
//
//	204:
//	500: error
func (ch *chatHandler) deleteChatroom(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")

	err := ch.chat.DeleteChatroom(context, user, chatroomID)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusNoContent, gin.H{})
}

// swagger:model chatInviteCursorRes
type chatInviteCursorRes struct {
	Cursor string                    `json:"cursor"`
	Data   []*models.DisplayChatroom `json:"data"`
}

// swagger:route GET /chat/invitations chat listInvitation
//
// list invitations
//
// Responses:
//
//	200: chatInviteCursorRes
//	500: error
func (ch *chatHandler) listInvitation(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	lang := c.GetHeader("language")
	di := extractDeviceInfo(c)

	var p CursorParams
	if err := c.Bind(&p); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	offset, err := getOffsetFromCursor(p.Cursor)
	if err != nil {
		Error(c, models.BadRequestError)
		return
	}

	invitations, total, err := ch.chat.ListInvitation(context, user, offset, 20)
	if err != nil {
		Error(c, err)
		return
	}

	dc := []*models.DisplayChatroom{}
	for _, inv := range invitations {
		// TODO: angie, remove version checker after Oct 21, 2021
		// Switch type pic to type text
		if bversion.CompareVersion(di.Version, "5.3.0") == -1 && inv.LastMessage != nil && inv.LastMessage.Type == models.ContentTypePicture {
			announcement, err := i18n.Translate(lang, i18nKeyMeetPhotoOldVersion)
			if err != nil {
				Error(c, err)
				return
			}

			inv.LastMessage.Type = models.ContentTypeText
			inv.LastMessage.Content = announcement
		}

		// TODO: leon, remove version checker after Dec 17, 2021
		// Switch type gift to type i18n
		if bversion.CompareVersion(di.Version, "5.9.0") == -1 && inv.LastMessage != nil && inv.LastMessage.Type == models.ContentTypeGift {
			announcement, err := i18n.Translate(lang, models.I18nKeyChatGiftOldVersion)
			if err != nil {
				Error(c, err)
				return
			}

			inv.LastMessage.Type = models.ContentTypeText
			inv.LastMessage.Content = announcement
		}

		if inv.Type == models.ChatroomTypeGroup {
			inv.LastMessage = &models.ChatMessage{
				Type:    models.ContentTypeSystem,
				Content: models.I18nKeyGroupchatInviteMessage,
			}
		}

		tmp := inv.Parse(ch.gcsHelper, lang, user.ID, models.WithUserParseFunc(models.WithDistance(&user.GeoLocation)))
		dc = append(dc, tmp)
	}

	JSON(c, http.StatusOK, chatInviteCursorRes{
		Cursor: getNextCursorFromOffset(offset, 20, total),
		Data:   dc,
	})
}

// swagger:route POST /chat/rooms/:chatroomID/invitation chat acceptInvitation
//
// accept specific chatroom invitation
//
// Responses:
//
//	204:
//	500: error
func (ch *chatHandler) acceptInvitation(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")

	if err := ch.chat.AcceptInvitation(context, user, chatroomID); err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusNoContent, gin.H{})
}

// swagger:route DELETE /chat/rooms/:chatroomID/invitation chat declineInvitation
//
// decline specific chatroom invitation
//
// Responses:
//
//	204:
//	500: error
func (ch *chatHandler) declineInvitation(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")

	if err := ch.chat.DeclineInvitation(context, user, chatroomID); err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusNoContent, gin.H{})
}

// swagger:model chatNotificationRes
type chatNotificationRes struct {
	IsUnread bool `json:"is_unread"`
}

// swagger:route GET /chat/notification chat getNotification
//
// get unread chat message and invitation notification
//
// Responses:
//
//	200: chatNotificationRes
//	500: error
func (ch *chatHandler) getNotification(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)

	isUnread, err := ch.chat.GetNotification(context, user)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, chatNotificationRes{
		IsUnread: isUnread,
	})
}

// swagger:route POST /chat/rooms/:chatroomID/gifts chat sendGift
// Send a gift to a user in chatroom
// Responses:
//
//	200: displayMessage
//	400: error
//	500: error
func (ch *chatHandler) sendGift(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	lang := c.GetHeader("language")
	chatroomID := c.Param("chatroomID")

	reqParams := models.SendChatGiftParams{}
	if err := c.Bind(&reqParams); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	cr, err := ch.chat.GetRoomByID(context, user, chatroomID)
	if err != nil {
		Error(c, err)
		return
	}

	toUserIDs := []string{}
	if reqParams.UserID == "" {
		toUserIDs = append(toUserIDs, cr.Members[0].ID)
	} else {
		toUserIDs = append(toUserIDs, reqParams.UserID)
	}

	receivers, err := ch.user.GetPlainUsers(context, toUserIDs)
	if err != nil {
		Error(c, err)
		return
	}
	var checkTermsOfUseAcceptedErr error
	for _, receiver := range receivers {
		if err := checkTermsOfUseAccepted(context, ch.eventlog, ch.chat, receiver); err != nil {
			checkTermsOfUseAcceptedErr = err
		}
	}
	if checkTermsOfUseAcceptedErr != nil {
		Error(c, checkTermsOfUseAcceptedErr)
		return
	}

	sendInfo, err := ch.giftStore.Send(context, user, models.SourceChat, cr.ID, reqParams.GiftID, toUserIDs)
	if err != nil {
		Error(c, err)
		return
	}

	giftInfo, err := ch.giftHelper.GetGiftInfo(context, reqParams.GiftID)
	if err != nil {
		Error(c, err)
		return
	}

	respGiftInfo, err := ch.giftHelper.GetGiftInfo(context, sendInfo.GiftIDs[0])
	if err != nil {
		Error(c, err)
		return
	}

	toUser, err := ch.user.Get(context, toUserIDs[0])
	if err != nil {
		Error(c, err)
		return
	}

	msg, err := ch.chat.SendGiftToChatroom(context, user, chatroomID, toUser, giftInfo, respGiftInfo)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, msg.Parse(ch.gcsHelper, lang, user.ID))
}

// swagger:parameters getChatGiftTabs
type chatGetTabsParams struct {
	UserID string `form:"user_id"`
}

// swagger:route GET /chat/rooms/:chatroomID/gift_tabs chat getChatGiftTabs
// Get gift tabs in chatroom
// Responses:
//
//	200: GiftTabsRes
//	500: error
func (ch *chatHandler) getGiftTabs(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")
	di := extractDeviceInfo(c)

	var input chatGetTabsParams
	if err := c.BindQuery(&input); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	cr, err := ch.chat.GetRoomByID(context, user, chatroomID)
	if err != nil {
		Error(c, err)
		return
	}
	if len(cr.Members) == 0 {
		Error(c, models.BadRequestError)
		return
	}

	userPet, err := ch.pet.GetPet(context, cr.Members[0])
	if err != nil {
		Error(c, err)
		return
	}
	petGiftInfo := &models.PetGiftInfo{}
	if userPet != nil {
		petGiftInfo = userPet.ToPetGiftInfo()
	}

	tabs, err := ch.giftStore.GetTabs(
		context, user, models.DeviceInfo{
			Type:    di.Type,
			Version: di.Version,
		},
		models.SourceChat, chatroomID, input.UserID, userPet,
	)
	if err != nil {
		Error(c, err)
		return
	}

	lastUpdate, err := ch.giftHelper.GetGiftListLastUpdateTimestamp(context)
	if err != nil {
		Error(c, err)
		return
	}

	displayTabs := []*models.DisplayGiftTab{}
	for _, tab := range tabs {
		displayTabs = append(displayTabs, tab.Parse())
	}
	userRoyalLevel := models.RoyalLevelNone
	info, err := ch.royalCenter.GetInfo(context, user)
	if err != nil {
		context.WithFields(logrus.Fields{
			"userID": user.ID,
			"err":    err,
		}).Warn("royalCenter.GetInfo failed")
	} else if info != nil {
		userRoyalLevel = info.Level
	}
	JSON(c, http.StatusOK, models.GiftTabsRes{
		Tabs:            displayTabs,
		GiftsLastUpdate: lastUpdate,
		Pet:             petGiftInfo.Parse(),
		UserRoyalLevel:  userRoyalLevel,
	})
}

// swagger:route POST /chat/rooms chat createGroupchat
// Create group chatroom
// Responses:
//
//	200: displayChatroom
//	400: error
//	500: error
func (ch *chatHandler) createGroupchat(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	lang := c.GetHeader("language")

	reqParams := models.CreateGroupchatParams{}
	if err := c.Bind(&reqParams); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	cr, err := ch.chat.CreateGroupchat(context, user, reqParams)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, cr.Parse(ch.gcsHelper, lang, user.ID))
}

// swagger:route PATCH /chat/rooms/:chatroomID chat updateGroupchat
// Update group chatroom
// Responses:
//
//	200: displayChatroom
//	400: error
//	500: error
func (ch *chatHandler) updateGroupchat(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	lang := c.GetHeader("language")
	chatroomID := c.Param("chatroomID")

	reqParams := models.UpdateGroupchatParams{}
	if err := c.Bind(&reqParams); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	cr, err := ch.chat.UpdateGroupchat(context, user, chatroomID, reqParams)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, cr.Parse(ch.gcsHelper, lang, user.ID))
}

// swagger:parameters getUserList
type chatListUsersParams struct {
	CursorParams
	// `q` is fr searching users by users' name
	// in:query
	SearchTerm string `json:"q" form:"q"`
	// in:query
	ChatroomID string `json:"chatroom_id" form:"chatroom_id"`
}

// swagger:route GET /chat/users chat getUserList
//
// # List users for groupchat searching
//
// Responses:
//
//	200: models.ChatListUsersCurRes
//	400: error
//	500: error
func (ch *chatHandler) getUserList(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	userInfo := c.MustGet("UserInfo").(*models.User)

	input := &chatListUsersParams{}
	if err := c.Bind(input); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	offset, err := getOffsetFromCursor(input.Cursor)
	if err != nil {
		Error(c, err)
		return
	}
	region := userInfo.GetViewerRegion()

	options := []user.SearchOptionFunc{
		user.SortByFollowersCount(),
		user.WithRegion(region),
	}
	if input.SearchTerm != "" {
		options = append(options, user.WithSearchTerm(input.SearchTerm))
	}

	res, total, err := ch.chat.GetUserList(context, userInfo, offset, defaultLimit, input.ChatroomID, options...)
	if err != nil {
		Error(c, err)
		return
	}

	nextCursor := getNextCursorFromOffset(offset, defaultLimit, total)
	JSON(c, http.StatusOK, models.ChatListUsersCurRes{
		Data:   res,
		Cursor: nextCursor,
	})
}

// swagger:parameters addMembers
type chatOperateMembersParams struct {
	// in:body
	UserIDs []string `json:"user_ids"`
}

// swagger:route POST /chat/rooms/:chatroomID/members chat addMembers
//
// # Add new members in groupchat
//
// Responses:
//
//	204:
//	500: error
func (ch *chatHandler) addMembers(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")

	input := &chatOperateMembersParams{}
	if err := c.Bind(input); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	err := ch.chat.AddMembers(context, user, chatroomID, input.UserIDs)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusNoContent, gin.H{})
}

// swagger:route DELETE /chat/rooms/:chatroomID/members/:userID chat deleteMember
//
// # Delete member in groupchat
//
// Responses:
//
//	204:
//	500: error
func (ch *chatHandler) deleteMember(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")
	targetUserID := c.Param("userID")

	err := ch.chat.DeleteMember(context, user, chatroomID, targetUserID)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusNoContent, gin.H{})
}

// swagger:parameters getMemberList
type chatListMembersParams struct {
	CursorParams
}

// swagger:route GET /chat/rooms/:chatroomID/members chat getMemberList
//
// # List chatroom members
//
// Responses:
//
//	200: models.ChatListMembersCurRes
//	400: error
//	500: error
func (ch *chatHandler) getMemberList(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")

	input := &chatListMembersParams{}
	if err := c.Bind(input); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	offset, err := getOffsetFromCursor(input.Cursor)
	if err != nil {
		Error(c, err)
		return
	}

	res, total, err := ch.chat.GetMemberList(context, user, chatroomID, offset, defaultLimit)
	if err != nil {
		Error(c, err)
		return
	}

	nextCursor := getNextCursorFromOffset(offset, defaultLimit, total)
	JSON(c, http.StatusOK, models.ChatListMembersCurRes{
		Data:   res,
		Cursor: nextCursor,
	})
}

// swagger:parameters getRecommendList
type chatListRecommendUsersParams struct {
	CursorParams
	// in:query
	ChatroomID string `json:"chatroom_id" form:"chatroom_id"`
}

// swagger:route GET /chat/users/recommend chat getRecommendList
//
// # List recommend users could be invited to groupchat
//
// Responses:
//
//	200: models.ChatListUsersCurRes
//	400: error
//	500: error
func (ch *chatHandler) getRecommendList(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)

	input := &chatListRecommendUsersParams{}
	if err := c.Bind(input); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	offset, err := getOffsetFromCursor(input.Cursor)
	if err != nil {
		Error(c, err)
		return
	}

	res, total, err := ch.chat.GetRecommendList(context, user, offset, defaultLimit, input.ChatroomID)
	if err != nil {
		Error(c, err)
		return
	}

	nextCursor := getNextCursorFromOffset(offset, defaultLimit, total)
	JSON(c, http.StatusOK, models.ChatListUsersCurRes{
		Data:   res,
		Cursor: nextCursor,
	})
}

// swagger:route POST /chat/rooms/:chatroomID/leave chat leaveGroupchat
//
// # Leave groupchat
//
// Responses:
//
//	204:
//	500: error
func (ch *chatHandler) leaveGroupchat(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	chatroomID := c.Param("chatroomID")

	err := ch.chat.LeaveGroupchat(context, user, chatroomID)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusNoContent, gin.H{})
}
