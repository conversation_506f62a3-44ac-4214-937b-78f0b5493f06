package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/17media/api/app/wave/api/apis"
	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/app/wave/service/messenger"
	"github.com/17media/api/app/wave/stores/gamecoinpool"
	"github.com/17media/api/app/wave/stores/live"
	"github.com/17media/api/app/wave/stores/money"
	"github.com/17media/api/app/wave/stores/redblue"
	"github.com/17media/api/app/wave/stores/user"
	"github.com/17media/api/base/ctx"
	bmodels "github.com/17media/api/models"
	smessenger "github.com/17media/api/service/messenger"
	"github.com/17media/logrus"
)

type redBlueHandler struct {
	user         user.Store
	msger        messenger.Service
	bank         money.Bank
	live         live.Store
	redblue      redblue.Store
	gamecoinpool gamecoinpool.Store
}

// NewRedBlueHandler init redblueHandler
func NewRedBlueHandler(
	arg *gin.RouterGroup,
	rg *gin.RouterGroup,
	user user.Store,
	msger messenger.Service,
	bank money.Bank,
	live live.Store,
	redblue redblue.Store,
	gamecoinpool gamecoinpool.Store,
) {
	rh := redBlueHandler{
		user:         user,
		msger:        msger,
		bank:         bank,
		live:         live,
		redblue:      redblue,
		gamecoinpool: gamecoinpool,
	}

	apis.Handle(rg, "POST", "/pubsub", authenticated(user), rh.pubsub)
	apis.Handle(rg, "POST", "/info", authenticated(user), rh.getInfo)
	apis.Handle(rg, "POST", "/info/:gameID/options/:optionID", authenticated(user), rh.bet)
	apis.Handle(rg, "POST", "/info/:gameID/action", authenticated(user), rh.action)
	apis.Handle(rg, "GET", "/history", authenticated(user), rh.history)
	apis.Handle(rg, "GET", "/unread", authenticated(user), rh.getUnread)

	apis.Handle(arg, "GET", "/info", authenticated(user, needAdmin()), rh.getInfoForAdmin)
	apis.Handle(arg, "GET", "/history", authenticated(user, needAdmin()), rh.historyForAdmin)
	apis.Handle(arg, "GET", "/profit", authenticated(user, needAdmin()), rh.getProfit)
	apis.Handle(arg, "GET", "/pool", authenticated(user, needAdmin()), rh.getPool)
}

// swagger:route POST /game-redblue/pubsub redblue pubsub
//
// # Pubsub mock API
//
// Responses:
//
//	204:
//	400:
func (rh *redBlueHandler) pubsub(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)

	p := models.RedBlueResponse{}
	if err := c.Bind(&p); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	// TODO: get channel from store method
	channel := strconv.Itoa(p.ActiveGame.ID)
	provider := rh.msger.GetProviderFromConfig(context, "")
	message := &models.MsgRedBlueGame{
		Message: models.Message{
			Type: models.MsgTypeRedBlueGame,
		},
		RedBlue: p.Parse(models.IsRedBluePubsub()),
	}

	if err := rh.msger.GrantChannelPermByCategory(
		context, channel, smessenger.AllChanAuthKey, messenger.CategoryGame,
	); err != nil {
		context.WithFields(logrus.Fields{
			"channel": channel,
			"err":     err,
		}).Error("Failed to grant permission")
		Error(c, err)
		return
	}

	if _, err := rh.msger.Publish(context, channel, message, &smessenger.Option{
		Mp: &provider,
	}); err != nil {
		context.WithFields(logrus.Fields{
			"err":      err,
			"provider": bmodels.MessageProviderAbly,
			"channel":  channel,
		}).Warn("msger.Publish() failed")
		Error(c, err)
		return
	}

	JSON(c, http.StatusNoContent, gin.H{})
}

// swagger:parameters getInfo
type getInfoParams struct {
	LiveID string `json:"live_id"`
}

// swagger:route POST /game-redblue/info redblue getInfo
//
// Get games' info
//
// Responses:
//
//	200: displayRedBlueResponse
//	400:
//	500:
func (rh *redBlueHandler) getInfo(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)

	params := getInfoParams{}
	if err := c.Bind(&params); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	liveInfo, err := rh.live.GetPlainLive(context, params.LiveID)
	if err != nil {
		Error(c, err)
		return
	}

	if liveInfo.Status != models.LiveStatusStreaming {
		Error(c, live.ErrLiveIsNotStreaming)
		return
	}

	// user can always play redblue game if user is invisible
	if user.InvisiblePreference != models.InvisiblePreferenceEnableInvisible {
		// if user is not in live, return ErrUserNotOnLive
		isUserInLive, err := rh.live.IsUserInLive(context, liveInfo, user)
		if err != nil {
			context.WithField("err", err).Error("live.IsUserInLive failed")
			Error(c, err)
			return
		}
		if !isUserInLive {
			Error(c, live.ErrUserNotOnLive)
			return
		}
	}

	gamesInfo, err := rh.redblue.Get(context, user)
	if err != nil {
		Error(c, err)
		return
	}

	ub, err := rh.bank.GetUserBalance(context, user.ID, models.CurrencyPoint)
	if err != nil {
		context.WithField("err", err).Error("bank.GetUserBalance failed")
		Error(c, err)
		return
	}

	gamesInfo.ActiveGame.PlayerRemainedCoins = int(ub[models.CurrencyPoint].Amount)

	JSON(c, http.StatusOK, gamesInfo.Parse())
}

// swagger:parameters bet
type betParams struct {
	LiveID string `json:"live_id"`
	Coins  int    `json:"coins"`
}

// swagger:model betRes
type betRes struct {
	PlayerRemainedCoins int `json:"player_remained_coins"`
}

// swagger:route POST /game-redblue/info/:gameID/options/:optionID redblue bet
//
// # Bet on specific option
//
// Responses:
//
//	200: betRes
//	400:
//	500:
func (rh *redBlueHandler) bet(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	gameID := c.Param("gameID")
	optionID := c.Param("optionID")

	params := betParams{}
	if err := c.Bind(&params); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	liveInfo, err := rh.live.GetPlainLive(context, params.LiveID)
	if err != nil {
		Error(c, err)
		return
	}

	if liveInfo.Status != models.LiveStatusStreaming {
		Error(c, live.ErrLiveIsNotStreaming)
		return
	}

	// user can always play redblue game if user is invisible
	if user.InvisiblePreference != models.InvisiblePreferenceEnableInvisible {
		// if user is not in live, return ErrUserNotOnLive
		isUserInLive, err := rh.live.IsUserInLive(context, liveInfo, user)
		if err != nil {
			context.WithField("err", err).Error("live.IsUserInLive failed")
			Error(c, err)
			return
		}
		if !isUserInLive {
			Error(c, live.ErrUserNotOnLive)
			return
		}
	}

	parsedGameID, err := strconv.ParseInt(gameID, 10, 64)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err": err,
			"val": string(gameID),
		}).Error("strconv.ParseInt failed")
		Error(c, err)
		return
	}

	parsedOptionID, err := strconv.ParseInt(optionID, 10, 64)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err": err,
			"val": string(optionID),
		}).Error("strconv.ParseInt failed")
		Error(c, err)
		return
	}

	_, remainedCoins, err := rh.redblue.Bet(context, user, int(parsedGameID), models.RedBlueResultOptionID(parsedOptionID), params.Coins)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, betRes{
		PlayerRemainedCoins: remainedCoins,
	})
}

// swagger:parameters action
type actionParams struct {
	Type models.PlayerActionType `json:"type"`
}

// swagger:route POST /game-redblue/info/:gameID/action redblue action
//
// # Update user action in game
//
// Responses:
//
//	204:
//	400:
//	500:
func (rh *redBlueHandler) action(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	user := c.MustGet("UserInfo").(*models.User)
	gameID := c.Param("gameID")

	params := actionParams{}
	if err := c.Bind(&params); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	parsedGameID, err := strconv.ParseInt(gameID, 10, 64)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err": err,
			"val": string(gameID),
		}).Error("strconv.ParseInt failed")
		Error(c, err)
		return
	}

	err = rh.redblue.Action(context, user, int(parsedGameID), params.Type)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusNoContent, gin.H{})
}

// swagger:model historyRes
type historyRes struct {
	Data []models.RedBlueResultOptionID `json:"data"`
}

// swagger:route GET /game-redblue/history redblue history
//
// # Get redblue global history games result
//
// Responses:
//
//	200: historyRes
//	500:
func (rh *redBlueHandler) history(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	u := c.MustGet("UserInfo").(*models.User)

	optionIDs, err := rh.redblue.GetHistory(context, u)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, historyRes{
		Data: optionIDs,
	})
}

// swagger:route GET /game-redblue/unread redblue getUnread
//
// # Get user redblue unread result
//
// Responses:
//
//	200: redBluePlayerSummary
//	500:
func (rh *redBlueHandler) getUnread(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)
	u := c.MustGet("UserInfo").(*models.User)

	s, err := rh.redblue.GetUnread(context, u)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, s)
}

// swagger:parameters getProfit
type getProfitParams struct {
	StartDate string `form:"start_date"`
	EndDate   string `form:"end_date"`
}

// swagger:route GET /admin/game-redblue/profit redblue getProfit
//
// Get game's profit & rate by giving date range
//
// Responses:
//
//	200: profitSummary
//	400:
//	500:
func (rh *redBlueHandler) getProfit(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)

	params := getProfitParams{}
	if err := c.Bind(&params); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	res, err := rh.gamecoinpool.GetProfit(
		context, models.GameCoinPoolSourceTypeRedBlueGame, params.StartDate, params.EndDate,
	)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, res)
}

// swagger:parameters getPool
type getPoolParams struct {
	StartDate string `form:"start_date"`
	EndDate   string `form:"end_date"`
}

// swagger:route GET /admin/game-redblue/pool redblue getPool
//
// # Get game's pool summary by giving date range
//
// Responses:
//
//	200: poolSummary
//	400:
//	500:
func (rh *redBlueHandler) getPool(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)

	params := getPoolParams{}
	if err := c.Bind(&params); err != nil {
		Error(c, models.BadRequestError)
		return
	}

	res, err := rh.gamecoinpool.GetPool(
		context, models.GameCoinPoolSourceTypeRedBlueGame, params.StartDate, params.EndDate,
	)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, res)
}

// swagger:route GET /admin/game-redblue/info redblue getInfoForAdmin
//
// # Get game's info
//
// Responses:
//
//	200: redBlueGameAdminInfo
//	400:
//	500:
func (rh *redBlueHandler) getInfoForAdmin(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)

	res, err := rh.redblue.GetForAdmin(
		context,
	)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, res)
}

// swagger:model redblueGameHistoryForAdminResp
type redblueGameHistoryForAdminResp struct {
	Cursor string                           `json:"cursor"`
	Data   []*models.RedBlueGameHistoryInfo `json:"data"`
}

// swagger:route GET /admin/game-redblue/history redblue historyForAdmin
//
// # Get game history
//
// Responses:
//
//	200: redblueGameHistoryForAdminResp
//	400:
//	500:
func (rh *redBlueHandler) historyForAdmin(c *gin.Context) {
	context := c.MustGet("ctx").(ctx.CTX)

	res, err := rh.redblue.GetHistoryForAdmin(
		context,
	)
	if err != nil {
		Error(c, err)
		return
	}

	JSON(c, http.StatusOK, redblueGameHistoryForAdminResp{
		Cursor: "",
		Data:   res,
	})
}
