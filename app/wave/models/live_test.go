package models

import (
	"errors"
	"reflect"
	"testing"
)

func TestBulletMessageType_ToCompensationBaggageSrcID(t *testing.T) {
	tests := []struct {
		name string
		m    BulletMessageType
		want string
		err  error
	}{
		{
			name: "original bullet",
			m:    BulletMessageTypeOriginal,
			want: CompensationOriginalBulletBaggageSrcID,
			err:  nil,
		},
		{
			name: "red bullet",
			m:    BulletMessageTypeRed,
			want: CompensationRedBulletBaggageSrcID,
			err:  nil,
		},
		{
			name: "all room bullet",
			m:    BulletMessageTypeAllRoom,
			want: CompensationAllRoomBulletBaggageSrcID,
			err:  nil,
		},
		{
			name: "royal bullet",
			m:    BulletMessageTypeRoyal,
			want: "",
			err:  ErrUnsupportedConversion,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.m.ToCompensationBaggageSrcID()
			if !errors.Is(err, tt.err) {
				t.Errorf("ToCompensationBaggageSrcID() error = %v, wantErr %v", err, tt.err)
				return
			}
			if got != tt.want {
				t.Errorf("ToCompensationBaggageSrcID() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseGroupcallProgress(t *testing.T) {
	type args struct {
		rawValue string
	}
	tests := []struct {
		name    string
		args    args
		want    *GroupcallProgress
		wantErr error
	}{
		{
			name: "invalid format",
			args: args{
				rawValue: "",
			},
			want:    nil,
			wantErr: ErrInvalidRawGroupcallProgressFormat,
		},
		{
			name: "invalid createdAt",
			args: args{
				rawValue: "invalidCreatedAt::1733816531",
			},
			want:    nil,
			wantErr: ErrInvalidGroupcallProgressCreatedAt,
		},
		{
			name: "invalid expiredAt",
			args: args{
				rawValue: "1733816550::invalidExpiredAt",
			},
			want:    nil,
			wantErr: ErrInvalidGroupcallProgressExpiredAt,
		},
		{
			name: "normal case",
			args: args{
				rawValue: "1733816550::1733816531",
			},
			want: &GroupcallProgress{
				CreatedAt: 1733816550,
				ExpiredAt: 1733816531,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseGroupcallProgress(tt.args.rawValue)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("ParseGroupcallProgress() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseGroupcallProgress() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGroupcallProgress_ToRedisValue(t *testing.T) {
	type fields struct {
		CreatedAt int64
		ExpiredAt int64
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "normal case",
			fields: fields{
				CreatedAt: 1733816550,
				ExpiredAt: 1733816531,
			},
			want: "1733816550::1733816531",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := GroupcallProgress{
				CreatedAt: tt.fields.CreatedAt,
				ExpiredAt: tt.fields.ExpiredAt,
			}
			if got := p.ToRedisValue(); got != tt.want {
				t.Errorf("ToRedisValue() = %v, want %v", got, tt.want)
			}
		})
	}
}
