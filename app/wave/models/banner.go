package models

import (
	bversion "github.com/17media/api/base/version"
	"github.com/17media/api/service/storage/gcs"
)

const (
	supportMotionVersion     = "5.20.0"
	supportDecocenterVersion = "5.36.0"
)

// BannerActionType is type for different banner action
// | Type | Value |
// | --- | --- |
// | BannerActionTypeDummy    | 0 |
// | BannerActionTypeRequestNotificationPerm  | 1 |
// | BannerActionTypeOpenWebPage  | 2 |
// | BannerActionTypeFollowUser  | 3 |
// | BannerActionTypeOpenWebPage  | 4 |
// | BannerActionTypeOpenDecocenterJoinLiveEffectTab  | 5 |
// | BannerActionTypeOpenDecocenterBadgeTab  | 6 |
// | BannerActionTypeOpenDecocenterChatFrameTab  | 7 |
// | BannerActionTypeOpenDecocenterPhotoFrameTab  | 8 |
type BannerActionType int

const (
	// BannerActionTypeDummy is a dummy type
	BannerActionTypeDummy BannerActionType = iota

	// BannerActionTypeRequestNotificationPerm is a type of banner that request user's notification permission
	BannerActionTypeRequestNotificationPerm

	// BannerActionTypeOpenWebPage is a type of banner that open a web view with web_url
	BannerActionTypeOpenWebPage

	// BannerActionTypeFollowUser is a type of banner that follow the target user directly
	BannerActionTypeFollowUser

	// BannerActionTypeOpenUserProfile is a type of banner that opens target user's profile
	BannerActionTypeOpenUserProfile

	// BannerActionTypeOpenDecocenterJoinLiveEffectTab is a type of banner that open decocenter join live effect tab
	BannerActionTypeOpenDecocenterJoinLiveEffectTab

	// BannerActionTypeOpenDecocenterBadgeTab is a type of banner that open decocenter badge tab
	BannerActionTypeOpenDecocenterBadgeTab

	// BannerActionTypeOpenDecocenterChatFrameTab is a type of banner that open decocenter chat frame tab
	BannerActionTypeOpenDecocenterChatFrameTab

	// BannerActionTypeOpenDecocenterPhotoFrameTab is a type of banner that open decocenter photo frame tab
	BannerActionTypeOpenDecocenterPhotoFrameTab
)

// BannerStatus is type for current banner status
// | Status | Value |
// | --- | --- |
// | BannerStatusFuture     | 0 |
// | BannerStatusEnded      | 1 |
// | BannerStatusActive     | 2 |
// | BannerStatusFuture     | 3 |
// | BannerStatusAvailable  | 4 |
type BannerStatus int

const (
	// BannerStatusDummy is a dummy status
	BannerStatusDummy BannerStatus = iota
	// BannerStatusEnded is for the banners that has ended
	BannerStatusEnded
	// BannerStatusActive is for the banners that is active now
	BannerStatusActive
	// BannerStatusFuture is for the banners that will be in the future
	BannerStatusFuture
	// BannerStatusAvailable is for the banners that hasn't ended
	BannerStatusAvailable
)

// BannerType is the type for banner
type BannerType int

const (
	// BannerTypeTop is the banner on the top of homepage
	BannerTypeTop BannerType = iota
	// BannerTypeShow is the banner in the live list
	BannerTypeShow
	// BannerTypeOfficialShow is the banner at discover tab
	BannerTypeOfficialShow
)

// Banner records store in mongodb
type Banner struct {
	ID         string           `bson:"id" json:"id"`
	Type       BannerType       `bson:"type" json:"type"`
	Label      string           `bson:"label" json:"label"`
	ImageURL   string           `bson:"image_url" json:"image_url"`
	MotionURL  string           `bson:"motion_url" json:"motion_url"`
	ActionType BannerActionType `bson:"action_type" json:"action_type"`
	WebURL     string           `bson:"web_url" json:"web_url"`
	UserID     string           `bson:"user_id" json:"user_id"`
	StartedAt  int64            `bson:"started_at" json:"started_at"`
	EndedAt    int64            `bson:"ended_at" json:"ended_at"`
	CreatedAt  int64            `bson:"created_at" json:"created_at"`
	Weight     int32            `bson:"weight" json:"weight"`
	Region     string           `bson:"region" json:"region"`
}

// DisplayBanner display for users
// swagger:model waveDisplayBanner
type DisplayBanner struct {
	ID         string           `json:"id"`
	Type       BannerType       `json:"type"`
	Label      string           `json:"label"`
	ImageURL   string           `json:"image_url"`
	MotionURL  string           `json:"motion_url"`
	MediaURL   string           `json:"media_url"`
	ActionType BannerActionType `json:"action_type"`
	WebURL     string           `json:"web_url"`
	UserID     string           `json:"user_id"`
	StartedAt  int64            `json:"started_at"`
	EndedAt    int64            `json:"ended_at"`
	Weight     *int32           `json:"weight,omitempty"`
	Region     *string          `json:"region,omitempty"`
}

// BannerParams for creating or updating banner
// swagger:parameters adminCreateBanner adminPatchBanner
type BannerParams struct {
	// in:body
	ImageURL *string `json:"image_url"`
	// in:body
	MotionURL *string `json:"motion_url"`
	// in:body
	Type *BannerType `json:"type"`
	// in:body
	Label *string `json:"label"`
	// in:body
	ActionType *BannerActionType `json:"action_type"`
	// in:body
	WebURL *string `json:"web_url"`
	// in:body
	UserID *string `json:"user_id"`
	// in:body
	StartedAt *int64 `json:"started_at"`
	// in:body
	EndedAt *int64 `json:"ended_at"`
	// in:body
	Weight *int32 `json:"weight"`
	// in:body
	Region *string `json:"region"`
}

// ParseBannerFunc is the functional args for parsing banner
type ParseBannerFunc func(*parseBannerOption)

type parseBannerOption struct {
	isAdmin bool
	version string
}

// BannerForAdmin is to parse banner by admin
func BannerForAdmin() ParseBannerFunc {
	return func(o *parseBannerOption) {
		o.isAdmin = true
	}
}

// BannerWithVersion is to parse banner by user's app version
func BannerWithVersion(version string) ParseBannerFunc {
	return func(o *parseBannerOption) {
		o.version = version
	}
}

// Parse is for parsing data to display format
func (b *Banner) Parse(gcsHelper *gcs.Helper, opts ...ParseBannerFunc) *DisplayBanner {
	o := &parseBannerOption{}
	for _, f := range opts {
		f(o)
	}

	db := &DisplayBanner{
		ID:         b.ID,
		Type:       b.Type,
		Label:      b.Label,
		ImageURL:   gcsHelper.GetCDN(b.ImageURL),
		MotionURL:  gcsHelper.GetCDN(b.MotionURL),
		ActionType: b.ActionType,
		WebURL:     b.WebURL,
		UserID:     b.UserID,
		StartedAt:  b.StartedAt,
		EndedAt:    b.EndedAt,
	}

	if o.isAdmin {
		db.Weight = &b.Weight
		db.Region = &b.Region
	}

	if o.version != "" && bversion.CompareVersion(o.version, supportMotionVersion) >= 0 && b.MotionURL != "" {
		db.MediaURL = db.MotionURL
	} else {
		db.MediaURL = db.ImageURL
	}

	// TODO: remove after 2023/10/01
	if o.version != "" && bversion.CompareVersion(o.version, supportDecocenterVersion) == -1 && b.ActionType >= BannerActionTypeOpenDecocenterJoinLiveEffectTab {
		db.ActionType = BannerActionTypeDummy
	}

	return db
}
