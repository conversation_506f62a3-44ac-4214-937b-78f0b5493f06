// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import basegoroutine "github.com/17media/api/base/goroutine"

import mock "github.com/stretchr/testify/mock"
import time "time"

// Pool is an autogenerated mock type for the Pool type
type Pool struct {
	mock.Mock
}

// Close provides a mock function with given fields:
func (_m *Pool) Close() {
	_m.Called()
}

// GetSize provides a mock function with given fields:
func (_m *Pool) GetSize() int {
	ret := _m.Called()

	var r0 int
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	return r0
}

// GracefulClose provides a mock function with given fields: options
func (_m *Pool) GracefulClose(options ...basegoroutine.PoolOption) {
	_va := make([]interface{}, len(options))
	for _i := range options {
		_va[_i] = options[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	_m.Called(_ca...)
}

// Schedule provides a mock function with given fields: task
func (_m *Pool) Schedule(task func()) error {
	ret := _m.Called(task)

	var r0 error
	if rf, ok := ret.Get(0).(func(func()) error); ok {
		r0 = rf(task)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ScheduleTimeout provides a mock function with given fields: timeout, task
func (_m *Pool) ScheduleTimeout(timeout time.Duration, task func()) error {
	ret := _m.Called(timeout, task)

	var r0 error
	if rf, ok := ret.Get(0).(func(time.Duration, func()) error); ok {
		r0 = rf(timeout, task)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}
