package main

import (
	"flag"
	"fmt"

	"github.com/jmoiron/sqlx"

	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/app/wave/stores/user"
	"github.com/17media/api/base/ctx"
	mdb "github.com/17media/api/base/db"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/setup/mysql/mysqlgiftwriter"
	"github.com/17media/logrus"
)

const (
	dryRun = true
)

type record struct {
	UserID    string `db:"userID"`
	Timestamp int64  `db:"timestamp"`
}

func main() {
	m := dimanager.DefaultManager
	m.Compile()
	flag.Parse()

	context := ctx.Background()
	db := mysqlgiftwriter.GetMySQLGiftWriter(m)
	dbx := sqlx.NewDb(db, mdb.SQLDriver)
	userStore := user.GetUser(m)

	records := []*record{}
	s := `
		SELECT
			requestUserID as userID,
			FLOOR(MIN(purchaseTimeMs)/1000) as timestamp
		FROM
			Receipt
		GROUP BY requestUserID
`
	if err := dbx.Select(&records, s); err != nil {
		context.WithFields(logrus.Fields{"err": err}).Error("sqlx.Select error")
		return
	}

	for _, r := range records {
		ups := &models.UserParams{
			FirstPaidAt: &r.Timestamp,
		}

		if dryRun {
			fmt.Println(r.UserID, r.Timestamp)
			continue
		}

		if err := userStore.Patch(context, r.UserID, ups); err != nil {
			panic(err)
		}
	}
}
