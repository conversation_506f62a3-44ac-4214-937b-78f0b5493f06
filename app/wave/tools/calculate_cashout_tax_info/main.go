package main

import (
	"flag"
	"fmt"

	"github.com/jmoiron/sqlx"

	"github.com/17media/api/app/wave/base/script"
	"github.com/17media/api/app/wave/models"
	"github.com/17media/api/app/wave/stores/cashout"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/setup/mysql/mysqlgiftwriter"
)

const (
	dryRun = true
)

var (
	cashoutIDs = []int64{}
	// optionFuncs: cashout.IsDryRun(), cashout.SkipIsCalculatedCheck()
	optionFuncs = []cashout.CalculateCashoutTaxInfoOptionFunc{cashout.IsDryRun(), cashout.SkipIsCalculatedCheck()}
)

// original version
func main() {
	m := dimanager.DefaultManager
	m.Compile()

	flag.Parse()
	script.LoadDependency(m)

	context := ctx.Background()
	cashoutStore := cashout.GetCashout(m)
	dbx := sqlx.NewDb(mysqlgiftwriter.GetMySQLGiftWriter(m), "mysql")
	cashouts := []*models.Cashout{}
	sql := "SELECT id,userID,point,microPoint,currency,amount,createTime,status,tradeID,healthInsuranceFeeType,isForeigner,note,cashoutDate FROM Cashout WHERE id IN (?)"
	query, args, err := sqlx.In(sql, cashoutIDs)
	if err != nil {
		fmt.Printf("sqlx.In error: %+v", err)
		return
	}

	if err := dbx.Select(&cashouts, query, args...); err != nil {
		fmt.Printf("select Cashout table error: %+v", err)
		return
	}

	success := 0
	for _, c := range cashouts {
		fmt.Printf("id: %d, currency: %s, amount: %d, status: %d \n", c.ID, c.Currency, c.Amount, c.Status)
		if !dryRun {
			if err := cashoutStore.CalculateCashoutTaxInfo(context, c.ID, optionFuncs...); err != nil {
				fmt.Printf("CalculateCashoutTaxInfo failed: %v\n", err)
				continue
			}
			success += 1
		}
	}

	fmt.Printf("total: %d, success: %d\n", len(cashouts), success)
	fmt.Println("Done.")
}
