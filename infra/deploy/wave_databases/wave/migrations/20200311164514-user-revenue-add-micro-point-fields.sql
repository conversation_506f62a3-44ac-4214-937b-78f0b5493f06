-- +migrate Up
ALTER TABLE `User`
  ADD COLUMN `microRevenuePoint` bigint(20) NOT NULL DEFAULT 0 AFTER `revenuePoint`,
  ADD COLUMN `microCashablePoint` bigint(20) NOT NULL DEFAULT 0 AFTER `cashablePoint`;

ALTER TABLE `RevenueToCash`
  ADD COLUMN `microRevenuePoint` bigint(20) NOT NULL DEFAULT 0 AFTER `revenuePoint`;

ALTER TABLE `Cashout`
  ADD COLUMN `microPoint` bigint(20) NOT NULL DEFAULT 0 AFTER `point`;

-- +migrate Down
ALTER TABLE `User` DROP COLUMN `microRevenuePoint`, `microCashablePoint`;

ALTER TABLE `RevenueToCash` DROP COLUMN `microRevenuePoint`;

ALTER TABLE `Cashout` DROP COLUMN `microPoint`;
