kind: DaemonSet
apiVersion: extensions/v1beta1
metadata:
  name: daemonset
  namespace: kube-system
  labels:
    app: startup-script
spec:
  selector:
    matchLabels:
      name: daemonset
      app: daemonset
  template:
    metadata:
      labels:
        name: daemonset
        app: daemonset
    spec:
      hostPID: true
      terminationGracePeriodSeconds: 30
      containers:
      - name: daemonset
        image: gcr.io/google-containers/startup-script:v1
        securityContext:
          privileged: true
        env:
        - name: STARTUP_SCRIPT
          value: |
            #! /usr/bin/env bash
            set -o errexit
            set -o pipefail
            set -o nounset

            echo '* soft nofile 262144' >> /etc/security/limits.conf
            echo '* hard nofile 262144' >> /etc/security/limits.conf

            sysctl -w fs.inotify.max_user_watches=100000
            sysctl -w vm.max_map_count=262144
