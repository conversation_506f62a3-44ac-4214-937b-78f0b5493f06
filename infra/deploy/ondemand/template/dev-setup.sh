#!/bin/bash

COMPOSE_VERSION="1.22.0"
GO_VERSION="1.13.5"
RG_VERSION="0.10.0"
PROTOC_VERSION="3.3.0"

DOCKER_USER="adventurer"
DOCKER_PASS="peorth"

UBUNTU_USER="ubuntu"
UBUNTU_HOME="/home/<USER>"


echo "==>"
echo "==> setup basic requirements (SHELL: ${SHELL})"
echo "==>"

sudo apt update
sudo apt install -y wget git vim curl tmux telnet zsh jq autojump tig arcanist silversearcher-ag zip python-pip


echo "==>"
echo "==> setup docker"
echo "==>"

if [ -z "$(which docker)" ]; then
  curl -fsSL https://get.docker.com | sudo CHANNEL="stable" sh -
  sudo usermod -aG docker ${UBUNTU_USER}
else
  echo "skip"
fi


echo "==>"
echo "==> setup docker-compose"
echo "==>"

if [ -z "$(which docker-compose)" ]; then
  sudo curl -o /usr/local/bin/docker-compose \
            -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)"
  sudo chmod +x /usr/local/bin/docker-compose
else
  echo "skip"
fi


echo "==>"
echo "==> setup docker login"
echo "==>"

sudo su - ${UBUNTU_USER} -c "docker login -u ${DOCKER_USER} -p ${DOCKER_PASS}"


echo "==>"
echo "==> setup rg"
echo "==>"

if [ -z "$(which rg)" ]; then
  curl -o /tmp/ripgrep_${RG_VERSION}_amd64.deb \
       -L https://github.com/BurntSushi/ripgrep/releases/download/${RG_VERSION}/ripgrep_${RG_VERSION}_amd64.deb
  sudo dpkg -i /tmp/ripgrep_${RG_VERSION}_amd64.deb
  rm -f /tmp/ripgrep_${RG_VERSION}_amd64.deb
else
  echo "skip"
fi


echo "==>"
echo "==> setup golang ($GO_VERSION)"
echo "==>"

if [ ! -e "/usr/local/go/bin/go" ] || ! (echo "$(/usr/local/go/bin/go version)" | grep "${GO_VERSION}"); then
  curl https://dl.google.com/go/go${GO_VERSION}.linux-amd64.tar.gz | sudo tar -C /usr/local -zxf -
else
  echo "skip"
fi


echo "==>"
echo "==> setup protobuf"
echo "==>"

if [ ! -e "/usr/local/bin/protoc" ]; then
  curl -o /tmp/protoc-${PROTOC_VERSION}-linux-x86_64.zip \
       -L https://github.com/google/protobuf/releases/download/v${PROTOC_VERSION}/protoc-${PROTOC_VERSION}-linux-x86_64.zip
  unzip /tmp/protoc-${PROTOC_VERSION}-linux-x86_64.zip -d /tmp/protoc3
  rm -rf /tmp/protoc-${PROTOC_VERSION}-linux-x86_64.zip

  sudo mv /tmp/protoc3/bin/* /usr/local/bin/
  sudo mv /tmp/protoc3/include/* /usr/local/include/
fi


echo "==>"
echo "==> setup oh-my-zsh"
echo "==>"

if [ ! -d "${UBUNTU_HOME}/.oh-my-zsh" ]; then
  sudo su - ${UBUNTU_USER} -c "git clone https://github.com/robbyrussell/oh-my-zsh.git \${HOME}/.oh-my-zsh"
  sudo su - ${UBUNTU_USER} -c "cp \${HOME}/.oh-my-zsh/templates/zshrc.zsh-template ~/.zshrc"

  # Fix permission issue for zsh under ubuntu
  sudo su - ${UBUNTU_USER} -c "chmod g-w,o-w \${HOME}/.oh-my-zsh/plugins/git"
  sudo su - ${UBUNTU_USER} -c "chmod g-w,o-w \${HOME}/.oh-my-zsh/plugins"
  sudo su - ${UBUNTU_USER} -c "chmod g-w,o-w \${HOME}/.oh-my-zsh"
else
  echo "skip"
fi


echo "==>"
echo "==> ensure shell environment setup (ZSH)"
echo "==>"

if ! grep "GOPATH" ${UBUNTU_HOME}/.zshrc; then
  echo "
export PATH=\${PATH}:/usr/local/go/bin
export GOROOT=/usr/local/go
export PATH=\${PATH}:\${GOROOT}/bin
export GOPATH=\${HOME}/workspace/go
export PATH=\${PATH}:\${GOPATH}/bin
" | sudo su - ${UBUNTU_USER} -c "tee -a \${HOME}/.zshrc"
else
  echo "skip"
fi


echo "==>"
echo "==> ensure shell environment setup (BASH)"
echo "==>"

if [ ! -f "${UBUNTU_HOME}/.bash_profile" ]; then
  echo "# .bash_profile

# Get the aliases and functions
if [ -f ~/.bashrc ]; then
  . ~/.bashrc
fi

export GOPATH=\${HOME}/workspace/go
export PATH=\${PATH}:\${HOME}/.local/bin:\$HOME/bin:/usr/local/go/bin:\${PATH}:\${GOPATH}/bin
" | sudo su - ${UBUNTU_USER} -c "tee \${HOME}/.bash_profile"
else
  echo "skip"
fi


echo "==>"
echo "==> setup private key for github pull (read-only)"
echo "==>"

sudo rm -vf ${UBUNTU_HOME}/.ssh/github
echo "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" | sudo su - ${UBUNTU_USER} -c "tee -a \${HOME}/.ssh/github"

sudo chmod 0400 ${UBUNTU_HOME}/.ssh/github

echo "==>"
echo "==> setup ssh config for github.com access"
echo "==>"

echo "Host github.com
    StrictHostKeyChecking no
    IdentityFile ~/.ssh/github" | sudo su - ${UBUNTU_USER} -c "tee -a \${HOME}/.ssh/config"

sudo chmod 0600 ${UBUNTU_HOME}/.ssh/config


echo "==>"
echo "==> setup default access key for 'ubuntu' user"
echo "==>"

echo 'ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDYG05UiDUGZsdh349FpbtP01jf6VeOhcDmykZ+dn8ZoUCTuNHXPxeYIQmrVq8G42gcNIFG9wmCS2gqGffOdlLQ6Wu+Sdsa4IlBSQvpOrZZIiRvVNrZqg6AcjWKZY4M6bI4mgH/N6vIUjjB1M+oBfZYwx6vBlR7LcpnMzBl+qNG4b42Vb9QKEE6Qp2eOlOt2q3KPDUgRU7JlOMEEeyEgPBT/Z0Zn/TQ8SpzgsqFm3rOtqnhaDToh+ItGDG8gvUfD8lwYpSukrhrhLCwpSVkhH5E6JP4/LZqa+wyHC1hnmsdz+lSQQ+0TUEQrnqf7TH1Jtyyt4f8qkyHcOtDXbRhCPmL <EMAIL>' | sudo su - ${UBUNTU_USER} -c "tee -a \${HOME}/.ssh/authorized_keys"

sudo chmod 0600 ${UBUNTU_HOME}/.ssh/authorized_keys


echo "==>"
echo "==> setup git config"
echo "==>"

sudo su - ${UBUNTU_USER} -c "git config --global url.'**************:'.insteadOf 'https://github.com/'"


echo "==>"
echo "==> go mod tidy"
echo "==>"

sudo su - ${UBUNTU_USER} -c "source \${HOME}/.bashrc && \
  ( go get -u github.com/17media/api || true ) && \
  cd \${HOME}/workspace/go/src/github.com/17media/api && \
  echo 'be patient!!! first time go mod tidy required minutes to run...' && \
  go mod tidy"


echo "==>"
echo "==> setup python requirements"
echo "==>"

# HACK: don't waste time on ansible installation
sed '/ansible/d' ${UBUNTU_HOME}/workspace/go/src/github.com/17media/api/infra/requirements.txt | tee /tmp/requirements.txt
sudo su - ${UBUNTU_USER} -c 'pip install -r /tmp/requirements.txt'


echo "==>"
echo "==> setup default login shell for user 'ubuntu'"
echo "==>"

sudo chsh -s /usr/bin/zsh ${UBUNTU_USER}


echo "==>"
echo "==> all completed"
echo "==>"
