# This script is used to setup the necessary virtual machines for the dataset
# migration. Run this script to prepare for the dataset migration.
#!/bin/bash

set -o xtrace
set -o errexit
set -o pipefail

if [ $# -ne 2 ]; then
    echo "Error! Invalid number of arguments."
    echo "Usage: ./datasetMigrationSetup.sh \
[staging|production] [initialize|cleanup]"
    exit 1
fi

RUN_ENV=$1
RUN_MODE=$2

if [ "$RUN_ENV" != "staging" ] && [ "$RUN_ENV" != "production" ]; then
    echo "Error! Invalid run environment."
    echo "Usage: ./datasetMigrationSetup.sh \
[staging|production] [initialize|cleanup]"
    exit 1
fi

if [ "$RUN_MODE" != "initialize" ] && [ "$RUN_MODE" != "cleanup" ]; then
    echo "Error! Invalid run mode."
    echo "Usage: ./datasetMigrationSetup.sh \
[staging|production] [initialize|cleanup]"
    exit 1
fi

function createNetworkIPs()
{
    local MACHINE_NAME=$1
    local PROJECT_ID=$2
    local REGION=$3

    # Creating internal IP for instance
    gcloud compute addresses create "internal-ip-$MACHINE_NAME" \
        --project $PROJECT_ID                                   \
        --region $REGION                                        \
        --subnet 'tf-network'                                   \
        --ip-version 'IPV4'

    # Creating external IP for instance
    gcloud compute addresses create "external-ip-$MACHINE_NAME" \
        --project $PROJECT_ID                                   \
        --region $REGION
}

function createDisks()
{
    local MACHINE_NAME=$1
    local PROJECT_ID=$2
    local ZONE=$3
    local SIZE=$4
    local SOURCE_SNAPSHOT=$5

    # Creating additional data disk for instance
    gcloud compute disks create "$MACHINE_NAME-data" \
        --project $PROJECT_ID                        \
        --zone $ZONE                                 \
        --size $SIZE                                 \
        --type 'pd-ssd'                              \
        --source-snapshot $SOURCE_SNAPSHOT
}

function createVM()
{
    local MACHINE_NAME=$1
    local PROJECT_ID=$2
    local ZONE=$3
    local CPU=$4
    local MEM=$5
    local TAGS=$6
    local DB=$7

    gcloud compute instances create $MACHINE_NAME       \
--project $PROJECT_ID                                   \
--zone $ZONE                                            \
--min-cpu-platform 'Intel Skylake'                      \
--custom-extensions                                     \
--custom-cpu $CPU                                       \
--custom-memory $MEM                                    \
--boot-disk-type 'pd-ssd'                               \
--boot-disk-size '500GB'                                \
--no-boot-disk-auto-delete                              \
--disk "name=$MACHINE_NAME-data,mode=rw,device-name=data\
,boot=no,auto-delete=no"                                \
--image-project 'media17-stag'                          \
--image 'media17-ami-ubuntu-1604-mysql-5-7-201811221122'\
--network 'tf-network'                                  \
--subnet 'tf-network'                                   \
--address "external-ip-$MACHINE_NAME"                   \
--private-network-ip "internal-ip-$MACHINE_NAME"        \
--tags $TAGS                                            \
--labels "db=$DB,mysql_version=5_7_24,service=mysql"    \
--deletion-protection                                   \
--scopes 'compute-ro','default'                         \
--metadata shutdown-script='#!/bin/bash
systemctl stop fluentd.service
systemctl stop maxwell.service
systemctl stop datadog-agent
systemctl stop mysql'
}

function deleteResource()
{
    local PROJECT_ID=$1
    local RESOURCE_TYPE=$2
    local RESOURCE_NAME=$3

    if [ "$RESOURCE_TYPE" == "disks" ] || \
        [ "$RESOURCE_TYPE" == "instances" ]; then
        ZONE=`gcloud compute disks list --format json |       \
            jq -r --arg RESOURCE_NAME "$RESOURCE_NAME"        \
            '.[] | select(.name == $RESOURCE_NAME) | .zone' | \
            awk -F[/] '{print $NF}'`
        gcloud compute            \
            --project $PROJECT_ID \
            $RESOURCE_TYPE delete \
            --zone $ZONE          \
            $RESOURCE_NAME

    elif [ "$RESOURCE_TYPE" == "addresses" ]; then
        REGION=`gcloud compute addresses list --format json |   \
            jq -r --arg RESOURCE_NAME "$RESOURCE_NAME"          \
            '.[] | select(.name == $RESOURCE_NAME) | .region' | \
            awk -F[/] '{print $NF}'`
        gcloud compute            \
            --project $PROJECT_ID \
            $RESOURCE_TYPE delete \
            --region $REGION      \
            $RESOURCE_NAME
    fi
}

function copyScript()
{
    local PROJECT_ID=$1
    local LOCAL_PATH=$2
    local INSTANCE_NAME=$3
    local REMOTE_PATH=$4

    echo "Wait 15 seconds for $INSTANCE_NAME to be ready for scp."
    sleep 15

    gcloud compute --project $PROJECT_ID scp $LOCAL_PATH $INSTANCE_NAME:$REMOTE_PATH
}

if [ $RUN_MODE == "initialize" ]; then
    echo "Initializing your $RUN_ENV migration cluster.\n"
    if [ "$RUN_ENV" == "staging" ]; then
        echo "Creating network IP addresses."
        createNetworkIPs 'mysql-gift-1-backup' 'media17-stag' 'asia-east1'
        createNetworkIPs 'mysql-report-1-backup' 'media17-stag' 'asia-east1'

        echo "Creating data disks."
        createDisks                                       \
            'mysql-gift-1-backup'                         \
            'media17-stag'                                \
            'asia-east1-c'                                \
            '1000GB'                                      \
            'mysql-gift-golden-version-snapshot-20181212'

        createDisks                                         \
            'mysql-report-1-backup'                         \
            'media17-stag'                                  \
            'asia-east1-c'                                  \
            '500GB'                                         \
            'mysql-report-golden-version-snapshot-20181212'

        echo "Creating virtual machines."
        createVM                  \
            'mysql-gift-1-backup' \
            'media17-stag'        \
            'asia-east1-c'        \
            '8'                   \
            '52GB'                \
            'mysql'               \
            'gift'

        createVM                    \
            'mysql-report-1-backup' \
            'media17-stag'          \
            'asia-east1-c'          \
            '4'                     \
            '26GB'                  \
            'mysql'                 \
            'report'

        echo "Copy necessary scripts into VM"
        copyScript                                                    \
            'media17-stag'                                            \
            "$GOPATH/src/github.com/17media/api/infra/deploy/3rdparty/\
mysql-dataset-migration/datasetMigrationInternalSetup.sh"             \
            'mysql-gift-1-backup'                                     \
            '~/datasetMigrationInternalSetup.sh'

        copyScript                                                    \
            'media17-stag'                                            \
            "$GOPATH/src/github.com/17media/api/infra/deploy/3rdparty/\
mysql-dataset-migration/datasetMigrationInternalSetup.sh"             \
            'mysql-report-1-backup'                                   \
            '~/datasetMigrationInternalSetup.sh'
    fi

    if [ "$RUN_ENV" == "production" ]; then
        echo "Creating network IP addresses."
        createNetworkIPs                 \
            'mysql-gift-oregon-4-backup' \
            'media17-prod'               \
            'us-west1'

        createNetworkIPs                 \
            'mysql-gift-oregon-5-backup' \
            'media17-prod'               \
            'us-west1'

        createNetworkIPs                   \
            'mysql-report-oregon-3-backup' \
            'media17-prod'                 \
            'us-west1'

        createNetworkIPs                   \
            'mysql-report-oregon-5-backup' \
            'media17-prod'                 \
            'us-west1'

        echo "Creating data disks."
        createDisks                      \
            'mysql-gift-oregon-4-backup' \
            'media17-prod'               \
            'us-west1-c'                 \
            '1000GB'                     \
            `gcloud compute snapshots list | awk '/gift/ {print $1}' | tail -1`

        createDisks                      \
            'mysql-gift-oregon-5-backup' \
            'media17-prod'               \
            'us-west1-a'                 \
            '1000GB'                     \
            `gcloud compute snapshots list | awk '/gift/ {print $1}' | tail -1`

        createDisks                        \
            'mysql-report-oregon-3-backup' \
            'media17-prod'                 \
            'us-west1-c'                   \
            '500GB'                        \
            `gcloud compute snapshots list | awk '/report/ {print $1}' | tail -1`

        createDisks                        \
            'mysql-report-oregon-5-backup' \
            'media17-prod'                 \
            'us-west1-a'                   \
            '500GB'                        \
            `gcloud compute snapshots list | awk '/report/ {print $1}' | tail -1`

        echo "Creating virtual machines."
        createVM                                                                \
            'mysql-gift-oregon-4-backup'                                        \
            'media17-prod'                                                      \
            'us-west1-c'                                                        \
            '32'                                                                \
            '208GB'                                                             \
            'deploy-target,mysql,mysql-digdag,mysql-replications,percona-ports' \
            'gift'

        createVM                                                                \
            'mysql-gift-oregon-5-backup'                                        \
            'media17-prod'                                                      \
            'us-west1-a'                                                        \
            '32'                                                                \
            '208GB'                                                             \
            'deploy-target,mysql,mysql-digdag,mysql-replications,percona-ports' \
            'gift'

        createVM                                                                \
            'mysql-report-oregon-3-backup'                                      \
            'media17-prod'                                                      \
            'us-west1-c'                                                        \
            '4'                                                                 \
            '26GB'                                                              \
            'deploy-target,mysql,mysql-digdag,mysql-replications,percona-ports' \
            'report'

        createVM                                                                \
            'mysql-report-oregon-5-backup'                                      \
            'media17-prod'                                                      \
            'us-west1-a'                                                        \
            '4'                                                                 \
            '26GB'                                                              \
            'deploy-target,mysql,mysql-digdag,mysql-replications,percona-ports' \
            'report'

        echo "Wait 15 seconds for instances to be ready"
        sleep 15

        echo "Copy necessary scripts into VM"
        copyScript                                                    \
            'media17-prod'                                            \
            "$GOPATH/src/github.com/17media/api/infra/deploy/3rdparty/\
mysql-dataset-migration/datasetMigrationInternalSetup.sh"             \
            'mysql-gift-oregon-4-backup'                              \
            '~/datasetMigrationInternalSetup.sh'

        copyScript                                                    \
            'media17-prod'                                            \
            "$GOPATH/src/github.com/17media/api/infra/deploy/3rdparty/\
mysql-dataset-migration/datasetMigrationInternalSetup.sh"             \
            'mysql-gift-oregon-5-backup'                              \
            '~/datasetMigrationInternalSetup.sh'

        copyScript                                                    \
            'media17-prod'                                            \
            "$GOPATH/src/github.com/17media/api/infra/deploy/3rdparty/\
mysql-dataset-migration/datasetMigrationInternalSetup.sh"             \
            'mysql-report-oregon-3-backup'                            \
            '~/datasetMigrationInternalSetup.sh'

        copyScript                                                    \
            'media17-prod'                                            \
            "$GOPATH/src/github.com/17media/api/infra/deploy/3rdparty/\
mysql-dataset-migration/datasetMigrationInternalSetup.sh"             \
            'mysql-report-oregon-5-backup'                            \
            '~/datasetMigrationInternalSetup.sh'
    fi

    echo -e "\n\nCongratulations! All $RUN_ENV instance setup have completed!"
    echo '-------------------------------------'
    echo ''

elif [ "$RUN_MODE" == "cleanup" ]; then
    choice=""
    while [ "$choice" == "" ]; do
        read -p "You are about to remove all migration backup machines and its \
dependents. Are you sure you want to continue? (y/n) " choice
        case "$choice" in
            y|Y )
                echo "-- Continue"
                ;;
            n|N )
                echo "-- Break"
                exit 1
                ;;
            * )
                echo "-- Invalid input"
                choice=""
                ;;
        esac
    done

    if [ "$RUN_ENV" == "staging" ]; then
        echo -e '\t\t\t Removing delete prevention flags for disks.'
        gcloud compute --project 'media17-stag'      \
            instances update 'mysql-report-1-backup' \
            --no-deletion-protection

        gcloud compute --project 'media17-stag'    \
            instances update 'mysql-gift-1-backup' \
            --no-deletion-protection

        echo -e '\t\t\t Deleting virtual machines.'
        deleteResource             \
            'media17-stag'         \
            'instances'            \
            'mysql-report-1-backup'

        deleteResource            \
            'media17-stag'        \
            'instances'           \
            'mysql-gift-1-backup'

        echo -e '\t\t\t Deleting data disks.'
        deleteResource               \
            'media17-stag'           \
            'disks'                  \
            'mysql-report-1-backup'

        deleteResource                   \
            'media17-stag'               \
            'disks'                      \
            'mysql-report-1-backup-data'

        deleteResource            \
            'media17-stag'        \
            'disks'               \
            'mysql-gift-1-backup'

        deleteResource                 \
            'media17-stag'             \
            'disks'                    \
            'mysql-gift-1-backup-data'

        echo -e '\t\t\t Releasing static IPs.'
        deleteResource                          \
            'media17-stag'                      \
            'addresses'                         \
            'internal-ip-mysql-report-1-backup'

        deleteResource                          \
            'media17-stag'                      \
            'addresses'                         \
            'external-ip-mysql-report-1-backup'

        deleteResource                        \
            'media17-stag'                    \
            'addresses'                       \
            'internal-ip-mysql-gift-1-backup'

        deleteResource                         \
            'media17-stag'                     \
            'addresses'                        \
            'external-ip-mysql-gift-1-backup'

    elif [ "$RUN_ENV" == "production" ]; then
        echo -e '\t\t\t Removing delete prevention flags for disks.'
        gcloud compute --project 'media17-prod'             \
            instances update 'mysql-report-oregon-3-backup' \
            --no-deletion-protection

        gcloud compute --project 'media17-prod'             \
            instances update 'mysql-report-oregon-5-backup' \
            --no-deletion-protection

        gcloud compute --project 'media17-prod'           \
            instances update 'mysql-gift-oregon-4-backup' \
            --no-deletion-protection

        gcloud compute --project 'media17-prod'           \
            instances update 'mysql-gift-oregon-5-backup' \
            --no-deletion-protection

        echo -e '\t\t\t Deleting virtual machines.'
        deleteResource                     \
            'media17-prod'                 \
            'instances'                    \
            'mysql-report-oregon-3-backup'

        deleteResource                     \
            'media17-prod'                 \
            'instances'                    \
            'mysql-report-oregon-5-backup'

        deleteResource                   \
            'media17-prod'               \
            'instances'                  \
            'mysql-gift-oregon-4-backup'

        deleteResource                   \
            'media17-prod'               \
            'instances'                  \
            'mysql-gift-oregon-5-backup'

        echo -e '\t\t\t Deleting data disks.'
        deleteResource                    \
            'media17-prod'                \
            'disks'                       \
            'mysql-report-oregon-3-backup'

        deleteResource                          \
            'media17-prod'                      \
            'disks'                             \
            'mysql-report-oregon-3-backup-data'

        deleteResource                    \
            'media17-prod'                \
            'disks'                       \
            'mysql-report-oregon-5-backup'

        deleteResource                          \
            'media17-prod'                      \
            'disks'                             \
            'mysql-report-oregon-5-backup-data'

        deleteResource                  \
            'media17-prod'              \
            'disks'                     \
            'mysql-gift-oregon-4-backup'

        deleteResource                         \
            'media17-prod'                     \
            'disks'                            \
            'mysql-gift-oregon-4-backup-data'

        deleteResource                   \
            'media17-prod'               \
            'disks'                      \
            'mysql-gift-oregon-5-backup'

        deleteResource                        \
            'media17-prod'                    \
            'disks'                           \
            'mysql-gift-oregon-5-backup-data'

        echo -e '\t\t\t Releasing static IPs.'
        deleteResource                                \
            'media17-prod'                            \
            'addresses'                               \
            'internal-ip-mysql-report-oregon-3-backup'

        deleteResource                                \
            'media17-prod'                            \
            'addresses'                               \
            'external-ip-mysql-report-oregon-3-backup'

        deleteResource                                \
            'media17-prod'                            \
            'addresses'                               \
            'internal-ip-mysql-report-oregon-5-backup'

        deleteResource                                \
            'media17-prod'                            \
            'addresses'                               \
            'external-ip-mysql-report-oregon-5-backup'

        deleteResource                               \
            'media17-prod'                           \
            'addresses'                              \
            'internal-ip-mysql-gift-oregon-4-backup'

        deleteResource                               \
            'media17-prod'                           \
            'addresses'                              \
            'external-ip-mysql-gift-oregon-4-backup'

        deleteResource                              \
            'media17-prod'                          \
            'addresses'                             \
            'internal-ip-mysql-gift-oregon-5-backup'

        deleteResource                              \
            'media17-prod'                          \
            'addresses'                             \
            'external-ip-mysql-gift-oreogn-5-backup'
    fi

    echo -e "\n\nCongratulations! All $RUN_ENV instance cleanup have completed!"
    echo '-------------------------------------'
    echo ''
fi
