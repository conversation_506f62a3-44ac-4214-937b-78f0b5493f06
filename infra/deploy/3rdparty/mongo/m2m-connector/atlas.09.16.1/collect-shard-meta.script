// updated 09.14, fix the unique key problem 
print('sh.enableSharding("17media");');
print('db=db.getSiblingDB("17media");');
src_db = db.getSiblingDB("config")

src_db.collections.find().forEach(function(col){
    if(col && col.key && !col.dropped ){        
        str = 'db.'+ col._id.split(".")[1]+'.ensureIndex('+ tojson(col.key);
        if(col.unique) 
            str+= ",{unique: true}";
        str+= ');'
        print(str);
        str = 'sh.shardCollection("'+ col._id  +'",  '+ tojson(col.key) 
        if(col.unique) 
            str+= ",{unique: true}";
        str+= ');'
        print(str);           
    }   
})
src_db.chunks.find().sort({max: 1}).forEach(function(c) {
    var str = tojsononeline(c);
    if(str.indexOf("minKey")>=0 || str.indexOf("maxKey")>=0)
        return;
    else{
        print("sh.splitAt(" + tojsononeline(c.ns)+", "+ tojsononeline(c.max)+ ");");        
    }        
});

    
 