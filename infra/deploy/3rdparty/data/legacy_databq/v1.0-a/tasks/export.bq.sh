#!/usr/bin/env bash

if [ $# -eq 0 ]
  then
    echo "No arguments supplied"
    exit
fi

/bin/bash /usr/local/bin/auth.sh

mongoshost=${MONGOSHOST}
mongosport=${MONGOSPORT}
auto_bq_reload=${AUTOBQRELOAD}
bq_table_prefix=${BQTABLEPREFIX}
export_type='csv'
export_extra_ops='--readPreference=secondaryPreferred'
gcs_bucket='gs://media17-csv/'
if [ -z "$mongoshost" ]
  then
    mongoshost="**********"
fi

if [ -z "$mongosport" ]
  then
    mongosport="27019"
fi
echo "$mongoshost:$mongosport"

if [ -z "$bq_table_prefix" ]
  then
    bq_table_prefix="media17"
fi

echo "$export_type ==> $gcs_bucket"
echo "BQ prefix: $bq_table_prefix"

if [ -z "$auto_bq_reload" ] || [ "$auto_bq_reload" == "false" ]
  then
    auto_bq_reload=false
else
    auto_bq_reload=true
fi
echo "Auto reload BQ: $auto_bq_reload"

# cols=`mongo localhost/17media --eval "db.getCollectionNames()" | python ~/mlaunch/col.py`
col=$1

dump_folder="/mgodump/"
# make sure i'm in correctly folder
cd ${dump_folder}

rm -rf ${dump_folder}sql
rm -rf ${dump_folder}ddl
rm -rf ${dump_folder}schema
mkdir -p ${dump_folder}sql
mkdir -p ${dump_folder}ddl
mkdir -p ${dump_folder}schema

fields=`python /usr/local/bin/fields.py $col`

echo mongoexport ${export_extra_ops} --db=17media -o ${dump_folder}$col.${export_type} -c $col --fields $fields --type=${export_type}
mongoexport --host=${mongoshost} --port=${mongosport} ${export_extra_ops} --db=17media -o ${dump_folder}$col.${export_type} -c $col --fields $fields --type=${export_type}

if [ "$export_type" == "csv" ]
  then
    ddlgenerator postgresql ${dump_folder}$col.${export_type} --limit 5000 --text | \
      sed 's/,//g' | \
      sed 's/ NOT NULL//g' | \
      sed 's/INTEGER/integer/g' | \
      sed 's/TEXT/string/g' | \
      sed 's/DECIMAL(.*)/float/g' | \
      sed 's/NUMERIC(.*)/float/g' | \
      sed 's/BOOLEAN/boolean/g' | \
      sed 1d | \
      head -n -2 | \
      python /usr/local/bin/to_schema.py > ${dump_folder}schema/$col.json
else
  echo 'Currently only support csv format export'
  exit
fi

header=`sed -n 1p ${dump_folder}$col.${export_type}`
sed -i 1d ${dump_folder}$col.${export_type}

echo $header

split -C 300000000 ${dump_folder}$col.${export_type} -d ${dump_folder}$col.${export_type}.

gsutil -q cp ${dump_folder}$col.${export_type}.* ${gcs_bucket}

sleep 3

files=`gsutil ls ${gcs_bucket}$col.${export_type}.*`
# echo userID,openID,countryCode,version,deviceType,deviceModel,packageName,isAdmin,isChoice,isVerified,isSafe,lastLogin,registerTime,followingCount,followerCount,postCount >> /databq/mdump/sql/$col.bq.sh

echo '#!/usr/bin/env bash' >> ${dump_folder}sql/$col.bq.sh

if [ "$auto_bq_reload" == "true" ]
  then
    echo bq rm -f -t ${bq_table_prefix}.$col >> ${dump_folder}sql/$col.bq.sh

    echo $files
    for fn in $files
    do
      echo bq load --max_bad_records=1000 --nosynchronous_mode --schema=${dump_folder}/schema/$col.json ${bq_table_prefix}.$col $fn >> ${dump_folder}sql/$col.bq.sh
    done
fi
