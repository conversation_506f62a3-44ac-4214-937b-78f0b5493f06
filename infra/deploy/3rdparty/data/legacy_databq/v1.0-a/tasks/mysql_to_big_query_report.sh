#!/usr/bin/env bash

set +e

/bin/bash /usr/local/bin/auth.sh

TABLE_SCHEMA=$1
TABLE_NAME=$2

auth=" -h media17report.cluster-c0h0xvusrkln.us-west-2.rds.amazonaws.com -umedia17report -pMachipopo99 "

mytime=`date '+%Y%m%d'`
hostname=`hostname | tr 'A-Z' 'a-z'`
file_prefix="$TABLE_NAME$mytime"
bucket_name=media17-mysql/$file_prefix
splitat="40000000"
bulkfiles=200
maxbad=300
tmp_blobq="blob_query.txt"
tmp_jsonq="json_query.txt"
dump_folder="/mydump/"

# make sure i'm in correctly folder
cd ${dump_folder}

# make sure schema and table names are supplied
if [ $# -ne 2 ];then
  echo "DB and table name required"
  exit 1
fi

# make sure the table does not has blob or text columns
cat > $tmp_blobq << heredoc
select sum(IF((DATA_TYPE LIKE '%blob%'),1, 0)) from INFORMATION_SCHEMA.columns where TABLE_SCHEMA = '$TABLE_SCHEMA' AND TABLE_NAME = '$TABLE_NAME'
heredoc
mycount=`mysql $auth -Bs < $tmp_blobq`
if [ $mycount -ne 0 ];then
  echo "blob or text column found in table $TABLE_NAME"
  exit 3
fi

# create google cloud bucket
set -x
gsutil mb gs://$bucket_name
if [ $? -ne 0 ];then
echo "bucket $bucket_name could not be created in cloud"
fi
set +x
#
#
# create JSON schema from mysql table structure
cat > $tmp_jsonq << heredoc
select CONCAT('{"name": "', COLUMN_NAME, '","type":"', IF(DATA_TYPE like "%int%", "INTEGER",IF(DATA_TYPE = "decimal","FLOAT","STRING")) , '"},') as json from information_schema.columns where TABLE_SCHEMA = '$TABLE_SCHEMA' AND TABLE_NAME = '$TABLE_NAME';
heredoc
echo '[' >  $TABLE_NAME.json
mysql $auth -Bs < $tmp_jsonq | sed '$s/,$//' >> $TABLE_NAME.json
mysql $auth $TABLE_SCHEMA -Bse"show create table $TABLE_NAME\G" > $TABLE_NAME.sql
echo ', {"name": "hostname","type":"STRING"} ]' >>  $TABLE_NAME.json

# copy json and create table data to cloud
gsutil cp $TABLE_NAME.json gs://$bucket_name/
gsutil cp $TABLE_NAME.sql gs://$bucket_name/

# dump data
time mysql $auth $TABLE_SCHEMA -Bse"select * from $TABLE_NAME" > $TABLE_NAME.txt1
tr -d "\r" < $TABLE_NAME.txt1 > $TABLE_NAME.txt

sed -i "s/$/\t$TABLE_SCHEMA/"  $TABLE_NAME.txt
sed -i 's/(Ctrl-v)(Ctrl-m)//g' $TABLE_NAME.txt

# split files with prefix
set -x
time split -C $splitat $TABLE_NAME.txt $file_prefix
set +x

# loop and upload files to google cloud
for file in `ls $file_prefix*`
do
# big query does not seem to like double quotes and NULL
time sed -i -e 's/\"//g' -e's/NULL//g' $file
time gzip $file

# copy to google cloud
set -x
time gsutil cp $file.gz gs://$bucket_name/
set +x
if [ $? -ne 0 ];then
echo "$file could not be copied to cloud"
exit 3
fi
rm -f $file.gz
done

# import data to big query
for mylist in `gsutil ls gs://$bucket_name/*.gz | xargs -n$bulkfiles | tr ' ', ','`
do
time bq load --nosync -F '\t' --max_bad_record=$maxbad media17.$TABLE_NAME $mylist $TABLE_NAME.json
if [ $? -ne 0 ];then
echo "bq load failed for $file, check file exist in cloud"
exit 2
fi

#sleep 35
done
rm -f $TABLE_NAME.json $TABLE_NAME.sql $TABLE_NAME.txt
exit
