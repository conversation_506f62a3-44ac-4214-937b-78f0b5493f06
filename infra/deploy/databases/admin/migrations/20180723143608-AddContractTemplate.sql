
-- +migrate Up
CREATE TABLE `ContractTemplate` (
	`id` int(11) unsigned NOT NULL AUTO_INCREMENT,
	`title` varchar(50) NOT NULL COMMENT 'the title of contractTemplate',
	`region` varchar(8) NOT NULL DEFAULT 'TW',
	`createAt` bigint(20) NOT NULL COMMENT 'create time of ContractTemplate',
	`lateModifiyAt` bigint(20) NOT NULL COMMENT 'last modify time of ContractTemplate',
	`lastModifyAgentID` int(11) unsigned NOT NULL COMMENT 'last modify agentID of ContractTemplate',
	`isDelete` tinyint(1) NOT NULL DEFAULT '0',
	PRIMARY KEY (`id`),
	UNIQUE KEY title (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ContractTermTemplate` (
	  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
	  `contractTemplateID` int(11) unsigned NOT NULL,
	  `condition` varchar(50) NOT NULL DEFAULT '',
	  `gte` int(11) NOT NULL DEFAULT '0' COMMENT 'Greater than or equal',
	  `lt` int(11) NOT NULL DEFAULT '-1' COMMENT 'Less than',
	  `prop` varchar(50) NOT NULL DEFAULT '',
	  `value` int(11) NOT NULL,
	  PRIMARY KEY (`id`),
	  KEY `ContractTermTemplateID` (`contractTemplateID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- +migrate Down

DROP TABLE `ContractTemplate`;
DROP TABLE `ContractTermTemplate`;
