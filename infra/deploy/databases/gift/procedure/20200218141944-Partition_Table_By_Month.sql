
-- +migrate Up

delimiter //
CREATE DEFINER=`sre`@`%` PROCEDURE `Partition_Table_By_Month`(IN v_tablename VARCHAR(50),v_interval INT)
BEGIN
    DECLARE v_interval_add INT;
    SET v_interval_add=v_interval+1;
    START TRANSACTION;
    SET @t=CONCAT('alter table ',v_tablename,' REORGANIZE partition pMax INTO ','(','partition ', CONCAT('p',DATE_FORMAT(DATE_ADD(NOW(),INTERVAL v_interval MONTH),'%Y%m')) , ' VALUES LESS THAN ','(',((unix_timestamp(date_add(curdate()-day(curdate())+1,interval v_interval_add month))-28800)*1000),')',' , ','partition pMax VALUES LESS THAN (MAXVALUE));');
    SELECT @t;
    PREPARE stmt FROM @t;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    COMMIT;
END //
delimiter ;


-- +migrate Down

DROP PROCEDURE `Partition_Table_By_Month` ;
