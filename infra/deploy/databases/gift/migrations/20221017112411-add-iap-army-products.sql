
-- +migrate Up
INSERT INTO `Merchandise` (`sellingChannel`, `isOnline`, `merchandiseID`, `productID`, `manualShip`, `manualCreateBundle`, `period`) VALUES
(1, 1, '519', 'army_rank_corporal_2830_30d', 0, 0, 'P30D'),
(2, 1, '519', 'army_rank_corporal_2830_30d', 0, 0, 'P30D');

INSERT INTO `MerchandiseInterval` (`id`, `merchandiseID`, `sellingChannel`, `effectiveTimeMs`, `endTimeMS`, `currency`) VALUES
('MINV-2GFIyHzphhPCl5DPYJE8ltId7bj', '519', 1, 1672502399000, 9999999999999, 'USD'),
('MINV-2GHzrJ9P8uIiDblFYUoIFMr4P6e', '519', 2, 1672502399000, 9999999999999, 'USD');

INSERT INTO `MerchandiseIntervalItem` (`intervalID`, `productID`, `amount`) VALUES
('MINV-2GFIyHzphhPCl5DPYJE8ltId7bj', 'army_rank_corporal_2830_30d', '9.99'),
('MINV-2GHzrJ9P8uIiDblFYUoIFMr4P6e', 'army_rank_corporal_2830_30d', '9.99');

INSERT INTO `PointProduct` (`platform`, `sequence`, `productID`, `price`, `cnPrice`, `point`, `bonusAmount`, `bonusPercentage`, `name`, `twName`, `cnName`, `krName`, `jpName`, `currency`, `currencyPrice`) VALUES
('IOS', 1, 'ARMY_IAP_RANK_CORPORAL_ONE_MONTH', 9.99, '0', 2380, '0', '0', '', '', '', '', '', 'USD', 9.99),
('IOS', 1, 'ARMY_IAP_RANK_CORPORAL_ONE_MONTH_NOT_DISPLAY', 9.99, '0', 2380, '0', '0', '', '', '', '', '', 'USD', 9.99),
('ANDROID', 1, 'ARMY_IAP_RANK_CORPORAL_ONE_MONTH', 9.99, '0', 2380, '0', '0', '', '', '', '', '', 'USD', 9.99),
('ANDROID', 1, 'ARMY_IAP_RANK_CORPORAL_ONE_MONTH_NOT_DISPLAY', 9.99, '0', 2380, '0', '0', '', '', '', '', '', 'USD', 9.99);

INSERT INTO `ProductToArmy` (`productID`, `rank`) VALUES 
('519', 5);

INSERT INTO `Product` (`productID`,`type`,`name`,`twName`,`cnName`,`krName`,`jpName`,`arName`) VALUES 
('ARMY_IAP_RANK_CORPORAL_ONE_MONTH','ARMY','Army','主播戰隊','','','アーミー','');

-- +migrate Down
DELETE FROM `Merchandise` WHERE (`sellingChannel`, `merchandiseID`) IN (
  (1, '519'),
  (2, '519')
);

DELETE FROM `MerchandiseInterval` WHERE id IN (
  'MINV-2GFIyHzphhPCl5DPYJE8ltId7bj',
  'MINV-2GHzrJ9P8uIiDblFYUoIFMr4P6e'
);

DELETE FROM `MerchandiseIntervalItem` WHERE intervalID IN (
  'MINV-2GFIyHzphhPCl5DPYJE8ltId7bj',
  'MINV-2GHzrJ9P8uIiDblFYUoIFMr4P6e'
);

DELETE FROM `PointProduct` WHERE (`platform`, `sequence`, `productID`) IN (
  ('IOS', 1, 'ARMY_IAP_RANK_CORPORAL_ONE_MONTH'),
  ('IOS', 1, 'ARMY_IAP_RANK_CORPORAL_ONE_MONTH_NOT_DISPLAY'),
  ('ANDROID', 1, 'ARMY_IAP_RANK_CORPORAL_ONE_MONTH'),
  ('ANDROID', 1, 'ARMY_IAP_RANK_CORPORAL_ONE_MONTH_NOT_DISPLAY')
);

DELETE FROM `ProductToArmy` WHERE productID IN (
  '519'
);

DELETE FROM `Product` WHERE `type` = "ARMY" AND `productID` IN (
  'ARMY_IAP_RANK_CORPORAL_ONE_MONTH'
);