
-- +migrate Up
CREATE TABLE `RedEnvelopeEvent` (
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`redEnvelopeID` varchar(36) NOT NULL DEFAULT '' COMMENT 'redEnvelopeID of this event',
	`creatorID` varchar(36) NOT NULL DEFAULT '' COMMENT 'creatorID of this event',
	`srcID` varchar(36) NOT NULL DEFAULT '',
	`srcType` int(11) NOT NULL COMMENT 'the start time of this event',
	`createAt` bigint(20) NOT NULL,
	`startTime` bigint(20) NOT NULL COMMENT 'the insert time of this event',
	`point` int(11) NOT NULL DEFAULT '0',
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `RedEnvelopeUsage` (
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`eventID` varchar(36) NOT NULL DEFAULT '',
	`userID` varchar(36) NOT NULL DEFAULT '',
	`point` int(11) NOT NULL,
	`timestamp` bigint(20) NOT NULL,
	PRIMARY KEY (`id`),
	KEY `eventID` (`eventID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `RedEnvelopeReturn` (
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`eventID` varchar(36) NOT NULL DEFAULT '',
	`userID` varchar(36) NOT NULL DEFAULT '',
	`currency` int(36) NOT NULL DEFAULT '0',
	`amount` bigint(20) NOT NULL DEFAULT '0',
	`timestamp` bigint(20) NOT NULL,
	PRIMARY KEY (`id`),
	KEY `eventID` (`eventID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- +migrate Down
DROP TABLE IF EXISTS `RedEnvelopeEvent`;
DROP TABLE IF EXISTS `RedEnvelopeUsage`;
DROP TABLE IF EXISTS `RedEnvelopeReturn`;
