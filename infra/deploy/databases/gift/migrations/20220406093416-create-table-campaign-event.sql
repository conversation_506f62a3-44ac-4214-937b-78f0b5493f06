
-- +migrate Up
CREATE TABLE IF NOT EXISTS CampaignEvent (
	id bigint UNSIGNED NOT NULL AUTO_INCREMENT,
	tradeID varchar(27) NOT NULL default '',
	orderID varchar(36) NOT NULL default '',
	userID varchar(50) NOT NULL,
	merchandiseID varchar(50) NOT NULL default '',
	sellingChannel int unsigned NOT NULL default 0,
	region varchar(10) NOT NULL default '',
	currencyAmount DECIMAL(16,3) NOT NULL DEFAULT 0,
	currency varchar(11) NOT NULL default '',
	transactionTimeMillis bigint(20) NOT NULL default 0,
	createTimeMillis bigint(20) NOT NULL default 0,
	updateTimeMillis bigint(20) NOT NULL default 0,
	immediateStatus int unsigned NOT NULL default 0,
	PRIMARY KEY (id),
	UNIQUE uni_orderID (orderID),
	INDEX idx_tradeID (tradeID),
	INDEX idx_immediateStatus (immediateStatus),
	INDEX idx_region_time (region, transactionTimeMillis)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS CampaignEventLog (
	id bigint UNSIGNED NOT NULL AUTO_INCREMENT,
	userID varchar(50) NOT NULL,
	campaignID varchar(20) NOT NULL,
	eventID bigint UNSIGNED NOT NULL,
	item varchar(100) NOT NULL default '',
	hasReward tinyint unsigned NOT NULL default 0,
	createTimeMillis bigint(20) NOT NULL default 0,
	memo text NOT NULL,
	PRIMARY KEY (id),
	UNIQUE uni_campaignID_eventID (campaignID, eventID),
	INDEX idx_userID (userID),
	INDEX idx_eventID (eventID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS CampaignLog (
	id bigint UNSIGNED NOT NULL AUTO_INCREMENT,
	campaignID varchar(20) NOT NULL,
	cycle int unsigned NOT NULL default 0,
	createTimeMillis bigint(20) NOT NULL default 0,
	memo text NOT NULL,
	PRIMARY KEY (id),
	INDEX idx_campaignID (campaignID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- +migrate Down
DROP TABLE IF EXISTS CampaignEvent;
DROP TABLE IF EXISTS CampaignEventLog;
DROP TABLE IF EXISTS CampaignLog;
