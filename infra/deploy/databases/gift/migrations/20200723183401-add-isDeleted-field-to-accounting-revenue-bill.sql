
-- +migrate Up
ALTER TABLE `AccountingRevenueBill`
    DROP COLUMN `yearMonth`,
    ADD COLUMN `createDate` date NOT NULL COMMENT 'create date in contract timezone' AFTER `contractID`,
    ADD COLUMN `isDeleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'isDeleted 0: false, 1: true' AFTER `createTimeMs`;

-- +migrate Down
ALTER TABLE `AccountingRevenueBill`
    ADD COLUMN `yearMonth` varchar(9) NOT NULL COMMENT 'year-month string like YYYY-MM' AFTER `userID`,
    DROP COLUMN `createDate`,
    DROP COLUMN `isDeleted`;
