
-- +migrate Up
CREATE TABLE IF NOT EXISTS `StripeCustomer` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `userID` varchar(50) NOT NULL DEFAULT '',
  `customerID` varchar(255) NOT NULL DEFAULT '',
  `paymentMethodID` varchar(255) NOT NULL DEFAULT '',

  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_userID` (`userID`),
  UNIQUE KEY `idx_customerID` (`customerID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_bin;

-- +migrate Down
DROP TABLE IF EXISTS `StripeCustomer`;
