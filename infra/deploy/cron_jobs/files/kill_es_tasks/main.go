package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strconv"

	"github.com/17media/logrus"
)

const (
	esGetTaskPath    = "http://%s/_tasks?actions=*search&detailed"
	esCancelTaskPath = "http://%s/_tasks/%s/_cancel"
)

type task struct {
	Node              string `json:"node"`
	ID                int    `json:"id"`
	TaskType          string `json:"type"`
	Action            string `json:"action"`
	Description       string `json:"description"`
	StartTimeInMil    int    `json:"start_time_in_millis"`
	RunningTimeInNano int    `json:"running_time_in_nanos"`
	Cancellable       bool   `json:"cancellable"`
}

type node struct {
	Name             string          `json:"name"`
	TransportAddress string          `json:"transport_address"`
	Host             string          `json:"host"`
	IP               string          `json:"ip"`
	Roles            []string        `json:"roles"`
	Tasks            map[string]task `json:"tasks"`
}

type esTaskResponse struct {
	Nodes map[string]node `json:"nodes"`
}

type esTaskKiller struct {
	esEndpoint      string
	esUsername      string
	esPassword      string
	taskTimeoutNano int
}

func (k *esTaskKiller) killTasks() {

	// 1. Get Tasks
	getTaskResp := esTaskResponse{}
	err := k.callESAPI(http.MethodGet, fmt.Sprintf(esGetTaskPath, k.esEndpoint), &getTaskResp)
	if err != nil {
		logrus.Error(err)
		return
	}

	keysToKill := []string{}

	for _, node := range getTaskResp.Nodes {
		for key, task := range node.Tasks {
			if task.RunningTimeInNano > k.taskTimeoutNano && task.Cancellable {
				logrus.Infof("Add task: %s to kill list, Desription: %s", key, task.Description)
				keysToKill = append(keysToKill, key)
			}
		}
	}

	logrus.Infof("keysToKill:%s, Count:%d", keysToKill, len(keysToKill))

	// 2. Kill task
	esCancelResponse := esTaskResponse{}
	for _, key := range keysToKill {
		err := k.callESAPI(http.MethodPost, fmt.Sprintf(esCancelTaskPath, k.esEndpoint, key), &esCancelResponse)
		if err != nil {
			logrus.Error(err)
			return
		}
		logrus.Infof("Task: %s killed", key)
	}
}

func (k *esTaskKiller) callESAPI(method string, url string, respObjectType interface{}) error {
	client := http.Client{}
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		return err
	}

	req.SetBasicAuth(k.esUsername, k.esPassword)
	req.Header.Set("Content-type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return err
	}

	defer resp.Body.Close()

	if respObjectType != nil {
		err = json.NewDecoder(resp.Body).Decode(respObjectType)
		if err != nil {
			return err
		}
	}

	return nil
}

func newESTaskKiller() (*esTaskKiller, error) {
	k := esTaskKiller{}

	k.esEndpoint = os.Getenv("ES_ENDPOINT")
	k.esUsername = os.Getenv("ES_USERNAME")
	k.esPassword = os.Getenv("ES_PASSWORD")
	taskTimeout := os.Getenv("ES_TASK_TIMEOUT")

	if k.esEndpoint == "" {
		return nil, errors.New("Please set ENV: ES_ENDPOINT")
	}

	if k.esUsername == "" {
		return nil, errors.New("Please set ENV: ES_USERNAME")
	}

	if k.esPassword == "" {
		return nil, errors.New("Please set ENV: ES_PASSWORD")
	}

	if taskTimeout == "" {
		return nil, errors.New("Please set ENV: ES_TASK_TIMEOUT")
	}
	taskTimeoutInt, err := strconv.Atoi(taskTimeout)
	if err != nil {
		return nil, err
	}
	k.taskTimeoutNano = taskTimeoutInt * 1000 * 1000 * 1000

	return &k, nil
}

func main() {
	k, err := newESTaskKiller()
	if err != nil {
		logrus.Error(err)
		return
	}
	k.killTasks()
}
