default:
  k8s:
    namespace: "default"
    chart: "datadog"
    min_capacity: 1
    max_capacity: 1
    cpuRequest: "1"
    memoryRequest: 2Gi
    cpuLimit: "6"
    memoryLimit: 4Gi
    cluster: "cache"
    API_KEY: "1129470cd38983edf5ca3429e8fcc1ff"

k8sprod:
  k8s:
    namespace: "k8sprod"
  envs:
    REDIS_HOST: "redis-17622.internal.c1.us-west1-mz.gce.cloud.redislabs.com"
    REDIS_PORT: "17622"
    REDIS_PASSWORD: "${redis.vars.cache_pwd}"

k8ssta:
  k8s:
    namespace: "k8ssta"
  envs:
    REDIS_HOST: "redis-13514.internal.c1.asia-east1-1.gce.cloud.redislabs.com:13514"
    REDIS_PORT: "13514"
    REDIS_PASSWORD: "${redis.vars.cache_pwd}"

k8suat:
  k8s:
    namespace: "k8suat"
  envs:
    REDIS_HOST: "redis-19171.internal.c11896.asia-east1-1.gcp.cloud.rlrcp.com:19171"
    REDIS_PORT: "19171"
    REDIS_PASSWORD: "${redis.vars.cache_pwd}"

awssta:
  k8s:
    namespace: "awssta"
  envs:
    REDIS_HOST: "redis-13514.internal.c1.asia-east1-1.gce.cloud.redislabs.com:13514"
    REDIS_PORT: "13514"
    REDIS_PASSWORD: "${redis.vars.cache_pwd}"

awsprod:
  k8s:
    namespace: "awsprod"
  envs:
    REDIS_HOST: "redis-17622.internal.c1.us-west1-mz.gce.cloud.redislabs.com"
    REDIS_PORT: "17622"
    REDIS_PASSWORD: "${redis.vars.cache_pwd}"
