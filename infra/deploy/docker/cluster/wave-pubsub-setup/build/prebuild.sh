#!/bin/bash -e
# prebuild.sh builds Go app

build=$(dirname $0)
go_version=$1
parallel_build_index=${2:-0}

go_build_cache_dir=$GOPATH/docker_pkg_$parallel_build_index/linux_amd64
mkdir -p $go_build_cache_dir

# Use golang container to build binary. 
docker run --rm \
  -v "$GOPATH/src/github.com/17media/api":/go/src/github.com/17media/api \
  -v "$go_build_cache_dir":/go/pkg/linux_amd64 \
  -v "$GOPATH/pkg/mod":/go/pkg/mod \
  -v "$build":/app \
  -e "GOPATH=/go" \
  "gcr.io/media17-prod/17media/golang-gcc-swagger:$go_version" sh -c \
  "cd /go/src/github.com/17media/api/app/wave-pubsub-setup && CGO_ENABLED=0 go build -o /app/app ."
