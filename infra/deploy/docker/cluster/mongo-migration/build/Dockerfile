FROM migrate/migrate:v4.17.1

# download macgyver
ENV MACGYVER_VERSION 1.2.1
RUN wget -q https://github.com/17media/macgyver/releases/download/v${MACGYVER_VERSION}/macgyver.tar.gz -O - | tar -zx -C /usr/bin

# install mongodb
RUN echo 'http://dl-cdn.alpinelinux.org/alpine/v3.9/main' >> /etc/apk/repositories && \
    echo 'http://dl-cdn.alpinelinux.org/alpine/v3.9/community' >> /etc/apk/repositories
RUN apk update && \
     apk add --no-cache bash mongodb yaml-cpp=0.6.2-r2

COPY mongo /app/mongo
COPY entrypoint.sh /app/entrypoint.sh

WORKDIR /app

ENTRYPOINT ["/bin/bash", "/app/entrypoint.sh"]