default:
  k8s:
    serviceType: "ClusterIP"
    namespace: "default"
    nodeSelectorKey: "workload"
    nodeSelectorValue: "general"
    # Sample Config for loadBalancerSourceRanges:
    # - loadBalancerSourceRanges: '[\"***************/32\", \"***************/32\"]'
    loadBalancerSourceRanges: ""
    chart: "main"
    externalTrafficPolicy: ""
    annotations: {}
    canary: # Used primarily in canary analysis
      enabled: false
      name: "concert-stream-ph"
    hpa: # HPA must be turned off for canary analysis
      enabled: true
    multihpa:
      cpu:
        targetUtilization: 40
      mem:
        targetUtilization: 60
    ingress:
      enabled: false
      hosts: []
      annotations: {}
    hostAliases:
      enabled: false
      # ip: x.x.x.x
      # hostname: abc.example.com
    # Can disable cron job if necessary
    # NOTE: default is true
    # do_cron_job: false | true
    nodeAffinityRegular:
      enabled: false
    preferredDistributedAcrossNodes:
      enabled: false
    podDisruptionBudget:
      enabled: false
    min_capacity: 2
    max_capacity: 10
    cpuRequest: "3"
    memoryRequest: "14Gi"
    cpuLimit: "4"
    memoryLimit: "14Gi"
    targetPercentage: 75 # 400m x 75% = 300m
    readinessProbeHTTP:
      enabled: true
      httpGet_path: "/readiness"
      httpGet_port: 80
      httpGet_scheme: "HTTP"
      initialDelaySeconds: 5
      periodSeconds: 10
      timeoutSeconds: 1
      successThreshold: 2
      failureThreshold: 3
    livenessProbeHTTP:
      enabled: true
      httpGet_path: "/health"
      httpGet_port: 80
      httpGet_scheme: "HTTP"
      initialDelaySeconds: 300
      periodSeconds: 10
      timeoutSeconds: 1
      successThreshold: 1
      failureThreshold: 3
    readinessProbeTCP:
      enabled: false
    httpPort: 80
    # httpsPort: 443
    # internal communicate is currently for pods not for cluster
    internalCommunicate:
      enabled: false
    # Sample Config for lifecyclePostStartCmd, lifecyclePreStartCmd:
    # - lifecyclePostStartCmd: '[\"/bin/sh\", \"-c\", \"echo Hello > /dev/null\"]'
    # - lifecyclePreStopCmd: '[\"/usr/sbin/nginx\",\"-s\",\"quit\"]'
    lifecyclePostStartCmd: ""
    lifecyclePreStopCmd: ""
  ports:
    service: 80
  flags:
    port: 80
    debug: true
    color_log: false

    readiness_gc_limit: 20

    # redis flags
    redis_cache_uri: "redis:6379"
    redis_cache_pwd: ""
    redis_persist_uri: "redis:6379"
    redis_persist_username: ""
    redis_persist_pwd: ""

#    # mongo flags
#    mongo_pool_size_multiplier: "${mongo.vars.poolSizeMultiplier}"
#    mongo_uri: "mongodb://mongo:27017"
#    mongo_auth_db: "admin"
#    mongo_db: "concert-stream"
#    mongo_enableSSL: false
#    mongo_set_safe: false
#    mongo_check_index: false

    # metrics
    metric_destination: "datadog"
    ## datadog related
    datadog_host: "${datadog_agent.vars.host}"
    datadog_port: "${datadog_agent.vars.port}"

    # log to fluentd or not
    logtofluentd: "${fluentd.vars.enable}"
    fluentd_addr: "${fluentd.vars.host}"
    fluentd_port: "${fluentd.vars.port}"
    isSandBox: true

    # etcd
    etcd_hosts: "${etcd.vars.concert_hosts}"
    etcd_root: "${etcd.vars.config_root}"

    # kms related
    kms_provider: "${kms.app.vars.runtimeProvider}"
    kms_key: "projects/${kms.app.vars.GCPprojectID}/locations/${kms.app.vars.GCPlocationID}/keyRings/${kms.app.vars.GCPkeyRingID}/cryptoKeys/${kms.app.vars.runtimeCryptoKeyID}"

    # concert stream
    concert_stream_region: "PH"

    # rsa private/public key
    jwt_private_key: ${internalJwt.vars.jwt_private_key}
    jwt_public_key: ${internalJwt.vars.jwt_public_key}
    jwt_refresh_key: ${internalJwt.vars.jwt_refresh_key}
    jwt_access_key: ${internalJwt.vars.jwt_access_key}

  envs:
    # kms envs
    kms_cryptoProvider: ${kms.app.vars.cryptoProvider}
    kms_GCPprojectID: ${kms.app.vars.GCPprojectID}
    kms_GCPlocationID: ${kms.app.vars.GCPlocationID}
    kms_GCPkeyRingID: ${kms.app.vars.GCPkeyRingID}
    kms_GCPcryptoKeyID: ${kms.app.vars.GCPcryptoKeyID}

k8suat:
  k8s:
    namespace: "k8suat"
    min_capacity: 2
    max_capacity: 10
  flags:
    debug: false

k8ssta:
  k8s:
    namespace: "k8ssta"
    min_capacity: 1
    max_capacity: 10
  flags:
    debug: false

k8sprod:
  k8s:
    namespace: "k8sprod"
    min_capacity: 1
    max_capacity: 10
  flags:
    debug: false
    isSandBox: false
    
awssta:
  k8s:
    namespace: "awssta"
    min_capacity: 2
    max_capacity: 10
  flags:
    debug: false

awsprod:
  k8s:
    namespace: "awsprod"
    min_capacity: 2
    max_capacity: 10
  flags:
    debug: false
    isSandBox: false
