#!/usr/bin/env python
import logging
import numbers
import os
import re
import string
import yaml
from . import const

current_path = os.path.dirname(os.path.realpath(__file__))


class ServiceConfig(object):
    def __init__(self, kubernetes, env, tag, env_suffix):
        # config stores parsed yaml config
        self.config = {}
        # raw stores un-parsed yaml config. i.e. contains templates to be
        # replaced.
        self.raw = {}
        self.kubernetes = kubernetes
        self.env = env
        self.env_suffix = env_suffix
        self.tag = tag

    # Get config of a service with template variable replaced.
    def get(self, service):
        return self.__get(service, {})

    def __get(self, service, traversed):
        # If config already exists, return directly
        if service in self.config.keys():
            return self.config[service]

        # If the service is traversed again, there's circular import.
        if service in traversed:
            raise Exception('Service %s has circular import. Traversed: %s' %
                            (service, traversed))
        traversed[service] = True

        # Get the raw content with template variable
        content = read_yaml(service)

        # Find all template placeholders in the raw content
        placeholders = re.compile(r'\$\{(\w+)\}').findall(content)
        substitute_map = {
            x: self.__get_template(x, service, traversed) for x in placeholders
        }

        # Replace all templates and parse using yaml
        l = string.Template(content).substitute(substitute_map)
        logging.debug('loading to yaml: %s' % l)
        d = yaml.load(l, Loader=yaml.SafeLoader)
        if 'default' not in d:
            raise Exception('%s should contain \'default\' key' %
                            yaml_path(service))

        # Merge default key with environment specific key
        self.config[service] = merge(d['default'], d.get(self.env, {}))
        logging.debug('service %s: %s' % (service, self.config[service]))

        return self.config[service]

    # gets the corresponding template value
    # TODO: use jinja2 template since it's more flexible
    def __get_template(self, key, self_service, traversed):
        # Don't process this placeholder, it may be intended to be an env var
        # FIXME: use jinja2 template, key may also not be an env var
        if "__" not in key:
            return "${%s}" % key

        # set special key
        v = self.__get_special_key(self_service, key)
        if v is not None:
            return v

        tokens = key.split("__")
        if len(tokens) < 2:
            raise Exception('key %s should contain at least 2 elements. Got %s'
                            % (key, tokens))

        # First element should be service name
        service = tokens.pop(0).replace(const.DASH_PLACEHOLDER, "-")

        # If the key is with the format of [service]:host, treat it specially.
        if tokens[0] == 'host':
            if not self.kubernetes:
                # For docker-compose, host name will be service name
                return service
            return self.kubernetes.get_host(service)

        cfg = self.__get(service, traversed)
        # Iterate through the array and go into cfg dictionary
        for i in range(0, len(tokens)):
            cfg = cfg.get(tokens[i])
            if cfg is None:
                raise Exception('key %s.%s may not exist' % (service, '.'.join(tokens)))
        # The remaining cfg should be a string. If not panic
        if not isinstance(cfg, (basestring, numbers.Number)):
            raise Exception('config %s from key %s is not a primitive type' %
                            (cfg, key))
        return cfg

    def __get_special_key(self, self_service, key):
        if key == '__env':
            return self.env
        elif key == '__service':
            return self_service
        elif key == '__img_tag':
            return self.tag
        return None


# reads yaml file and converts "." in template placeholders into "__"
def read_yaml(service):
    path = yaml_path(service)
    logging.debug('Reading yaml file %s' % path)
    with open(path, 'r') as f:
        content = f.read()

    # Find all template placeholders and replace "." into "__"
    placeholders = re.compile(r'(\$\{[\w\.\-]+\})').findall(content)
    for p in placeholders:
        new = p.replace(".", "__").replace("-", const.DASH_PLACEHOLDER)
        content = content.replace(p, new)

    # merge environment specific config into default ones.
    return content


def yaml_path(service):
    return os.path.join(current_path, os.pardir, const.CONFIG_PATH,
                        service, 'config.yaml')


# merges env tree into default one
def merge(default, env):
    if isinstance(default, dict) and isinstance(env, dict):
        for k, v in default.iteritems():
            if k not in env:
                env[k] = v
            else:
                env[k] = merge(v, env[k])
    return env
