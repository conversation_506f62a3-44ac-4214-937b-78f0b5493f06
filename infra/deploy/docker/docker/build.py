#!/usr/bin/env python
'''build.py builds docker image by service'''
import io
import sys
import os
import random
import signal
import shutil
import string
import time
import logging
import subprocess
import concurrent.futures
import threading
from . import const
from .utils import image_tag, image_extra_tag

def build_images(services, tag, service_config, force, keep, parallel_num, disable_swagger):
    """
    Build the services in parallel and wait until all processes have completed.
    parallel_num is the maximum number of processes that may run simultaneously.
    """
    logging.info('parallel build in max %d processes' % parallel_num)

    workers = []
    futures = []
    executor = concurrent.futures.ThreadPoolExecutor(max_workers=parallel_num)
    for service in services:
        cfg = service_config.get(service)
        if const.CFG_IMAGE in cfg and not force:
            logging.info('%s docker build is skipped. Add '
                         '--force to enforce build' % service)
            continue

        full_image_tag = image_tag(cfg, service, tag)
        w = worker(service, full_image_tag, image_extra_tag(cfg, full_image_tag), keep, disable_swagger)
        workers.append(w)

        future = executor.submit(w.start)
        futures.append(future)

    try:
        # Keep main thread running to capture KeyboardInterrupt
        while True:
            done, _ = concurrent.futures.wait(futures, timeout=0.5, return_when=concurrent.futures.FIRST_EXCEPTION)
            # Check results for first exception exception
            for df in done:
                res = df.result()

            # All workers are successfully done
            if len(done) == len(workers):
                break

            time.sleep(0.5)
    except (KeyboardInterrupt, Exception) as e:
        logging.debug('Got exception, going to kill workers and shutdown executor')
        kill_workers(workers)
        executor.shutdown(True)
        raise e

def kill_workers(workers):
    for w in workers:
        w.kill()

class worker:
    def __init__(self, service, image_tag, extra_image_tag, keep, disable_swagger):
        """
        worker controls build processes of a service.
        There are three subprocesses will be run sequentially
            1. prebuild (optional) without retry
            2. docker build with retry (for network problem)
            3. docker extra tag (optional) tags the built image with an extra tag
        It creates a temporary directory for build processes and removes the dir before it exits if keep is False.
        The output of subprocesses will be print before it exits.
        """
        logging.debug('Create build process for %s with tag %s' % (service, image_tag))
        self.service = service
        self.image_tag = image_tag
        self.extra_image_tag = extra_image_tag
        self.keep = keep
        self.tmp_dir = None
        self.running_subprocess = None
        self.subprocesses = []
        self.subprocesses_output = []
        self.is_killed = False
        self.disable_swagger = disable_swagger

    def start(self):
        """
        - Create temporary directory for the build process
        - Compose prebuild command, docker build command and extra tag command.
        - And start to execute the commands
        """
        # Get the number of thread name `Thread-0` as the index for prebuild command
        index = threading.currentThread().getName()[-1]
        logging.info('Starting %s worker with index %s' % (self.service, index))

        # Setup build environment
        # Make a temporary folder not to pollute source directory
        self.tmp_dir = self.__make_tmp_dir()
        build_dir = os.path.join(self.tmp_dir, 'build')
        logging.debug('Building %s image in tmp directory: %s' % (self.service, build_dir))
        self.__setup_custombuild(self.service, build_dir)

        # Compose commands
        prebuild_cmd = self.__compose_prebuild_cmd(build_dir, str(index), self.disable_swagger)
        if len(prebuild_cmd) > 0:
            self.subprocesses.append(build_subprocess(service=self.service,
                                                               name="prebuild",
                                                               cmd=prebuild_cmd,
                                                               retry=False))

        docker_build_cmd = self.__compose_docker_build_cmd(self.image_tag, build_dir)
        self.subprocesses.append(build_subprocess(service=self.service,
                                                           name="docker build",
                                                           cmd=docker_build_cmd,
                                                           retry=True))

        if self.extra_image_tag != '':
            extra_image_tag_cmd = self.__compose_extra_tag_cmd(self.image_tag, self.extra_image_tag)
            self.subprocesses.append(build_subprocess(service=self.service,
                                                                name="docker extra tag",
                                                                cmd=extra_image_tag_cmd,
                                                                retry=False))

        # Execute subprocesses
        has_error = False
        while (self.running_subprocess or len(self.subprocesses) != 0):
            if self.is_killed:
                logging.debug("%s worker is killed " % self.service)
                if self.running_subprocess:
                    logging.debug("killing subprocess of %s" % self.service)
                    self.running_subprocess.kill()
                break

            if not self.running_subprocess:
                sp = self.subprocesses.pop(0)
                sp.start()
                self.running_subprocess = sp

            # Check running subprocess status
            if not self.running_subprocess.is_done():
                time.sleep(0.5)
                continue

            # Store output
            self.subprocesses_output.append(self.running_subprocess.get_output())
            if not self.running_subprocess.is_success():
                has_error = True
                break
            self.running_subprocess = None

        if self.keep:
            logging.info('Keep build directory: %s' % self.tmp_dir)
        else:
            shutil.rmtree(self.tmp_dir)

        self.__print_logs()
        if has_error:
            raise Exception('Error when building %s' % self.service)
        elif self.is_killed:
            logging.info('%s worker is killed and exits' % self.service)
        else:
            logging.info('Built %s image successfully' % self.service)

    def kill(self):
        self.is_killed = True

    def __make_tmp_dir(self):
        randonword_length = 6
        randonword = 'tmp_' + ''.join(random.choice(string.lowercase)
                                for i in range(randonword_length))
        tmp_dir = os.path.join(os.getcwd(), randonword)
        os.mkdir(tmp_dir)
        return tmp_dir

    def __setup_custombuild(self, service, build_dir):
        """
        Setup custom build using build directory from cluster/<service>/build
        """
        # Path of custom build directory
        src_build_dir = os.path.join('cluster', service, 'build')

        # Setup environment
        self.__setup_build(service, src_build_dir, build_dir)

    def __setup_build(self, service, src_build_dir, build_dir):
        """
        Setup build environment:
        1. Copy build directory from cluster/<service>/build to temporary directory if it exists
        2. Generate prebuild.sh based on configuration
        """

        # Copy build directory
        if os.path.exists(src_build_dir):
            try:
                shutil.copytree(src_build_dir, build_dir)
            except IOError as ioe:
                raise Exception('failed to generate prebuild.sh, err %s' %
                                str(ioe))
        else:
            logging.info('Not copying build directory for %s' % service)

        # Create actual prebuild.sh to be run
        prebuild_path = os.path.join(build_dir, const.PREBUILD)
        if os.path.exists(prebuild_path):
            try:
                prebuild_file = io.open(prebuild_path, 'r+t')
                lines = prebuild_file.readlines()
                prebuild_file.seek(0, io.SEEK_SET)
                for line in lines:
                    tmpl_str = string.Template(line)
                    prebuild_file.write(tmpl_str.safe_substitute())
                prebuild_file.close()
            except IOError as ioe:
                raise Exception('failed to generate prebuild.sh, err %s' %
                                str(ioe))
        else:
            logging.debug('Not generating build script for %s' % service)

    def __compose_prebuild_cmd(self, build_dir, index, disable_swagger):
        prebuild_cmd = []
        prebuild_path = os.path.join(build_dir, const.PREBUILD)
        disable_swagger_str="0"
        if disable_swagger:
            disable_swagger_str="1"
        if os.path.exists(prebuild_path):
            prebuild_cmd = [prebuild_path, const.GO_VERSION, index, disable_swagger_str]
            logging.debug('prebuild command of %s: %s' % (self.service , ' '.join(prebuild_cmd)))
        else:
            logging.debug('Not building app for %s' % self.service)
        return prebuild_cmd

    def __compose_docker_build_cmd(self, image_tag, build_dir):
        dockerfile_path = os.path.join(build_dir, const.DOCKERFILE)
        docker_build_cmd = ['docker', 'build', '-t', image_tag, build_dir]
        logging.debug('docker build command of %s: %s' % (self.service , ' '.join(docker_build_cmd)))
        return docker_build_cmd

    def __compose_extra_tag_cmd(self, image_tag, extra_image_tag):
        extra_image_tag_cmd = ['docker', 'tag', self.image_tag, self.extra_image_tag]
        logging.debug('extra image tag command of %s: %s' % (self.service , ' '.join(extra_image_tag_cmd)))
        return extra_image_tag_cmd

    def __print_logs(self):
        if len(self.subprocesses_output) > 0:
            logging.info('%s subprocesses output:' % self.service)
            for output in self.subprocesses_output:
                if len(output) > 0:
                    logging.info('-----------\n%s' % output)


class build_subprocess:
    def __init__(self, service, name, cmd, retry=False):
        self.service = service
        self.name = name
        self.cmd = cmd
        self.process = None
        self.output = None
        self.returncode = -1
        self.retry = retry
        self.retry_count = 0

    def start(self):
        self.process = subprocess.Popen(self.cmd,
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.STDOUT,
                                        preexec_fn=os.setsid)

    def is_done(self):
        if not self.process:
            return True

        # Check process status
        self.returncode = self.process.poll()
        if self.returncode is None:
            return False
        logging.debug("%s %s process return code %d" % (self.service, self.name, self.returncode))

        # Get output
        self.output, _ = self.process.communicate()
        self.process = None

        if self.returncode == 0:
            logging.info("%s %s success" % (self.service, self.name))
            return True

        if self.retry == True and self.retry_count < const.DOCKER_BUILD_MAX_RETRY_COUNT:
            self.retry_count += 1
            logging.info("%s %s with retry %d" % (self.service, self.name, self.retry_count))

            cmd = 'sleep 3 && ' + ' '.join(self.cmd)
            logging.debug("%s %s retry cmd %s" % (self.service, self.name, cmd))
            self.process = subprocess.Popen(cmd,
                                            stdout=subprocess.PIPE,
                                            stderr=subprocess.STDOUT,
                                            shell=True,
                                            preexec_fn=os.setsid)
            return False
        else:
            logging.error("%s %s error with return code %d" % (self.service, self.name, self.returncode))
            return True

    def is_success(self):
        return self.returncode == 0

    def get_output(self):
        return self.output

    def kill(self):
        if self.process:
            os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)
            self.process = None
