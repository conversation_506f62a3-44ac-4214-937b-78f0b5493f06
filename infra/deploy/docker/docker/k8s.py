#!/usr/bin/env python

import logging
import os
import time

from . import const

from .service_config import ServiceConfig
from .utils import image_tag
from .utils import exec_helm, exec_kubectl, cmd_exists
from .utils import route53_create_record_set, route53_delete_record_set

current_path = os.path.dirname(os.path.realpath(__file__))
workspace = ""
k8s_chart = ""

default_namespace_services = [
    'lit', 'lit-worker',
    'wave', 'wave-slackbot', 'wave-worker',
]

def deploy(env, service, operation, timeout, env_suffix, tag, canary):
    global workspace
    global k8s_chart
    logging.getLogger('botocore').setLevel(logging.WARNING)
    # Supported operation: create, up
    funcs = {
        'create': create,
        'up': up,
        'roll_back': roll_back,
        'restart': restart,
        'delete': delete,
        'status': status,
    }
    if operation not in funcs:
        raise Exception('operation %s no supported' % operation)

    if not cmd_exists('kubectl'):
        logging.error("You need install 'kubectl' for k8s")
        exit(1)

    if not cmd_exists('helm'):
        logging.error("You need install 'helm' for k8s")
        exit(1)

    k8s_chart = ServiceConfig(None,
                              env,
                              'latest',
                              None).get(service)['k8s']['chart']

    workspace = os.path.join(current_path, os.pardir,
                             const.WORKSPACE_PATH, service)
    funcs[operation](env,
                     service,
                     timeout,
                     env_suffix,
                     tag,
                     canary)


# pylint: disable=W0613
def create(env, service, timeout, env_suffix, tag, canary):
    """create creates an k8s service"""

    if "_" in env:
        override_env = '%s' % (env.replace("_", "-"))
    else:
        override_env = '%s' % (env)

    if canary:
        helm_install_cmd = "helm install --name=" + override_env + "-" + service + "-canary"
    else:
        helm_install_cmd = "helm install --name=" + override_env + "-" + service

    helm_install_cmd += " --set version=" + tag
    helm_install_cmd += " --values=" + k8s_chart + ".k8s.values.yaml"
    helm_install_cmd += " --namespace=" + override_env + " ./k8s"

    exec_helm(helm_install_cmd, workspace)

    if (service != "etcd") and ("monitor" not in service):
        rollout_cmd = (
            "kubectl rollout status deployment/%s-%s-%s" %
            (override_env, service, k8s_chart))

        kube_namespace = "default" if service in default_namespace_services else override_env

        exec_kubectl(rollout_cmd, kube_namespace, workspace)


# pylint: disable=W0613
def delete(env, service, timeout, env_suffix, tag, canary):
    """delete an k8s service"""

    override_env = env

    if canary:
       helm_name = ('%s-%s-canary' % (override_env, service))
    else:
       helm_name = ('%s-%s' % (override_env, service))

    helm_list_cmd = ('helm list %s --namespace %s' % (helm_name, override_env))
    result = exec_helm(helm_list_cmd, workspace)
    if result == '':
        logging.warning("The release '%s' not found in helm", helm_name)
        return

    # Remove the record set via Route53
    if service == 'revprox':
        kube_namespace = "default" if service in default_namespace_services else override_env
        cmd = (
            "kubectl get service/%s-%s-%s --no-headers=true" %
            (override_env, service, k8s_chart))
        result = exec_kubectl(cmd, kube_namespace, workspace)
        service_external_ip = result.split()[3]

    # Remove the releases via helm
    helm_del_cmd = ('helm del %s --purge' % (helm_name))
    exec_helm(helm_del_cmd, workspace)


# pylint: disable=W0613
def up(env, service, timeout, env_suffix, tag, canary):
    """up updates service with new version"""

    override_env = env

    helm_upgrade_cmd = "helm upgrade"

    if canary:
        helm_upgrade_cmd += " --install " + override_env + "-" + service + "-canary"
    else:
        helm_upgrade_cmd += " --install " + override_env + "-" + service

    helm_upgrade_cmd += " --set version=" + tag
    helm_upgrade_cmd += " --values=" + k8s_chart + ".k8s.values.yaml"
    helm_upgrade_cmd += " --namespace=" + override_env + " ./k8s"

    exec_helm(helm_upgrade_cmd, workspace)

    # to make it more closer to actual state
    # add few seconds wait before the result output of `kubectl rollout status`
    time.sleep(10)

    if (service != "etcd") and ("monitor" not in service):
        rollout_cmd = (
            "kubectl rollout status deployment/%s-%s-%s" %
            (override_env, service, k8s_chart))
        kube_namespace = "default" if service in default_namespace_services else override_env
        exec_kubectl(rollout_cmd, kube_namespace, workspace)


# pylint: disable=W0613
def restart(env, service, timeout, env_suffix, tag, canary):
    """Restart pods with calculated timeout"""

    override_env = env

    kube_namespace = "default" if service in default_namespace_services else override_env

    get_deployment_cmd = (
        "kubectl get deployment/%s-%s-%s --no-headers=true" %
        (override_env, service, k8s_chart))
    result = exec_kubectl(get_deployment_cmd, kube_namespace, workspace)
    current_count = result.split()[2]
    sleep_second = 200 / int(current_count)
    logging.info('Restart with timeout: %s' % sleep_second)

    get_pod_cmd = (
        "kubectl get pods -l app=%s-%s-%s --no-headers=true" %
        (override_env, service, k8s_chart))
    result = exec_kubectl(get_pod_cmd, kube_namespace, workspace)
    pods = result.split('\n')

    for i in pods:
        if i == "":
            break
        pod = i.split()[0]
        delete_pod_cmd = (
            "kubectl delete pods %s" % (pod))
        exec_kubectl(delete_pod_cmd, kube_namespace, workspace)
        time.sleep(sleep_second)


# pylint: disable=W0613
def roll_back(env, service, timeout, env_suffix, canary):
    """roll_back rolls back the service to previous version(s)"""

    override_env = env

    kube_namespace = "default" if service in default_namespace_services else override_env

    rollback_cmd = (
        "kubectl rollout undo deployment/%s-%s-%s" %
        (override_env, service, k8s_chart))
    exec_kubectl(rollback_cmd, override_env, workspace)

    if (service != "etcd") and ("monitor" not in service):
        rollout_cmd = (
            "kubectl rollout status deployment/%s-%s-%s" %
            (override_env, service, k8s_chart))
        exec_kubectl(rollout_cmd, kube_namespace, workspace)
        logging.info('Roll-back with deployment')

def status(env, service, timeout, env_suffix, tag, canary):
    """status checks if the deployment of the service was successfully rolled or not"""

    override_env = env

    # HINT: currently all deployments for LitApp are running under `default` namespace
    kube_namespace = "default" if service in default_namespace_services else override_env
    deployment_name = ("%s-%s-%s" % (override_env, service, k8s_chart))

    # Check current deployment is using the correct image
    sleep_interval = 3
    time_used = 0
    while True:
        get_deployment_cmd = ("kubectl get deployment %s -o jsonpath='{...image}'" % deployment_name)
        logging.debug("get_deployment_cmd: %s" % get_deployment_cmd)
        deployment = exec_kubectl(get_deployment_cmd, kube_namespace, workspace)
        config = ServiceConfig(None, env, tag, None).get(service)
        # FIXME: Remove this workaround when go-cron use an independent image.
        if service == "go-cron" or service == "goplatform":
            image = image_tag(config, "goapi", tag)
        elif service == "eventory-replay":
            image = image_tag(config, "eventory-worker", tag)
        elif service in ("revprox-tw", "revprox-jp", "revprox-id", "revprox-ph", "revprox-sg", "revprox-hk", "revprox-us"):
            image = image_tag(config, "revprox", tag)
        elif service in ("concert-stream-tw", "concert-stream-jp", "concert-stream-id", "concert-stream-ph", "concert-stream-sg", "concert-stream-hk", "concert-stream-us"):
            image = image_tag(config, "concert-stream", tag)
        else:
            image = image_tag(config, service, tag)
        if image in deployment:
            logging.debug("image found")
            break

        time.sleep(sleep_interval)
        time_used += sleep_interval
        if time_used >= timeout:
            raise Exception(('Run get_deployment_cmd timeout. Not found image: %s in deployment: %s.' % (image, deployment_name)))

    # Check rollout status of the deployment.
    # exec_kubectl raises an exception if the operation time of the command exceeds timeout.
    rollout_status_timeout = timeout - time_used
    rollout_status_cmd = ("kubectl rollout status deployment/%s --timeout=%ss" % (deployment_name, rollout_status_timeout))
    logging.debug("rollout_status_cmd: %s" % rollout_status_cmd)
    exec_kubectl(rollout_status_cmd, kube_namespace, workspace)
