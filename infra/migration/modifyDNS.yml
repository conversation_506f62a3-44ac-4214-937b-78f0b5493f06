dryrun-phase1:
-
  hostedZoneID: "Z1UQ7GBVP44AZW"
  name: "dryrun-admin.17.media"
  type: "A"
  dnsName: "**************"
  ttl: 60
-
  hostedZoneID: "Z1UQ7GBVP44AZW"
  name: "dryrun-payment.17.media"
  type: "A"
  dnsName: "***********"
  ttl: 60
  failover: "PRIMARY"
  setIdentifier: "dryrun-payment-staging-Primary"
  healthCheckID: "b6e021d1-2788-4ed3-99d3-e9099a5707cb"

dryrun-phase1-rollback:
-
  hostedZoneID: "Z1UQ7GBVP44AZW"
  name: "dryrun-admin.17.media"
  type: "A"
  dnsName: "dualstack.17admin-sta-**********.us-west-2.elb.amazonaws.com."
  aliasHostedZoneId: "Z1H1FL5HABSF5"
  evaluate: False
-
  hostedZoneID: "Z1UQ7GBVP44AZW"
  name: "dryrun-payment.17.media"
  type: "A"
  dnsName: "dualstack.payment-17-media-**********.us-west-2.elb.amazonaws.com."
  aliasHostedZoneId: "Z1H1FL5HABSF5"
  evaluate: True
  failover: "PRIMARY"
  setIdentifier: "dryrun-payment-staging-Primary"

migration-phase1:
-
  hostedZoneID: "Z1UQ7GBVP44AZW"
  name: "admin.17.media"
  type: "A"
  dnsName: "**************"
  ttl: 60
-
  hostedZoneID: "Z1UQ7GBVP44AZW"
  name: "payment.17.media"
  type: "A"
  dnsName: "***********"
  ttl: 60
  failover: "PRIMARY"
  setIdentifier: "payment-Prod-Primary"
  healthCheckID: "b6e021d1-2788-4ed3-99d3-e9099a5707cb"

migration-phase1-rollback:
-
  hostedZoneID: "Z1UQ7GBVP44AZW"
  name: "admin.17.media"
  type: "A"
  dnsName: "dualstack.17admin-**********.us-west-2.elb.amazonaws.com."
  aliasHostedZoneId: "Z1H1FL5HABSF5"
  evaluate: False
-
  hostedZoneID: "Z1UQ7GBVP44AZW"
  name: "payment.17.media"
  type: "A"
  dnsName: "dualstack.payment-17-media-**********.us-west-2.elb.amazonaws.com."
  aliasHostedZoneId: "Z1H1FL5HABSF5"
  evaluate: True
  failover: "PRIMARY"
  setIdentifier: "payment-Prod-Primary"
