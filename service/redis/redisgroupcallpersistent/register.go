package redisgroupcallpersistent

import (
	"fmt"
	"os"

	"github.com/17media/dig"

	"github.com/17media/api/base/db"
	"github.com/17media/api/base/metrics"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/service/redisv2/redisgroupcallpersistent"
	"github.com/17media/api/setup/dimanager"
)

var (
	met = metrics.New("redis", metrics.WithPodName())
)

func init() {
	RegisterGroupCallPersistent(dimanager.DefaultManager)
}

type gcPersistParams struct {
	dig.In

	RedisPoolMultiplier *float64 `name:"redis_pool_multiplier"`
	RedisMustConnect    *bool    `name:"redis_must_connect"`

	RedisPersistSrcURI *string `name:"redis_persist_groupcall_uri"`
	RedisPersistSrcPWD *string `name:"redis_persist_groupcall_pwd"`
}

// Added by <PERSON><PERSON>
// decorateU<PERSON> replaces uri with environment variable for unittest
func decorateURI(flagURI string) string {
	if h, ok := os.LookupEnv("X_DOCKER_RDS_HOST"); ok {
		if p, ok := os.LookupEnv("X_DOCKER_RDS_PORT"); ok {
			return fmt.Sprintf("%s:%s", h, p)
		}
	}
	return flagURI
}

// RegisterGroupCallPersistent registers the constructor of RegisterGroupCallPersistent object to the manager
func RegisterGroupCallPersistent(m *dimanager.Manager) {
	m.RegisterFloat64(`redis_pool_multiplier`, 100, `the redis pool size = NumCPU * multiplier`)
	m.RegisterBool("redis_must_connect", false, "redis uses MustConnect")

	m.RegisterString("redis_persist_groupcall_uri", "", "redis persist-groupcall URI")
	m.RegisterString("redis_persist_groupcall_pwd", "", "redis persist-groupcall password")

	fn := func(p gcPersistParams) redis.Service {

		pools := &redis.Pools{}

		if decorateURI(*p.RedisPersistSrcURI) != "" {
			srcPool := db.MustConnectRedis(
				decorateURI(*p.RedisPersistSrcURI),
				"",
				*p.RedisPersistSrcPWD,
				db.RedisParam{PoolMultiplier: *p.RedisPoolMultiplier, Retry: *p.RedisMustConnect},
			)
			pools.Src = srcPool

			pubsubPool := db.MustConnectRedis(
				decorateURI(*p.RedisPersistSrcURI),
				"",
				*p.RedisPersistSrcPWD,
				db.RedisParam{PoolMultiplier: *p.RedisPoolMultiplier, Retry: *p.RedisMustConnect},
			)
			pools.Pubsub = pubsubPool
		}

		main := redis.New("groupcall.persistent", met, pools)
		cluster := redisgroupcallpersistent.GetRedisGroupCallPersistent(m)

		return redis.NewProxy(main, cluster, "redisGroupcallPersistent")
	}

	m.ProvideConstructor(fn, `redisGroupCallPersistent`)
}

// GetRedisGroupCallPersistent returns the RedisGroupCallPersistent object
func GetRedisGroupCallPersistent(m *dimanager.Manager) redis.Service {
	var output redis.Service
	type params struct {
		dig.In
		Output redis.Service `name:"redisGroupCallPersistent"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
