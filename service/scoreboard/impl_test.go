package scoreboard

import (
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"golang.org/x/sync/errgroup"

	"github.com/17media/api/base/testutil"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/service/redis/redispersistent"
	"github.com/17media/api/setup/dimanager"
)

var (
	mockBoard, _ = New("TW", TypeSnackCompetitor, "pokemon", PeriodHour, time.Date(2024, 5, 5, 0, 0, 0, 0, time.UTC))
)

type serviceSuite struct {
	suite.Suite

	redis redis.Service

	container *testutil.Container

	im      *impl
	manager *dimanager.Manager
}

func (s *serviceSuite) SetupSuite() {
	s.manager = dimanager.DefaultManager
	container, err := testutil.RunDocker(testutil.ServiceRedis)
	s.Require().NoError(err)
	container.SetFlags(s.manager)
	s.container = container
}

func (s *serviceSuite) TearDownSuite() {
	s.Require().NoError(s.container.Remove())
}

func (s *serviceSuite) SetupSubTest() {
	s.manager.Compile()

	s.redis = redispersistent.GetRedisPersistent(s.manager)

	s.im = GetScoreBoard(s.manager).(*impl)
}

func (s *serviceSuite) TearDownSubTest() {
	s.manager.ClearMock()
	s.NoError(s.container.Clear())
}

func TestServiceSuite(t *testing.T) {
	suite.Run(t, new(serviceSuite))
}

func (s *serviceSuite) TestGetRank() {
	type args struct {
		board  Board
		member string
	}
	tests := []struct {
		desc    string
		args    args
		setup   func()
		want    int
		wantErr error
	}{
		{
			desc: "success",
			args: args{
				board:  mockBoard,
				member: "c",
			},
			setup: func() {
				err := s.redis.ZAdd(testutil.MockCTX, "scoreBoard:TW:SNACK_COMPETITOR:pokemon:HOUR:2024050508", map[string]int{
					"a": 100,
					"b": 200,
					"c": 300,
				})
				s.Require().NoError(err)
			},
			want:    1,
			wantErr: nil,
		},
		{
			desc: "no rank",
			args: args{
				board:  mockBoard,
				member: "c",
			},
			setup:   func() {},
			want:    0,
			wantErr: ErrNoRank,
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			tt.setup()
			rank, err := s.im.GetRank(testutil.MockCTX, tt.args.board, tt.args.member)
			s.Equal(tt.wantErr, err)
			s.Equal(tt.want, rank)
		})
	}
}

func (s *serviceSuite) TestGetScore() {
	type args struct {
		board  Board
		member string
	}
	tests := []struct {
		desc    string
		args    args
		setup   func()
		want    int
		wantErr error
	}{
		{
			desc: "success",
			args: args{
				board:  mockBoard,
				member: "c",
			},
			setup: func() {
				err := s.redis.ZAddFloat(testutil.MockCTX, "scoreBoard:TW:SNACK_COMPETITOR:pokemon:HOUR:2024050508", map[string]float64{
					"a": 100.3,
					"b": 200.2,
					"c": 300.1,
				})
				s.Require().NoError(err)
			},
			want:    300,
			wantErr: nil,
		},
		{
			desc: "no score",
			args: args{
				board:  mockBoard,
				member: "c",
			},
			setup:   func() {},
			want:    0,
			wantErr: ErrNoScore,
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			tt.setup()
			score, err := s.im.GetScore(testutil.MockCTX, tt.args.board, tt.args.member)
			s.Equal(tt.wantErr, err)
			s.Equal(tt.want, score)
		})
	}
}

func (s *serviceSuite) TestGetMembers() {
	type args struct {
		board Board
		min   int
		max   int
	}
	tests := []struct {
		desc    string
		args    args
		setup   func()
		want    []Member
		wantErr error
	}{
		{
			desc: "get from beginning",
			args: args{
				board: mockBoard,
				min:   1,
				max:   3,
			},
			setup: func() {
				err := s.redis.ZAddFloat(testutil.MockCTX, "scoreBoard:TW:SNACK_COMPETITOR:pokemon:HOUR:2024050508", map[string]float64{
					"a": 100.9,
					"b": 100.8,
					"c": 100.7,
					"d": 100.6,
					"e": 100.5,
					"f": 101,
				})
				s.Require().NoError(err)
			},
			want: []Member{
				{
					Rank:  1,
					Name:  "f",
					Score: 101,
				},
				{
					Rank:  2,
					Name:  "a",
					Score: 100,
				},
				{
					Rank:  3,
					Name:  "b",
					Score: 100,
				},
			},
			wantErr: nil,
		},
		{
			desc: "get from middle",
			args: args{
				board: mockBoard,
				min:   2,
				max:   4,
			},
			setup: func() {
				err := s.redis.ZAddFloat(testutil.MockCTX, "scoreBoard:TW:SNACK_COMPETITOR:pokemon:HOUR:2024050508", map[string]float64{
					"a": 100.9,
					"b": 100.8,
					"c": 100.7,
					"d": 100.6,
					"e": 100.5,
					"f": 101,
				})
				s.Require().NoError(err)
			},
			want: []Member{
				{
					Rank:  2,
					Name:  "a",
					Score: 100,
				},
				{
					Rank:  3,
					Name:  "b",
					Score: 100,
				},
				{
					Rank:  4,
					Name:  "c",
					Score: 100,
				},
			},
			wantErr: nil,
		},
		{
			desc: "wrong rank range",
			args: args{
				board: mockBoard,
				min:   3,
				max:   0,
			},
			setup:   func() {},
			want:    nil,
			wantErr: ErrRange,
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			tt.setup()
			members, err := s.im.GetMembers(testutil.MockCTX, tt.args.board, tt.args.min, tt.args.max)
			s.Equal(tt.wantErr, err)
			s.Equal(tt.want, members)
		})
	}
}

func (s *serviceSuite) TestCount() {
	type args struct {
		board Board
	}
	tests := []struct {
		desc    string
		args    args
		setup   func()
		want    int
		wantErr error
	}{
		{
			desc: "get from success",
			args: args{
				board: mockBoard,
			},
			setup: func() {
				err := s.redis.ZAddFloat(testutil.MockCTX, "scoreBoard:TW:SNACK_COMPETITOR:pokemon:HOUR:2024050508", map[string]float64{
					"a": 100.9,
					"b": 100.8,
					"c": 100.7,
					"d": 100.6,
					"e": 100.5,
					"f": 101,
				})
				s.Require().NoError(err)
			},
			want:    6,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			tt.setup()
			cnt, err := s.im.Count(testutil.MockCTX, tt.args.board)
			s.Equal(tt.wantErr, err)
			s.Equal(tt.want, cnt)
		})
	}
}

func (s *serviceSuite) TestIncrement() {
	type args struct {
		board     Board
		member    string
		increment int
	}
	tests := []struct {
		desc      string
		args      args
		setup     func()
		wantErr   error
		wantScore int
		wantRank  int
	}{
		{
			desc: "increment from zero value",
			args: args{
				board:     mockBoard,
				member:    "pikachu",
				increment: 100,
			},
			setup:     func() {},
			wantErr:   nil,
			wantScore: 100,
			wantRank:  0,
		},
		{
			desc: "increment from exist value",
			args: args{
				board:     mockBoard,
				member:    "pikachu",
				increment: 100,
			},
			setup: func() {
				err := s.redis.ZAddFloat(testutil.MockCTX, "scoreBoard:TW:SNACK_COMPETITOR:pokemon:HOUR:2024050508", map[string]float64{
					"pikachu": 100.9,
				})
				s.Require().NoError(err)
			},
			wantErr:   nil,
			wantScore: 200,
			wantRank:  0,
		},
		{
			desc: "same value but BBB (later update time) should be ranked 1",
			args: args{
				board:     mockBoard,
				member:    "BBB",
				increment: 100,
			},
			setup: func() {
				err := s.redis.ZAddFloat(testutil.MockCTX, "scoreBoard:TW:SNACK_COMPETITOR:pokemon:HOUR:2024050508", map[string]float64{
					"AAA": 200.3000,
					"BBB": 100.3600,
					"CCC": 50.3200,
				})
				s.Require().NoError(err)
			},
			wantErr:   nil,
			wantScore: 200,
			wantRank:  1,
		},
	}
	for _, tt := range tests {
		s.Run(tt.desc, func() {
			tt.setup()
			err := s.im.Increment(testutil.MockCTX, tt.args.board, tt.args.member, tt.args.increment)

			s.Equal(tt.wantErr, err)

			val, err := s.redis.ZScoreFloat(testutil.MockCTX, tt.args.board.Key(), tt.args.member)
			s.Require().NoError(err)
			s.Equal(tt.wantScore, int(val))

			ttl, err := s.redis.TTL(testutil.MockCTX, tt.args.board.Key())
			s.Require().NoError(err)
			s.LessOrEqual(ttl, int(tt.args.board.TTL().Seconds()))

			rank, err := s.redis.ZRevRank(testutil.MockCTX, tt.args.board.Key(), tt.args.member)
			s.Require().NoError(err)
			s.Equal(tt.wantRank, rank)
		})
	}

	s.Run("increment pikachu 100 times concurrently", func() {
		var eg errgroup.Group
		for i := 0; i < 100; i++ {
			eg.Go(func() error {
				return s.im.Increment(testutil.MockCTX, mockBoard, "pikachu", 1000000)
			})
		}
		s.Require().NoError(eg.Wait())

		val, err := s.redis.ZScoreFloat(testutil.MockCTX, mockBoard.Key(), "pikachu")
		s.Require().NoError(err)
		s.Equal(100000000, int(val))
	})
}

func (s *serviceSuite) TestCountByScore() {
	type args struct {
		board    Board
		minScore string
		maxScore string
	}
	tests := []struct {
		desc    string
		args    args
		setup   func()
		want    int
		wantErr error
	}{
		{
			desc: "successful case",
			args: args{
				board:    mockBoard,
				minScore: "0",
				maxScore: "1",
			},
			setup: func() {
				err := s.redis.ZAddFloat(testutil.MockCTX, "scoreBoard:TW:SNACK_COMPETITOR:pokemon:HOUR:2024050508", map[string]float64{
					"a": 100.9,
					"b": 100.8,
					"c": 100.7,
					"d": 0.5,
					"e": 0.4,
					"f": 0.3,
				})
				s.Require().NoError(err)
			},
			want:    3,
			wantErr: nil,
		},
	}
	for _, t := range tests {
		s.Run(t.desc, func() {
			t.setup()
			cnt, err := s.im.CountByScore(testutil.MockCTX, t.args.board, t.args.minScore, t.args.maxScore)
			s.Equal(t.wantErr, err)
			s.Equal(t.want, cnt)
		})
	}
}

func (s *serviceSuite) TestRemove() {
	type args struct {
		board   Board
		members []string
	}
	tests := []struct {
		desc   string
		args   args
		setup  func()
		assert func(err error)
	}{
		{
			desc: "successful case",
			args: args{
				board:   mockBoard,
				members: []string{"a", "c", "f"},
			},
			setup: func() {
				err := s.redis.ZAddFloat(testutil.MockCTX, "scoreBoard:TW:SNACK_COMPETITOR:pokemon:HOUR:2024050508", map[string]float64{
					"a": 100.9,
					"b": 100.8,
					"c": 100.7,
					"d": 0.5,
					"e": 0.4,
					"f": 0.3,
				})
				s.Require().NoError(err)
			},
			assert: func(err error) {
				s.NoError(err)

				cnt, _ := s.im.Count(testutil.MockCTX, mockBoard)
				s.Equal(3, cnt)
			},
		},
		{
			desc: "successful case when target member not exists",
			args: args{
				board:   mockBoard,
				members: []string{"not-exist-member"},
			},
			setup: func() {
				err := s.redis.ZAddFloat(testutil.MockCTX, "scoreBoard:TW:SNACK_COMPETITOR:pokemon:HOUR:2024050508", map[string]float64{
					"a": 100.9,
					"b": 100.8,
					"c": 100.7,
					"d": 0.5,
					"e": 0.4,
					"f": 0.3,
				})
				s.Require().NoError(err)
			},
			assert: func(err error) {
				s.NoError(err)

				cnt, _ := s.im.Count(testutil.MockCTX, mockBoard)
				s.Equal(6, cnt)
			},
		},
		{
			desc: "successful case when zset is empty",
			args: args{
				board:   mockBoard,
				members: []string{"a"},
			},
			assert: func(err error) {
				s.NoError(err)

				cnt, _ := s.im.Count(testutil.MockCTX, mockBoard)
				s.Equal(0, cnt)
			},
		},
	}
	for _, t := range tests {
		s.Run(t.desc, func() {
			if t.setup != nil {
				t.setup()
			}
			err := s.im.Remove(testutil.MockCTX, t.args.board, t.args.members...)
			t.assert(err)
		})
	}
}
