package ponta

import (
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/dig"
)

type params struct {
	dig.In

	MerchantCode            *string `name:"ponta_merchant_code"`
	MerchantStoreCode       *string `name:"ponta_merchant_store_code"`
	LoginEntrySiteIdetifier *string `name:"ponta_login_entry_site_idetifier"`
	OutgoingProxy           *string `name:"ponta_outgoing_proxy"`
}

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of ponta object to the manager
func Register(m *dimanager.Manager) {
	m.RegisterString("ponta_merchant_code", "", "ponta's unique merchant code")
	m.RegisterString("ponta_merchant_store_code", "", "ponta's unique merchant store code")
	m.RegisterString("ponta_login_entry_site_idetifier", "", "ponta's login entry site identifier")
	m.RegisterString("ponta_outgoing_proxy", "", "ponta's outgoing api proxy")

	fn := func(p params) Service {
		return New(p)
	}
	m.ProvideConstructor(fn, `pontaSvc`)
}

// GetPonta returns the ponta object
func GetPonta(m *dimanager.Manager) Service {
	var output Service
	type params struct {
		dig.In
		Output Service `name:"pontaSvc"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
