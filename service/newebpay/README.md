# SPG (NewebPay)

[TOC]

## MPG (Multi Payment Gateway)

### Overview

#### Purchase cancelled

```mermaid
sequenceDiagram

participant U as User
participant FE as Frontend
participant BG as Backend
participant M as MPG

U->>FE: 
FE->>BG: (POST) /v1/pay/orders [Prepare]
BG-->>FE: {requestURL, requestBody}
FE->>M: (POST) /MPG/mpg_gateway
M-->>FE: <302> ClientBackURL

```


#### Purchase successfully

```mermaid
sequenceDiagram

participant U as User
participant FE as Frontend
participant BG as Backend
participant M as MPG

U->>FE: 
FE->>BG: (POST) /v1/pay/orders [Prepare]
BG-->>FE: {requestURL, requestBody}
FE->>M: (POST) /MPG/mpg_gateway

par Notif
	M-->>BG: (POST) /v1/pay/orders/spgmpg/notif/:orderID
	opt double check
		BG->>M: (POST) API/QueryTradeInfo
		M-->>BG: 
	end
	BG->>BG: ledger.HandleNotifPayload()
and Return / Customer
	M->>BG: (POST) /v1/pay/orders/spgmpg/result/:orderID
	BG-->>FE: <302> ClientHostURL {orderID, tradeNo, sellingChannel, success, errMsg}
end

loop Check
	FE->>BG: (GET) /v1/pay/orders/{orderID}/infos
	BG-->>FE: 
end

```

### API

#### `/v1/pay/orders`

Prepare pre-receipt before communicating with 3rd-party payment.

**ENDPOINT FORMAT:** JSON

**METHOD:** POST

**PARAMETERS:**

| Key              | Type              | Description                                  |
| ---------------- | ----------------- | -------------------------------------------- |
| `payMethod`      | *int32*           |                                              |
| `sellingChannel` | *int32*           |                                              |
| `productID`      | *string* or *int* |                                              |
| `clientBackURL`  | *string*          | It's used in `MPG` to redirect to `Frontend` |
| `sellingChannel` | *string*          |                                              |

**RETURNS:**

HTTP status code: *200*

```json
{
  "requestURL": <string>,
  "requestBody": <string>
}
```

**ERRORS:**

HTTP status code: *420*

HTTP status code: *520*


#### `/v1/pay/orders/{orderID}/infos`

Check Result by orderID

**ENDPOINT FORMAT:** JSON

**METHOD:** GET

**RETURNS:**

HTTP status code: *200*

```json
{
  "state": <int>, // 1: prepare, 2: result, 3: notif
  "status": <string>,          // (optional)
  "message": <string>,         // (optional)
  "amt": <int>,                // (optional)
  "tradeNo": <string>,         // (optional)
  "merchantOrderNo": <string>, // (optional)
  "paymentType": <string>,     // (optional)
  "respondCode": <string>,     // (optional)
  "payTime": <string>,         // (optional)
  "paymentMethod": <string>,   // (optional)
  "codeNo": <string>,          // (optional)
  "expireDate": <string>,      // (optional)
  "expireTime": <string>       // (optional)
}
```

**ERRORS:**

HTTP status code: *420*

HTTP status code: *520*


#### `/v1/pay/orders/spgmpg/notif/:orderID`

Handle the purchase result returned from MPG with `NotifURL` asynchronously

**ENDPOINT FORMAT:** FORM

**METHOD:** POST

**PATH:**

| Key       | Type     | Description |
| --------- | -------- | ----------- |
| `orderID` | *string* |             |

**PARAMETERS:**

| Key          | Type     | Description                          |
| ------------ | -------- | ------------------------------------ |
| `Status`     | *string* |                                      |
| `MerchantID` | *string* |                                      |
| `TradeInfo`  | *string* | JSON format message encrytped by AES |
| `TradeSha`   | *string* | checksum of `TradeInfo`              |

**RETURNS:**

HTTP status code: *200*

**ERRORS:**

HTTP status code: *420*

HTTP status code: *520*


#### `/v1/pay/orders/spgmpg/result/:orderID`

Handle the purchase result returned from MPG with `ReturnURL` or `CustomerURL` synchronously

**ENDPOINT FORMAT:** FORM

**METHOD:** POST

**PATH:**

| Key       | Type     | Description |
| --------- | -------- | ----------- |
| `orderID` | *string* |             |

**PARAMETERS:**

| Key          | Type     | Description                          |
| ------------ | -------- | ------------------------------------ |
| `Status`     | *string* |                                      |
| `MerchantID` | *string* |                                      |
| `TradeInfo`  | *string* | JSON format message encrytped by AES |
| `TradeSha`   | *string* | checksum of `TradeInfo`              |

**RETURNS:**

HTTP status code: *200*

**ERRORS:**

HTTP status code: *420*

HTTP status code: *520*


### Redirection

#### Forward to `ClientHostURL` 

Redirect page to `Frontend` with `ClientHostURL` and parameters. `ClientHostURL` is stored in config.

**PARAMETERS:**

| Key              | Type     | Description          |
| ---------------- | -------- | -------------------- |
| `orderID`        | *string* |                      |
| `sellingChannel` | *string* |                      |
| `payMethod`      | *string* |                      |

**EXAMPLE:**

```sh
curl -X GET https://frontend.com/page?orderID=12345&payMethod=60&sellingChannel=26
```



### API Client

#### `/API/QueryTradeInfo`

Check online payment status with 3rd-party site instantly
