package pagingv2

import (
	"bytes"
	"crypto/sha1"
	"encoding/base64"
	"encoding/gob"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"reflect"
	"strconv"
	"strings"
	"time"

	btime "github.com/17media/api/base/time"

	"github.com/17media/logrus"
	"github.com/vmihailenco/msgpack/v5"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/goroutine"
	"github.com/17media/api/base/metrics"
	"github.com/17media/api/models/keys"
	"github.com/17media/api/service/localcache"
	"github.com/17media/api/service/redis"
)

const (
	// default number of items in a shard
	defaultShardSize = 100
	// max number of items in a shard
	maxShardSize = 1000
	// maxWorkerCount is the maximum worker number for getting shards
	maxWorkerCount = 5
	// the default timeout that bounds the time spending on getter
	defaultGetterTimeout = time.Second * 10

	// maxWarnCount is the threshold to send warn alert
	maxWarnCount = 1000
)

type strategy int

// renewPage strategies
const (
	// renewPage if no one is renewing, else return
	tryRenewOrReturn strategy = iota + 1
	// renewPage if no one is renewing, else wait for result
	tryRenewOrWait
	// renewPage even if there's someone renewing
	forceRenew
)

var (
	met     = metrics.New("pagingv2")
	timeNow = btime.TimeNow

	// following pools are package level goroutine pools used for renewTask and decodeTask
	renewPool  = goroutine.NewPool("pagingv2.renew", 1024, 0, 128, goroutine.WithStrategy(goroutine.QueueFirst))
	decodePool = goroutine.NewPool("pagingv2.decode", 64, 0, 16, goroutine.WithStrategy(goroutine.QueueFirst))

	errSerialization = errors.New("serialization not recognized")
)

type impl struct {
	cache      redis.Service
	localcache localcache.Service

	expirer *keyExpirer
	decoder *decoder

	keyPfx           string
	getter           Getter
	renewDuration    time.Duration
	cacheDuration    time.Duration
	maxCacheDuration time.Duration

	shardSize                 int
	getterTimeout             time.Duration
	noWaitForLock             bool
	deleteLatestKeyWhenUpdate bool
	serialization             Serialization
}

func newService(p Params) Service {
	im := &impl{
		cache:      p.Cache,
		localcache: p.Localcache,

		expirer: newExpirer(p.Cache, p.KeyPfx, p.CacheDuration, p.MaxCacheDuration),
		decoder: newDecoder(),

		keyPfx:           p.KeyPfx,
		getter:           p.Getter,
		renewDuration:    p.RenewDuration,
		cacheDuration:    p.CacheDuration,
		maxCacheDuration: p.MaxCacheDuration,

		shardSize:                 p.ShardSize,
		getterTimeout:             p.GetterTimeout,
		noWaitForLock:             p.NoWaitForLock,
		deleteLatestKeyWhenUpdate: p.DeleteLatestKeyWhenUpdate,
		serialization:             p.SerializationMethod,
	}

	if p.RenewDuration > p.CacheDuration {
		panic("renewDuration should be less than cacheDuration")
	}

	if p.CacheDuration > p.MaxCacheDuration {
		panic("cacheDuration should be less than maxCacheDuration")
	}

	if p.DeleteLatestKeyWhenUpdate && p.Localcache != nil {
		panic("DeleteLatestKeyWhenUpdate should not be used with Localcache")
	}

	if im.shardSize <= 0 || im.shardSize > maxShardSize {
		im.shardSize = defaultShardSize
	}

	if im.getterTimeout <= 0 {
		im.getterTimeout = defaultGetterTimeout
	}

	if im.serialization == DefaultSerialization {
		im.serialization = GobSerialization
	}

	return im
}

func (im *impl) Get(context ctx.CTX, key, cursor string, size int, container interface{}) (nextCursor string, totalCount int, err error) {
	defer met.BumpTime("time", "func", "Get", "pfx", im.keyPfx).End()

	context = ctx.WithValues(context, map[string]interface{}{
		"pfx": im.keyPfx,
		"key": key,
	})
	defer func() {
		// Since we sometimes use paging as a slice cache with a large number,
		// it has a side effect that we can't be notified when the cache is too large
		if size >= maxWarnCount && totalCount > size {
			met.BumpSum("maxCount.warn", 1, "pfx", im.keyPfx)
		}
	}()

	if !isEmptySlicePtr(container) {
		met.BumpSum("Get.err", 1, "pfx", im.keyPfx, "reason", "bad container")
		context.WithField("type", reflect.TypeOf(container)).Error("bad container")
		return "", 0, ErrBadContainer
	}

	createTs, totalCount, offset, shardSize, cursorOK, err := im.extractCursor(cursor)
	if err != nil {
		met.BumpSum("Get.err", 1, "pfx", im.keyPfx, "reason", "extract cursor")
		context.WithField("cursor", cursor).Error("bad cursor")
		return "", 0, err
	}
	if cursorOK {
		met.BumpSum("Get", 1, "pfx", im.keyPfx, "by", "cursor")
		return im.getFromCache(context, key, createTs, totalCount, shardSize, offset, size, true, container)
	}

	createTs, renewTs, totalCount, shardSize, latestKeyOK, err := im.extractLatestKey(context, key)
	if err != nil {
		met.BumpSum("Get.err", 1, "pfx", im.keyPfx, "reason", "extract latestKey")
		context.WithField("err", err).Error("extractLatestKey failed")
		return "", 0, err
	}
	if latestKeyOK {
		met.BumpSum("Get", 1, "pfx", im.keyPfx, "by", "latestKey")
		if timeNow().UnixNano() >= renewTs {
			if err := renewPool.ScheduleTimeout(time.Second, func() {
				im.renewPage(ctx.WithValue(context, "fromFunc", "get"), key, 0, tryRenewOrReturn, nil)
			}); err != nil {
				met.BumpSum("scheduletimeout.err", 1, "pfx", im.keyPfx, "reason", err.Error())
				context.WithField("err", err).Error("ScheduleTimeout failed")
			}
		}

		return im.getFromCache(context, key, createTs, totalCount, shardSize, 0, size, true, container)
	}

	met.BumpSum("Get", 1, "pfx", im.keyPfx, "by", "renew")
	return im.renewPage(context, key, size, tryRenewOrWait, container)
}

// getFromCache get needed elements from cache or localcache and returns cursor info
func (im *impl) getFromCache(context ctx.CTX, key string, createTs int64, totalCount, shardSize, offset, size int, extendTTL bool, container interface{}) (nextCursor string, length int, err error) {
	if offset+size > totalCount {
		size = totalCount - offset
	}

	totalShardCount := getTotalShardCount(totalCount, shardSize)
	fromShard, toShard := getCoveredShard(shardSize, offset, size)
	met.BumpAvg("throughput.ratio", float64(toShard-fromShard)/float64(totalShardCount), "pfx", im.keyPfx)

	_, err = im.getShards(context, key, createTs, fromShard, toShard, container)
	// If offset not 0 and cacheKey missing, we already lost the snapshot and should return error.
	// But if offset is 0, we can new a snapshot for the caller.
	if err == ErrCacheNotFound && offset == 0 {
		context.WithFields(logrus.Fields{
			"totalShardCount": totalShardCount,
			"fromShard":       fromShard,
			"toShard":         toShard,
			"totalCount":      totalCount,
			"shardSize":       shardSize,
			"offset":          offset,
			"size":            size,
		}).Info("getShards failed")
		met.BumpSum("cachemiss.renew", 1, "pfx", im.keyPfx)
		met.BumpAvg("cache.hit", 0, "pfx", im.keyPfx)
		return im.renewPage(context, key, size, tryRenewOrWait, container)
	}
	if err != nil {
		context.WithFields(logrus.Fields{
			"totalShardCount": totalShardCount,
			"fromShard":       fromShard,
			"toShard":         toShard,
			"totalCount":      totalCount,
			"shardSize":       shardSize,
			"offset":          offset,
			"size":            size,
			"err":             err,
		}).Error("im.getShards failed")
		met.BumpSum("getFromCache.err", 1, "pfx", im.keyPfx)
		met.BumpAvg("cache.hit", 0, "pfx", im.keyPfx)
		return "", 0, err
	}

	met.BumpAvg("cache.hit", 1, "pfx", im.keyPfx)

	if extendTTL {
		// extend ttl of all cacheKeys once endusers successfully get the cached data via Get
		cacheKeys := make([]string, totalShardCount)
		for i := 0; i < totalShardCount; i++ {
			cacheKeys[i] = im.toCacheKey(key, createTs, i)
		}
		im.expirer.Push(context, key, createTs, cacheKeys)
	}

	nextOffset := im.trim(totalCount, shardSize, offset, size, container)
	return toCursor(createTs, totalCount, nextOffset, shardSize), totalCount, nil
}

// getShards get rawData of [fromShard, toShard), and fill container if provided
func (im *impl) getShards(context ctx.CTX, key string, createTs int64, fromShard, toShard int, container interface{}) ([][]byte, error) {
	shardCount := toShard - fromShard
	if shardCount <= 0 {
		return [][]byte{}, nil
	}

	rawData := make([][]byte, shardCount)
	gotData := make([]bool, shardCount)

	allCacheKeys := make([]string, shardCount)
	for i := fromShard; i < toShard; i++ {
		allCacheKeys[i-fromShard] = im.toCacheKey(key, createTs, i)
	}

	// get from localcache
	if im.localcache != nil {
		for i := fromShard; i < toShard; i++ {
			if data, err := im.localcache.Get(context, allCacheKeys[i-fromShard]); err == nil {
				met.BumpAvg("localcache.hit", 1, "pfx", im.keyPfx)
				rawData[i-fromShard] = data
				gotData[i-fromShard] = true
			} else {
				met.BumpAvg("localcache.hit", 0, "pfx", im.keyPfx)
			}
		}
	}

	// collect cacheKeys whose data haven't got yet
	missingShards := []int{}
	missingCacheKeys := []string{}
	for i := fromShard; i < toShard; i++ {
		if !gotData[i-fromShard] {
			missingShards = append(missingShards, i)
			missingCacheKeys = append(missingCacheKeys, allCacheKeys[i-fromShard])
		}
	}

	// get missing shards from redis
	if len(missingShards) > 0 {
		mval, err := im.cache.MGetZip(context, missingCacheKeys)
		if err != nil {
			context.WithField("err", err).Error("cache.MGetZip failed")
			return nil, err
		}

		for i, v := range mval {
			if !v.Valid {
				// Due to a cacaheKey missing, the latestKey pointing to these cacheKeys
				// should be delete to keep the system robust.
				im.deleteLatestKey(context, key, createTs)
				context.WithFields(logrus.Fields{
					"i":                i,
					"fromShard":        fromShard,
					"toShard":          toShard,
					"lastestKey":       im.toLatestKey(key),
					"missingCacheKeys": missingCacheKeys,
				}).Error("cache.MGetZip cacheKey not found")
				return nil, ErrCacheNotFound
			}
			data := v.Value
			if im.localcache != nil {
				if err := im.localcache.Set(context, missingCacheKeys[i], data, im.localcacheDuration()); err != nil {
					met.BumpSum("localcache.set.fail", 1, "pfx", im.keyPfx)
				}
			}
			rawData[missingShards[i]-fromShard] = data
		}
	}

	met.BumpAvg("shard.size", float64(len(rawData[0])), "pfx", im.keyPfx)

	// if container is not provided, skip decoding rawData into container
	if container == nil {
		return rawData, nil
	}

	tElem := reflect.TypeOf(container).Elem().Elem()                           // elem
	tList := reflect.SliceOf(tElem)                                            // []elem
	lists := reflect.MakeSlice(reflect.SliceOf(tList), shardCount, shardCount) // [][]elem{}
	errChan := make(chan error, shardCount)
	for i, data := range rawData {
		im.decoder.Decode(data, lists.Index(i).Addr().Interface(), im.serialization, errChan)
	}

	// wait for all error to return
	for i := 0; i < shardCount; i++ {
		if err := <-errChan; err != nil {
			context.WithField("err", err).Error("decoder.Decode failed")
			return nil, err
		}
	}

	v := reflect.ValueOf(container)
	for i := 0; i < shardCount; i++ {
		v.Elem().Set(reflect.AppendSlice(v.Elem(), lists.Index(i)))
	}

	return rawData, nil
}

// deleteLatestKey delete latestKey with specific createTs
func (im *impl) deleteLatestKey(context ctx.CTX, key string, createTs int64) {
	latestCreateTs, _, _, _, _, err := im.extractLatestKey(context, key)
	if err != nil {
		return
	}

	if createTs == latestCreateTs {
		met.BumpSum("deleteLatestKey", 1, "pfx", im.keyPfx)
		im.cache.Del(context, im.toLatestKey(key))
	}
	return
}

func (im *impl) Update(context ctx.CTX, key string) error {
	defer met.BumpTime("time", "func", "Update", "pfx", im.keyPfx).End()
	context = ctx.WithValues(context, map[string]interface{}{
		"pfx": im.keyPfx,
		"key": key,
	})

	_, _, _, _, latestKeyOK, err := im.extractLatestKey(context, key)
	if err != nil {
		context.WithField("err", err).Error("extractLatestKey failed")
		return err
	}

	if !latestKeyOK {
		return nil
	}

	if im.deleteLatestKeyWhenUpdate {
		latestKey := im.toLatestKey(key)
		if _, err := im.cache.Del(context, latestKey); err != nil {
			context.WithField("err", err).Error("cache.Del latestKey failed")
			return err
		}
	}

	if _, _, err := im.renewPage(context, key, 0, forceRenew, nil); err != nil {
		context.WithField("err", err).Error("im.renewPage failed")
		return err
	}

	return nil
}

// renewPage force rebuild a snapshot, which do the following things
// - get all data from getter
// - compare the data to the latest snapshot if exists
// - set cache if no latest snapshot or snapshot changes
// - fill and trim container
func (im *impl) renewPage(context ctx.CTX, key string, size int, strategy strategy, container interface{}) (nextCursor string, length int, err error) {
	lockKey := im.toLockKey(key)
	setnxErr := im.cache.SetNXLegacy(context, im.toLockKey(key), []byte("1"), im.getterTimeout)
	if setnxErr != nil && setnxErr != redis.ErrNotFound {
		met.BumpSum("setnx.fail", 1, "pfx", im.keyPfx)
		context.WithField("err", setnxErr).Error("cache.SetNX failed")
		return "", 0, setnxErr
	}

	if (im.noWaitForLock || strategy == tryRenewOrReturn) && setnxErr == redis.ErrNotFound {
		return "", 0, ErrPageIsUpdating
	}

	if strategy == tryRenewOrWait && setnxErr == redis.ErrNotFound {
		startPolling := btime.TimeNow()
		for time.Since(startPolling) < im.getterTimeout {
			createTs, _, totalCount, shardSize, latestKeyOK, err := im.extractLatestKey(context, key)
			if err != nil {
				context.WithField("err", err).Error("extractLatestKey failed")
				return "", 0, err
			}

			if latestKeyOK {
				if container != nil {
					return im.getFromCache(context, key, createTs, totalCount, shardSize, 0, size, false, container)
				}
				return "", 0, nil
			}

			// defaultGetterTimeout is 10 seconds, so it might try to get latestKey about 10 times.
			time.Sleep(time.Millisecond * time.Duration(500+rand.Intn(1000)))
			continue
		}

		return "", 0, ErrPageIsUpdating
	}

	defer func() {
		// FIXME this might delete someone else's lockKey because `forceRenew` strategy doesn't
		// check setnxErr
		im.cache.Del(context, lockKey)
	}()

	// We need to rebuild a snapshot if strategy is forceRenew or SetNX succeeded
	createTs, renewTs, totalCount, shardSize, latestKeyOK, err := im.extractLatestKey(context, key)
	if err != nil {
		context.WithField("err", err).Error("extractLatestKey failed")
		return "", 0, err
	}

	// 2nd check to prevent rebuild snapshot while others has done the task.
	if strategy != forceRenew && latestKeyOK && renewTs > timeNow().UnixNano() {
		if container != nil {
			// only the function Get will invoke renewPage with non-nil container,
			// and need to get real data from cache
			return im.getFromCache(context, key, createTs, totalCount, shardSize, 0, size, false, container)
		}
		return "", 0, nil
	}

	defer met.BumpTime("time", "func", "renewPage", "pfx", im.keyPfx).End()

	newRawData, newTotalCount, err := im.getFromGetter(context, key, container)
	if err != nil {
		context.WithField("err", err).Error("getFromGetter failed")
		return "", 0, err
	}
	met.BumpAvg("cacheKeyCount", float64(len(newRawData)), "pfx", im.keyPfx)
	met.BumpAvg("totalCount", float64(newTotalCount), "pfx", im.keyPfx)

	// Because cacheKey's ttl can be extended but bound by maxCacheDuration, we defined
	// a snapshot is too old to be recycled if it exceed 1/2 maxCacheDuration.
	maxRecycelTime := createTs + im.maxCacheDuration.Nanoseconds()/2
	snapshotTooOld := timeNow().UnixNano() > maxRecycelTime
	latestKey := im.toLatestKey(key)

	// check if snapshot is too old or has changed
	if latestKeyOK && !snapshotTooOld && newTotalCount == totalCount {
		// get old raw data
		totalShardCount := getTotalShardCount(totalCount, shardSize)
		rawData, err := im.getShards(context, key, createTs, 0, totalShardCount, nil)
		if err != nil {
			context.WithField("err", err).Error("im.getShards failed")
			return "", 0, err
		}

		// If snapshot doesn't change
		if rawDataEqual(newRawData, rawData) {
			met.BumpAvg("recycled", 1, "pfx", im.keyPfx)
			// extend all cacheKeys' ttl
			cacheKeys := make([]string, totalShardCount)
			for i := 0; i < totalShardCount; i++ {
				cacheKeys[i] = im.toCacheKey(key, createTs, i)
			}
			im.expirer.Push(context, key, createTs, cacheKeys)

			// set new latestKey value
			newRenewTs := timeNow().UnixNano() + im.renewDuration.Nanoseconds()
			latestKeyVal := fmt.Sprintf("%d:%d:%d:%d", createTs, newRenewTs, totalCount, shardSize)
			if err := im.cache.Set(context, latestKey, []byte(latestKeyVal), im.cacheDuration); err != nil {
				context.WithField("err", err).Error("im.Set latestKey failed")
				return "", 0, err
			}
			if im.localcache != nil {
				im.localcache.DelLocal(context, latestKey)
			}

			nextOffset := im.trim(totalCount, shardSize, 0, size, container)
			return toCursor(createTs, totalCount, nextOffset, shardSize), totalCount, nil
		}
	}

	met.BumpAvg("recycled", 0, "pfx", im.keyPfx)
	// If latestKey not exist or pages not equal, set new cacheKeys and latestKey.
	newCreateTs := timeNow().UnixNano()
	newRenewTs := newCreateTs + im.renewDuration.Nanoseconds()
	// FIXME implement redis.MSetZip?
	for i, data := range newRawData {
		cacheKey := im.toCacheKey(key, newCreateTs, i)
		// We add 3 second more on cacheKey's cacheDuration in order to make sure
		// latestKey won't point to a already expired cache.
		if err := im.cache.SetZip(context, cacheKey, data, im.cacheDuration+time.Second*3); err != nil {
			context.WithField("err", err).Error("im.SetZip cacheKey failed")
			return "", 0, err
		}
	}
	latestKeyVal := fmt.Sprintf("%d:%d:%d:%d", newCreateTs, newRenewTs, newTotalCount, im.shardSize)
	if err := im.cache.Set(context, latestKey, []byte(latestKeyVal), im.cacheDuration); err != nil {
		context.WithField("err", err).Error("im.Set latestKey failed")
		return "", 0, err
	}
	if im.localcache != nil {
		im.localcache.DelLocal(context, latestKey)
	}

	met.BumpSum("renewPage.success", 1, "pfx", im.keyPfx)

	nextOffset := im.trim(newTotalCount, im.shardSize, 0, size, container)
	return toCursor(newCreateTs, newTotalCount, nextOffset, im.shardSize), newTotalCount, nil
}

// getFromGetter returns
// - encoded bytes of shards
// - length of list returned by getter
func (im *impl) getFromGetter(context ctx.CTX, key string, container interface{}) (rawData [][]byte, totalCount int, err error) {
	var wholeList interface{}
	run := true
	errChan := make(chan error, 1)
	bumpTime := met.BumpTime("time", "func", "getterResponse", "pfx", im.keyPfx)
	panicChan := goroutine.Go(func() {
		defer met.BumpTime("time", "func", "getter", "pfx", im.keyPfx).End()
		if run {
			list, err := im.getter(context, key)
			if err != nil {
				context.WithField("err", err).Error("im.getter failed")
				errChan <- err
				return
			}
			wholeList = list
			errChan <- nil
		}
	})

	select {
	case p := <-panicChan:
		// When goroutine.Go finishes, panicChan will be closed without
		// receiving anything. So we need to check nil here.
		if p != nil {
			met.BumpSum("getter", 1, "pfx", im.keyPfx, "reason", "getter panic")
			return nil, 0, ErrGetterPanic
		}
	case <-time.After(im.getterTimeout):
		met.BumpSum("getter", 1, "pfx", im.keyPfx, "reason", "getter timeout")
		run = false
		return nil, 0, ErrGetterTimeout
	}
	if err := <-errChan; err != nil {
		met.BumpSum("getter", 1, "pfx", im.keyPfx, "reason", "getter failed")
		return nil, 0, err
	}
	bumpTime.End()
	met.BumpSum("getter", 1, "pfx", im.keyPfx, "reason", "getter success")

	if wholeList == nil {
		return [][]byte{}, 0, nil
	}

	v := reflect.ValueOf(wholeList)
	totalCount = v.Len()

	// FIXME use worker to speed up?
	for i := 0; i < totalCount; i += im.shardSize {
		j := i + im.shardSize
		if j > totalCount {
			j = totalCount
		}
		b, err := toBytes(im.serialization, v.Slice(i, j).Interface())
		if err != nil {
			context.WithField("err", err).Error("toBytes failed")
			return nil, 0, err
		}
		rawData = append(rawData, b)
	}

	if container != nil {
		b, err := toBytes(im.serialization, wholeList)
		if err != nil {
			context.WithField("err", err).Error("toBytes failed")
			return nil, 0, err
		}

		errChan := make(chan error, 1)
		im.decoder.Decode(b, container, im.serialization, errChan)
		if err := <-errChan; err != nil {
			context.WithField("err", err).Error("decoder.Decode failed")
			return nil, 0, err
		}
	}

	return rawData, totalCount, nil
}

// getTotalShardCount returns the number of shards within a snapshot
func getTotalShardCount(totalCount, shardSize int) int {
	totalShardCount := totalCount / shardSize
	if totalCount%shardSize > 0 {
		totalShardCount++
	}

	return totalShardCount
}

// getCoveredShard return inclusive-from and exclusive-to index of shards that
// contains elements from offset to offset+size
func getCoveredShard(shardSize, offset, size int) (fromShard, toShard int) {
	if size == 0 {
		return 0, 0
	}

	fromShard = offset / shardSize
	toShard = (offset+size-1)/shardSize + 1
	return fromShard, toShard
}

func (im *impl) toLatestKey(key string) string {
	if im.serialization != GobSerialization {
		return keys.RedisKey(keys.PfxPagingv2, im.keyPfx, im.serialization.String(), "la", key)
	}
	return keys.RedisKey(keys.PfxPagingv2, im.keyPfx, "la", key)
}

func (im *impl) toLockKey(key string) string {
	if im.serialization != GobSerialization {
		return keys.RedisKey(keys.PfxPagingv2, im.keyPfx, im.serialization.String(), "lo", key)
	}
	return keys.RedisKey(keys.PfxPagingv2, im.keyPfx, "lo", key)
}

func (im *impl) toCacheKey(key string, createTs int64, shard int) string {
	shardKey := fmt.Sprintf("%s:%d:%d", key, createTs, shard)
	if im.serialization != GobSerialization {
		return keys.RedisKey(keys.PfxPagingv2, im.keyPfx, im.serialization.String(), "ca", shardKey)
	}
	return keys.RedisKey(keys.PfxPagingv2, im.keyPfx, "ca", shardKey)
}

// trim the slice container with offset and size
// totalCount is not equal to the length of container because container
// consists of only required shards.
func (im *impl) trim(totalCount, shardSize, offset, size int, container interface{}) (nextOffset int) {
	if container == nil {
		return -1
	}

	v := reflect.ValueOf(container)
	containerLength := v.Elem().Len()
	if containerLength == 0 || offset < 0 || offset > totalCount-1 {
		return -1
	}

	// get the correct size to trim the slice
	nextOffset = offset + size
	if nextOffset >= totalCount {
		size = totalCount - offset
		nextOffset = -1
	}

	// only the needed shards are in container, so need to adjust offset
	omitted := offset / shardSize * shardSize
	modifiedOffset := offset - omitted

	// boundary check for unexpected container length, or modifiedOffset
	// If the check fail, we just return empty container.
	// One known root cause is that, when rolling a new version of goapi, multiple shardSizes
	// of the same paging exist, and the calculation of the offset and size will go wrong.
	// - size increased:
	//		wrong `omitted` will be calculated, and thus wrong `modifiedOffset` and `size`.
	// 		panics might occurs in this case when trimming the container
	// - size decreased:
	// 		calculate incorrect needed shards and might get the non-existing cacheKey, which leads
	// 		to the deletion of latestKey.
	if modifiedOffset+size > containerLength {
		met.BumpSum("trim.failed", 1, "pfx", im.keyPfx)
		modifiedOffset = 0
		size = 0
		nextOffset = -1
	}

	v.Elem().Set(v.Elem().Slice(modifiedOffset, modifiedOffset+size))

	return nextOffset
}

// extractLatestKey gets the latestKey from redis and returns latest snapshot's info
func (im *impl) extractLatestKey(context ctx.CTX, key string) (createTs, renewTs int64, totalCount, shardSize int, ok bool, err error) {
	latestKey := im.toLatestKey(key)
	var v []byte
	getFromRedis := false

	// get from local
	if im.localcache != nil {
		if v, err = im.localcache.Get(context, latestKey); err == nil {
			met.BumpAvg("local.latestKey.hit", 1, "pfx", im.keyPfx)
		} else {
			met.BumpAvg("local.latestKey.hit", 0, "pfx", im.keyPfx)
		}
	}

	// get from redis
	if len(v) == 0 {
		v, err = im.cache.Get(context, latestKey)
		if err == redis.ErrNotFound {
			return 0, 0, 0, 0, false, nil
		} else if err != nil {
			context.WithField("err", err).Error("cache.Get latestKey failed")
			return 0, 0, 0, 0, false, err
		}

		getFromRedis = true
	}

	parts := bytes.Split(v, []byte(":"))
	if len(parts) != 4 {
		// If latestKey has bad format, delete it to prevent data locked for a long time.
		im.cache.Del(context, latestKey)
		if im.localcache != nil {
			im.localcache.DelLocal(context, latestKey)
		}
		return 0, 0, 0, 0, false, ErrBadLatestKey
	}
	createTs, _ = strconv.ParseInt(string(parts[0]), 10, 64)
	renewTs, _ = strconv.ParseInt(string(parts[1]), 10, 64)
	totalCount, _ = strconv.Atoi(string(parts[2]))
	shardSize, _ = strconv.Atoi(string(parts[3]))

	// set in local (with ttl = 1s) when now < renewTs - 1s, so local latestKey only exists
	// when redis latestKey exists.
	if im.localcache != nil && getFromRedis && timeNow().UnixNano() <= renewTs-time.Second.Nanoseconds() {
		im.localcache.Set(context, latestKey, v, time.Second)
	}

	return createTs, renewTs, totalCount, shardSize, true, nil
}

// extractCursor verifies the cursor by checking the signature, and
// extract info from it.
func (im *impl) extractCursor(cursor string) (createTs int64, totalCount, offset, shardSize int, ok bool, err error) {
	if cursor == "" {
		return 0, 0, 0, 0, false, nil
	}

	// cursor = <createTs>:<#items>:<offset>-<signature>, and will be splited
	// into two parts <createTs>:<#items>:<offset> and <signature>
	parts := strings.SplitN(cursor, "-", 2)
	if len(parts) != 2 {
		return 0, 0, 0, 0, false, ErrBadCursor
	}

	if parts[1] != toSignature(parts[0]) {
		return 0, 0, 0, 0, false, ErrBadCursor
	}

	splits := strings.Split(parts[0], ":")
	if len(splits) != 4 {
		return 0, 0, 0, 0, false, ErrBadCursor
	}
	createTs, err = strconv.ParseInt(splits[0], 10, 64)
	if err != nil {
		return 0, 0, 0, 0, false, ErrBadCursor
	}
	totalCount, err = strconv.Atoi(splits[1])
	if err != nil {
		return 0, 0, 0, 0, false, ErrBadCursor
	}
	offset, err = strconv.Atoi(splits[2])
	if err != nil {
		return 0, 0, 0, 0, false, ErrBadCursor
	}
	shardSize, err = strconv.Atoi(splits[3])
	if err != nil {
		return 0, 0, 0, 0, false, ErrBadCursor
	}

	return createTs, totalCount, offset, shardSize, true, nil
}

func (im *impl) localcacheDuration() time.Duration {
	ttl := im.cacheDuration / 2
	if ttl < time.Second {
		return time.Second
	}
	return ttl
}

func toCursor(createTs int64, totalCount, offset, shardSize int) string {
	// -1 offset means the page is fully fetched so return empty cursor
	if offset == -1 {
		return ""
	}

	cursor := fmt.Sprintf("%d:%d:%d:%d", createTs, totalCount, offset, shardSize)
	return cursor + "-" + toSignature(cursor)
}

func toSignature(cursor string) string {
	privateKey := "CqwjknKJweKWqe"
	h := sha1.New()
	h.Write([]byte(cursor + privateKey))
	s := base64.URLEncoding.EncodeToString(h.Sum(nil))
	return s
}

// toGobBytes encode a structure into byte by gob encoder
func toGobBytes(v interface{}) ([]byte, error) {
	var buf bytes.Buffer
	if err := gob.NewEncoder(&buf).Encode(v); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// toJSONBytes encode a structure into byte by encoding/json
func toJSONBytes(v interface{}) ([]byte, error) {
	return json.Marshal(v)
}

// toMsgPackBytes encode a structure into byte by msgpack
func toMsgPackBytes(v interface{}) ([]byte, error) {
	return msgpack.Marshal(v)
}

func toBytes(serialization Serialization, v interface{}) ([]byte, error) {
	switch serialization {
	case GobSerialization:
		return toGobBytes(v)
	case MsgPackSerialization:
		return toMsgPackBytes(v)
	default:
		return nil, errSerialization
	}
}

// isEmptySlicePtr check if an object is in the following type
// - *[]*struct
// - *[]struct
func isEmptySlicePtr(container interface{}) bool {
	if container == nil {
		return false
	}

	v := reflect.ValueOf(container)
	// check pointer
	if v.Kind() != reflect.Ptr {
		return false
	}

	e := v.Elem()
	// check slice
	if e.Kind() != reflect.Slice {
		return false
	}

	// check empty
	if e.Len() != 0 {
		return false
	}

	return true
}

func rawDataEqual(r1, r2 [][]byte) bool {
	if len(r1) != len(r2) {
		return false
	}
	for i := range r1 {
		if !bytes.Equal(r1[i], r2[i]) {
			return false
		}
	}
	return true
}
