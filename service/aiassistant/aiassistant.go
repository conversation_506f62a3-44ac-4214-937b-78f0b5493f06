package aiassistant

import (
	"errors"

	"github.com/17media/api/base/ctx"
	model "github.com/17media/api/models/aiassistant"
)

var (
	ErrBadRequest     = errors.New("bad request")
	ErrMLServiceError = errors.New("ml service response error")
)

//go:generate mockery --name Service --with-expecter
type Service interface {
	// GetTopics get suggestion topics from ML
	GetTopics(context ctx.CTX, p model.GetTopicParams) (*model.GetTopicResp, error)

	// StopTopic stop current topic privided by ML
	StopTopic(context ctx.CTX, p model.StopTopicParams) (*model.StopTopicResp, error)

	// SendTopicReaction send user feedback on the topic to ML
	SendTopicReaction(context ctx.CTX, p model.TopicReactionParams) error

	// SetStreamStatus sets the stream status to ML when the stream starts or ends
	SetStreamStatus(context ctx.CTX, p model.SetStreamStatusParams) error

	// SetAICohostSettings sets the AI cohost settings to ML
	SetAICohostSettings(context ctx.CTX, p model.AICohostSettingsParams) error

	// SendCohostFeedback sends user feedback on the AI cohost to ML
	SendCohostFeedback(context ctx.CTX, p model.AICohostFeedbackParams) error

	// GetThemes return themes used by AI co-hosted games
	GetThemes(context ctx.CTX, p model.GetThemesParams) (*model.GetThemesResp, error)

	// StartGame start a AI co-hosted game
	StartGame(context ctx.CTX, p model.StartGameParams) (*model.StartGameResp, error)

	// EndGame stop a AI co-hosted game
	EndGame(context ctx.CTX, p model.EndGameParams) (*model.EndGameResp, error)
}
