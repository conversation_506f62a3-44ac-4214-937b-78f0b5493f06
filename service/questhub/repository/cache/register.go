package cache

import (
	"github.com/17media/dig"

	"github.com/17media/api/service/cache"
	"github.com/17media/api/service/questhub/repository"
	"github.com/17media/api/setup/dimanager"
)

func init() {
	registerCacheQuestSetting(dimanager.DefaultManager)
}

func registerCacheQuestSetting(m *dimanager.Manager) {
	type params struct {
		dig.In
		Inner repository.QuestSetting `name:"questSettingRepository"`
		Cache cache.Service           `name:"cache"`
	}

	fn := func(p params) repository.QuestSetting {
		return NewQuestSetting(
			p.Inner,
			p.Cache,
		)
	}
	m.ProvideConstructor(fn, `cacheQuestSettingRepository`)
}

func getQuestSetting(m *dimanager.Manager) repository.QuestSetting {
	var output repository.QuestSetting
	type params struct {
		dig.In
		Output repository.QuestSetting `name:"cacheQuestSettingRepository"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
