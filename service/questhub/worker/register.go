package worker

import (
	"github.com/17media/dig"

	"github.com/17media/api/service/questhub/repository"
	_ "github.com/17media/api/service/questhub/repository/cache" // dimanager register cache repository
	"github.com/17media/api/setup/dimanager"
)

func init() {
	Register(dimanager.DefaultManager)
}

func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		QuestSettingRepository repository.QuestSetting `name:"cacheQuestSettingRepository"`
		QuestRecordRepository  repository.QuestRecord  `name:"questRecordRepository"`
	}

	fn := func(p params) Worker {
		return New(p.QuestSettingRepository, p.QuestRecordRepository)
	}
	m.ProvideConstructor(fn, `questhubWorker`)
}

func GetQuesthubWorker(m *dimanager.Manager) Worker {
	var output Worker
	type params struct {
		dig.In
		Output Worker `name:"questhub<PERSON>orker"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
