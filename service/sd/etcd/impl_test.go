package sdetcd

import (
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	clientv3 "go.etcd.io/etcd/client/v3"

	"github.com/17media/api/base/ctx"
	bDocker "github.com/17media/api/base/docker"
	"github.com/17media/api/service/sd"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/setup/etcd/etcddiscovery"
)

type serviceSuite struct {
	suite.Suite

	mockCTX ctx.CTX

	im      sd.Service
	manager *dimanager.Manager

	localhost string
	etcdPort  string

	etcdClient *clientv3.Client
	factory    *serviceFactory
}

func (s *serviceSuite) SetupSuite() {
	localhost, ports, err := bDocker.RunExtDockers([]string{"etcd"})
	s.Require().NoError(err)

	s.localhost = localhost
	s.etcdPort = ports[0]

	s.manager = dimanager.DefaultManager
	s.manager.ProvideString("service_discovery_etcd_hosts", localhost+":"+s.etcdPort)
	s.manager.ProvideInt("etcd_dial_timeout", 10)

	s.mockCTX = ctx.Background()
}

func (s *serviceSuite) TearDownSuite() {
	s.NoError(bDocker.RemExtDockers())
}

func (s *serviceSuite) SetupTest() {
	s.manager.ClearMock()
	s.manager.Compile()

	s.etcdClient = etcddiscovery.GetDiscoverySrvEtcdClient(s.manager)
	s.factory = &serviceFactory{s.etcdClient}
	s.im = s.factory.Create(sd.Setting{})
}

func (s *serviceSuite) TearDownTest() {
	_, err := s.etcdClient.Delete(s.mockCTX, "", clientv3.WithPrefix())
	s.Require().NoError(err)
	s.im.Close(s.mockCTX)
}

func TestServiceSuite(t *testing.T) {
	suite.Run(t, new(serviceSuite))
}

func (s *serviceSuite) TestRegister() {
	tests := []struct {
		Desc string
		Func func()
		Err  error
	}{
		{
			Desc: "normal path",
			Func: func() {
				s.im = s.factory.Create(sd.Setting{
					Prefix: "prefix",
					HostIP: "127.0.0.1",
				})
			},
			Err: nil,
		},
		{
			Desc: "Service Info not provide",
			Func: func() {
				s.im = s.factory.Create(sd.Setting{
					Prefix: "",
					HostIP: "127.0.0.1",
				})
			},
			Err: sd.ErrInvalidServiceInfo,
		},
	}

	for _, test := range tests {
		s.TearDownTest()
		s.SetupTest()

		if test.Func != nil {
			test.Func()
		}

		err := s.im.Register(s.mockCTX, sd.WithReadinessMonitor())
		s.Require().Equal(test.Err, err, test.Desc)
	}
}

func (s *serviceSuite) TestListService() {
	mockPrefix := "prefix"
	mockServices := []*sd.ServiceInfo{
		{Name: mockPrefix, Host: "127.0.0.1"},
		{Name: mockPrefix, Host: "*********"},
	}

	tests := []struct {
		Desc   string
		Func   func()
		ExpRes []*sd.ServiceInfo
		Err    error
	}{
		{
			Desc: "normal path",
			Func: func() {
				s.im.Watch(s.mockCTX, mockPrefix)
				for _, service := range mockServices {
					value, err := encode(service)
					s.Require().NoError(err)

					key := getServiceDiscoveryKey(service.Name, service.Host)
					_, err = s.etcdClient.Put(s.mockCTX, key, value)
					s.Require().NoError(err)
				}
				// wait for watcher update
				time.Sleep(time.Second)
			},
			ExpRes: []*sd.ServiceInfo{
				{Name: mockPrefix, Host: "*********"},
				{Name: mockPrefix, Host: "127.0.0.1"},
			},
			Err: nil,
		},
		{
			Desc: "empty case",
			Func: func() {
				s.im.Watch(s.mockCTX, mockPrefix)
			},
			ExpRes: []*sd.ServiceInfo{},
			Err:    nil,
		},
		{
			Desc:   "prefix not watched",
			ExpRes: nil,
			Err:    sd.ErrPfxNotWatched,
		},
	}

	for _, test := range tests {
		s.TearDownTest()
		s.SetupTest()

		if test.Func != nil {
			test.Func()
		}

		srv, err := s.im.ListService(s.mockCTX, mockPrefix)
		s.Require().Equal(test.ExpRes, srv, test.Desc)
		s.Require().Equal(test.Err, err, test.Desc)
	}
}

func (s *serviceSuite) TestGetService() {
	mockPrefix := "prefix"
	mockServices := []*sd.ServiceInfo{
		{Name: mockPrefix, Host: "127.0.0.1"},
		{Name: mockPrefix, Host: "*********"},
	}

	tests := []struct {
		Desc   string
		Func   func()
		ExpRes *sd.ServiceInfo
		Err    error
	}{
		{
			Desc: "normal path",
			Func: func() {
				s.im.Watch(s.mockCTX, mockPrefix, sd.WithSelector(sd.SelectorStrategyRoundRobin))
				for _, service := range mockServices {
					value, err := encode(service)
					s.Require().NoError(err)

					key := getServiceDiscoveryKey(service.Name, service.Host)
					_, err = s.etcdClient.Put(s.mockCTX, key, value)
					s.Require().NoError(err)
				}
				// wait for watcher update
				time.Sleep(time.Second)
			},
			ExpRes: &sd.ServiceInfo{Name: mockPrefix, Host: "*********"},
			Err:    nil,
		},
		{
			Desc: "empty case",
			Func: func() {
				s.im.Watch(s.mockCTX, mockPrefix)
			},
			ExpRes: nil,
			Err:    sd.ErrNoAvailableService,
		},
		{
			Desc:   "prefix not watched",
			ExpRes: nil,
			Err:    sd.ErrPfxNotWatched,
		},
	}

	for _, test := range tests {
		s.TearDownTest()
		s.SetupTest()

		if test.Func != nil {
			test.Func()
		}

		srv, err := s.im.GetService(s.mockCTX, mockPrefix)
		if test.Err != nil {
			s.Require().Empty(srv, test.Desc)
			s.Require().Equal(test.Err, err, test.Desc)
		} else {
			s.Contains(mockServices, srv, test.Desc)
			s.NoError(err)
		}
	}
}
