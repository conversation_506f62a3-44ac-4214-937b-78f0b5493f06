package playstore

import (
	"errors"

	playstoreModel "github.com/17media/api/models/playstore"

	"google.golang.org/api/androidpublisher/v3"

	"github.com/17media/api/base/ctx"
)

var (
	ErrPackageClientNotFound = errors.New("package client not found")
	ErrUnhandledNotif        = errors.New("unhandled notif")
)

// subscription NotificationType from play store
// https://developer.android.com/google/play/billing/rtdn-reference#sub

type SubscriptionNotifType int

const (
	SubscriptionNotifUnknown              SubscriptionNotifType = 0
	SubscriptionNotifRecovered            SubscriptionNotifType = 1
	SubscriptionNotifRenewed              SubscriptionNotifType = 2
	SubscriptionNotifCanceled             SubscriptionNotifType = 3
	SubscriptionNotifPurchase             SubscriptionNotifType = 4
	SubscriptionNotifOnHold               SubscriptionNotifType = 5
	SubscriptionNotifInGracePeriod        SubscriptionNotifType = 6
	SubscriptionNotifRestarted            SubscriptionNotifType = 7
	SubscriptionNotifPriceChangeComfirmed SubscriptionNotifType = 8
	SubscriptionNotifDeferred             SubscriptionNotifType = 9
	SubscriptionNotifPaused               SubscriptionNotifType = 10
	SubscriptionNotifPauseScheduleChanged SubscriptionNotifType = 11
	SubscriptionNotifRevoked              SubscriptionNotifType = 12
	SubscriptionNotifExpired              SubscriptionNotifType = 13
)

var SubscriptionNotifString = map[SubscriptionNotifType]string{
	SubscriptionNotifUnknown:              "UNKNOWN",
	SubscriptionNotifRecovered:            "SUBSCRIPTION_RECOVERED",
	SubscriptionNotifRenewed:              "SUBSCRIPTION_RENEWED",
	SubscriptionNotifCanceled:             "SUBSCRIPTION_CANCELED",
	SubscriptionNotifPurchase:             "SUBSCRIPTION_PURCHASED",
	SubscriptionNotifOnHold:               "SUBSCRIPTION_ON_HOLD",
	SubscriptionNotifInGracePeriod:        "SUBSCRIPTION_IN_GRACE_PERIOD",
	SubscriptionNotifRestarted:            "SUBSCRIPTION_RESTARTED",
	SubscriptionNotifPriceChangeComfirmed: "SUBSCRIPTION_PRICE_CHANGE_CONFIRMED",
	SubscriptionNotifDeferred:             "SUBSCRIPTION_DEFERRED",
	SubscriptionNotifPaused:               "SUBSCRIPTION_PAUSED",
	SubscriptionNotifPauseScheduleChanged: "SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED",
	SubscriptionNotifRevoked:              "SUBSCRIPTION_REVOKED",
	SubscriptionNotifExpired:              "SUBSCRIPTION_EXPIRED",
}

func (n SubscriptionNotifType) String() string {
	return SubscriptionNotifString[n]
}

// one time product notification from play store
// https://developer.android.com/google/play/billing/rtdn-reference#one-time
type OneTimeProductNotifType int

const (
	OneTimeProductNotifTypeUnknown   OneTimeProductNotifType = iota // 0
	OneTimeProductNotifTypePurchased                                // 1
	OneTimeProductNotifTypeCanceled                                 // 2
)

var OneTimeProductNotifTypeString = map[OneTimeProductNotifType]string{
	OneTimeProductNotifTypeUnknown:   "ONE_TIME_PRODUCT_UNKNOWN",
	OneTimeProductNotifTypePurchased: "ONE_TIME_PRODUCT_PURCHASED",
	OneTimeProductNotifTypeCanceled:  "ONE_TIME_PRODUCT_CANCELED",
}

var OneTimeProductNotifTypeValue = map[string]OneTimeProductNotifType{
	"ONE_TIME_PRODUCT_UNKNOWN":   OneTimeProductNotifTypeUnknown,
	"ONE_TIME_PRODUCT_PURCHASED": OneTimeProductNotifTypePurchased,
	"ONE_TIME_PRODUCT_CANCELED":  OneTimeProductNotifTypeCanceled,
}

func (nt OneTimeProductNotifType) String() string {
	return OneTimeProductNotifTypeString[nt]
}

// CancelReason: The reason why a subscription was canceled or is not
// https://developers.google.com/android-publisher/api-ref/rest/v3/purchases.subscriptions
const (
	CancelReasonUserCancel      int64 = 0
	CancelReasonSystemCancel    int64 = 1
	CancelReasonNewSubscription int64 = 2
	CancelReasonDeveloperCancel int64 = 3
)

// PaymentState: The payment state of the subscription.
// https://developers.google.com/android-publisher/api-ref/rest/v3/purchases.subscriptions?hl=zh-tw
const (
	PaymentStatePending                           int64 = 0
	PaymentStateReceived                          int64 = 1
	PaymentStateFreeTrial                         int64 = 2
	PaymentStatePendingDeferredUpgradeOrDowngrade int64 = 3
)

type SubscriptionData struct {
	NotifType    SubscriptionNotifType
	Subscription *androidpublisher.SubscriptionPurchase
}

type OneTimeProductData struct {
	NotifType OneTimeProductNotifType
	Product   *androidpublisher.ProductPurchase
}

type Notif struct {
	SubscriptionData   *SubscriptionData
	OneTimeProductData *OneTimeProductData
	Container          *playstoreModel.PayloadContainer
}

func (n *Notif) IsSubscriptionNotif() bool {
	return n.SubscriptionData != nil
}

func (n *Notif) IsOneTimeProductNotif() bool {
	return n.OneTimeProductData != nil
}

func (n *Notif) IsUnknownNotif() bool {
	return !n.IsSubscriptionNotif() && !n.IsOneTimeProductNotif()
}

// Service is the interface for interacting with play store
type Service interface {
	VerifySubscription(context ctx.CTX, packageName string, subscriptionID string, token string) (*androidpublisher.SubscriptionPurchase, error)
	GetSubscriptionV2(context ctx.CTX, packageName string, token string) (*androidpublisher.SubscriptionPurchaseV2, error)
	VerifyProduct(context ctx.CTX, packageName string, productID string, token string) (*androidpublisher.ProductPurchase, error)
	AcknowledgeSubscription(context ctx.CTX, packageName string, subscriptionID string, token string) error
	AcknowledgeProduct(context ctx.CTX, packageName string, productID string, token string) error
	CancelSubscription(context ctx.CTX, packageName string, subscriptionID string, token string) error
	GetVoidedPurchases(context ctx.CTX, packageName string) ([]*androidpublisher.VoidedPurchase, error)
	ParseNotif(context ctx.CTX, payload []byte) (*Notif, error)
}
