package sequence

import (
	"testing"
	"time"

	"github.com/17media/api/base/ctx"

	"github.com/stretchr/testify/suite"

	bdocker "github.com/17media/api/base/docker"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/service/redis/redispersistent"
	"github.com/17media/api/setup/dimanager"
)

var (
	mockCTX = ctx.Background()
)

type seqSuite struct {
	suite.Suite
	im      Service
	redis   redis.Service
	manager *dimanager.Manager
}

func (s *seqSuite) SetupSuite() {
	localhost, ports, err := bdocker.RunExtDockers([]string{"redis"})
	s.Require().NoError(err)

	s.manager = dimanager.DefaultManager
	s.manager.ProvideString("redis_persist_uri", localhost+":"+ports[0])
}

func (s *seqSuite) SetupTest() {
	s.manager.ClearMock()
	s.manager.Compile()

	s.redis = redispersistent.GetRedisPersistent(s.manager)
	s.im = GetSequence(s.manager)
}

func (s *seqSuite) TearDownSuite() {
	s.NoError(bdocker.RemExtDockers())
}

func TestSeqSuite(t *testing.T) {
	suite.Run(t, new(seqSuite))
}

func (s *seqSuite) TestSeqence() {
	key := "TEST-KEY"
	_, err := s.im.Current(mockCTX, key)
	s.Error(err)

	err = s.redis.Set(mockCTX, key, []byte("0"), time.Minute)
	s.NoError(err)

	_, err = s.im.Current(mockCTX, key)
	s.Error(err)
	s.Equal("Not Found", err.Error())

	res, err := s.im.Next(mockCTX, key)
	s.NoError(err)
	s.Equal(int64(1), res)

	res, err = s.im.Current(mockCTX, key)
	s.NoError(err)
	s.Equal(int64(1), res)

	res, err = s.im.Next(mockCTX, key)
	s.NoError(err)
	s.Equal(int64(2), res)

	res, err = s.im.Current(mockCTX, key)
	s.NoError(err)
	s.Equal(int64(2), res)

	// Test if stored key is not an integer
	err = s.redis.Set(mockCTX, key, []byte("abc"), time.Minute)
	s.NoError(err)
	res, err = s.im.Current(mockCTX, key)
	s.Error(err)
}
