package redisv2

import (
	"bytes"
	"compress/gzip"
	"context"
	"fmt"
	"io/ioutil"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/fatih/set"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"

	"github.com/17media/api/base/ctx"
	bdocker "github.com/17media/api/base/docker"
	"github.com/17media/api/base/metrics"
	redisModel "github.com/17media/api/models/redis"
)

type redisSuite struct {
	suite.Suite
	redis *redImpl
}

var (
	mockCTX = ctx.Background()
	nxKey   = "nxkey"
	nxField = "nxfield"
)

func newRedisClient(host string, ports []string) *redis.ClusterClient {
	addrs := []string{}
	for i := range ports {
		addrs = append(addrs, host+":"+ports[i])
	}

	return redis.NewClusterClient(&redis.ClusterOptions{
		Addrs: addrs,
	})
}

func pingEachShard(client *redis.ClusterClient) error {
	return client.ForEachShard(mockCTX, func(ctx context.Context, shard *redis.Client) error {
		return shard.Ping(ctx).Err()
	})
}

func (r *redisSuite) SetupSuite() {
	localhost, ports, err := bdocker.RunExtDockers([]string{"redis-cluster"})
	r.Require().NoError(err)

	client := newRedisClient(localhost, ports)
	r.redis = New("unittest", metrics.New(""), client).(*redImpl)

	err = pingEachShard(r.redis.client)
	r.Require().NoError(err)
}

func (r *redisSuite) TearDownSuite() {
	r.NoError(bdocker.RemExtDockers())
}

func (r *redisSuite) SetupTest() {
}

func (r *redisSuite) TearDownTest() {
	isSandbox = func() bool { return true }
	err := r.redis.FlushAll(mockCTX)
	r.Require().NoError(err)
}

func (r *redisSuite) TestSetZip() {
	key := "getzip"
	val := []byte("hello world")

	err := r.redis.SetZip(mockCTX, key, val, Forever)
	r.Require().NoError(err)

	res, err := r.redis.Get(mockCTX, key)
	r.Require().NoError(err)
	reader, err := gzip.NewReader(bytes.NewBuffer(res))
	r.Require().NoError(err)
	jdata, err := ioutil.ReadAll(reader)
	reader.Close()
	r.Require().NoError(err)
	r.Equal(val, jdata)
}

func (r *redisSuite) TestGetSet() {
	err := r.redis.Set(mockCTX, "testKey", []byte("100"), time.Minute)
	r.Require().NoError(err)

	oldVal, err := r.redis.GetSet(mockCTX, "testKey", []byte("200"), time.Minute)
	r.Require().NoError(err)
	r.Require().Equal("100", string(oldVal))

	val, err := r.redis.Get(mockCTX, "testKey")
	r.Require().NoError(err)
	r.Require().Equal("200", string(val))
}

func (r *redisSuite) TestSet() {
	cases := []struct {
		Key    string
		Value  string
		expire time.Duration
	}{
		{
			"test-key",
			"test-value",
			Forever,
		},
		{
			"test-key1",
			"test-value1",
			10 * time.Second,
		},
	}
	for caseID, cas := range cases {
		r.redis.Set(mockCTX, cas.Key, []byte(cas.Value), cas.expire)

		value, err := r.redis.client.Do(mockCTX, "GET", cas.Key).Text()
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.Value, value, fmt.Sprintf("case %d", caseID))

		ttl, _ := r.redis.client.Do(mockCTX, "TTL", cas.Key).Int()
		if cas.expire == Forever {
			r.Equal(-1, ttl, fmt.Sprintf("case %d", caseID))
		} else {
			r.Equal(10, ttl, fmt.Sprintf("case %d", caseID))
		}
	}
}

func (r *redisSuite) TestSetXX() {
	r.redis.SetXX(mockCTX, "testavvvv", []byte("1234"), 10*time.Second)

	err := r.redis.client.Do(mockCTX, "GET", "testavvvv").Err()
	r.Error(err)

	r.redis.Set(mockCTX, "testavvvv", []byte("1234"), 10*time.Second)
	r.redis.SetXX(mockCTX, "testavvvv", []byte("1234"), 10*time.Second)
	value, err := r.redis.client.Do(mockCTX, "GET", "testavvvv").Text()
	r.NoError(err)
	r.Equal(value, "1234")
}

func (r *redisSuite) TestRandomKey() {
	_, err := r.redis.RandomKey(mockCTX)
	r.Require().Equal(redis.Nil, err)

	for i := 0; i < 100; i++ {
		err := r.redis.Set(mockCTX, fmt.Sprintf("key%d", i), []byte("value"), 0)
		r.Require().NoError(err)
	}

	_, err = r.redis.RandomKey(mockCTX)
	r.Require().NoError(err)
}

func (r *redisSuite) TestFlushAll() {
	for i := 0; i < 1000; i++ {
		r.redis.Set(mockCTX, fmt.Sprintf("test%d", i), []byte("1234"), 10*time.Second)
	}

	isSandbox = func() bool { return true }
	err := r.redis.FlushAll(mockCTX)
	r.Require().NoError(err)

	// Check each master since FlushAll just works in only one master
	allMasterEmpty := true
	err = r.redis.client.ForEachMaster(mockCTX, func(ctx context.Context, master *redis.Client) error {
		keys, err := master.Keys(ctx, "*").Result()
		if len(keys) != 0 {
			allMasterEmpty = false
		}

		return err
	})
	r.NoError(err)
	r.True(allMasterEmpty)

	isSandbox = func() bool { return false }
	err = r.redis.FlushAll(mockCTX)
	r.Equal(redisModel.ErrWrongEnv, err)
}

func (r *redisSuite) TestType() {
	r.redis.Set(mockCTX, "test", []byte("1234"), 10*time.Second)
	usage, err := r.redis.Type(mockCTX, "test")
	r.Require().NoError(err)
	r.Equal("string", string(usage))
}

func (r *redisSuite) TestStrlen() {
	r.redis.Set(mockCTX, "test", []byte("1234"), 10*time.Second)
	count, err := r.redis.Strlen(mockCTX, "test")
	r.Require().NoError(err)
	r.Equal(4, count)
}

func (r *redisSuite) TestGet() {
	r.redis.Set(mockCTX, "test-key2", []byte("test-value"), Forever)
	cases := []struct {
		Key      string
		ErrIsNil bool
		ExpValue string
	}{
		{
			"test-key2",
			true,
			"test-value",
		},
		// Key not exist should return error
		{
			"some-key-not-exist",
			false,
			"not-important",
		},
	}
	for caseID, cas := range cases {
		value, err := r.redis.Get(mockCTX, cas.Key)
		if cas.ErrIsNil {
			r.Nil(err, fmt.Sprintf("case %d", caseID))
			r.Equal(cas.ExpValue, string(value), fmt.Sprintf("case %d", caseID))
		} else {
			r.NotNil(err, fmt.Sprintf("case %d", caseID))
		}
	}
}

func (r *redisSuite) TestGetZip() {
	key := "getzip"
	c := []byte("hello world")
	buf := &bytes.Buffer{}
	writer := gzip.NewWriter(buf)
	writer.Write(c)
	writer.Flush()
	writer.Close()
	r.redis.Set(mockCTX, key, buf.Bytes(), Forever)

	value, err := r.redis.GetZip(mockCTX, key)
	r.Equal(nil, err)
	r.Equal(string(c), string(value))
}

func (r *redisSuite) TestMGetZip() {
	m := map[string]string{
		"getzip-1": "hello world",
		"getzip-2": "17media",
	}
	for key, val := range m {
		buf := &bytes.Buffer{}
		writer := gzip.NewWriter(buf)
		writer.Write([]byte(val))
		writer.Flush()
		writer.Close()
		r.redis.Set(mockCTX, key, buf.Bytes(), Forever)
	}

	cases := []struct {
		Desc         string
		Keys         []string
		ExpectedErr  error
		ExpectedDest []redisModel.MVal
	}{
		{
			"MGetZip success",
			[]string{"getzip-1", "getzip-2"},
			nil,
			[]redisModel.MVal{
				redisModel.MVal{Valid: true, Value: []byte("hello world")},
				redisModel.MVal{Valid: true, Value: []byte("17media")},
			},
		},
		{
			"Key not exist should return empty value",
			[]string{"getzip-1", "getzip-not-exist"},
			nil,
			[]redisModel.MVal{
				redisModel.MVal{Valid: true, Value: []byte("hello world")},
				redisModel.MVal{Valid: false, Value: []byte("")},
			},
		},
		{
			"MGetZip 1 key success",
			[]string{"getzip-1"},
			nil,
			[]redisModel.MVal{
				redisModel.MVal{Valid: true, Value: []byte("hello world")},
			},
		},
		{
			"MGet 1 key but doesn't exist",
			[]string{"getzip-not-exist"},
			nil,
			[]redisModel.MVal{
				redisModel.MVal{Valid: false, Value: []byte("")},
			},
		},
	}
	for _, cas := range cases {
		dest, err := r.redis.MGetZip(mockCTX, cas.Keys)
		r.Equal(cas.ExpectedErr, err, cas.Desc)
		r.Equal(cas.ExpectedDest, dest, cas.Desc)
	}
}

func (r *redisSuite) TestMGet() {
	r.redis.Set(mockCTX, "test-key1", []byte("test-value1"), Forever)
	r.redis.Set(mockCTX, "test-key2", []byte("test-value2"), Forever)
	r.redis.Set(mockCTX, "test-key3", []byte("test-value3"), Forever)
	r.redis.client.Do(mockCTX, "HSET", "test-wrong-type", "test-field", "test-value")

	cases := []struct {
		Desc         string
		Keys         []string
		ExpectedErr  error
		ExpectedDest []redisModel.MVal
	}{
		{
			"MGet success",
			[]string{"test-key1", "test-key2", "test-key3"},
			nil,
			[]redisModel.MVal{
				redisModel.MVal{Valid: true, Value: []byte("test-value1")},
				redisModel.MVal{Valid: true, Value: []byte("test-value2")},
				redisModel.MVal{Valid: true, Value: []byte("test-value3")},
			},
		},
		{
			"Key not exist should return empty value",
			[]string{"test-key1", "some-key-not-exist", "test-key2"},
			nil,
			[]redisModel.MVal{
				redisModel.MVal{Valid: true, Value: []byte("test-value1")},
				redisModel.MVal{Valid: false, Value: []byte("")},
				redisModel.MVal{Valid: true, Value: []byte("test-value2")},
			},
		},
		{
			"MGet 1 key success",
			[]string{"test-key1"},
			nil,
			[]redisModel.MVal{
				redisModel.MVal{Valid: true, Value: []byte("test-value1")},
			},
		},
		{
			"MGet 1 key but doesn't exist",
			[]string{"some-key-not-exist"},
			nil,
			[]redisModel.MVal{
				redisModel.MVal{Valid: false, Value: []byte("")},
			},
		},
		{
			"MGet 1 key but wrong type",
			[]string{"test-wrong-type"},
			nil,
			[]redisModel.MVal{
				redisModel.MVal{Valid: false, Value: []byte("")},
			},
		},
	}

	for _, cas := range cases {
		dest, err := r.redis.MGet(mockCTX, cas.Keys)
		r.Equal(cas.ExpectedErr, err, cas.Desc)
		r.Equal(cas.ExpectedDest, dest, cas.Desc)
	}
}

func (r *redisSuite) TestDel() {
	r.NoError(r.redis.Set(mockCTX, "test-delete-key-1", []byte("test-delete-value"), Forever))
	r.NoError(r.redis.Set(mockCTX, "test-delete-key-2", []byte("test-delete-value"), Forever))
	r.NoError(r.redis.Set(mockCTX, "test-delete-key-3", []byte("test-delete-value"), Forever))
	cases := []struct {
		keys                  []string
		expectedDeletedKeyNum int
	}{
		{
			[]string{"test-delete-key-1"},
			1,
		},
		{
			[]string{"test-delete-key-2", "test-delete-key-3", "non-exist-key"},
			2,
		},
		{
			[]string{"non-exist-key"},
			0,
		},
	}

	for _, c := range cases {
		res, err := r.redis.Del(mockCTX, c.keys...)
		r.NoError(err)
		r.Equal(c.expectedDeletedKeyNum, res)
	}

	res, err := r.redis.Exists(mockCTX, "test-delete-key-1")
	r.NoError(err)
	r.Equal(false, res)
	res, err = r.redis.Exists(mockCTX, "test-delete-key-2")
	r.NoError(err)
	r.Equal(false, res)
	res, err = r.redis.Exists(mockCTX, "test-delete-key-3")
	r.NoError(err)
	r.Equal(false, res)
}

func (r *redisSuite) TestHSet() {
	cases := []struct {
		Key    string
		field  string
		val    string
		expire time.Duration
	}{
		{
			"Htest-key",
			"d1cd5a6a-3346-4263-85ef-c9f3a600de39",
			"test-value",
			10 * time.Second,
		},
		{
			"Htest-key",
			"d1cd5a6a-3346-4263-85ef-c9f3a600de59",
			"test-value1",
			Forever,
		},
	}
	for caseID, cas := range cases {
		err := r.redis.HSet(mockCTX, cas.Key, cas.field, []byte(cas.val), cas.expire)
		r.NoError(err)
		value, err := r.redis.client.Do(mockCTX, "HGET", cas.Key, cas.field).Text()
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.val, value, fmt.Sprintf("case %d", caseID))

		ttl, _ := r.redis.client.Do(mockCTX, "TTL", cas.Key).Int()
		if cas.expire == Forever {
			r.Equal(-1, ttl, fmt.Sprintf("case %d", caseID))
		} else {
			r.Equal(10, ttl, fmt.Sprintf("case %d", caseID))
		}
	}
}

func (r *redisSuite) TestUnlink() {
	r.NoError(r.redis.Set(mockCTX, "test-unlink-key-1", []byte("test-unlink-value"), Forever))
	r.NoError(r.redis.Set(mockCTX, "test-unlink-key-2", []byte("test-unlink-value"), Forever))
	r.NoError(r.redis.Set(mockCTX, "test-unlink-key-3", []byte("test-unlink-value"), Forever))
	cases := []struct {
		keysToUnlink []string
		keysExist    []struct {
			key   string
			exist bool
		}
		expectedUnlinkedKeyNum int
	}{
		{
			keysToUnlink: []string{"test-unlink-key-1"},
			keysExist: []struct {
				key   string
				exist bool
			}{
				{key: "test-unlink-key-1", exist: false},
				{key: "test-unlink-key-2", exist: true},
				{key: "test-unlink-key-3", exist: true},
			},
			expectedUnlinkedKeyNum: 1,
		},
		{
			keysToUnlink: []string{"test-unlink-key-2", "test-unlink-key-3", "non-exist-key"},
			keysExist: []struct {
				key   string
				exist bool
			}{
				{key: "test-unlink-key-1", exist: false},
				{key: "test-unlink-key-2", exist: false},
				{key: "test-unlink-key-3", exist: false},
			},
			expectedUnlinkedKeyNum: 2,
		},
		{
			keysToUnlink: []string{"non-exist-key"},
			keysExist: []struct {
				key   string
				exist bool
			}{
				{key: "test-unlink-key-1", exist: false},
				{key: "test-unlink-key-2", exist: false},
				{key: "test-unlink-key-3", exist: false},
			},
			expectedUnlinkedKeyNum: 0,
		},
	}

	for _, c := range cases {
		res, err := r.redis.Unlink(mockCTX, c.keysToUnlink...)
		r.NoError(err)
		r.Equal(c.expectedUnlinkedKeyNum, res)
		for _, keyExist := range c.keysExist {
			res, err := r.redis.Exists(mockCTX, keyExist.key)
			r.NoError(err)
			r.Equal(keyExist.exist, res)
		}
	}
}

func (r *redisSuite) TestHSetNX() {
	cases := []struct {
		Key    string
		field  string
		val    string
		expire time.Duration
	}{
		{
			"Htest-key",
			"d1cd5a6a-3346-4263-85ef-c9f3a600de39",
			"test-value",
			10 * time.Second,
		},
		{
			"Htest-key",
			"d1cd5a6a-3346-4263-85ef-c9f3a600de59",
			"test-value1",
			Forever,
		},
	}
	for caseID, cas := range cases {
		ok, err := r.redis.HSetNX(mockCTX, cas.Key, cas.field, []byte(cas.val), cas.expire)
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		value, err := r.redis.client.Do(mockCTX, "HGET", cas.Key, cas.field).Text()
		r.Equal(true, ok, fmt.Sprintf("case %d", caseID))
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.val, value, fmt.Sprintf("case %d", caseID))

		ttl, _ := r.redis.client.Do(mockCTX, "TTL", cas.Key).Int()
		if cas.expire == Forever {
			r.Equal(-1, ttl, fmt.Sprintf("case %d", caseID))
		} else {
			r.Equal(10, ttl, fmt.Sprintf("case %d", caseID))
		}
		// is already set, ok should be false
		ok, err = r.redis.HSetNX(mockCTX, cas.Key, cas.field, []byte(cas.val), cas.expire)
		r.Equal(false, ok, fmt.Sprintf("case %d", caseID))
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
	}
}

func (r *redisSuite) TestSetStructAndGetStruct() {
	type subStruct struct {
		I64 int64
		F64 float64
		Str string
	}
	type testStruct struct {
		I              int
		I32            int32
		I64            int64
		F32            float32
		F64            float64
		Str            string
		Slice          []string
		Map            map[string]string
		Struct         subStruct
		PtrStr         *string
		PtrI64         *int64
		PtrF64         *float64
		PtrStruct      *subStruct
		PtrStrSlice    *[]string
		PtrMap         *map[string]string
		SlicePtrStruct []*subStruct
		MapPtrStruct   map[string]*subStruct
		SliceStruct    []subStruct
		MapStruct      map[string]subStruct
	}
	ptrStr := "PtrStr"
	ptrI64 := int64(8)
	ptrF64 := 9.0
	ptrStrSlice := []string{"str8", "str9"}
	ptrMap := map[string]string{"str10": "str11"}

	cases := []struct {
		key    string
		val    testStruct
		expire time.Duration
	}{
		{
			"Struct",
			testStruct{
				I:           1,
				I32:         2,
				I64:         3,
				F32:         4.0,
				F64:         5.0,
				Str:         "str1",
				Slice:       []string{"str2", "str3"},
				Map:         map[string]string{"str4": "str5"},
				Struct:      subStruct{I64: 6, F64: 7.0, Str: "str6"},
				PtrStr:      &ptrStr,
				PtrI64:      &ptrI64,
				PtrF64:      &ptrF64,
				PtrStruct:   &subStruct{I64: 10, F64: 11.0, Str: "str7"},
				PtrStrSlice: &ptrStrSlice,
				PtrMap:      &ptrMap,
				SlicePtrStruct: []*subStruct{
					&subStruct{I64: 12, F64: 13.0, Str: "str12"},
					&subStruct{I64: 14, F64: 15.0, Str: "str13"},
				},
				MapPtrStruct: map[string]*subStruct{
					"str14": &subStruct{I64: 16, F64: 17.0, Str: "str15"},
					"str16": &subStruct{I64: 18, F64: 19.0, Str: "str17"},
				},
				SliceStruct: []subStruct{
					subStruct{I64: 20, F64: 21.0, Str: "str18"},
					subStruct{I64: 22, F64: 23.0, Str: "str19"},
				},
				MapStruct: map[string]subStruct{
					"str20": subStruct{I64: 24, F64: 25.0, Str: "str21"},
					"str22": subStruct{I64: 26, F64: 27.0, Str: "str23"},
				},
			},
			10 * time.Second,
		},
		{
			"Struct-empty",
			testStruct{},
			Forever,
		},
	}
	for _, cas := range cases {
		err := r.redis.SetStruct(mockCTX, cas.key, cas.val, cas.expire)
		r.NoError(err, cas.key)

		_, err = r.redis.HIncrby(mockCTX, cas.key, "I", 20)
		r.NoError(err, cas.key)

		_, err = r.redis.HIncrby(mockCTX, cas.key, "I32", 30)
		r.NoError(err, cas.key)

		_, err = r.redis.HIncrby(mockCTX, cas.key, "I64", 40)
		r.NoError(err, cas.key)

		dataFromRedis := testStruct{}
		err = r.redis.GetStruct(mockCTX, cas.key, &dataFromRedis)
		r.NoError(err, cas.key)
		r.Equal(cas.val.I+20, dataFromRedis.I, cas.key)
		r.Equal(cas.val.I32+30, dataFromRedis.I32, cas.key)
		r.Equal(cas.val.I64+40, dataFromRedis.I64, cas.key)
		r.Equal(cas.val.F32, dataFromRedis.F32, cas.key)
		r.Equal(cas.val.F64, dataFromRedis.F64, cas.key)
		r.Equal(cas.val.Str, dataFromRedis.Str, cas.key)
		r.EqualValues(cas.val.Slice, dataFromRedis.Slice, cas.key)
		r.EqualValues(cas.val.Map, dataFromRedis.Map, cas.key)
		r.EqualValues(cas.val.Struct, dataFromRedis.Struct, cas.key)
		if cas.val.PtrStr != nil {
			r.Equal(*cas.val.PtrStr, *dataFromRedis.PtrStr, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrStr, cas.key)
		}
		if cas.val.PtrI64 != nil {
			r.Equal(*cas.val.PtrI64, *dataFromRedis.PtrI64, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrI64, cas.key)
		}
		if cas.val.PtrF64 != nil {
			r.Equal(*cas.val.PtrF64, *dataFromRedis.PtrF64, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrF64, cas.key)
		}
		if cas.val.PtrStruct != nil {
			r.EqualValues(*cas.val.PtrStruct, *dataFromRedis.PtrStruct, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrStruct, cas.key)
		}
		if cas.val.PtrStrSlice != nil {
			r.EqualValues(*cas.val.PtrStrSlice, *dataFromRedis.PtrStrSlice, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrStrSlice, cas.key)
		}
		if cas.val.PtrMap != nil {
			r.EqualValues(*cas.val.PtrMap, *dataFromRedis.PtrMap, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrMap, cas.key)
		}
		r.EqualValues(cas.val.SlicePtrStruct, dataFromRedis.SlicePtrStruct, cas.key)
		r.EqualValues(cas.val.MapPtrStruct, dataFromRedis.MapPtrStruct, cas.key)
		r.EqualValues(cas.val.SliceStruct, dataFromRedis.SliceStruct, cas.key)
		r.EqualValues(cas.val.MapStruct, dataFromRedis.MapStruct, cas.key)
	}
}

func (r *redisSuite) TestSetStructAndGetStructPtr() {
	type subStruct struct {
		I64 int64
		F64 float64
		Str string
	}
	type testStruct struct {
		I              int
		I32            int32
		I64            int64
		F32            float32
		F64            float64
		Str            string
		Slice          []string
		Map            map[string]string
		Struct         subStruct
		PtrStr         *string
		PtrI64         *int64
		PtrF64         *float64
		PtrStruct      *subStruct
		PtrStrSlice    *[]string
		PtrMap         *map[string]string
		SlicePtrStruct []*subStruct
		MapPtrStruct   map[string]*subStruct
		SliceStruct    []subStruct
		MapStruct      map[string]subStruct
	}
	ptrStr := "PtrStr"
	ptrI64 := int64(8)
	ptrF64 := 9.0
	ptrStrSlice := []string{"str8", "str9"}
	ptrMap := map[string]string{"str10": "str11"}

	cases := []struct {
		key    string
		val    *testStruct
		expire time.Duration
	}{
		{
			"Struct",
			&testStruct{
				I:           1,
				I32:         2,
				I64:         3,
				F32:         4.0,
				F64:         5.0,
				Str:         "str1",
				Slice:       []string{"str2", "str3"},
				Map:         map[string]string{"str4": "str5"},
				Struct:      subStruct{I64: 6, F64: 7.0, Str: "str6"},
				PtrStr:      &ptrStr,
				PtrI64:      &ptrI64,
				PtrF64:      &ptrF64,
				PtrStruct:   &subStruct{I64: 10, F64: 11.0, Str: "str7"},
				PtrStrSlice: &ptrStrSlice,
				PtrMap:      &ptrMap,
				SlicePtrStruct: []*subStruct{
					&subStruct{I64: 12, F64: 13.0, Str: "str12"},
					&subStruct{I64: 14, F64: 15.0, Str: "str13"},
				},
				MapPtrStruct: map[string]*subStruct{
					"str14": &subStruct{I64: 16, F64: 17.0, Str: "str15"},
					"str16": &subStruct{I64: 18, F64: 19.0, Str: "str17"},
				},
				SliceStruct: []subStruct{
					subStruct{I64: 20, F64: 21.0, Str: "str18"},
					subStruct{I64: 22, F64: 23.0, Str: "str19"},
				},
				MapStruct: map[string]subStruct{
					"str20": subStruct{I64: 24, F64: 25.0, Str: "str21"},
					"str22": subStruct{I64: 26, F64: 27.0, Str: "str23"},
				},
			},
			10 * time.Second,
		},
		{
			"Struct-empty",
			&testStruct{},
			Forever,
		},
	}
	for _, cas := range cases {
		err := r.redis.SetStruct(mockCTX, cas.key, cas.val, cas.expire)
		r.NoError(err, cas.key)

		_, err = r.redis.HIncrby(mockCTX, cas.key, "I", 20)
		r.NoError(err, cas.key)

		_, err = r.redis.HIncrby(mockCTX, cas.key, "I32", 30)
		r.NoError(err, cas.key)

		_, err = r.redis.HIncrby(mockCTX, cas.key, "I64", 40)
		r.NoError(err, cas.key)

		dataFromRedis := testStruct{}
		err = r.redis.GetStruct(mockCTX, cas.key, &dataFromRedis)
		r.NoError(err, cas.key)
		r.Equal(cas.val.I+20, dataFromRedis.I, cas.key)
		r.Equal(cas.val.I32+30, dataFromRedis.I32, cas.key)
		r.Equal(cas.val.I64+40, dataFromRedis.I64, cas.key)
		r.Equal(cas.val.F32, dataFromRedis.F32, cas.key)
		r.Equal(cas.val.F64, dataFromRedis.F64, cas.key)
		r.Equal(cas.val.Str, dataFromRedis.Str, cas.key)
		r.EqualValues(cas.val.Slice, dataFromRedis.Slice, cas.key)
		r.EqualValues(cas.val.Map, dataFromRedis.Map, cas.key)
		r.EqualValues(cas.val.Struct, dataFromRedis.Struct, cas.key)
		if cas.val.PtrStr != nil {
			r.Equal(*cas.val.PtrStr, *dataFromRedis.PtrStr, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrStr, cas.key)
		}
		if cas.val.PtrI64 != nil {
			r.Equal(*cas.val.PtrI64, *dataFromRedis.PtrI64, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrI64, cas.key)
		}
		if cas.val.PtrF64 != nil {
			r.Equal(*cas.val.PtrF64, *dataFromRedis.PtrF64, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrF64, cas.key)
		}
		if cas.val.PtrStruct != nil {
			r.EqualValues(*cas.val.PtrStruct, *dataFromRedis.PtrStruct, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrStruct, cas.key)
		}
		if cas.val.PtrStrSlice != nil {
			r.EqualValues(*cas.val.PtrStrSlice, *dataFromRedis.PtrStrSlice, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrStrSlice, cas.key)
		}
		if cas.val.PtrMap != nil {
			r.EqualValues(*cas.val.PtrMap, *dataFromRedis.PtrMap, cas.key)
		} else {
			r.Nil(dataFromRedis.PtrMap, cas.key)
		}
		r.EqualValues(cas.val.SlicePtrStruct, dataFromRedis.SlicePtrStruct, cas.key)
		r.EqualValues(cas.val.MapPtrStruct, dataFromRedis.MapPtrStruct, cas.key)
		r.EqualValues(cas.val.SliceStruct, dataFromRedis.SliceStruct, cas.key)
		r.EqualValues(cas.val.MapStruct, dataFromRedis.MapStruct, cas.key)
	}
}

func (r *redisSuite) TestStructChanged() {
	type subStruct struct {
		I64 int64
		F64 float64
		Str string
	}
	type testStruct struct {
		I   int        `json:"i"`
		I32 int32      `json:"j"`
		Sub *subStruct `json:"sub"`
	}
	type testNewStruct struct {
		I int `json:"-"`
	}

	cases := []struct {
		key    string
		val    *testStruct
		res    *testNewStruct
		expire time.Duration
	}{
		{
			"remove integer",
			&testStruct{
				I:   1,
				I32: int32(1),
			},
			&testNewStruct{
				I: 1,
			},
			10 * time.Second,
		},
		{
			"remove sub struct",
			&testStruct{
				I: 1,
				Sub: &subStruct{
					I64: int64(1),
				},
			},
			&testNewStruct{
				I: 1,
			},
			10 * time.Second,
		},
		{
			"Struct-empty",
			&testStruct{},
			&testNewStruct{},
			Forever,
		},
	}
	for _, cas := range cases {
		err := r.redis.SetStruct(mockCTX, cas.key, cas.val, cas.expire)
		r.NoError(err, cas.key)

		dataFromRedis := &testNewStruct{}
		err = r.redis.GetStruct(mockCTX, cas.key, dataFromRedis)
		r.NoError(err, cas.key)
		r.Equal(cas.res, dataFromRedis, cas.key)
	}
}
func (r *redisSuite) TestSetStructFail() {
	cases := []struct {
		key    string
		val    interface{}
		expire time.Duration
	}{
		{
			"Struct-nil",
			nil,
			10 * time.Second,
		},
		{
			"Struct-slice",
			[]string{"test1", "test2"},
			10 * time.Second,
		},
		{
			"Struct-text",
			"test3",
			10 * time.Second,
		},
		{
			"Struct-int",
			171717,
			10 * time.Second,
		},
	}
	for _, cas := range cases {
		err := r.redis.SetStruct(mockCTX, cas.key, cas.val, cas.expire)
		r.Error(err, cas.key)
	}
}

func (r *redisSuite) TestGetStructFail() {
	data := map[string]interface{}{}
	err := r.redis.GetStruct(mockCTX, "not:exists:key", &data)
	r.Error(err)
}

func (r *redisSuite) TestMSet() {
	keyVals := map[string][]byte{"a": []byte("1"), "b": []byte("abcdef12345+-[]")}
	err := r.redis.MSet(mockCTX, keyVals, Forever)
	r.NoError(err)
	v, err := r.redis.get(mockCTX, "a", false)
	r.NoError(err)
	r.Equal("1", string(v))

	v, err = r.redis.get(mockCTX, "b", false)
	r.NoError(err)
	r.Equal("abcdef12345+-[]", string(v))
}

func (r *redisSuite) TestHMSet() {
	cases := []struct {
		Key    string
		ma     map[string][]byte
		expire time.Duration
	}{
		{
			"HMtest-key",
			map[string][]byte{"abc": []byte("1"), "sd": []byte("2")},
			10 * time.Second,
		},
	}
	for caseID, cas := range cases {
		err := r.redis.HMSet(mockCTX, cas.Key, cas.ma, cas.expire)
		r.NoError(err)

		value, err := r.redis.client.Do(mockCTX, "HGET", cas.Key, "abc").Text()
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal("1", value, fmt.Sprintf("case %d", caseID))

		value, err = r.redis.client.Do(mockCTX, "HGET", cas.Key, "sd").Text()
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal("2", value, fmt.Sprintf("case %d", caseID))

		ttl, _ := r.redis.client.Do(mockCTX, "TTL", cas.Key).Int()
		r.Equal(10, ttl, fmt.Sprintf("case %d", caseID))
	}

	err := r.redis.HMSet(mockCTX, "test-key", map[string][]byte{}, 1*time.Second)
	r.Equal("ERR wrong number of arguments for 'hset' command", err.Error())
}

func (r *redisSuite) TestHGet() {
	cases := []struct {
		key    string
		field  string
		val    string
		expire time.Duration
		expErr error
	}{
		{
			"Htest-key1",
			"d1cd5a6a-3346-4263-85ef-c9f3a600de39",
			"test-value",
			10 * time.Second,
			nil,
		},
		{
			"Htest-key1",
			"d1cd5a6a-3346-4263-85ef-c9f3a600de59",
			"test-value1",
			Forever,
			nil,
		},
		{
			nxKey,
			nxField,
			"",
			10 * time.Second,
			ErrNotFound,
		},
	}
	for caseID, cas := range cases {
		if cas.key != nxKey {
			err := r.redis.client.Do(mockCTX, "HSET", cas.key, cas.field, cas.val).Err()
			r.Require().NoError(err)
		}
		val, err := r.redis.HGet(mockCTX, cas.key, cas.field)
		r.Equal(cas.expErr, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.val, string(val), fmt.Sprintf("case %d", caseID))
	}
}

func (r *redisSuite) TestHGetAll() {
	r.redis.client.Do(mockCTX, "HSET", "Htest-key2", "d1cd5a6a-3346-4263-85ef-c9f3a600de39", "test-value")
	r.redis.client.Do(mockCTX, "HSET", "Htest-key2", "d1cd5a6a-3346-4263-85ef-c9f3a600de59", "test-value1")

	cases := []struct {
		Key  string
		smap map[string][]byte
		err  error
	}{
		{
			"Htest-key2",
			map[string][]byte{
				"d1cd5a6a-3346-4263-85ef-c9f3a600de39": []byte("test-value"),
				"d1cd5a6a-3346-4263-85ef-c9f3a600de59": []byte("test-value1"),
			},
			nil,
		},
		{
			"no-such-key",
			map[string][]byte{},
			ErrNotFound,
		},
	}
	for caseID, cas := range cases {
		mapstring, err := r.redis.HGetAll(mockCTX, cas.Key)
		r.Equal(cas.err, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.smap, mapstring, fmt.Sprintf("case %d", caseID))
	}
}

func (r *redisSuite) TestHDel() {
	r.redis.client.Do(mockCTX, "HSET", "Htest-key3", "test-field3", "test-value")

	cases := []struct {
		Key      string
		field    string
		value    string
		ExpValue []byte
	}{
		{
			"Htest-key3",
			"test-field3",
			"test-value",
			nil,
		},
	}
	for caseID, cas := range cases {
		val, err := r.redis.HGet(mockCTX, cas.Key, cas.field)
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.value, string(val), fmt.Sprintf("case %d", caseID))

		_, err = r.redis.HDel(mockCTX, cas.Key, cas.field)
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))

		val, err = r.redis.HGet(mockCTX, cas.Key, cas.field)
		r.Equal(redis.Nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.ExpValue, val, fmt.Sprintf("case %d", caseID))
	}
}

func (r *redisSuite) TestHLen() {
	r.redis.client.Do(mockCTX, "HSET", "Htest-key4", "test-field3", "test-value")
	r.redis.client.Do(mockCTX, "HSET", "Htest-key4", "test-field4", "test-value1")

	cases := []struct {
		Key    string
		value  string
		explen int
	}{
		{
			"Htest-key4",
			"test-value",
			2,
		},
		{
			"Htest-key767576656",
			"test-value",
			0,
		},
	}
	for caseID, cas := range cases {
		val, err := r.redis.HLen(mockCTX, cas.Key)
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.explen, val, fmt.Sprintf("case %d", caseID))
	}
}

func (r *redisSuite) TestIncrby() {
	r.redis.client.Do(mockCTX, "SET", "test-key5", "1")

	cases := []struct {
		Key      string
		addvalue int64
		expvalue int64
	}{
		{
			"test-key5",
			2,
			3,
		},
	}
	for caseID, cas := range cases {
		res, err := r.redis.Incrby(mockCTX, cas.Key, int(cas.addvalue))
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))

		val, _ := r.redis.Get(mockCTX, cas.Key)

		stringVal := string(val)
		intVal, err := strconv.ParseInt(stringVal, 10, 64)
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.expvalue, res, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.expvalue, intVal, fmt.Sprintf("case %d", caseID))
	}
}

func (r *redisSuite) TestHIncrby() {
	r.redis.client.Do(mockCTX, "HSET", "Htest-key10", "test-field5", 2)

	cases := []struct {
		Key      string
		field    string
		addvalue int64
		expvalue int64
	}{
		{
			"Htest-key10",
			"test-field5",
			2,
			4,
		},
	}
	for caseID, cas := range cases {
		res, err := r.redis.HIncrby(mockCTX, cas.Key, cas.field, int(cas.addvalue))
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Require().Equal(int64(4), res)

		val, _ := r.redis.HGet(mockCTX, cas.Key, cas.field)
		stringVal := string(val)
		intVal, err := strconv.ParseInt(stringVal, 10, 64)
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.expvalue, intVal, fmt.Sprintf("case %d", caseID))
	}
}

func (r *redisSuite) TestScanMatch() {
	for i := 0; i < 1000; i++ {
		r.redis.Set(mockCTX, fmt.Sprintf("test:key%d", i), []byte("test"), Forever)
	}

	cursor, items, err := r.redis.ScanMatch(mockCTX, 0, "*", 1)
	r.Require().NoError(err)
	r.Greater(cursor, int64(0))
	r.Greater(len(items), 0)

	cursor, items, err = r.redis.ScanMatch(mockCTX, cursor, "*", 1)
	r.Require().NoError(err)
	r.Greater(cursor, int64(0))
	r.Greater(len(items), 0)

	cursor, items, err = r.redis.ScanMatch(mockCTX, 0, "not-exist", 100)
	r.Require().NoError(err)
	r.Greater(cursor, int64(0))
	r.Len(items, 0, "len(items) is not 0")
}

func (r *redisSuite) TestHScan() {
	r.redis.client.Do(mockCTX, "HSET", "Htest-key556", "d1cd5a6a-3346-4263-85ef-c9f3a600de39", "test-value")
	r.redis.client.Do(mockCTX, "HSET", "Htest-key556", "d1cd5a6a-3346-4263-85ef-c9f3a600de59", "test-value1")

	cases := []struct {
		Key    string
		smap   map[string][]byte
		cursor int
	}{
		{
			"Htest-key556",
			map[string][]byte{
				"d1cd5a6a-3346-4263-85ef-c9f3a600de39": []byte("test-value"),
				"d1cd5a6a-3346-4263-85ef-c9f3a600de59": []byte("test-value1"),
			},
			0,
		},
	}
	for caseID, cas := range cases {
		mapstring, cursor, err := r.redis.HScan(mockCTX, cas.Key, 0, 10)
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.smap, mapstring, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.cursor, cursor)
	}
}

func (r *redisSuite) TestHScannil() {
	r.redis.client.Do(mockCTX, "HSET", "Htest-key556", "d1cd5a6a-3346-4263-85ef-c9f3a600de39", "test-value")
	r.redis.client.Do(mockCTX, "HSET", "Htest-key556", "d1cd5a6a-3346-4263-85ef-c9f3a600de59", "test-value1")

	cases := []struct {
		Key    string
		smap   map[string][]byte
		cursor int
	}{
		{
			"Htest-key55635345",
			map[string][]byte{},
			0,
		},
	}
	for caseID, cas := range cases {
		mapstring, cursor, err := r.redis.HScan(mockCTX, cas.Key, 0, 10)
		r.Equal(nil, err, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.smap, mapstring, fmt.Sprintf("case %d", caseID))
		r.Equal(cas.cursor, cursor)
	}
}

func (r *redisSuite) TestZAddXX() {
	r.redis.ZAddXX(mockCTX, "Ztest-key2", map[string]int{"abd": 1})
	val, err := r.redis.ZScore(mockCTX, "Ztest-key2", "abd")
	r.Equal(0, val)
	r.Equal(redis.Nil, err)

	r.redis.ZAdd(mockCTX, "Ztest-key2", map[string]int{"abd": 1})
	r.redis.ZAdd(mockCTX, "Ztest-key2", map[string]int{"abd": 3})
	val, err = r.redis.ZScore(mockCTX, "Ztest-key2", "abd")
	r.Equal(3, val)
	r.NoError(err)
}

func (r *redisSuite) TestZAddNX() {
	r.Require().NoError(r.redis.ZAdd(mockCTX, "Ztest-key1", map[string]int{"aaa": 1}))
	r.Require().NoError(r.redis.ZAddNX(mockCTX, "Ztest-key1", map[string]int{"aaa": 2, "bbb": 2}))

	val, err := r.redis.ZScore(mockCTX, "Ztest-key1", "aaa")
	r.Equal(1, val)
	r.NoError(err)

	val, err = r.redis.ZScore(mockCTX, "Ztest-key1", "bbb")
	r.Equal(2, val)
	r.NoError(err)
}

func (r *redisSuite) TestZAdd() {
	r.redis.ZAdd(mockCTX, "Ztest-key1", map[string]int{"abd": 1, "sdf": 2})

	val, err := r.redis.ZScore(mockCTX, "Ztest-key1", "abd")
	r.Equal(1, val)
	r.NoError(err)

	val, err = r.redis.ZScore(mockCTX, "Ztest-key1", "sdf")
	r.Equal(2, val)
	r.NoError(err)
}

func (r *redisSuite) TestZAddFloat() {
	r.redis.ZAddFloat(mockCTX, "Ztest-key1", map[string]float64{"abd": 1.987654, "sdf": 2.123456})

	val, err := r.redis.ZScoreFloat(mockCTX, "Ztest-key1", "abd")
	r.Equal(1.987654, val)
	r.NoError(err)

	val, err = r.redis.ZScoreFloat(mockCTX, "Ztest-key1", "sdf")
	r.Equal(2.123456, val)
	r.NoError(err)
}

func (r *redisSuite) TestZScore() {
	r.redis.ZAdd(mockCTX, "Ztest-key2", map[string]int{"b": 2})
	val, err := r.redis.ZScore(mockCTX, "Ztest-key2", "b")
	r.Equal(2, val)
	r.NoError(err)

	// Test for non-exist key
	val, err = r.redis.ZScore(mockCTX, nxKey, "b")
	r.Equal(0, val, "test for non-exist key")
	r.Equal(ErrNotFound, err, "test for non-exist key")
}

func (r *redisSuite) TestZScoreFloat() {
	r.redis.ZAddFloat(mockCTX, "Ztest-key2", map[string]float64{"b": 2.45})
	val, err := r.redis.ZScoreFloat(mockCTX, "Ztest-key2", "b")
	r.Require().Equal(2.45, val)
	r.Require().NoError(err)

	// test max integer of float64
	r.redis.ZAddFloat(mockCTX, "Ztest-keyMax", map[string]float64{"b": 9007199254740992})
	val, err = r.redis.ZScoreFloat(mockCTX, "Ztest-keyMax", "b")
	r.Require().Equal(float64(9007199254740992), val)
	// numbers over 2^53 are only approximation
	r.Require().Equal(float64(9007199254740993), val)
	r.Require().NoError(err)

	// Test for non-exist key
	val, err = r.redis.ZScoreFloat(mockCTX, nxKey, "b")
	r.Require().Equal(0.0, val, "test for non-exist key")
	r.Require().Equal(ErrNotFound, err, "test for non-exist key")
}

func (r *redisSuite) TestZIncrby() {
	r.redis.ZAdd(mockCTX, "Ztest-key3", map[string]int{"b": 2})
	val, err := r.redis.ZIncrby(mockCTX, "Ztest-key3", "b", 2)
	r.NoError(err)
	r.Equal(4, val)
}

func (r *redisSuite) TestZIncrbyFloat() {
	r.redis.ZAddFloat(mockCTX, "Ztest-key3", map[string]float64{"b": 2})
	val, err := r.redis.ZIncrbyFloat(mockCTX, "Ztest-key3", "b", 5.8)
	r.Require().NoError(err)
	r.Require().Equal(7.8, val)
}

func (r *redisSuite) TestZScan() {
	r.redis.ZAdd(mockCTX, "Ztest-key4", map[string]int{"b": 5})
	r.redis.ZAdd(mockCTX, "Ztest-key4", map[string]int{"c": 2})
	r.redis.ZAdd(mockCTX, "Ztest-key4", map[string]int{"d": 1})
	r.redis.ZAdd(mockCTX, "Ztest-key4", map[string]int{"e": 10})
	m := map[string]int{"d": 1, "c": 2, "b": 5, "e": 10}
	ma, cursor, err := r.redis.ZScan(mockCTX, "Ztest-key4", 0, 10)
	r.NoError(err)
	r.Equal(m, ma)
	r.Equal(0, cursor)
}

func (r *redisSuite) TestZCard() {
	r.redis.ZAdd(mockCTX, "Ztest-key5", map[string]int{"b": 5})
	r.redis.ZAdd(mockCTX, "Ztest-key5", map[string]int{"c": 2})
	r.redis.ZAdd(mockCTX, "Ztest-key5", map[string]int{"d": 1})
	r.redis.ZAdd(mockCTX, "Ztest-key5", map[string]int{"e": 10})
	val, err := r.redis.ZCard(mockCTX, "Ztest-key5")
	r.NoError(err)
	r.Equal(4, val)
}

func (r *redisSuite) TestZCount() {
	r.redis.ZAdd(mockCTX, "Ztest-key5", map[string]int{"b": 5})
	r.redis.ZAdd(mockCTX, "Ztest-key5", map[string]int{"c": 2})
	r.redis.ZAdd(mockCTX, "Ztest-key5", map[string]int{"d": 1})
	r.redis.ZAdd(mockCTX, "Ztest-key5", map[string]int{"e": 10})
	tests := []struct {
		Min      string
		Max      string
		ExpCount int
	}{
		{
			Min: "-inf", Max: "inf", ExpCount: 4,
		},
		{
			Min: "5", Max: "inf", ExpCount: 2,
		},
		{
			Min: "1", Max: "5", ExpCount: 3,
		},
		{
			Min: "-inf", Max: "2", ExpCount: 2,
		},
		{
			Min: "3", Max: "6", ExpCount: 1,
		},
		{
			Min: "30", Max: "50", ExpCount: 0,
		},
	}
	for _, test := range tests {
		val, err := r.redis.ZCount(mockCTX, "Ztest-key5", test.Min, test.Max)
		r.NoError(err)
		r.Equal(test.ExpCount, val)
	}
}

func (r *redisSuite) TestZREVRANGE() {
	for i := 0; i < 1000; i++ {
		r.redis.ZAddFloat(mockCTX, "Ztest-key6", map[string]float64{strconv.Itoa(i): float64(i)})
	}
	val, err := r.redis.ZRevrange(mockCTX, "Ztest-key6", 0, 10)
	r.Equal(val, []string{"999", "998", "997", "996", "995", "994", "993", "992", "991", "990"})
	r.NoError(err)
}

func (r *redisSuite) TestRANGE() {
	for i := 0; i < 1000; i++ {
		r.redis.ZAddFloat(mockCTX, "Ztest-key7", map[string]float64{strconv.Itoa(i): float64(i)})
	}
	val, err := r.redis.ZRange(mockCTX, "Ztest-key7", 0, 10)
	r.Equal(val, []string{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"})
	r.NoError(err)
}

func (r *redisSuite) TestZREVRANGEOffset() {
	for i := 0; i < 1000; i++ {
		r.redis.ZAddFloat(mockCTX, "Ztest-key6", map[string]float64{strconv.Itoa(i): float64(i)})

	}
	val, err := r.redis.ZRevrange(mockCTX, "Ztest-key6", 4, 10)
	r.Equal([]string{"995", "994", "993", "992", "991", "990", "989", "988", "987", "986"}, val)
	r.NoError(err)
}

func (r *redisSuite) TestZRevRangeWithScores() {
	for i := 0; i < 1000; i++ {
		r.redis.ZAddFloat(mockCTX, "Ztest-key10", map[string]float64{strconv.Itoa(i): float64(i)})

	}
	val, err := r.redis.ZRevRangeWithScores(mockCTX, "Ztest-key10", 0, 10)
	r.Equal([]redisModel.ZVal{
		{"999", 999},
		{"998", 998},
		{"997", 997},
		{"996", 996},
		{"995", 995},
		{"994", 994},
		{"993", 993},
		{"992", 992},
		{"991", 991},
		{"990", 990},
	}, val)
	r.NoError(err)
}

func (r *redisSuite) TestZRevRangeWithFloatScores() {
	for i := 0; i < 1000; i++ {
		r.redis.ZAddFloat(mockCTX, "Ztest-key10", map[string]float64{strconv.Itoa(i): float64(i) * 0.001})

	}
	val, err := r.redis.ZRevRangeWithFloatScores(mockCTX, "Ztest-key10", 0, 10)
	r.Equal([]redisModel.ZFloatVal{
		{"999", 0.999},
		{"998", 0.998},
		{"997", 0.997},
		{"996", 0.996},
		{"995", 0.995},
		{"994", 0.994},
		{"993", 0.993},
		{"992", 0.992},
		{"991", 0.991},
		{"990", 0.990},
	}, val)
	r.NoError(err)
}

func (r *redisSuite) TestZRangeByScoreWithScores() {
	for i := 0; i < 1000; i++ {
		r.redis.ZAddFloat(mockCTX, "Ztest-key-range-by-score", map[string]float64{strconv.Itoa(i): float64(i)})
	}

	val, err := r.redis.ZRangeByScoreWithScores(mockCTX, "Ztest-key-range-by-score", "500", "505")
	r.NoError(err)
	r.Equal([]redisModel.ZVal{
		{"500", 500},
		{"501", 501},
		{"502", 502},
		{"503", 503},
		{"504", 504},
		{"505", 505},
	}, val)

	val, err = r.redis.ZRangeByScoreWithScores(mockCTX, "Ztest-key-range-by-score", "995", "+inf")
	r.NoError(err)
	r.Equal([]redisModel.ZVal{
		{"995", 995},
		{"996", 996},
		{"997", 997},
		{"998", 998},
		{"999", 999},
	}, val)

	val, err = r.redis.ZRangeByScoreWithScores(mockCTX, "Ztest-key-range-by-score", "234", "234")
	r.NoError(err)
	r.Equal([]redisModel.ZVal{
		{"234", 234},
	}, val)

	val, err = r.redis.ZRangeByScoreWithScores(mockCTX, "Ztest-key-range-by-score", "101", "100")
	r.NoError(err)
	r.Equal([]redisModel.ZVal{}, val)

	val, err = r.redis.ZRangeByScoreWithScores(mockCTX, "not-exist", "-inf", "+inf")
	r.NoError(err)
	r.Equal([]redisModel.ZVal{}, val)
}

func (r *redisSuite) TestZRevRangeByScoreWithScores() {
	for i := 0; i < 1000; i++ {
		r.redis.ZAddFloat(mockCTX, "Ztest-key11", map[string]float64{strconv.Itoa(i): float64(i)})
	}
	val, err := r.redis.ZRevRangeByScoreWithScores(mockCTX, "Ztest-key11", "990", "+inf")
	r.Equal([]redisModel.ZVal{
		{"999", 999},
		{"998", 998},
		{"997", 997},
		{"996", 996},
		{"995", 995},
		{"994", 994},
		{"993", 993},
		{"992", 992},
		{"991", 991},
		{"990", 990},
	}, val)
	r.NoError(err)
}

func (r *redisSuite) TestZRevRangeByScoreWithFloatScores() {
	for i := 0; i < 1000; i++ {
		r.redis.ZAddFloat(mockCTX, "Ztest-key11", map[string]float64{strconv.Itoa(i): float64(i) * 0.001})
	}
	val, err := r.redis.ZRevRangeByScoreWithFloatScores(mockCTX, "Ztest-key11", "0.99", "+inf")
	r.Equal([]redisModel.ZFloatVal{
		{"999", 0.999},
		{"998", 0.998},
		{"997", 0.997},
		{"996", 0.996},
		{"995", 0.995},
		{"994", 0.994},
		{"993", 0.993},
		{"992", 0.992},
		{"991", 0.991},
		{"990", 0.990},
	}, val)
	r.NoError(err)
}

func (r *redisSuite) TestZRem() {
	r.redis.ZAdd(mockCTX, "Ztest-key7", map[string]int{"b": 2})

	err := r.redis.ZRem(mockCTX, "Ztest-key7", "b")
	r.NoError(err)
	_, err = r.redis.ZScore(mockCTX, "Ztest-key7", "b")
	r.Equal(redis.Nil, err)

	r.redis.ZAdd(mockCTX, "Ztest-key7", map[string]int{"a": 1, "b": 2})
	r.NoError(r.redis.ZRem(mockCTX, "Ztest-key7", "a", "b"))
	_, err = r.redis.ZScore(mockCTX, "Ztest-key7", "a")
	r.Equal(redis.Nil, err)
	_, err = r.redis.ZScore(mockCTX, "Ztest-key7", "b")
	r.Equal(redis.Nil, err)
}

func (r *redisSuite) TestZRemRangeByScore() {
	key := "ZRemRangeByScore-key"
	r.redis.ZAdd(mockCTX, key, map[string]int{"one": 1})
	r.redis.ZAdd(mockCTX, key, map[string]int{"two": 2})
	r.redis.ZAdd(mockCTX, key, map[string]int{"three": 3})

	count, err := r.redis.ZRemRangeByScore(mockCTX, key, "0", "2")
	r.NoError(err)
	r.Equal(2, count)
	score, err := r.redis.ZScore(mockCTX, key, "three")
	r.NoError(err)
	r.Equal(3, score)

	_, err = r.redis.ZScore(mockCTX, key, "one")
	r.Equal(redis.Nil, err)

	count, err = r.redis.ZRemRangeByScore(mockCTX, key, "0", "10")
	r.NoError(err)
	r.Equal(1, count)
	length, err := r.redis.ZRange(mockCTX, key, 0, 100)
	r.NoError(err)
	r.Equal(0, len(length))
}

func (r *redisSuite) TestZRevRank() {
	r.redis.ZAdd(mockCTX, "ZRevRank-key", map[string]int{"one": 1})
	r.redis.ZAdd(mockCTX, "ZRevRank-key", map[string]int{"two": 2})
	r.redis.ZAdd(mockCTX, "ZRevRank-key", map[string]int{"three": 3})

	rank, err := r.redis.ZRevRank(mockCTX, "ZRevRank-key", "one")
	r.NoError(err, "test rank 2")
	r.Equal(2, rank, "test rank 2")

	rank, err = r.redis.ZRevRank(mockCTX, "ZRevRank-key", "two")
	r.NoError(err, "test rank 1")
	r.Equal(1, rank, "test rank 1")

	rank, err = r.redis.ZRevRank(mockCTX, "ZRevRank-key", "three")
	r.NoError(err, "test rank 0")
	r.Equal(0, rank, "test rank 0")

	rank, err = r.redis.ZRevRank(mockCTX, "ZRevRank-key", "four")
	r.Equal(ErrNotFound, err, "test member not exist")
	r.Equal(0, rank, "test member not exist")

	rank, err = r.redis.ZRevRank(mockCTX, "ZRevRank-non-key", "four")
	r.Equal(ErrNotFound, err, "test key not exist")
	r.Equal(0, rank, "test key not exist")
}

func (r *redisSuite) TestMapToZ() {
	m := map[string]int{"abc": 1234}
	zs := mapToZ(m)
	r.Equal([]redis.Z{{Score: 1234, Member: "abc"}}, zs)
}

func (r *redisSuite) TestMapToZFloat() {
	m := map[string]float64{"abc": 123.4}
	zs := mapToZFloat(m)
	r.Equal([]redis.Z{{Score: 123.4, Member: "abc"}}, zs)
}

func (r *redisSuite) TestSetNX() {
	r.redis.Set(mockCTX, "Ntest-key7", []byte("2"), 20*time.Second)
	res, err := r.redis.SetNX(mockCTX, "Ntest-key7", []byte("2"), 10*time.Second)
	r.NoError(err)
	r.False(res)

	res, err = r.redis.SetNX(mockCTX, "Ntest-key8", []byte("2"), 10*time.Second)
	r.NoError(err)
	r.True(res)

	ttl, err := r.redis.client.Do(mockCTX, "TTL", "Ntest-key8").Int()
	r.NoError(err)
	r.Equal(10, ttl)

	res, err = r.redis.SetNX(mockCTX, "Ntest-key9", []byte("2"), Forever)
	r.NoError(err)
	r.True(res)

	ttl, err = r.redis.client.Do(mockCTX, "TTL", "Ntest-key9").Int()
	r.NoError(err)
	r.Equal(-1, ttl)
}

func (r *redisSuite) TestExpire() {
	err := r.redis.Set(mockCTX, "key", []byte("val"), 0)
	r.Require().NoError(err)

	err = r.redis.Expire(mockCTX, "key", 10*time.Second)
	r.NoError(err)

	ttl, err := r.redis.TTL(mockCTX, "key")
	r.NoError(err)
	r.Equal(10, ttl)

	err = r.redis.Expire(mockCTX, "nonexistent-key", 10*time.Second)
	r.Equal(redisModel.ErrExpireNotExistOrTimeout, err)

	// test Persist command
	err = r.redis.Expire(mockCTX, "key", Forever)
	r.NoError(err)
	ttl, err = r.redis.TTL(mockCTX, "key")
	r.Equal(redisModel.ErrNoTTL, err)
	r.Equal(-1, ttl)

	// test Persist command for non existent key
	err = r.redis.Expire(mockCTX, "nonexistent-key", Forever)
	r.Equal(redisModel.ErrExpireNotExistOrTimeout, err)

	// test Persist command for multiple invoke case
	err = r.redis.Set(mockCTX, "no-timeout-key", []byte("val"), 0)
	r.Require().NoError(err)
	ttl, err = r.redis.TTL(mockCTX, "no-timeout-key")
	r.Equal(redisModel.ErrNoTTL, err)
	r.Equal(-1, ttl)
	err = r.redis.Expire(mockCTX, "no-timeout-key", Forever)
	r.Require().NoError(err)
}

func (r *redisSuite) TestSAddFullInfo() {
	val, err := r.redis.SAddFullInfo(mockCTX, "ZAdd-key1", "ZAdd-val1")
	r.Require().NoError(err)
	r.Equal(int64(1), val)
	val, err = r.redis.SAddFullInfo(mockCTX, "ZAdd-key1", "ZAdd-val1")
	r.Require().NoError(err)
	r.Equal(int64(0), val)
	v, err := r.redis.SMembers(mockCTX, "ZAdd-key1")
	r.Nil(err)
	r.Equal(len(v), 1)
	r.Equal(true, v[0] == "ZAdd-val1")
}

func (r *redisSuite) TestSAddAndSMembers() {
	r.NoError(r.redis.SAdd(mockCTX, "ZAdd-key1", "ZAdd-val1"))
	r.NoError(r.redis.SAdd(mockCTX, "ZAdd-key1", "ZAdd-val2"))
	r.NoError(r.redis.SAdd(mockCTX, "ZAdd-key1", "ZAdd-val2"))
	v, err := r.redis.SMembers(mockCTX, "ZAdd-key1")
	r.Nil(err)
	r.Equal(len(v), 2)
	r.Equal(true, v[0] == "ZAdd-val1" || v[1] == "ZAdd-val1")
	r.Equal(true, v[0] == "ZAdd-val2" || v[1] == "ZAdd-val2")
}

func (r *redisSuite) TestSAddAndSRemWithMembers() {
	r.NoError(r.redis.SAdd(mockCTX, "ZAdd-key1", []string{"ZAdd-val3", "ZAdd-val4"}...))
	r.NoError(r.redis.SAdd(mockCTX, "ZAdd-key1", []string{"ZAdd-val5", "ZAdd-val6"}...))
	v, err := r.redis.SMembers(mockCTX, "ZAdd-key1")
	r.NoError(err)
	r.Equal(len(v), 4)
	r.NoError(r.redis.SRem(mockCTX, "ZAdd-key1", string("ZAdd-val5")))
	r.NoError(r.redis.SRem(mockCTX, "ZAdd-key1", []string{"ZAdd-val3", "ZAdd-val6"}...))
	v, err = r.redis.SMembers(mockCTX, "ZAdd-key1")
	r.NoError(err)
	r.Equal(len(v), 1)
	r.Equal(true, v[0] == "ZAdd-val4")

	r.Error(r.redis.SAdd(mockCTX, "ZAdd-key1"))
	r.Error(r.redis.SRem(mockCTX, "ZAdd-key1"))
}

func (r *redisSuite) TestSRem() {
	r.NoError(r.redis.SAdd(mockCTX, "ZAdd-key1", "ZAdd-val1"))
	v, err := r.redis.SMembers(mockCTX, "ZAdd-key1")
	r.Nil(err)
	r.Equal(len(v), 1)
	r.Equal(true, v[0] == "ZAdd-val1")
	r.NoError(r.redis.SRem(mockCTX, "ZAdd-key1", "ZAdd-val1"))
	v, err = r.redis.SMembers(mockCTX, "ZAdd-key1")
	r.Nil(err)
	r.Equal(len(v), 0)
}

func (r *redisSuite) TestSPop() {
	r.NoError(r.redis.SAdd(mockCTX, "ZAdd-key1", "ZAdd-val1"))
	v, err := r.redis.SPop(mockCTX, "ZAdd-key1")
	r.Nil(err)
	r.Equal(v, "ZAdd-val1")
	r.NoError(r.redis.SRem(mockCTX, "ZAdd-key1", "ZAdd-val1"))
	_, err = r.redis.SPop(mockCTX, "ZAdd-key1")
	r.Equal(ErrNotFound, err)
}

func (r *redisSuite) TestSMPop() {
	expected := set.New()
	expected.Add("ZAdd-val1", "ZAdd-val2", "ZAdd-val3", "ZAdd-val4")
	r.Require().NoError(r.redis.SAdd(mockCTX, "ZAdd-key1", "ZAdd-val1", "ZAdd-val2", "ZAdd-val3", "ZAdd-val4"))
	v, err := r.redis.SMPop(mockCTX, "ZAdd-key1", 2)
	r.Require().NoError(err)
	r.Require().Equal(2, len(v))
	for _, str := range v {
		expected.Remove(str)
	}
	v, err = r.redis.SMPop(mockCTX, "ZAdd-key1", 6)
	r.Require().NoError(err)
	r.Require().Equal(2, len(v))
	for _, str := range v {
		expected.Remove(str)
	}
	r.Require().True(expected.IsEmpty())
}

func (r *redisSuite) TestZPopMin() {
	r.Require().NoError(r.redis.ZAdd(mockCTX, "ZAdd-key1", map[string]int{"ZAdd-val4": 400}))
	r.Require().NoError(r.redis.ZAdd(mockCTX, "ZAdd-key1", map[string]int{"ZAdd-val3": 300}))
	r.Require().NoError(r.redis.ZAdd(mockCTX, "ZAdd-key1", map[string]int{"ZAdd-val2": 200}))
	r.Require().NoError(r.redis.ZAdd(mockCTX, "ZAdd-key1", map[string]int{"ZAdd-val1": 100}))

	v, err := r.redis.ZPopMin(mockCTX, "ZAdd-key1", 2)
	r.Require().NoError(err)
	r.Require().Equal([]redisModel.ZVal{{"ZAdd-val1", 100}, {"ZAdd-val2", 200}}, v)

	v, err = r.redis.ZPopMin(mockCTX, "ZAdd-key1", 6)
	r.Require().NoError(err)
	r.Require().Equal([]redisModel.ZVal{{"ZAdd-val3", 300}, {"ZAdd-val4", 400}}, v)

	v, err = r.redis.ZPopMin(mockCTX, "ZAdd-key1", 6)
	r.Require().NoError(err)
	r.Require().Equal(0, len(v))
}

func (r *redisSuite) TestHMGet() {
	key := "hset-key"
	fs := []string{"field1", "field2", nxField, "field4", "emptyValField"}
	vs := []string{"1", "2", "", "4", ""}
	for i, f := range fs {
		if f != nxField {
			r.redis.HSet(mockCTX, key, f, []byte(vs[i]), 30*time.Second)
		}
	}

	mVals, err := r.redis.HMGet(mockCTX, key, fs...)
	r.Require().NoError(err, "test for hset-key")
	r.Equal(len(fs), len(mVals), "test for hset-key")
	for i, f := range fs {
		if f == nxField {
			r.Equal(false, mVals[i].Valid, "test for hset-key")
			r.Equal(vs[i], "", "test for hset-key")
		} else {
			r.Equal(true, mVals[i].Valid, "test for hset-key")
			r.Equal(vs[i], string(mVals[i].Value), "test for hset-key")
		}
	}

	novals, err := r.redis.HMGet(mockCTX, nxKey, "n", "o", "", "v")
	r.Require().NoError(err, "test for non exist hset-key")
	r.Equal(4, len(novals), "test for non exist hset-key")
	for _, v := range novals {
		r.Equal(false, v.Valid, "test for non exist hset-key")
		r.Equal("", string(v.Value), "test for non exist hset-key")
	}
}

func (r *redisSuite) TestSIsMember() {
	key := "test-key"
	res, err := r.redis.SIsMember(mockCTX, key, "data1")
	r.NoError(err)
	r.Equal(false, res)

	err = r.redis.SAdd(mockCTX, key, "data1")
	r.NoError(err)
	err = r.redis.SAdd(mockCTX, key, "data2")
	r.NoError(err)

	res, err = r.redis.SIsMember(mockCTX, key, "data1")
	r.NoError(err)
	r.Equal(true, res)

	res, err = r.redis.SIsMember(mockCTX, key, "data2")
	r.NoError(err)
	r.Equal(true, res)

	err = r.redis.SRem(mockCTX, key, "data1")
	r.NoError(err)

	res, err = r.redis.SIsMember(mockCTX, key, "data1")
	r.NoError(err)
	r.Equal(false, res)
}

func (r *redisSuite) TestLRange() {
	key := "test-key"
	r.NoError(r.redis.RPush(mockCTX, key, []byte("TestLRange-1")))
	r.NoError(r.redis.RPush(mockCTX, key, []byte("TestLRange-2")))
	r.NoError(r.redis.RPush(mockCTX, key, []byte("TestLRange-3")))

	res, err := r.redis.LRange(mockCTX, key, 0, 3)
	r.NoError(err)
	vs := [][]byte{[]byte("TestLRange-1"), []byte("TestLRange-2"), []byte("TestLRange-3")}
	r.Equal(vs, res)

	res, err = r.redis.LRange(mockCTX, key, 0, 10000)
	r.NoError(err)
	vs = [][]byte{[]byte("TestLRange-1"), []byte("TestLRange-2"), []byte("TestLRange-3")}
	r.Equal(vs, res)

	res, err = r.redis.LRange(mockCTX, key, 2, 1)
	r.NoError(err)
	r.Equal([][]byte{[]byte("TestLRange-3")}, res)
}

func (r *redisSuite) TestPushPop() {
	key := "test-key"
	_, err := r.redis.LPop(mockCTX, key)
	r.Error(err)
	_, err = r.redis.RPop(mockCTX, key)
	r.Error(err)

	r.NoError(r.redis.LPush(mockCTX, key, []byte("TestPushPop-1")))
	r.NoError(r.redis.LPush(mockCTX, key, []byte("TestPushPop-2")))
	res, err := r.redis.LPop(mockCTX, key)
	r.NoError(err)
	r.Equal([]byte("TestPushPop-2"), res)

	r.NoError(r.redis.RPush(mockCTX, key, []byte("TestPushPop-3")))

	res, err = r.redis.RPop(mockCTX, key)
	r.NoError(err)
	r.Equal([]byte("TestPushPop-3"), res)

	res, err = r.redis.LPop(mockCTX, key)
	r.NoError(err)
	r.Equal([]byte("TestPushPop-1"), res)
}

func (r *redisSuite) TestLRem() {
	key := "test-key"
	tests := []struct {
		Desc          string
		Element       []string
		Count         int
		RemoveElement string
		ExpCount      int
		ExpElement    []string
	}{
		{
			// Note that non-existing keys are treated like empty lists,
			// so when key does not exist, the command will always return 0.
			Desc:          "non-existing key",
			Element:       []string{},
			Count:         0,
			RemoveElement: "",
			ExpCount:      0,
			ExpElement:    []string{},
		},
		{
			Desc:          "remove one from head",
			Element:       []string{"a", "b", "c"},
			Count:         1,
			RemoveElement: "b",
			ExpCount:      1,
			ExpElement:    []string{"c", "a"},
		},
		{
			Desc:          "remove one from head with duplicated element",
			Element:       []string{"a", "b", "a", "c"},
			Count:         1,
			RemoveElement: "a",
			ExpCount:      1,
			ExpElement:    []string{"c", "b", "a"},
		},
		{
			Desc:          "remove two from head with duplicated element",
			Element:       []string{"a", "b", "a", "a", "c"},
			Count:         2,
			RemoveElement: "a",
			ExpCount:      2,
			ExpElement:    []string{"c", "b", "a"},
		},
		{
			Desc:          "remove one from tail",
			Element:       []string{"a", "b", "c"},
			Count:         -1,
			RemoveElement: "b",
			ExpCount:      1,
			ExpElement:    []string{"c", "a"},
		},
		{
			Desc:          "remove one from tail with duplicated element",
			Element:       []string{"a", "b", "a", "c"},
			Count:         -1,
			RemoveElement: "a",
			ExpCount:      1,
			ExpElement:    []string{"c", "a", "b"},
		},
		{
			Desc:          "remove two from tail with duplicated element",
			Element:       []string{"a", "b", "a", "a", "c"},
			Count:         -2,
			RemoveElement: "a",
			ExpCount:      2,
			ExpElement:    []string{"c", "a", "b"},
		},
		{
			Desc:          "remove all elements equal to input",
			Element:       []string{"a", "b", "a", "a", "c"},
			Count:         0,
			RemoveElement: "a",
			ExpCount:      3,
			ExpElement:    []string{"c", "b"},
		},
	}

	for _, test := range tests {
		r.SetupTest()

		for _, e := range test.Element {
			err := r.redis.LPush(mockCTX, key, []byte(e))
			r.NoError(err, test.Desc)
		}
		count, err := r.redis.LRem(mockCTX, key, test.Count, []byte(test.RemoveElement))
		r.NoError(err, test.Desc)
		r.Equal(test.ExpCount, count, test.Desc)

		actual, err := r.redis.LRange(mockCTX, key, 0, 10)
		r.NoError(err, test.Desc)
		r.Len(actual, len(test.ExpElement), test.Desc)
		for i := 0; i < len(actual); i++ {
			r.Equal(test.ExpElement[i], string(actual[i]), test.Desc)
		}

		r.TearDownTest()
	}
}

func (r *redisSuite) TestLIndex() {
	key := "test-key"
	_, err := r.redis.LIndex(mockCTX, key, 0)
	r.Equal(ErrNotFound, err)

	r.NoError(r.redis.LPush(mockCTX, key, []byte("TestLIndex-1")))
	r.NoError(r.redis.LPush(mockCTX, key, []byte("TestLIndex-2")))

	res, err := r.redis.LIndex(mockCTX, key, 0)
	r.NoError(err)
	r.Equal([]byte("TestLIndex-2"), res)

	res, err = r.redis.LIndex(mockCTX, key, 1)
	r.NoError(err)
	r.Equal([]byte("TestLIndex-1"), res)

	res, err = r.redis.LIndex(mockCTX, key, -1)
	r.NoError(err)
	r.Equal([]byte("TestLIndex-1"), res)
}

func (r *redisSuite) TestLSet() {
	key := "test-key"
	err := r.redis.LSet(mockCTX, key, 0, []byte("TestLSetItem-1"))
	r.Error(err)

	r.NoError(r.redis.LPush(mockCTX, key, []byte("TestLSetItem-1")))
	r.NoError(r.redis.LPush(mockCTX, key, []byte("TestLSetItem-2")))

	err = r.redis.LSet(mockCTX, key, 0, []byte("TestLSetItem-3"))
	r.NoError(err)

	res, err := r.redis.LIndex(mockCTX, key, 0)
	r.NoError(err)
	r.Equal([]byte("TestLSetItem-3"), res)
}

func (r *redisSuite) TestLLen() {
	key := "test-key"
	res, err := r.redis.LLen(mockCTX, "non-exist-key")
	r.NoError(err)
	r.Equal(0, res)

	r.NoError(r.redis.LPush(mockCTX, key, []byte("TestLLen-1")))
	r.NoError(r.redis.LPush(mockCTX, key, []byte("TestLLen-2")))

	res, err = r.redis.LLen(mockCTX, key)
	r.NoError(err)
	r.Equal(2, res)

	r.NoError(r.redis.LPush(mockCTX, key, []byte("TestLLen-3")))

	res, err = r.redis.LLen(mockCTX, key)
	r.NoError(err)
	r.Equal(3, res)
}

func (r *redisSuite) TestSCard() {
	key := "test-key"
	res, err := r.redis.SCard(mockCTX, key)
	r.NoError(err)
	r.Equal(0, res)

	err = r.redis.SAdd(mockCTX, key, "data1")
	r.NoError(err)
	err = r.redis.SAdd(mockCTX, key, "data2")
	r.NoError(err)

	res, err = r.redis.SCard(mockCTX, key)
	r.NoError(err)
	r.Equal(2, res)

	err = r.redis.SRem(mockCTX, key, "data1")
	r.NoError(err)

	res, err = r.redis.SCard(mockCTX, key)
	r.NoError(err)
	r.Equal(1, res)
}

func (r *redisSuite) TestExists() {
	key := "test-key"
	res, err := r.redis.Exists(mockCTX, key)
	r.NoError(err)
	r.False(res)

	err = r.redis.SAdd(mockCTX, key, "data1")
	r.NoError(err)

	res, err = r.redis.Exists(mockCTX, key)
	r.NoError(err)
	r.True(res)
}

func (r *redisSuite) TestTTL() {
	key1 := "key1"
	res, err := r.redis.TTL(mockCTX, key1)
	r.Equal(retTTLNoKey, res)
	r.Equal(ErrNotFound, err)

	r.NoError(r.redis.Set(mockCTX, key1, []byte(""), Forever))

	res, err = r.redis.TTL(mockCTX, key1)
	r.Equal(redisModel.ErrNoTTL, err)
	r.Equal(retTTLNoExpire, res)

	r.NoError(r.redis.Set(mockCTX, key1, []byte(""), 10*time.Second))

	res, err = r.redis.TTL(mockCTX, key1)
	r.NoError(err)
	r.True(res > 5 && res <= 10)
}

func (r *redisSuite) TestMemoryUsage() {
	key1, key2 := "k1", "k2"
	value1, value2 := "v1", "v2"
	r.redis.Set(mockCTX, key1, []byte(value1), Forever)
	r.redis.Set(mockCTX, key2, []byte(value2), Forever)

	res, err := r.redis.MemoryUsage(mockCTX, key1)
	r.NoError(err)
	r.True(res > 0)

	res, err = r.redis.MemoryUsage(mockCTX, key2)
	r.NoError(err)
	r.True(res > 0)
}

func (r *redisSuite) TestPTTL() {
	key1 := "key1"
	res, err := r.redis.PTTL(mockCTX, key1)
	r.Equal(retTTLNoKey, res)
	r.Equal(ErrNotFound, err)

	r.NoError(r.redis.Set(mockCTX, key1, []byte(""), Forever))

	res, err = r.redis.PTTL(mockCTX, key1)
	r.Equal(redisModel.ErrNoTTL, err)
	r.Equal(retTTLNoExpire, res)

	r.NoError(r.redis.Set(mockCTX, key1, []byte(""), 10*time.Second))

	res, err = r.redis.PTTL(mockCTX, key1)
	r.NoError(err)
	r.True(res > 5000 && res <= 10000)
}

func (r *redisSuite) TestMTTL() {
	tests := []struct {
		desc   string
		keys   []string
		setup  func()
		assert func([]int, error)
	}{
		{
			desc: "normal case",
			keys: []string{"key1", "key2", "key3"},
			setup: func() {
				r.Require().NoError(r.redis.Set(mockCTX, "key1", []byte("1"), Forever))
				r.Require().NoError(r.redis.Set(mockCTX, "key2", []byte("1"), 10*time.Minute))
			},
			assert: func(ints []int, err error) {
				r.Require().Len(ints, 3)
				r.Require().Equal(-1, ints[0])
				r.Require().GreaterOrEqual(600, ints[1])
				r.Require().LessOrEqual(0, ints[1])
				r.Require().Equal(-2, ints[2])
			},
		},
	}

	for _, test := range tests {
		r.Run(test.desc, func() {
			if test.setup != nil {
				test.setup()
			}

			res, err := r.redis.MTTL(mockCTX, test.keys)
			test.assert(res, err)
		})
	}
}

func (r *redisSuite) TestSScan() {
	r.redis.SAdd(mockCTX, "SScan:test:1", "member1")
	r.redis.SAdd(mockCTX, "SScan:test:1", "member2")
	r.redis.SAdd(mockCTX, "SScan:test:1", "member3")
	expRes := []string{"member1", "member2", "member3"}

	res, cursor, err := r.redis.SScan(mockCTX, "SScan:test:1", 0, 10)
	r.NoError(err)
	r.Equal(0, cursor)
	r.Equal(len(expRes), len(res))
	for _, val := range expRes {
		r.Contains(res, val)
	}

	res = []string{}
	cursor = 0
	var members []string
	for {
		members, cursor, err = r.redis.SScan(mockCTX, "SScan:test:1", cursor, 1)
		r.NoError(err)
		res = append(res, members...)
		if cursor == 0 {
			break
		}
	}
	r.Equal(len(expRes), len(res))
	for _, val := range expRes {
		r.Contains(res, val)
	}
}

func (r *redisSuite) TestPFAdd() {
	value, err := r.redis.PFAdd(mockCTX, "pfKey", "member1")
	r.NoError(err)
	r.Equal(1, value)

	// member2 not exist
	value, err = r.redis.PFAdd(mockCTX, "pfKey", "member2")
	r.NoError(err)
	r.Equal(1, value)

	// member1 already exist
	value, err = r.redis.PFAdd(mockCTX, "pfKey", "member1")
	r.NoError(err)
	r.Equal(0, value)
}

func (r *redisSuite) TestScriptDoAndConfirm() {

	script := `
	redis.call('SET', KEYS[1], ARGV[1])
	local val = redis.call('GET', KEYS[1])
	return val
	`
	// wait 1 replicas , timeout 10ms
	value, confirmed, err := r.redis.ScriptDoAndConfirm(mockCTX, 1000*time.Millisecond, script, []string{"Satori"}, "Komeiji")
	reply, ok := value.([]byte)
	r.NoError(err)
	r.Equal(int64(1), confirmed)
	r.Equal(true, ok)
	r.Equal(string(reply), "Komeiji")

}

func (r *redisSuite) TestScriptDo() {
	setScript := `
		local r = redis.call('SET', KEYS[1], ARGV[1], 'EX', ARGV[2])
		return r
	`

	for i := 0; i < 100; i++ {
		key := fmt.Sprintf("key%d", i)
		val := fmt.Sprintf("val%d", i)

		res, err := r.redis.ScriptDo(mockCTX, setScript, []string{key}, val, 1000)
		r.Require().NoError(err)
		r.Require().NotNil(res)
	}

	getScript := `
		local r = redis.call('GET', KEYS[1])
		return r
	`

	for i := 0; i < 100; i++ {
		key := fmt.Sprintf("key%d", i)
		val := fmt.Sprintf("val%d", i)
		res, err := r.redis.ScriptDo(mockCTX, getScript, []string{key}, val)
		r.NoError(err)
		r.Equal([]byte(val), res.([]byte))
	}
}

func (r *redisSuite) TestPubSub() {
	waitForSubscribe, waitForReceiver := sync.WaitGroup{}, sync.WaitGroup{}
	waitForSubscribe.Add(1)
	waitForReceiver.Add(1)
	channel, message := "notifier", "This is a test"
	localContext, cancel := ctx.WithCancel(ctx.Background())
	errChan := make(chan error, 3)
	go func() {
		if err := r.redis.Subscribe(localContext,
			func() error {
				waitForSubscribe.Done()
				return nil
			},
			func(topic string, msg []byte) error {
				waitForReceiver.Done()
				if channel != topic {
					errChan <- fmt.Errorf("channel doesn't match")
				}
				if message != string(msg) {
					errChan <- fmt.Errorf("message doesn't match")
				}

				// Must execute the cancel() to let subscriber process to exit
				cancel()
				return nil
			},
			channel,
		); err != nil {
			errChan <- err
		}
	}()

	done := make(chan int, 1)
	go func() {
		waitForSubscribe.Wait()
		done <- 1
	}()
	go func() {
		waitForReceiver.Wait()
		done <- 1
	}()
	select {
	case <-done:
		err := r.redis.Publish(mockCTX, channel, []byte(message))
		r.Require().NoError(err)
		select {
		case <-done:
		case <-time.After(time.Second):
			cancel()
			r.Fail("Waiting for receiving message times out")
		}

	case <-time.After(time.Second):
		cancel()
		r.Fail("Waiting for subscription times out")
	}
	if len(errChan) != 0 {
		r.Fail((<-errChan).Error())
	}
}

func (r *redisSuite) TestMGetEx() {
	tests := []struct {
		desc   string
		keys   map[int]string
		setup  func()
		assert func(map[int]string, []redisModel.MVal, error)
	}{
		{
			desc: "normal case",
			keys: map[int]string{
				0: "key1",
				1: "key2",
			},
			setup: func() {
				r.Require().NoError(r.redis.Set(mockCTX, "key1", []byte("key1"), Forever))
				r.Require().NoError(r.redis.Set(mockCTX, "key2", []byte("key2"), Forever))
			},
			assert: func(keys map[int]string, vals []redisModel.MVal, err error) {
				r.Require().NoError(err)

				for i, val := range vals {
					r.Require().True(val.Valid)
					r.Require().Equal(keys[i], string(val.Value))

					ttl, err := r.redis.TTL(mockCTX, keys[i])
					r.Require().NoError(err)
					r.Require().GreaterOrEqual(600, ttl)
					r.Require().LessOrEqual(0, ttl)
				}
			},
		},
	}

	for _, test := range tests {
		r.Run(test.desc, func() {
			if test.setup != nil {
				test.setup()
			}

			res, err := r.redis.MGetEx(mockCTX, 10*time.Minute, []string{"key1", "key2"})
			test.assert(test.keys, res, err)
		})
	}
}

func TestRedisSuite(t *testing.T) {
	suite.Run(t, new(redisSuite))
}
