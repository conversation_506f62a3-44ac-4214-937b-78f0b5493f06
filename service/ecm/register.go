package ecm

import (
	"github.com/17media/dig"

	"github.com/17media/api/setup/dimanager"
)

func init() {
	Register(dimanager.DefaultManager)
}

func Register(m *dimanager.Manager) {
	m.RegisterString("emulator_control_manager_endpoint", "", "emulator control manager endpoint")

	type params struct {
		ECMEndpoint *string `name:"emulator_control_manager_endpoint"`
		dig.In
	}

	fn := func(p params) Service {
		return New(
			*p.ECMEndpoint,
		)
	}
	m.ProvideConstructor(fn, `ecmService`)
}

func GetEcm(m *dimanager.Manager) Service {
	var output Service
	type params struct {
		dig.In
		Output Service `name:"ecmService"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
