package ringrouter

import (
	"context"
	"strconv"

	"go.opencensus.io/trace"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/encoding"
	"github.com/17media/api/base/metrics"
	rpcModel "github.com/17media/api/models/grpc/relationv2"
	"github.com/17media/api/service/managedsx/db"
)

var (
	intGrpcMet = metrics.New("rel2.intgrpc")
)

// NewInternalGRPC returns a relation GRPC Server
func NewInternalGRPC(ringrouter Service) rpcModel.InternalServiceServer {
	return &internalServer{ringrouter}
}

// internalServer implements internal GRPC Server
type internalServer struct {
	ringrouter Service
}

// Get .
func (is *internalServer) Get(context context.Context, input *rpcModel.InternalGetInput) (*rpcModel.GetOutput, error) {
	defer intGrpcMet.BumpTime("time", "func", "Get", "table", input.TableName).End()

	c := ctx.Background()
	c.Context = context
	c, span := ctx.StartSpan(c, "relation.internal.Get")
	span.AddAttributes(
		trace.StringAttribute("table", input.TableName),
	)
	defer span.End()
	c = ctx.WithValues(c, map[string]interface{}{
		"input": input,
	})

	from, err := db.DeserializeToPk([]byte(input.From))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	to, err := db.DeserializeToPk([]byte(input.To))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	record, err := is.ringrouter.Get(c, input.TableName, input.SetKey, from, to, WithMachineStartTime(input.MachineStartTime), WithNoCache(input.NoCache))
	if err != nil {
		c.WithField("err", err).Error("ringrouter.Get failed")
		return nil, err
	}

	// no need to take care of nil pointer. it could be serialized
	output, err := encoding.SerializeMap(record)
	if err != nil {
		c.WithField("err", err).Error("SerializeMap failed")
		return nil, err
	}
	return &rpcModel.GetOutput{
		Content: output,
	}, nil
}

// GetM .
func (is *internalServer) GetM(context context.Context, input *rpcModel.InternalGetMInput) (*rpcModel.GetOutput, error) {
	defer intGrpcMet.BumpTime("time", "func", "GetM", "table", input.TableName).End()

	c := ctx.Background()
	c.Context = context
	c, span := ctx.StartSpan(c, "relation.internal.GetM")
	span.AddAttributes(
		trace.StringAttribute("table", input.TableName),
	)
	defer span.End()
	c = ctx.WithValues(c, map[string]interface{}{
		"input": input,
	})

	from, err := db.DeserializeToPk([]byte(input.From))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	tos, err := db.DeserializeToPkSlice([]byte(input.Tos))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPkSlice failed")
		return nil, err
	}

	records, err := is.ringrouter.GetM(c, input.TableName, input.SetKey, from, tos, WithMachineStartTime(input.MachineStartTime), WithNoCache(input.NoCache))
	if err != nil {
		c.WithField("err", err).Error("ringrouter.GetM failed")
		return nil, err
	}
	if records == nil {
		records = []db.Record{}
	}

	output, err := encoding.Serialize(records)
	if err != nil {
		c.WithField("err", err).Error("Serialize failed")
		return nil, err
	}
	return &rpcModel.GetOutput{
		Content: output,
	}, nil
}

// Set .
func (is *internalServer) Set(context context.Context, input *rpcModel.InternalSetInput) (*rpcModel.Empty, error) {
	defer intGrpcMet.BumpTime("time", "func", "Set", "table", input.TableName).End()

	c := ctx.Background()
	c.Context = context
	c, span := ctx.StartSpan(c, "relation.internal.Set")
	span.AddAttributes(
		trace.StringAttribute("table", input.TableName),
	)
	defer span.End()
	c = ctx.WithValues(c, map[string]interface{}{
		"input": input,
	})

	from, err := db.DeserializeToPk([]byte(input.From))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	to, err := db.DeserializeToPk([]byte(input.To))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	obj, err := db.DeserializeToRecord([]byte(input.Obj))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToRecord failed")
		return nil, err
	}

	if err := is.ringrouter.Set(c, input.TableName, input.SetKey, from, to, obj, input.SortingColumn, WithMachineStartTime(input.MachineStartTime), WithNoCache(input.NoCache)); err != nil {
		c.WithField("err", err).Error("ringrouter.Set failed")
		return nil, err
	}

	return &rpcModel.Empty{}, nil
}

// Del .
func (is *internalServer) Del(context context.Context, input *rpcModel.InternalDelInput) (*rpcModel.Empty, error) {
	defer intGrpcMet.BumpTime("time", "func", "Del", "table", input.TableName).End()

	c := ctx.Background()
	c.Context = context
	c, span := ctx.StartSpan(c, "relation.internal.Del")
	span.AddAttributes(
		trace.StringAttribute("table", input.TableName),
	)
	defer span.End()
	c = ctx.WithValues(c, map[string]interface{}{
		"input": input,
	})

	from, err := db.DeserializeToPk([]byte(input.From))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	to, err := db.DeserializeToPk([]byte(input.To))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	if err := is.ringrouter.Del(c, input.TableName, input.SetKey, from, to, input.SortingColumn, WithMachineStartTime(input.MachineStartTime), WithNoCache(input.NoCache)); err != nil {
		c.WithField("err", err).Error("ringrouter.Del failed")
		return nil, err
	}

	return &rpcModel.Empty{}, nil
}

// Incr .
func (is *internalServer) Incr(context context.Context, input *rpcModel.InternalIncrInput) (*rpcModel.IncrOutput, error) {
	defer intGrpcMet.BumpTime("time", "func", "Incr", "table", input.TableName).End()

	c := ctx.Background()
	c.Context = context
	c, span := ctx.StartSpan(c, "relation.internal.Incr")
	span.AddAttributes(
		trace.StringAttribute("table", input.TableName),
	)
	defer span.End()
	c = ctx.WithValues(c, map[string]interface{}{
		"input": input,
	})

	from, err := db.DeserializeToPk([]byte(input.From))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	to, err := db.DeserializeToPk([]byte(input.To))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	newValue, err := is.ringrouter.Incr(c, input.TableName, input.SetKey, from, to, input.SortingColumn, input.Delta, WithMachineStartTime(input.MachineStartTime), WithNoCache(input.NoCache))
	if err != nil {
		c.WithField("err", err).Error("ringrouter.Del failed")
		return nil, err
	}

	return &rpcModel.IncrOutput{ResultValue: newValue}, nil
}

// GetList .
func (is *internalServer) GetList(context context.Context, input *rpcModel.InternalGetListInput) (*rpcModel.GetListOutput, error) {
	defer intGrpcMet.BumpTime("time", "func", "GetList", "table", input.TableName).End()

	c := ctx.Background()
	c.Context = context
	c, span := ctx.StartSpan(c, "relation.internal.GetList")
	span.AddAttributes(
		trace.StringAttribute("table", input.TableName),
	)
	defer span.End()
	c = ctx.WithValues(c, map[string]interface{}{
		"input": input,
	})

	from, err := db.DeserializeToPk([]byte(input.From))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	to, err := db.DeserializeToPk([]byte(input.To))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	records, hasNext, err := is.ringrouter.GetList(c, input.TableName, input.SetKey, from, to, input.SortingColumn, int(input.StartPos), int(input.Limit), WithMachineStartTime(input.MachineStartTime), WithNoCache(input.NoCache))
	if err != nil {
		c.WithField("err", err).Error("ringrouter.GetList failed")
		return nil, err
	}

	nextCursor := ""
	if hasNext {
		nextCursor = strconv.Itoa(int(input.StartPos + input.Limit))
	}
	content, err := db.SerializeRecordSlice(records)
	if err != nil {
		c.WithField("err", err).Error("json.Marshal failed")
		return nil, err
	}

	return &rpcModel.GetListOutput{
		Content:    content,
		NextCursor: nextCursor,
	}, nil
}

// Count .
func (is *internalServer) Count(context context.Context, input *rpcModel.InternalCountInput) (*rpcModel.CountOutput, error) {
	defer intGrpcMet.BumpTime("time", "func", "Count", "table", input.TableName).End()

	c := ctx.Background()
	c.Context = context
	c, span := ctx.StartSpan(c, "relation.internal.Count")
	span.AddAttributes(
		trace.StringAttribute("table", input.TableName),
	)
	defer span.End()
	c = ctx.WithValues(c, map[string]interface{}{
		"input": input,
	})

	from, err := db.DeserializeToPk([]byte(input.From))
	if err != nil {
		c.WithField("err", err).Error("DeserializeToPk failed")
		return nil, err
	}

	count, err := is.ringrouter.Count(c, input.TableName, input.SetKey, from, WithMachineStartTime(input.MachineStartTime))
	if err != nil {
		c.WithField("err", err).Error("ringrouter.Count failed")
		return nil, err
	}

	return &rpcModel.CountOutput{Count: int64(count)}, nil
}

// Ping
func (is *internalServer) Ping(context context.Context, empty *rpcModel.Empty) (*rpcModel.Empty, error) {
	// It's used to approve self node alive. Or, rpc error code will be returned
	return &rpcModel.Empty{}, nil
}
