package paidy

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/17media/api/base/bhttp"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/metrics"
	btime "github.com/17media/api/base/time"
	"github.com/17media/logrus"
)

var (
	bhttpClientDo      = bhttp.Client.Do
	met                = metrics.New("paidySrv")
	ParseFormatISO8601 = "2006-01-02T15:04:05.000Z"
)

type impl struct {
	secretKey string
	endpoint  string
}

func New(secretKey *string) Service {
	im := &impl{
		secretKey: *secretKey,
		endpoint:  "https://api.paidy.com",
	}

	return im
}

type paymentResp struct {
	ID          string            `json:"id"`
	CreatedAt   string            `json:"created_at"`
	ExpiresAt   string            `json:"expires_at"`
	Amount      int64             `json:"amount"`
	Currency    string            `json:"currency"`
	Description string            `json:"description"`
	StoreName   string            `json:"store_name"`
	IsTest      bool              `json:"test"`
	Status      string            `json:"status"`
	Tier        string            `json:"tier"`
	Buyer       BuyerInfo         `json:"buyer"`
	Order       order             `json:"order"`
	Captures    []capture         `json:"captures"`
	Refunds     []refund          `json:"refunds"`
	Metadata    map[string]string `json:"metadata"`
}

type order struct {
	Tax       int64  `json:"tax"`
	Shipping  int64  `json:"shipping"`
	OrderRef  string `json:"order_ref"`
	Items     []item `json:"items"`
	UpdatedAt string `json:"updated_at"`
}

type capture struct {
	ID        string            `json:"id"`
	CreatedAt string            `json:"created_at"`
	Amount    int64             `json:"amount"`
	Tax       int64             `json:"tax"`
	Shipping  int64             `json:"shipping"`
	Items     []item            `json:"items"`
	Metadata  map[string]string `json:"metadata"`
}

type refund struct {
	ID        string            `json:"id"`
	CreatedAt string            `json:"created_at"`
	CaptureID string            `json:"capture_id"`
	Amount    int64             `json:"amount"`
	Reason    string            `json:"reason"`
	Metadata  map[string]string `json:"metadata"`
}

type item struct {
	ProductID   string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	UnitPrice   int64  `json:"unit_price"`
	Quantity    int64  `json:"quantity"`
}

type tokenResp struct {
	ID          string            `json:"id"`
	MerchantID  string            `json:"merchant_id"`
	WalletID    string            `json:"wallet_id"`
	Status      string            `json:"status"`
	Origin      BuyerInfo         `json:"origin"`
	Description string            `json:"description"`
	Kind        string            `json:"kind"`
	Metadata    map[string]string `json:"metadata"`
	WebhookUrl  string            `json:"webhook_url"`
	ConsumerID  string            `json:"consumer_id"`
	Suspensions []suspension      `json:"suspensions"`
	Test        bool              `json:"test"`
	VersionNr   int64             `json:"version_nr"`
	CreatedAt   string            `json:"created_at"`
	UpdatedAt   string            `json:"updated_at"`
	ActivatedAt string            `json:"activated_at"`
	DeletedAt   string            `json:"deleted_at"`
}

type suspension struct {
	Timestamp string `json:"timestamp"`
	Authority string `json:"authority"`
}

type orderReqs struct {
	OrderRef string  `json:"order_ref"`
	Items    []*item `json:"items"`
}

func toPaymentInfo(pRes *paymentResp) (*PaymentInfo, error) {
	createdAt, err := time.Parse(ParseFormatISO8601, pRes.CreatedAt)
	if err != nil && pRes.CreatedAt != "" {
		return nil, err
	}

	expiresAt, err := time.Parse(ParseFormatISO8601, pRes.ExpiresAt)
	if err != nil && pRes.ExpiresAt != "" {
		return nil, err
	}

	captureInfos, err := toCaptureInfos(pRes.Captures)
	if err != nil {
		return nil, err
	}

	refundInfos, err := toRefundInfos(pRes.Refunds)
	if err != nil {
		return nil, err
	}

	status, ok := PaymentStatusMap[pRes.Status]
	if !ok {
		err = fmt.Errorf("paymentResp.Status undefined")
		return nil, err
	}

	return &PaymentInfo{
		ID:        pRes.ID,
		CreatedAt: createdAt,
		ExpiresAt: expiresAt,
		Amount:    pRes.Amount,
		Currency:  pRes.Currency,
		IsTest:    pRes.IsTest,
		Status:    status,
		Buyer:     &pRes.Buyer,
		Order: &OrderInfo{
			OrderID:      pRes.Order.OrderRef,
			ProductItems: toProductItems(pRes.Order.Items),
		},
		Captures: captureInfos,
		Refunds:  refundInfos,
	}, nil
}

func toCaptureInfos(captures []capture) ([]*CaptureInfo, error) {
	captureInfos := []*CaptureInfo{}
	for _, c := range captures {
		createdAt, err := time.Parse(ParseFormatISO8601, c.CreatedAt)
		if err != nil && c.CreatedAt != "" {
			return nil, err
		}

		captureInfos = append(captureInfos, &CaptureInfo{
			ID:           c.ID,
			CreatedAt:    createdAt,
			Amount:       c.Amount,
			ProductItems: toProductItems(c.Items),
		})
	}

	return captureInfos, nil
}

func toRefundInfos(refunds []refund) ([]*RefundInfo, error) {
	refundInfos := []*RefundInfo{}
	for _, r := range refunds {
		createdAt, err := time.Parse(ParseFormatISO8601, r.CreatedAt)
		if err != nil && r.CreatedAt != "" {
			return nil, err
		}

		refundInfos = append(refundInfos, &RefundInfo{
			ID:        r.ID,
			CreatedAt: createdAt,
			CaptureID: r.CaptureID,
			Amount:    r.Amount,
			Reason:    r.Reason,
		})
	}

	return refundInfos, nil
}

func toProductItems(its []item) []*ProductItem {
	productItems := []*ProductItem{}
	for _, it := range its {
		productItems = append(productItems, &ProductItem{
			ProductID: it.ProductID,
			UnitPrice: it.UnitPrice,
			Quantity:  it.Quantity,
		})
	}
	return productItems
}

func toTokenInfo(tRes *tokenResp) (*TokenInfo, error) {
	createdAt, err := time.Parse(ParseFormatISO8601, tRes.CreatedAt)
	if err != nil && tRes.CreatedAt != "" {
		return nil, err
	}

	updatedAt, err := time.Parse(ParseFormatISO8601, tRes.UpdatedAt)
	if err != nil && tRes.UpdatedAt != "" {
		return nil, err
	}

	activatedAt, err := time.Parse(ParseFormatISO8601, tRes.ActivatedAt)
	if err != nil && tRes.ActivatedAt != "" {
		return nil, err
	}

	deletedAt, err := time.Parse(ParseFormatISO8601, tRes.DeletedAt)
	if err != nil && tRes.DeletedAt != "" {
		return nil, err
	}

	userID, ok := tRes.Metadata["userID"]
	if !ok {
		err = fmt.Errorf("can't found userID in token's Metadata")
		return nil, err
	}

	status, ok := TokenStatusMap[tRes.Status]
	if !ok {
		err = fmt.Errorf("tokenResp.Status undefined")
		return nil, err
	}

	return &TokenInfo{
		ID:     tRes.ID,
		Status: status,
		OriginBuyerInfo: &BuyerInfo{
			Name1: tRes.Origin.Name1,
			Name2: tRes.Origin.Name2,
			Phone: tRes.Origin.Phone,
			Email: tRes.Origin.Email,
		},
		UserID:      userID,
		IsTest:      tRes.Test,
		CreatedAt:   createdAt,
		UpdatedAt:   updatedAt,
		ActivatedAt: activatedAt,
		DeletedAt:   deletedAt,
	}, nil
}

func (im *impl) RetrievePayment(context ctx.CTX, paymentID string) (paymentInfo *PaymentInfo, err error) {
	context = ctx.WithValues(context, map[string]interface{}{
		"paymentID": paymentID,
	})

	path := fmt.Sprintf("%s/payments/%s", im.endpoint, paymentID)
	statusCode, respBody, err := im.request(context, "GET", path, nil)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "path": path, "body": respBody}).Error("request failed")
		return nil, err
	}
	if statusCode != http.StatusOK {
		errMsg := "status code is not OK"
		context.WithFields(logrus.Fields{"statusCode": statusCode}).Error(errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	var pRes paymentResp
	if err = json.Unmarshal(respBody, &pRes); err != nil {
		context.WithFields(logrus.Fields{"err": err, "respBody": string(respBody)}).Error("json.Unmarshal failed")
		return nil, err
	}

	paymentInfo, err = toPaymentInfo(&pRes)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "paymentResp": pRes}).Error("ToPaymentInfo failed")
		return nil, err
	}

	return paymentInfo, nil
}

func (im *impl) CapturePayment(context ctx.CTX, paymentID string) (paymentInfo *PaymentInfo, err error) {
	context = ctx.WithValues(context, map[string]interface{}{
		"paymentID": paymentID,
	})

	path := fmt.Sprintf("%s/payments/%s/captures", im.endpoint, paymentID)
	statusCode, respBody, err := im.request(context, "POST", path, []byte{'{', '}'})
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "path": path, "body": respBody}).Error("request failed")
		return nil, err
	}
	if statusCode != http.StatusOK {
		errMsg := "status code is not OK"
		context.WithFields(logrus.Fields{"statusCode": statusCode}).Error(errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	var pRes paymentResp
	if err = json.Unmarshal(respBody, &pRes); err != nil {
		context.WithFields(logrus.Fields{"err": err, "respBody": string(respBody)}).Error("json.Unmarshal failed")
		return nil, err
	}

	paymentInfo, err = toPaymentInfo(&pRes)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "paymentResp": pRes}).Error("ToPaymentInfo failed")
		return nil, err
	}

	return paymentInfo, nil
}

func (im *impl) ParseNotifPayload(context ctx.CTX, payload string) (*Notif, error) {
	notifPayload := struct {
		PaymentID     string `json:"payment_id"`
		CaptureID     string `json:"capture_id"`
		TokenID       string `json:"token_id"`
		EventType     string `json:"event_type"`
		OrderRef      string `json:"order_ref"`
		Status        string `json:"status"`
		Reason        string `json:"reason"`
		Timestamp     string `json:"timestamp"`
		EventDatetime string `json:"event_datetime"`
	}{}
	if err := json.Unmarshal([]byte(payload), &notifPayload); err != nil {
		context.WithFields(logrus.Fields{
			"payload": payload,
			"err":     err,
		}).Error("json.Unmarshal failed")
		return nil, err
	}

	context.WithField("notifPayload", notifPayload).Info("notifPayload")

	notifTime, err := time.Parse(ParseFormatISO8601, notifPayload.Timestamp)
	if err != nil {
		context.WithFields(logrus.Fields{
			"timpstamp": notifPayload.Timestamp,
			"err":       err,
		}).Error("time.Parse failed")
		return nil, err
	}

	if notifPayload.EventType != EventTypePayment {
		// token type notif
		return &Notif{
			EventType: EventTypeToken,
			TokenID:   notifPayload.TokenID,
			Status:    notifPayload.Status,
			Timpstamp: notifTime.Unix(),
		}, nil
	}

	return &Notif{
		EventType: EventTypePayment,
		PaymentID: notifPayload.PaymentID,
		CaptureID: notifPayload.CaptureID,
		Status:    notifPayload.Status,
		OrderRef:  notifPayload.OrderRef,
		Reason:    notifPayload.Reason,
		Timpstamp: notifTime.Unix(),
	}, nil
}

func toItems(productItems []*ProductItem, title string, desc string) []*item {
	items := []*item{}
	for _, p := range productItems {
		items = append(items, &item{
			ProductID:   p.ProductID,
			Title:       title,
			Description: desc,
			UnitPrice:   p.UnitPrice,
			Quantity:    p.Quantity,
		})
	}
	return items
}

func (im *impl) CreatePaymentByToken(context ctx.CTX, input CreatePaymentInput) (paymentInfo *PaymentInfo, err error) {
	context = ctx.WithValues(context, map[string]interface{}{
		"createPaymentInput": input,
		"inputBuyerData":     input.BuyerData,
		"inputOrder":         input.Order,
	})

	reqBody := struct {
		TokenID   string                 `json:"token_id"`
		Amount    int64                  `json:"amount"`
		Currency  string                 `json:"currency"`
		StoreName string                 `json:"store_name"`
		BuyerData map[string]interface{} `json:"buyer_data"`
		Order     *orderReqs             `json:"order"`
		Metadata  map[string]string      `json:"metadata"`
	}{
		TokenID:   input.TokenID,
		Amount:    input.Amount,
		Currency:  input.Currency,
		StoreName: "17LIVE",
		BuyerData: map[string]interface{}{
			"user_id":                   input.BuyerData.UserID,
			"account_registration_date": input.BuyerData.AccountRegistrationDate.Format(btime.ParseFormatYYYYMMDDWithHyphen),
			"account_balance":           input.BuyerData.AccountBalance,
			"last_used_date":            input.BuyerData.LastUsedDate.Format(btime.ParseFormatYYYYMMDDWithHyphen),
			"last_used_amount":          input.BuyerData.LastUsedAmount,
			"store_id":                  "17LIVE",
		},
		Order: &orderReqs{
			OrderRef: input.Order.OrderID,
			Items:    toItems(input.Order.ProductItems, "ベビーコイン", ""),
		},
	}
	bs, err := json.Marshal(reqBody)
	if err != nil {
		context.WithField("err", err).Error("fail to json.Marshal")
		return nil, err
	}

	path := fmt.Sprintf("%s/payments", im.endpoint)
	statusCode, respBody, err := im.request(context, "POST", path, bs)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "path": path, "body": string(bs)}).Error("request failed")
		return nil, err
	}
	if statusCode != http.StatusOK {
		errMsg := "status code is not OK"
		context.WithFields(logrus.Fields{
			"statusCode": statusCode,
			"reqBody":    string(bs),
			"respBody":   string(respBody),
		}).Error(errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	var pRes paymentResp
	if err = json.Unmarshal(respBody, &pRes); err != nil {
		context.WithFields(logrus.Fields{"err": err, "respBody": string(respBody)}).Error("json.Unmarshal failed")
		return nil, err
	}

	paymentInfo, err = toPaymentInfo(&pRes)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "paymentResp": pRes}).Error("toPaymentInfo failed")
		return nil, err
	}

	return paymentInfo, nil
}

func (im *impl) DeleteToken(context ctx.CTX, tokenID string, reason Reason) (tokenInfo *TokenInfo, err error) {
	context = ctx.WithValues(context, map[string]interface{}{
		"tokenID": tokenID,
		"reason":  reason,
	})

	if reason.Code == "" {
		reason.Code = TokenDeleteCodeConsumerRequested
	}

	if reason.Description == "" {
		reason.Description = "consumer requested"
	}

	reqBody := struct {
		WalletID string `json:"wallet_id"`
		Reason   Reason `json:"reason"`
	}{
		WalletID: "default",
		Reason:   reason,
	}
	bs, err := json.Marshal(reqBody)
	if err != nil {
		context.WithField("err", err).Error("fail to json.Marshal")
		return nil, err
	}

	path := fmt.Sprintf("%s/tokens/%s/delete", im.endpoint, tokenID)
	statusCode, respBody, err := im.request(context, "POST", path, bs)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "path": path, "body": respBody}).Error("request failed")
		return nil, err
	}
	if statusCode != http.StatusOK {
		errMsg := "status code is not OK"
		context.WithFields(logrus.Fields{"statusCode": statusCode}).Error(errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	var tRes tokenResp
	if err = json.Unmarshal(respBody, &tRes); err != nil {
		context.WithFields(logrus.Fields{"err": err, "respBody": string(respBody)}).Error("json.Unmarshal failed")
		return nil, err
	}

	tokenInfo, err = toTokenInfo(&tRes)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "tokenResp": tRes}).Error("toTokenInfo failed")
		return nil, err
	}

	return tokenInfo, nil
}

func (im *impl) RetrieveToken(context ctx.CTX, tokenID string) (tokenInfo *TokenInfo, err error) {
	context = ctx.WithValues(context, map[string]interface{}{
		"tokenID": tokenID,
	})

	path := fmt.Sprintf("%s/tokens/%s", im.endpoint, tokenID)
	statusCode, respBody, err := im.request(context, "GET", path, nil)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "path": path, "body": respBody}).Error("request failed")
		return nil, err
	}
	if statusCode != http.StatusOK {
		errMsg := "status code is not OK"
		context.WithFields(logrus.Fields{"statusCode": statusCode}).Error(errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	var tRes tokenResp
	if err = json.Unmarshal(respBody, &tRes); err != nil {
		context.WithFields(logrus.Fields{"err": err, "respBody": string(respBody)}).Error("json.Unmarshal failed")
		return nil, err
	}

	tokenInfo, err = toTokenInfo(&tRes)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "tokenResp": tRes}).Error("toTokenInfo failed")
		return nil, err
	}

	return tokenInfo, nil
}

func (im *impl) ClosePayment(context ctx.CTX, paymentID string) (err error) {
	context = ctx.WithValues(context, map[string]interface{}{
		"paymentID": paymentID,
	})

	path := fmt.Sprintf("%s/payments/%s/close", im.endpoint, paymentID)
	statusCode, respBody, err := im.request(context, "POST", path, []byte{'{', '}'})
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "path": path, "body": respBody}).Error("request failed")
		return err
	}

	if statusCode != http.StatusOK {
		// paidy return StatusConflict when we tried to close a payment that is already CLOSED
		if statusCode == http.StatusConflict {
			return nil
		}

		errMsg := "status code is not OK"
		context.WithFields(logrus.Fields{"statusCode": statusCode, "body": respBody}).Error(errMsg)
		return fmt.Errorf(errMsg)
	}

	return nil
}

func (im *impl) RefundPayment(context ctx.CTX, paymentID, captureID string) (paymentInfo *PaymentInfo, err error) {
	context = ctx.WithValues(context, map[string]interface{}{
		"paymentID": paymentID,
		"captureID": captureID,
	})

	reqBody := map[string]string{
		"capture_id": captureID,
	}

	bs, err := json.Marshal(reqBody)
	if err != nil {
		context.WithField("err", err).Error("fail to json.Marshal")
		return nil, err
	}

	path := fmt.Sprintf("%s/payments/%s/refunds", im.endpoint, paymentID)
	statusCode, respBody, err := im.request(context, "POST", path, bs)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "path": path, "body": respBody}).Error("request failed")
		return nil, err
	}
	if statusCode != http.StatusOK {
		errMsg := "status code is not OK"
		context.WithFields(logrus.Fields{"statusCode": statusCode}).Error(errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	var pRes paymentResp
	if err = json.Unmarshal(respBody, &pRes); err != nil {
		context.WithFields(logrus.Fields{"err": err, "respBody": string(respBody)}).Error("json.Unmarshal failed")
		return nil, err
	}

	paymentInfo, err = toPaymentInfo(&pRes)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "paymentResp": pRes}).Error("ToPaymentInfo failed")
		return nil, err
	}

	return paymentInfo, nil
}

func (im *impl) request(context ctx.CTX, method, path string, content []byte) (status int, body []byte, err error) {
	defer met.BumpTime("time", "func", "request").End()

	req, err := http.NewRequest(method, path, bytes.NewBuffer(content))
	if err != nil {
		met.BumpSum("err", 1, "reason", "newRequest", "func", "request")
		context.WithField("err", err).Error("http.NewRequest failed")
		return 0, nil, err
	}

	req.Header.Add("Paidy-Version", "2018-04-10")
	req.Header.Add("Authorization", "Bearer "+im.secretKey)
	req.Header.Add("Content-Type", "application/json")

	resp, err := bhttpClientDo(req)
	if err != nil {
		met.BumpSum("err", 1, "reason", "bhttpClientDo", "func", "request")
		context.WithField("err", err).Error("bhttp.Client.Do failed")
		return 0, nil, err
	}

	body, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		met.BumpSum("err", 1, "reason", "ioutil.ReadAll", "func", "request")
		context.WithField("err", err).Error("ioutil.ReadAll failed")
		return 0, nil, err
	}
	defer resp.Body.Close()
	context.WithFields(logrus.Fields{"path": path, "statusCode": resp.StatusCode, "respBody": string(body)}).Info("response info")

	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		met.BumpSum("warn", 1, "reason", "statusCode", "func", "request")
	}

	return resp.StatusCode, body, nil
}
