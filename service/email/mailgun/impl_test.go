package mailgun

import (
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/service/email"
)

type mailgunSuite struct {
	suite.Suite
	context ctx.CTX
	service email.Service
}

func (s *mailgunSuite) SetupSuite() {
	s.context = ctx.Background()

	apiKey := "************************************"
	domain := "mail.17.media"
	publicKey := "pubkey-d42e7b0190cc57d61bee2b931e936715"

	s.service = New(Params{
		APIKey:    &apiKey,
		Domain:    &domain,
		PublicKey: &publicKey,
	})
}

func (s *mailgunSuite) TestMailgun() {
	data := email.Data{}
	data.To = make([]email.Address, 0)
	data.To = append(data.To, email.Address{Email: "<EMAIL>"})
	data.From = email.Address{Email: "<EMAIL>"}
	data.Subject = "This is test title"
	data.Body = email.Content{Type: "text/plain", Value: "This is test content."}
	err := s.service.SendEmail(s.context, data)
	s.NoError(err, "SendEmail failed")
}

func TestMailgunSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("skip long test")
	}
	suite.Run(t, new(mailgunSuite))
}
