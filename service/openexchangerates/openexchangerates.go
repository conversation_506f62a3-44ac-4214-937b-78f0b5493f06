package openexchangerates

import (
	"errors"

	"github.com/17media/api/base/ctx"
	"golang.org/x/text/currency"
)

var (
	// ErrStatusCodeNotOK is error return when the status code of response is not OK
	ErrStatusCodeNotOK = errors.New("status code is not OK")

	// ErrUnsupportedCurrency is error return when the fromCurrency is unsupported
	ErrUnsupportedCurrency = errors.New("unsupported fromCurrency")

	// ErrInvalidFromCurrency is error return when the fromCurrency in response is invalid
	ErrInvalidFromCurrency = errors.New("fromCurrency is invalid")

	// ErrOverQuota is error return when the usage quota of API is over
	ErrOverQuota = errors.New("quota of openexchangerate API is over")
)

// Service is interface for openexchangerates
type Service interface {
	// GetRates return all available exchange rates
	GetRates(context ctx.CTX, fromCurrency currency.Unit) (map[currency.Unit]float64, error)

	// GetUsage return current usage of API
	GetUsage(context ctx.CTX) (requestCount, requestQuota int64, err error)
}
