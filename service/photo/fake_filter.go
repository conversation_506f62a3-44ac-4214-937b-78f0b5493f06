package photo

import (
	"github.com/17media/api/base/ctx"
	cmodels "github.com/17media/api/models/commodity"
)

// googleVision describes parameters using by Google Vision API library
type fakeVision struct {
}

// NewFakeFilter returns a fake image filter
func NewFakeFilter() ImageFilter {
	return &fakeVision{}
}

// EvaluateByURL returns if the given url is a legal photo
func (ifilter *fakeVision) EvaluateByURL(context ctx.CTX, imgURL string) (*cmodels.GoogleVisionAnnotations, error) {
	context.WithField("imgURL", imgURL).Error("photo: use fake vision API client")
	filters := &cmodels.GoogleVisionAnnotations{
		Adult:    5,
		Spoof:    5,
		Violence: 5,
		Medical:  5,
	}
	return filters, nil
}

func (ifilter *fakeVision) IsLegal(context ctx.CTX, filters *cmodels.GoogleVisionAnnotations) bool {
	context.WithField("filters", filters).Error("photo: use fake vision API client")
	// fake image ifilter always return false (illegal)
	return false
}
