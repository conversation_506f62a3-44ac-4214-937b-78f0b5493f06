package config

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	visionModel "github.com/17media/api/models/vision"
)

var (
	mockRegion = "TW"
)

type mockFuncs struct {
	mock.Mock
}

func (m *mockFuncs) getCfg(region string) visionConfig {
	ret := m.Called(region)
	return ret.Get(0).(visionConfig)
}

type Suite struct {
	suite.Suite

	mockFuncs *mockFuncs
}

func (s *Suite) SetupSuite() {
	s.mockFuncs = new(mockFuncs)
	getCfg = s.mockFuncs.getCfg
}
func (s *Suite) TearDownSuite() {}
func (s *Suite) SetupTest()     {}
func (s *Suite) TearDownTest()  {}

func (s *Suite) TestConfig() {
	tests := []struct {
		name       string
		mockConfig string
		expErr     error
	}{
		{
			name: "success",
			mockConfig: `
cloudVision:
  enableSafeSearch: true
  adultConfidence: 0.1
  spoofConfidence: 0.2
  medicalConfidence: 0.3
  violenceConfidence: 0.4
  racyConfidence: 0.5
  nsfwConfidence: 0.6
  useContent: true
  enableLabel: true
  labelMaxResult: 3
  labelThresholds:
    child: 0.3
shumeiVision:
  useContent: true
  allowedTypes: [1,2,3,4,5,6]
enabledProviders:
  - cloud
  - shumei
`,
			expErr: nil,
		},
		{
			name: "not allow shumei type",
			mockConfig: `
cloudVision:
  enableSafeSearch: true
  adultConfidence: 0.1
  spoofConfidence: 0.2
  medicalConfidence: 0.3
  violenceConfidence: 0.4
  racyConfidence: 0.5
  nsfwConfidence: 0.6
  useContent: true
  enableLabel: true
  labelMaxResult: 3
  labelThresholds:
    child: 0.3
shumeiVision:
  useContent: true
  allowedTypes: [99]
enabledProviders:
  - cloud
  - shumei
`,
			expErr: errors.New("invalid shumei type"),
		},
		{
			name: "cloud vision both disable",
			mockConfig: `
cloudVision:
  enableSafeSearch: false
  adultConfidence: 0.1
  spoofConfidence: 0.2
  medicalConfidence: 0.3
  violenceConfidence: 0.4
  racyConfidence: 0.5
  nsfwConfidence: 0.6
  useContent: true
  enableLabel: false
  labelMaxResult: 3
  labelThresholds:
    child: 0.3
shumeiVision:
  useContent: true
  allowedTypes: [1,2,3,4,5,6]
enabledProviders:
  - cloud
  - shumei
`,
			expErr: errors.New("must enable at least one of enableSafeSearch or enableLabel"),
		},
		{
			name: "invalid and no valid providers",
			mockConfig: `
cloudVision:
  enableSafeSearch: true
  adultConfidence: 0.1
  spoofConfidence: 0.2
  medicalConfidence: 0.3
  violenceConfidence: 0.4
  racyConfidence: 0.5
  nsfwConfidence: 0.6
  useContent: true
  enableLabel: true
  labelMaxResult: 3
  labelThresholds:
    child: 0.3
shumeiVision:
  useContent: true
  allowedTypes: [1,2,3,4,5,6]
enabledProviders:
  - eric's eyes
`,
			expErr: errors.New("must enable at least one of providers"),
		},
	}
	for _, t := range tests {
		s.Run(t.name, func() {
			cfg := &visionConfig{}
			intf, _, err := cfg.Check([]byte(t.mockConfig))
			if t.expErr != nil {
				s.EqualError(err, t.expErr.Error())
			} else {
				s.Require().NoError(err)
				s.NotPanics(func() {
					cfg.Apply(intf)
				})
			}
		})
	}
}

func (s *Suite) TestGetProviders() {

	s.mockFuncs.On("getCfg", mockRegion).Return(visionConfig{
		EnabledProviders: []visionModel.Provider{visionModel.ProviderCloud},
	}).Once()
	s.Equal(GetProviders(mockRegion), []visionModel.Provider{visionModel.ProviderCloud})
}

func (s *Suite) TestGetCloudVisionConfig() {
	s.mockFuncs.On("getCfg", mockRegion).Return(visionConfig{
		CloudVision: CloudVisionConfig{
			EnableSafeSearch: true,
			EnableLabel:      true,
			LabelThresholds: map[string]float32{
				"child": 0.3,
			},
		},
	}).Once()
	cfg := GetCloudVisionConfig(mockRegion)
	s.Equal(cfg, CloudVisionConfig{
		EnableSafeSearch: true,
		EnableLabel:      true,
		LabelThresholds: map[string]float32{
			"child": 0.3,
		},
	})

	s.False(cfg.IsLabelHitThreshold("child", 0.2))
	s.True(cfg.IsLabelHitThreshold("child", 0.4))
	// should be false when not exists
	s.False(cfg.IsLabelHitThreshold("cat", 0.4))
}

func (s *Suite) TestGetShumeiTypesString() {
	s.mockFuncs.On("getCfg", mockRegion).Return(visionConfig{
		ShumeiVision: ShumeiVisionConfig{
			AllowedTypes: []visionModel.ShumeiType{
				visionModel.ShumeiTypePolicy,
				visionModel.ShumeiTypeQRCode,
			},
		},
	}).Once()
	s.Equal(GetShumeiTypesString(mockRegion), "POLITY_QRCODE")
}

func TestSuite(t *testing.T) {
	suite.Run(t, new(Suite))
}
