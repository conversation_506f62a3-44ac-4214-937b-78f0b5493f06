package pk

import (
	"database/sql"

	"github.com/17media/dig"

	"github.com/17media/api/service/agora"
	"github.com/17media/api/service/cache"
	"github.com/17media/api/service/kv"
	"github.com/17media/api/service/queryv2"
	"github.com/17media/api/service/redis"
	_ "github.com/17media/api/service/redis/redispersistent"
	"github.com/17media/api/service/region"
	"github.com/17media/api/service/scoreboard"
	"github.com/17media/api/setup/dimanager"
	_ "github.com/17media/api/setup/mysql/mysqlgiftwriter"
	"github.com/17media/api/stores/arcade"
	collaborationChecker "github.com/17media/api/stores/collaboration/checker"
	"github.com/17media/api/stores/event"
	"github.com/17media/api/stores/eventory"
	groupcallBridger "github.com/17media/api/stores/groupcall/bridger"
	"github.com/17media/api/stores/leaderboard"
	"github.com/17media/api/stores/leaderboard/anonymous"
	liveHelper "github.com/17media/api/stores/live/helper"
	"github.com/17media/api/stores/monster"
	"github.com/17media/api/stores/pk/helper"
	"github.com/17media/api/stores/pk/match"
	"github.com/17media/api/stores/pk/notifier"
	"github.com/17media/api/stores/pk/revenge"
	"github.com/17media/api/stores/pk/schedule"
	"github.com/17media/api/stores/user"

	_ "github.com/17media/api/stores/videochat/config"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of pk object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		Helper               helper.Helper                `name:"pkHelper"`
		User                 user.Store                   `name:"user"`
		LiveHelper           liveHelper.Helper            `name:"liveHelper"`
		Cache                cache.Service                `name:"cache"`
		Kv                   kv.KV                        `name:"kv"`
		GiftWriter           *sql.DB                      `name:"mysqlGiftWriter"`
		RedisPersistent      redis.Service                `name:"redisPersistent"`
		Query                queryv2.Mongo                `name:"queryv2WithoutFillNil"`
		Notifier             notifier.Store               `name:"pkNotifier"`
		Match                match.Store                  `name:"pkMatch"`
		Monster              monster.Store                `name:"monster"`
		Region               region.Service               `name:"region"`
		Arcade               arcade.Store                 `name:"arcade"`
		Eventory             eventory.Store               `name:"eventory"`
		Event                event.Event                  `name:"event"`
		Leaderboard          leaderboard.Leaderboard      `name:"leaderboard"`
		GroupcallBridger     groupcallBridger.Bridger     `name:"groupcallBridger"`
		Revenge              revenge.Store                `name:"pkRevenge"`
		Schedule             schedule.Store               `name:"pkSchedule"`
		Scoreboard           scoreboard.Service           `name:"scoreBoard"`
		Anonymous            anonymous.Store              `name:"anonymous"`
		Agora                agora.Service                `name:"agora"`
		CollaborationChecker collaborationChecker.Checker `name:"collaborationChecker"`
	}

	fn := func(p params) Store {
		return New(
			p.Helper,
			p.User,
			p.LiveHelper,
			p.Cache,
			p.Kv,
			p.GiftWriter,
			p.RedisPersistent,
			p.Query,
			p.Notifier,
			p.Match,
			p.Monster,
			p.Region,
			p.Arcade,
			p.Eventory,
			p.Event,
			p.Leaderboard,
			p.GroupcallBridger,
			p.Revenge,
			p.Schedule,
			p.Scoreboard,
			p.Anonymous,
			p.Agora,
			p.CollaborationChecker,
		)
	}
	m.ProvideConstructor(fn, `pk`)
}

// GetPK returns the pk object
func GetPK(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"pk"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
