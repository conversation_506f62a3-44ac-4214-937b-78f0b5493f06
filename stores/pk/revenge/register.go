package revenge

import (
	"github.com/17media/dig"

	"github.com/17media/api/service/redis"
	_ "github.com/17media/api/service/redis/redispersistent"
	"github.com/17media/api/setup/dimanager"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		RedisPersistent redis.Service `name:"redisPersistent"`
	}

	fn := func(p params) Store {
		return New(
			p.RedisPersistent,
		)
	}
	m.ProvideConstructor(fn, `pkRevenge`)
}

// GetPKRevenge returns the revenge object
func GetPKRevenge(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"pkRevenge"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
