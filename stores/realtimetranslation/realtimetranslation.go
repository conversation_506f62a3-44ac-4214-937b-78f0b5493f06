package realtimetranslation

import (
	"errors"

	"github.com/17media/api/base/ctx"
	queueModel "github.com/17media/api/models/queue"
)

type streamSubtitleEvent string

const (
	streamSubtitleEventCreate     streamSubtitleEvent = "create"
	streamSubtitleEventChangeRtmp streamSubtitleEvent = "changeRtmp"
	streamSubtitleEventEnd        streamSubtitleEvent = "end"
	streamSubtitleEventPause      streamSubtitleEvent = "pause"
	streamSubtitleEventResume     streamSubtitleEvent = "resume"
)

var (
	ErrorSubtitleJobNotFound error = errors.New("subtitle job not found")
)

type Store interface {
	// sends create event, streamID can only be registered once, duplicate registration wil be ignored
	RegisterSubtitleJob(context ctx.CTX, userID string, roomID string, streamID string, region string, rtmpURL string) error

	// send end event
	UnRegisterSubtitleJob(context ctx.CTX, streamID string) error

	// send changeRTMP event
	UpdateSubtitleJob(context ctx.CTX, streamID, rtmpURL string) error

	// send pause event
	PauseSubtitleJob(context ctx.CTX, streamID string) error

	// send resume event
	ResumeSubtitleJob(context ctx.CTX, streamID string) error

	// return if stt task is registered
	IsRegisteredSubtitleJob(context ctx.CTX, streamID string) (bool, error)

	// ProcessStreamTranslation Process Pub/Sub messages from ML and forwards them to mobile
	ProcessStreamTranslation(context ctx.CTX, b []byte, opt queueModel.CallbackOption) error

	GetJPSubtitleStreams(context ctx.CTX) ([]string, error)
}
