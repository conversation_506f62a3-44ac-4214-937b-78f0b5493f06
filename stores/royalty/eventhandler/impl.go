package eventhandler

import (
	"encoding/json"
	"strconv"
	"time"

	"google.golang.org/protobuf/proto"

	"github.com/17media/logrus"
	"github.com/17media/structs"

	"github.com/17media/api/base/ctx"
	baseQueue "github.com/17media/api/base/queue"
	baseTime "github.com/17media/api/base/time"
	"github.com/17media/api/models"
	"github.com/17media/api/models/keys"
	queueModel "github.com/17media/api/models/queue"
	royaltyModel "github.com/17media/api/models/royalty"
	"github.com/17media/api/service/kv"
	"github.com/17media/api/service/queryv2"
	"github.com/17media/api/service/queue"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/stores/live/stream"
	royaltyConfig "github.com/17media/api/stores/royalty/config"
	"github.com/17media/api/stores/royalty/rule"
	"github.com/17media/api/stores/user"
)

const (
	pfxLock        = "lock"
	pfxFirstFollow = "firstFollow"
	pfxCollections = "collections"
	dummy          = "1"

	lockTime            = 5 * time.Minute
	keepViewTime        = 5 * time.Minute
	keepViewDuration    = 48 * time.Hour
	assumedLiveDuration = 48 * time.Hour
)

var (
	datetimeNowToBytes = convertDatetimeNowToBytes
	getNow             = baseTime.NowTaipei
	// isAchievementEnable will check each achievement's events,
	// if the achievement is disabled, corresponding event will be ignored
	isAchievementEnable = royaltyConfig.IsAchievementEnable
)

// New returns EventHandler
func New(redisPersist redis.Service, mongo queryv2.Mongo, kv kv.KV, streamStore stream.Stream, publisher queue.Publisher, us user.Store) EventHandler {
	eh := &impl{
		redisPersist: redisPersist,
		mongo:        mongo,
		kv:           kv,
		us:           us,
	}

	handlers := map[royaltyModel.EventType]func(context ctx.CTX, event *royaltyModel.Event) error{
		royaltyModel.EventType_EnterLive:                 eh.processEnterLive,
		royaltyModel.EventType_KeepViewLive:              eh.processKeepViewLive,
		royaltyModel.EventType_CommentLive:               eh.processIncreasingEvent,
		royaltyModel.EventType_FirstCommentInLive:        eh.processFirstEventInLive,
		royaltyModel.EventType_FirstShareLive:            eh.processFirstEventInLive,
		royaltyModel.EventType_FirstFollowAfterLiveStart: eh.processFirstEventInLive,
		royaltyModel.EventType_FirstSendGift:             eh.processFirstEventInLive,
		royaltyModel.EventType_CommentDiffLive:           eh.processIncreasingEvent,
		royaltyModel.EventType_ShareALive:                eh.processIncreasingEvent,
		royaltyModel.EventType_ShareADiffStreamerLive:    eh.processIncreasingEvent,
		royaltyModel.EventType_DrawRedEnvelope:           eh.processIncreasingEvent,
		royaltyModel.EventType_DrawRedEnvelopeGetPoint:   eh.processIncreasingNEvent,
		royaltyModel.EventType_UsePip:                    eh.processIncreasingEvent,
		royaltyModel.EventType_UseBarrageComment:         eh.processIncreasingEvent,
		royaltyModel.EventType_BirthdayGift:              eh.processIncreasingNEvent,
		royaltyModel.EventType_GoldenTicket:              eh.processIncreasingEvent,
		royaltyModel.EventType_MonsterSettle:             eh.processIncreasingEvent,
		royaltyModel.EventType_PokeBack:                  eh.processIncreasingEvent,
		royaltyModel.EventType_ValentinePokeBack:         eh.processIncreasingEvent,
	}
	eh.eventHandler = handlers

	fh := &followHandler{
		publisher:    publisher,
		streamStore:  streamStore,
		redisPersist: redisPersist,
	}
	user.RegisterFollowHDL(fh)

	return eh
}

type impl struct {
	redisPersist redis.Service
	mongo        queryv2.Mongo
	eventHandler map[royaltyModel.EventType]func(context ctx.CTX, event *royaltyModel.Event) error
	kv           kv.KV
	us           user.Store
}

func (im *impl) HandleEvent(context ctx.CTX, data []byte, option queueModel.CallbackOption) error {
	context = ctx.WithValues(context, map[string]interface{}{
		"data": string(data),
	})

	event := &royaltyModel.Event{}
	if option.MarshalWithProto {
		if err := proto.Unmarshal(data, event); err != nil {
			context.WithField("err", err).Error("proto.Unmarshal failed")
			return nil
		}
	} else {
		if err := json.Unmarshal(data, event); err != nil {
			context.WithField("err", err).Error("json.Unmarshal failed")
			return nil
		}
	}

	eventrules, ok := rule.AllRules[event.Type]
	if !ok {
		// for preventing worker retry, return nil
		context.WithField("event_type", event.Type).Error("rules match event not found")
		return nil
	}
	for i := 0; i < len(eventrules); i++ {
		if isAchievementEnable(int(eventrules[i].Achievement)) == false {
			// for preventing worker retry, return nil
			return nil
		}
	}

	hf, ok := im.eventHandler[event.Type]
	if !ok {
		context.Error(ErrUnknownEventType.Error())
		return nil
	}

	if err := hf(context, event); err != nil {
		context.WithFields(logrus.Fields{"err": err, "type": event.Type}).Error("handler failed")
		return err
	}

	return nil
}

func convertDatetimeNowToBytes(now time.Time) []byte {
	return []byte(strconv.FormatInt(now.Unix(), 10))
}

func (im *impl) lock(context ctx.CTX, params []string) error {
	rKeys := []string{pfxLock}
	rKeys = append(rKeys, params...)
	key := rule.RedisKey(rKeys)

	err := im.redisPersist.SetNXLegacy(context, key, []byte(dummy), lockTime)
	if err == nil {
		return nil
	}

	if err == redis.ErrNotFound {
		return ErrSomeOneIsReceiving
	}

	context.WithField("err", err).Error("royalty lock setNX failed")
	return err
}

func (im *impl) unlock(context ctx.CTX, params []string) error {
	rKeys := []string{pfxLock}
	rKeys = append(rKeys, params...)
	key := rule.RedisKey(rKeys)

	if _, err := im.redisPersist.Del(context, key); err != nil {
		context.WithField("err", err).Error("royalty lock del failed")
		return err
	}
	return nil
}

func (im *impl) recordHistory(
	context ctx.CTX,
	userID string,
	achievement royaltyModel.AchievementType,
	rType rule.RoyaltyRuleType,
	progress int,
	eventTypes []royaltyModel.EventType,
	timestamp int64,
) error {
	// check existing first
	previous := &rule.RoyaltyHistory{}
	if err := im.kv.Get(
		context,
		keys.TabRoyaltyHistory,
		rule.RoyaltyHistoryTabelKey(userID, achievement),
		previous,
	); err != nil && err != kv.ErrNotFound {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("kv.Get failed")
		return err
	} else if progress != 0 && progress <= previous.Progress {
		// Ignore it
		context.WithFields(
			logrus.Fields{"userID": userID, "progress": progress, "achievement": achievement.String()},
		).Info("royaltyHistory ignored")
		return nil
	}

	history := rule.RoyaltyHistory{
		UserIDAchievement: rule.RoyaltyHistoryTabelKey(userID, achievement),
		UserID:            userID,
		Achievement:       achievement.String(),
		Type:              int(rType),
		Progress:          progress,
		Timestamp:         timestamp,
	}
	if isDone(achievement, progress, previous.Collections, eventTypes) {
		history.IsNewForEquipped = true
	}
	hMap := structs.Map(history)
	delete(hMap, pfxCollections)

	if len(eventTypes) > 0 {
		for i := range eventTypes {
			key := keys.CustomKey(".", pfxCollections, eventTypes[i].String())
			hMap[key] = timestamp
		}
	}

	context.WithFields(logrus.Fields(hMap)).Info("royaltyHistory ...")

	if err := im.kv.Patch(
		context,
		keys.TabRoyaltyHistory,
		rule.RoyaltyHistoryTabelKey(userID, achievement),
		hMap,
	); err != nil {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("royaltyHistory failed")
		return err
	}

	return nil
}

func isDone(achievement royaltyModel.AchievementType, progress int, prevCollections map[string]int64, eventTypes []royaltyModel.EventType) bool {
	s, ok := rule.AllAchievements[achievement]
	if !ok {
		return false
	}

	ruleType := s.Rule.Type
	totalProgress := s.Rule.GetTotalProgress()

	// Progress
	if ruleType == rule.Progress {
		return progress == totalProgress
	}

	// Collection
	doneCollections := len(prevCollections)
	for _, event := range eventTypes {
		if _, ok := prevCollections[event.String()]; !ok {
			doneCollections++
		}
	}
	return doneCollections == totalProgress
}

func (im *impl) matchRuleAndRecordHistory(
	context ctx.CTX, userID string,
	event *royaltyModel.Event,
	compareFunc func(index int, goal interface{}) bool,
) error {
	rules, ok := rule.AllRules[event.Type]
	if !ok {
		context.WithFields(
			logrus.Fields{"userID": userID},
		).Warning("rule not existing")
		return nil
	}
	// match the rule if possible
	for i, rule := range rules {
		for _, step := range rule.Steps {
			if compareFunc(i, step.Goal) {
				context.WithFields(
					logrus.Fields{
						"userID":      userID,
						"event":       event.Type.String(),
						"achievement": rule.Achievement.String(),
						"progress":    step.Progress,
					},
				).Info("rule matched")
				if err := im.recordHistory(
					context,
					userID,
					rule.Achievement,
					rule.Type,
					step.Progress,
					nil,
					event.Timestamp,
				); err != nil {
					return err
				}
				break
			}
		}
	}

	return nil
}

func equalInt(intS []int) func(index int, goal interface{}) bool {
	return func(index int, goal interface{}) bool {
		return intS[index] >= goal.(int)
	}
}

func (im *impl) accumulateContinuousEnter(context ctx.CTX, userID string, event *royaltyModel.Event) error {
	now, err := getNow()
	if err != nil {
		return err
	}

	nRedisKey, yRedisKeys := rule.EnterLiveRedisKeys(userID, now)

	if err := im.redisPersist.SetNXLegacy(
		context, nRedisKey, []byte(dummy), rule.KeepViewDuration,
	); err == redis.ErrNotFound {
		// Do nothing because today is done
		return nil
	} else if err != nil {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("redis.SetNX failed")
		return err
	}

	count := 1
	if existing, err := im.redisPersist.Exists(context, yRedisKeys); err != nil {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("redis.Exists failed")
		return err
	} else if existing {
		key := rule.RoyaltyTabelKey(userID, event.Type)
		incr := &rule.RoyaltyData{}
		if err := im.kv.Increment(context, keys.TabRoyalty, key, incr, "count", 1); err != nil {
			context.WithFields(
				logrus.Fields{"userID": userID, "err": err},
			).Error("kv.Incr failed")
			return err
		}
		count = incr.Count
	} else {
		key := rule.RoyaltyTabelKey(userID, event.Type)
		incr := &rule.RoyaltyData{UserIDEvent: key, Count: 1}
		if err := im.kv.Set(context, keys.TabRoyalty, key, *incr); err != nil {
			context.WithFields(
				logrus.Fields{"userID": userID, "err": err},
			).Error("kv.Set failed")
			return err
		}
	}

	if err := im.matchRuleAndRecordHistory(
		context, userID, event, equalInt([]int{count}),
	); err != nil {
		return err
	}

	return nil
}

func (im *impl) processEnterLive(context ctx.CTX, event *royaltyModel.Event) error {
	now, err := getNow()
	if err != nil {
		return err
	}

	userID := event.LiveEvent.ActorID
	if err := im.accumulateContinuousEnter(context, userID, event); err != nil {
		return err
	}

	// Set up reference
	rKeys := []string{
		userID,
		royaltyModel.EventType_KeepViewLive.String(),
	}
	if err := im.lock(context, rKeys); err == ErrSomeOneIsReceiving {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Warning("processEnterLive racing lock")
		return nil
	} else if err != nil {
		// Log and ignore this event at this monent.
		// Recover this status in the next step (processKeepViewLive)
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("processEnterLive lock failed")
		return nil
	}
	defer im.unlock(context, rKeys)

	if err := im.redisPersist.Set(
		context,
		rule.KeepViewTimeRedisKey(userID),
		datetimeNowToBytes(now),
		keepViewTime,
	); err != nil {
		// Log and ignore this event at this monent.
		// Recover this status in the next step (processKeepViewLive)
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("KeepViewTimeRedisKey Set failed")
		return nil
	}

	im.us.DelUserCache(context, userID)
	return nil
}

func (im *impl) processKeepViewLive(context ctx.CTX, event *royaltyModel.Event) error {
	now, err := getNow()
	if err != nil {
		return err
	}

	userID := event.LiveEvent.ActorID

	rKeys := []string{
		userID,
		event.Type.String(), //royaltyModel.EventType_KeepViewLive.String()
	}
	if err := im.lock(context, rKeys); err == ErrSomeOneIsReceiving {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Warning("processKeepViewLive racing lock")
		return nil
	} else if err != nil {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("processKeepViewLive lock failed")
		return nil
	}
	defer im.unlock(context, rKeys)

	val, err := im.redisPersist.Get(context, rule.KeepViewTimeRedisKey(userID))
	if err == redis.ErrNotFound {
		val = datetimeNowToBytes(now)
	} else if err != nil {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("KeepViewTimeRedisKey Get failed")
		return nil
	}

	// renew ttl, and recover status at the same time if missing in previous step
	if err := im.redisPersist.Set(
		context,
		rule.KeepViewTimeRedisKey(userID),
		datetimeNowToBytes(now),
		keepViewTime,
	); err != nil {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("KeepViewTimeRedisKey Set failed")
		return err
	}

	previousTime, _ := strconv.ParseInt(string(val), 10, 64)
	delta := now.Unix() - previousTime

	if delta <= 0 || delta > int64(keepViewTime.Seconds()) {
		context.WithFields(
			logrus.Fields{"userID": userID, "previous_time": previousTime, "delta": delta},
		).Error("time incorrect")
		return nil
	}

	// keep this key forever
	totalCount, err := im.redisPersist.Incrby(context, rule.KeepViewCountRedisKey(userID), int(delta))
	if err != nil {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("KeepViewCountRedisKey Incrby failed")
		return err
	}

	// keep this key for two days
	dailyCount, err := im.redisPersist.Incrby(context, rule.KeepViewDailyCountRedisKey(userID, now), int(delta))
	if err != nil {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("KeepViewDailyCountRedisKey Incrby failed")
		return err
	}
	if err := im.redisPersist.Expire(
		context, rule.KeepViewDailyCountRedisKey(userID, now), rule.KeepViewDuration,
	); err != nil {
		context.WithFields(
			logrus.Fields{"userID": userID, "err": err},
		).Error("KeepViewDailyCountRedisKey Expire failed")
		return err
	}

	// The order of counts is very important.
	// It needs to consist with the one in AllRules
	counts := []int64{totalCount, totalCount, dailyCount}
	f := func(index int, goal interface{}) bool {
		g := int64(goal.(time.Duration).Seconds())
		return counts[index] >= g && counts[index]-delta < g
	}

	if err := im.matchRuleAndRecordHistory(context, userID, event, f); err != nil {
		return err
	}

	return nil
}

func (im *impl) processIncreasing(context ctx.CTX, event *royaltyModel.Event, inc int64) error {
	context = ctx.WithValues(context, map[string]interface{}{
		"event": event,
	})

	userID := event.LiveEvent.ActorID

	key := rule.RoyaltyTabelKey(userID, event.Type)
	incrResult := &rule.RoyaltyData{}
	if err := im.kv.Increment(context, keys.TabRoyalty, key, incrResult, "count", inc); err != nil {
		context.WithField("err", err).Error("kv.Increment failed")
		return err
	}

	if err := im.matchRuleAndRecordHistory(
		context, userID, event, equalInt([]int{incrResult.Count}),
	); err != nil {
		return err
	}

	if event.Type != royaltyModel.EventType_CommentLive {
		// Comment in live room is very often
		im.us.DelUserCache(context, userID)
	}
	return nil
}

func (im *impl) processIncreasingEvent(context ctx.CTX, event *royaltyModel.Event) error {
	return im.processIncreasing(context, event, 1)
}

func (im *impl) processIncreasingNEvent(context ctx.CTX, event *royaltyModel.Event) error {
	return im.processIncreasing(context, event, event.LiveEvent.Count)
}

func (im *impl) processFirstEventInLive(context ctx.CTX, event *royaltyModel.Event) error {
	context = ctx.WithValues(context, map[string]interface{}{
		"event": event,
	})

	userID := event.LiveEvent.ActorID

	// Save royalty
	key := rule.RoyaltyTabelKey(userID, event.Type)
	incrResult := &rule.RoyaltyData{}
	if err := im.kv.Increment(context, keys.TabRoyalty, key, incrResult, "count", 1); err != nil {
		context.WithField("err", err).Error("kv.Increment failed")
		return err
	}

	// count > 1 means user has already met task condition, history has also saved.
	if incrResult.Count > 1 {
		return nil
	}

	// Save history
	if err := im.recordHistory(
		context,
		userID,
		royaltyModel.AchievementType_FirstInLive,
		rule.Collection,
		0, // no progress for collection type
		[]royaltyModel.EventType{event.Type},
		event.Timestamp,
	); err != nil {
		return err
	}

	im.us.DelUserCache(context, userID)
	return nil
}

func (im *impl) SetEventCount(context ctx.CTX, user *models.User, achieveType, count int, clearRelatedHistory bool) error {
	now, err := getNow()
	if err != nil {
		return err
	}

	context = ctx.WithValues(context, map[string]interface{}{
		"user":        user,
		"achieveType": achieveType,
		"count":       count,
	})

	if _, ok := royaltyModel.AchievementType_name[int32(achieveType)]; !ok {
		return ErrWrongAchieveType
	}

	at := royaltyModel.AchievementType(achieveType)

	// clear up title, because new count may disable old title.
	if err := im.kv.Patch(context, keys.TabUser, user.UserID, map[string]interface{}{
		"loyaltyTitle": "",
	}); err != nil {
		context.WithField("err", err).Error("im.kv.Patch failed")
		return err
	}

	switch at {
	case royaltyModel.AchievementType_Init:
		fallthrough
	case royaltyModel.AchievementType_ViewLive:
		fallthrough
	case royaltyModel.AchievementType_ViewLiveInADay:
		events := rule.AllAchievements[at].Events
		if clearRelatedHistory {
			// Delete whole histories related to the same event
			for _, e := range events {
				rules := rule.AllRules[e]
				for _, r := range rules {
					key := rule.RoyaltyHistoryTabelKey(user.UserID, r.Achievement)
					if err := im.kv.Del(context, keys.TabRoyaltyHistory, key); err != nil && err != kv.ErrNotFound {
						context.WithField("err", err).Error("kv.Del failed")
						return err
					}
				}
			}
		}

		// reset timer
		if err := im.redisPersist.Set(
			context,
			rule.KeepViewTimeRedisKey(user.UserID),
			[]byte(strconv.FormatInt(now.Unix(), 10)),
			rule.KeepViewTime,
		); err != nil {
			context.WithFields(
				logrus.Fields{"userID": user.UserID, "err": err},
			).Error("redis Set failed")
			return err
		}

		// reset counter
		if err := im.redisPersist.Set(
			context,
			rule.KeepViewCountRedisKey(user.UserID),
			[]byte(strconv.Itoa(count)),
			redis.Forever,
		); err != nil {
			context.WithFields(
				logrus.Fields{"userID": user.UserID, "err": err},
			).Error("redis Set failed")
			return err
		}

		// reset daily counter
		if err := im.redisPersist.Set(
			context,
			rule.KeepViewDailyCountRedisKey(user.UserID, now),
			[]byte(strconv.Itoa(count)),
			redis.Forever,
		); err != nil {
			context.WithFields(
				logrus.Fields{"userID": user.UserID, "err": err},
			).Error("redis Set failed")
			return err
		}

		counts := []int64{int64(count), int64(count), int64(count)}
		f := func(index int, goal interface{}) bool {
			g := int64(goal.(time.Duration).Seconds())
			return counts[index] >= g
		}

		for _, e := range events {
			event := &royaltyModel.Event{
				Type: e,
				LiveEvent: &royaltyModel.LiveEvent{
					ActorID:  user.UserID,
					StreamID: "SetEventStreamID",
				},
				Timestamp: baseTime.TimeNow().Unix(),
			}
			if err := im.matchRuleAndRecordHistory(context, user.UserID, event, f); err != nil {
				return err
			}
		}

	case royaltyModel.AchievementType_EnterALive:
		// Delete history
		key := rule.RoyaltyHistoryTabelKey(user.UserID, at)
		if err := im.kv.Del(context, keys.TabRoyaltyHistory, key); err != nil && err != kv.ErrNotFound {
			context.WithField("err", err).Error("kv.Del failed")
			return err
		}

		nRedisKey, yRedisKeys := rule.EnterLiveRedisKeys(user.UserID, now)
		// Delete now flag
		if _, err := im.redisPersist.Del(
			context, nRedisKey,
		); err != nil && err != redis.ErrNotFound {
			return err
		}

		// Reset yesterday flag
		if err := im.redisPersist.SetNXLegacy(
			context, yRedisKeys, []byte("1"), rule.KeepViewDuration,
		); err != nil && err != redis.ErrNotFound {
			return err
		}

		// Reset counter in Royalty table
		key = rule.RoyaltyTabelKey(user.UserID, royaltyModel.EventType_EnterLive)
		incr := &rule.RoyaltyData{UserIDEvent: key, Count: count}
		if err := im.kv.Set(context, keys.TabRoyalty, key, *incr); err != nil {
			return err
		}
		event := &royaltyModel.Event{
			Type: royaltyModel.EventType_EnterLive,
			LiveEvent: &royaltyModel.LiveEvent{
				ActorID:  user.UserID,
				StreamID: "SetEventStreamID",
			},
			Timestamp: baseTime.TimeNow().Unix(),
		}
		if err := im.matchRuleAndRecordHistory(
			context, user.UserID, event, equalInt([]int{count}),
		); err != nil {
			return err
		}
	case royaltyModel.AchievementType_FirstInLive:
		// Delete history
		key := rule.RoyaltyHistoryTabelKey(user.UserID, at)
		if err := im.kv.Del(context, keys.TabRoyaltyHistory, key); err != nil && err != kv.ErrNotFound {
			context.WithField("err", err).Error("kv.Del failed")
			return err
		}

		events := rule.AllAchievements[at].Events
		for _, e := range events {
			// Del royalty table.
			key := rule.RoyaltyTabelKey(user.UserID, e)
			if err := im.kv.Del(context, keys.TabRoyalty, key); err != nil && err != kv.ErrNotFound {
				context.WithField("err", err).Error("kv.Del failed")
				return err
			}
		}

		for i := 0; i < count && i < len(events); i++ {
			key := rule.RoyaltyTabelKey(user.UserID, events[i])
			v := rule.RoyaltyData{
				UserIDEvent: key,
				Count:       1,
			}
			if err := im.kv.Set(context, keys.TabRoyalty, key, v); err != nil {
				context.WithField("err", err).Error("kv.Set failed")
				return err
			}

			if err := im.recordHistory(
				context,
				user.UserID,
				royaltyModel.AchievementType_FirstInLive,
				rule.Collection,
				0, // no progress for collection type
				[]royaltyModel.EventType{events[i]},
				baseTime.TimeNow().Unix(),
			); err != nil {
				return err
			}
		}
	case royaltyModel.AchievementType_CommentTimes:
		fallthrough
	case royaltyModel.AchievementType_CommentDifferentLive:
		fallthrough
	case royaltyModel.AchievementType_ShareLive:
		fallthrough
	case royaltyModel.AchievementType_DrawRedEnvelopTimes:
		fallthrough
	case royaltyModel.AchievementType_DrawRedEnvelopPoints:
		fallthrough
	case royaltyModel.AchievementType_PipTimes:
		fallthrough
	case royaltyModel.AchievementType_BarrageCommentTimes:
		fallthrough
	case royaltyModel.AchievementType_AccompanyTimes:
		fallthrough
	case royaltyModel.AchievementType_BirthdayGiftTimes:
		fallthrough
	case royaltyModel.AchievementType_GoldenTicketSendTimes:
		fallthrough
	case royaltyModel.AchievementType_MonsterSettleTimes:
		fallthrough
	case royaltyModel.AchievementType_ShareDiffStreamerLive:
		fallthrough
	case royaltyModel.AchievementType_ValentinePokeBackTimes:
		fallthrough
	case royaltyModel.AchievementType_PokeTimes:
		// Delete history
		key := rule.RoyaltyHistoryTabelKey(user.UserID, at)
		if err := im.kv.Del(context, keys.TabRoyaltyHistory, key); err != nil && err != kv.ErrNotFound {
			context.WithField("err", err).Error("kv.Del failed")
			return err
		}

		events := rule.AllAchievements[at].Events
		for _, e := range events {
			// Set royalty table.
			key := rule.RoyaltyTabelKey(user.UserID, e)
			v := rule.RoyaltyData{
				UserIDEvent: key,
				Count:       count,
			}
			if err := im.kv.Set(context, keys.TabRoyalty, key, v); err != nil {
				context.WithField("err", err).Error("kv.Set failed")
				return err
			}

			event := &royaltyModel.Event{
				Type: e,
				LiveEvent: &royaltyModel.LiveEvent{
					ActorID:  user.UserID,
					StreamID: "SetEventStreamID",
				},
				Timestamp: baseTime.TimeNow().Unix(),
			}
			if err := im.matchRuleAndRecordHistory(
				context, user.UserID, event, equalInt([]int{count}),
			); err != nil {
				return err
			}
		}
	}

	return nil
}

// Follow api runs path: userStore->FollowStore, but we can not get streamer on-air information in these two stores,
// adding streamStore into either store causes circular import.
// So we register follow handler to userStore and fire FirstFollowAfterLiveStart here.
type followHandler struct {
	publisher    queue.Publisher
	streamStore  stream.Stream
	redisPersist redis.Service
}

func (fh *followHandler) firstFollowKey(streamID string) string {
	return keys.RedisKey(keys.PfxRoyalty, pfxFirstFollow, streamID)
}

func (fh *followHandler) FollowStreamer(context ctx.CTX, user, streamer *models.User) error {
	context = ctx.WithValues(context, map[string]interface{}{
		"user":     user,
		"streamer": streamer,
	})

	// Check streamer is on live
	_, streamID, err := fh.streamStore.IsUserOnLive(context, streamer.UserID)
	if err == stream.ErrNotBroadcasting {
		return nil
	} else if err != nil {
		context.WithField("err", err).Error("streamStore.IsUserOnLive failed")
		return err
	}

	// Check user is the first one who follows the streamer after streamer on-air
	firstFollowKey := fh.firstFollowKey(streamID)
	if err := fh.redisPersist.SetNXLegacy(context, firstFollowKey, []byte("1"), assumedLiveDuration); err != nil {
		return nil
	}

	if err := fh.publisher.Publish(
		context,
		&royaltyModel.Event{
			Type: royaltyModel.EventType_FirstFollowAfterLiveStart,
			LiveEvent: &royaltyModel.LiveEvent{
				ActorID:  user.UserID,
				StreamID: streamID,
			},
			Timestamp: baseTime.TimeNow().Unix(),
		}, baseQueue.MarshalWithProto()); err != nil {
		context.WithField("err", err).Error("publisher.Publish failed")
		return err
	}

	return nil
}
