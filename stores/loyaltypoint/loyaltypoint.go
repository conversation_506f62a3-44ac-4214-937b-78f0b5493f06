package loyaltypoint

import (
	"fmt"

	"github.com/jmoiron/sqlx"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models"
	"github.com/17media/api/models/intra"
	loyaltyPointModel "github.com/17media/api/models/purchase/product/loyaltypoint"
	loyaltyPointCfg "github.com/17media/api/stores/loyaltypoint/helper/config"
)

var (
	// ErrNegativeValue negative value
	ErrNegativeValue = fmt.Errorf("negative value")

	ErrInsufficientPoint = fmt.Errorf("point is not enough for this currency")

	ErrPrizeNotFound = fmt.Errorf("prize not found")

	ErrPrizeOptionNotFound = fmt.Errorf("prize option not found")

	ErrInvalidPrizeUpdateFields = fmt.Errorf("invalid prize update fields")

	ErrPrizeItemNotExist = fmt.Errorf("item not exist, cannot be created as a prize")

	ErrJobIsRunning = fmt.Errorf("job is running")

	ErrCreateNilPrize = fmt.Errorf("cannot create a nil prize")

	ErrDuplicateSubPrizeID = fmt.Errorf("duplicate subPrizeID")

	ErrRedemptionNotFound = fmt.Errorf("redemption not found")

	ErrInvalidRedemptionStatus = fmt.Errorf("invalid redemption status")

	ErrUpdateAutoSentRedemptionStatus = fmt.Errorf("cannot update the status of a redemption of auto-sent prize type")

	ErrAnotherUserCreatingPrize = fmt.Errorf("another user is creating prize now")

	ErrActionToOtherRegionPrize = fmt.Errorf("invalid action to the prize in other region")

	ErrActionToOtherRegionRedemption = fmt.Errorf("invalid action to the redemption in other region")

	ErrPrizeSoldOut = fmt.Errorf("the prize is sold out")

	ErrPrizeNoPersonalQuota = fmt.Errorf("the user has no more quota to the prize")
)

// Store is interface to operate loyalty point currency
type Store interface {
	Add(context ctx.CTX, input loyaltyPointModel.Op) error
	Use(context ctx.CTX, input loyaltyPointModel.Op) error
	//List(context ctx.CTX, userID string) error
	GetCumulativeUsage(context ctx.CTX, userID string) (int64, error)
	GetCumulativeUsageByRegion(context ctx.CTX, userID, region string) (int64, error)
	Report(context ctx.CTX, region string, timestamp int64) (string, error)
	SearchLogs(context ctx.CTX, cursor string, count int, condition loyaltyPointModel.LogSearchCond) (logs []*loyaltyPointModel.Log, nextCursor string, err error)
	ExportLogs(context ctx.CTX, condition loyaltyPointModel.LogSearchCond) (string, error)

	GetSetting(context ctx.CTX, region string) ([]*loyaltyPointModel.SettingItem, error)
	SetSetting(context ctx.CTX, setting []*loyaltyPointModel.SettingItem) error
	// SyncSetting sync loyalty point settings in the same region to be same values. We use it when a new super VIP selling channel is released.
	SyncSetting(context ctx.CTX, region string) error
	// ResetSettingPercentage resets the bonus percentage of loyalty point settings in the same region to be 100%. This should only be used when it needs to update loyalty point settings by current merchandises
	ResetSettingPercentage(context ctx.CTX, region string) error
	// ResetSettingByMerchandise resets loyalty point settings by the online merchandises of given region. This should only be used when it needs to update loyalty point settings by current merchandises
	// Warn: it will set the default loyaltyPoint as the current loyalty point of merchandise, and percentage to 100%.
	// Do it after reset all merchandises to original loyalty point (percentage 100%)
	ResetSettingByMerchandise(context ctx.CTX, region string) error

	// GetPrizeCategories get the prize categories from config
	GetPrizeCategories(context ctx.CTX) ([]loyaltyPointCfg.PrizeCategory, error)

	// Create prize creates a loyalty prize.
	CreatePrize(context ctx.CTX, prize *loyaltyPointModel.Prize) error
	ListPrizes(context ctx.CTX, offset, limit int, condition loyaltyPointModel.PrizeSearchCond) ([]*loyaltyPointModel.Prize, int, error)
	ListOnlinePrizes(context ctx.CTX, cursor string, count int, condition loyaltyPointModel.PrizeSearchCond) ([]*loyaltyPointModel.Prize, string, error)

	// UpdatePrize updates the prize with prizeUpdator.
	// If there is any invalid update fields, the prize will not be updated and returns invalidUpdatedFields and error.
	UpdatePrize(context ctx.CTX, prizeID string, userRegion string, prizeUpdator *loyaltyPointModel.PrizeUpdator) (fieldErrMsgs []string, err error)

	UpdateSequence(context ctx.CTX, updator map[string][]string) error
	// AssignSequence update sequence of a prize to assigned float number if the sequence hasn't been used
	AssignSequence(context ctx.CTX, prizeID, region string, sequence float64) error

	// ListRedemptions list redemptions in mysql LoyaltyPointRedeemLog with given condition as filter.
	ListRedemptions(context ctx.CTX, cursor string, count int, condition loyaltyPointModel.RedeemLogSearchCond) (redemptions []*loyaltyPointModel.RedemptionRecord, nextCursor string, err error)

	// ListRedemptionsByUserID get redemptions with pagingV2
	ListRedemptionsByUserID(context ctx.CTX, cursor string, count int, condition loyaltyPointModel.RedeemLogSearchCond) (redemptions []*loyaltyPointModel.RedemptionRecord, nextCursor string, err error)

	// UpdateRedemptionStatus update the status of the redemption with the unique orderID by OPs.
	UpdateRedemptionStatus(context ctx.CTX, orderID string, editorUser *intra.User, userRegion string, status loyaltyPointModel.RedemptionStatus, receivedAt, expiredAt int64) error

	// CreateRedemption create a new redemption
	CreateRedemption(context ctx.CTX, userInfo *models.User, params loyaltyPointModel.CreateRedemptionParams) (*loyaltyPointModel.RedemptionRecord, error)

	// InjectQuotaRemain adds the remain quota information to the prize
	InjectQuotaRemain(context ctx.CTX, prize *loyaltyPointModel.Prize) error

	// InjectQuotaPersonalRemain adds the remain personal quota information to the prize
	InjectQuotaPersonalRemain(context ctx.CTX, userID string, prize *loyaltyPointModel.Prize) error

	// GetLoyaltyPointsByRegion returns all LoyaltyPoint info given region
	GetLoyaltyPointsByRegion(context ctx.CTX, tx *sqlx.Tx, region string) ([]*loyaltyPointModel.LoyaltyPoint, error)
}
