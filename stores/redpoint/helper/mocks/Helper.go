// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"

	mock "github.com/stretchr/testify/mock"
)

// Helper is an autogenerated mock type for the Helper type
type Helper struct {
	mock.Mock
}

// DispatchUnreadBadgeCountNotif provides a mock function with given fields: context, userIDs
func (_m *Helper) DispatchUnreadBadgeCountNotif(context ctx.CTX, userIDs ...string) error {
	_va := make([]interface{}, len(userIDs))
	for _i := range userIDs {
		_va[_i] = userIDs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, ...string) error); ok {
		r0 = rf(context, userIDs...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DispatchUnreadBadgeCountNotifAllActiveUsers provides a mock function with given fields: context
func (_m *Helper) DispatchUnreadBadgeCountNotifAllActiveUsers(context ctx.CTX) error {
	ret := _m.Called(context)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX) error); ok {
		r0 = rf(context)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DispatchUnreadBadgeCountNotifAllFollowers provides a mock function with given fields: context, followeeUserID
func (_m *Helper) DispatchUnreadBadgeCountNotifAllFollowers(context ctx.CTX, followeeUserID string) error {
	ret := _m.Called(context, followeeUserID)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) error); ok {
		r0 = rf(context, followeeUserID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}
