package config

import (
	"fmt"
	"testing"
	"time"

	"gopkg.in/yaml.v2"

	"github.com/17media/api/base/ctx"
	bTime "github.com/17media/api/base/time"
	confModel "github.com/17media/api/models/config"
	regionModel "github.com/17media/api/models/region"
	mockI18n "github.com/17media/api/service/i18n/mocks"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

var (
	mockStartTimeStr = "2022-06-02 17:00:00 (GMT+0800)"
	mockEndTimeStr   = "2022-06-15 23:59:59 (GMT+0800)"
	st, _            = bTime.Parse(mockStartTimeStr)
	et, _            = bTime.Parse(mockEndTimeStr)

	mockCTX = ctx.Background()
)

type configSuite struct {
	suite.Suite
	mockI18n *mockI18n.Function
}

func (s *configSuite) SetupSuite() {
}

func (s *configSuite) SetupTest() {
	s.mockI18n = &mockI18n.Function{}
	i18nTranslate = s.mockI18n.Translate
}

func (s *configSuite) TearDownTest() {
	s.mockI18n.AssertExpectations(s.T())
}

func TestConfigSuite(t *testing.T) {
	suite.Run(t, new(configSuite))
}

func (s *configSuite) TestCommonCfg() {
	tests := []struct {
		Desc     string
		MockYAML string
		ExpCfg   CommonCfg
		ExpWarn  []string
		ExpErr   error
	}{
		{
			Desc: "normal case",
			MockYAML: `
enableIconAnimation: true
regionPromotion:
    TW:
        enable: true
        enableTimeRange:
            startTime: "2022-06-02 17:00:00 (GMT+0800)"
            endTime: "2022-06-15 23:59:59 (GMT+0800)"
        msgI18nKey: "LD_CTA_KEY"
        actionTarget: "luxurydice"
        versionControl:
            enable: true
            ios: "3.190.0"
            android: "2.106.0"
games:
    ff:
        provider: "17Live"
        enable: true
        gameID: "fruitfarm"
        icon: "https://xxx.yyy/zzz.png"
        url: "https://17live-game/fruitfarm/"
        webViewBgColor: "#ffecba"
        nameI18nkey: "ff_name_key"
        descI18nkey: "ff_desc_key"
        tippingGift:
          enable: true
          triggerPoint: 1000
          giftInfo:
            regular:
            - id: red_envelope
            - id: flower
            special:
              blueberry:
                id: va
              pineapple:
                id: gift_newyear_red_o
              strawberry:
                id: va2
        versionControl:
            enable: true
            ios: "3.126.0"
            android: "2.41.0"
    soccerxgps:
        provider: "xgps"
        enable: true
        gameID: "soccerxgps"
        icon: "https://xxx.yyy/zzz.png"
        getGameUrl: "https://nakama-trade-api-sta.17app.co/api/v1/xgps/user/getGameUrl"
        webViewBgColor: "#ffecba"
        nameI18nkey: "soccerXgps_name_key"
        descI18nkey: "soccerXgps_desc_key"
        versionControl:
            enable: true
            ios: "3.126.0"
            android: "2.41.0"
regionGameOrder:
    TW:
    - "ff"
    - "luxurydice"
`,
			ExpCfg: CommonCfg{
				EnableIconAnimation: true,
				GamesCfg: map[string]GameConfig{
					"ff": {
						Provider:       "17Live",
						Enable:         true,
						GameID:         "fruitfarm",
						Icon:           "https://xxx.yyy/zzz.png",
						Url:            "https://17live-game/fruitfarm/",
						WebViewBgColor: "#ffecba",
						NameI18nkey:    "ff_name_key",
						DescI18nkey:    "ff_desc_key",
						TippingGift: &TippingGiftConfig{
							Enable:       true,
							TriggerPoint: 1000,
							GiftInfo: TippingGiftInfoConfig{
								Regular: []TippingGiftItemConfig{
									{
										ID: "red_envelope",
									},
									{
										ID: "flower",
									},
								},
								Special: map[string]TippingGiftItemConfig{
									"blueberry": {
										ID: "va",
									},
									"pineapple": {
										ID: "gift_newyear_red_o",
									},
									"strawberry": {
										ID: "va2",
									},
								},
							},
						},
						VersionControl: VersionControl{
							Enable:  true,
							IOS:     "3.126.0",
							Android: "2.41.0",
						},
					},
					"soccerxgps": {
						Provider:       "xgps",
						Enable:         true,
						GameID:         "soccerxgps",
						Icon:           "https://xxx.yyy/zzz.png",
						GetGameUrl:     "https://nakama-trade-api-sta.17app.co/api/v1/xgps/user/getGameUrl",
						WebViewBgColor: "#ffecba",
						NameI18nkey:    "soccerXgps_name_key",
						DescI18nkey:    "soccerXgps_desc_key",
						VersionControl: VersionControl{
							Enable:  true,
							IOS:     "3.126.0",
							Android: "2.41.0",
						},
					},
				},
				RegionGameOrderCfg: map[string][]string{
					"TW": {"ff", "luxurydice"},
				},
				RegionPromotionCfg: map[string]*RegionPromotionConfig{
					"TW": {
						Enable: true,
						EnableTimeRange: TimeRange{
							StartTimeStr: mockStartTimeStr,
							EndTimeStr:   mockEndTimeStr,
							StartTime:    st,
							EndTime:      et,
						},
						MsgI18nKey:   "LD_CTA_KEY",
						ActionTarget: "luxurydice",
						VersionControl: VersionControl{
							Enable:  true,
							IOS:     "3.190.0",
							Android: "2.106.0",
						},
					},
				},
			},
		},
		{
			Desc:     "should return an error when YAML format is incorrect",
			MockYAML: "wrong data",
			ExpErr: &yaml.TypeError{Errors: []string{
				"line 1: cannot unmarshal !!str `wrong data` into config.CommonCfg",
			}},
		},
		{
			Desc: "regionPromotion: incorrect time format",
			MockYAML: `
regionPromotion:
    TW:
        enable: true
        enableTimeRange:
            startTime: "2022-09-15T16:00+08:00"
            endTime: "2022-09-16T23:59:59+08:00"
        msgI18nKey: "LD_CTA_KEY"
        actionTarget: "luxurydice"
        versionControl:
            enable: true
            ios: "3.190.0"
            android: "2.106.0"
`,
			ExpErr: fmt.Errorf("failed to parse startTime: parsing time \"2022-09-15T16:00+08:00\" as \"2006-01-02 15:04:05 (GMT-0700)\": cannot parse \"T16:00+08:00\" as \" \""),
		},
		{
			Desc: "not available provider ",
			MockYAML: `
enableIconAnimation: true
regionPromotion:
    TW:
        enable: true
        enableTimeRange:
            startTime: "2022-06-02 17:00:00 (GMT+0800)"
            endTime: "2022-06-15 23:59:59 (GMT+0800)"
        msgI18nKey: "LD_CTA_KEY"
        actionTarget: "luxurydice"
        versionControl:
            enable: true
            ios: "3.190.0"
            android: "2.106.0"
games:
    ff:
        provider: "17LiveQ"
        enable: true
        gameID: "fruitfarm"
        icon: "https://xxx.yyy/zzz.png"
        url: "https://17live-game/fruitfarm/"
        webViewBgColor: "#ffecba"
        nameI18nkey: "ff_name_key"
        descI18nkey: "ff_desc_key"
        versionControl:
            enable: true
            ios: "3.126.0"
            android: "2.41.0"
regionGameOrder:
    TW:
    - "ff"
    - "luxurydice"
`,
			ExpCfg: CommonCfg{
				EnableIconAnimation: true,
				GamesCfg: map[string]GameConfig{
					"ff": {
						Provider:       "17LiveQ",
						Enable:         true,
						GameID:         "fruitfarm",
						Icon:           "https://xxx.yyy/zzz.png",
						Url:            "https://17live-game/fruitfarm/",
						WebViewBgColor: "#ffecba",
						NameI18nkey:    "ff_name_key",
						DescI18nkey:    "ff_desc_key",
						VersionControl: VersionControl{
							Enable:  true,
							IOS:     "3.126.0",
							Android: "2.41.0",
						},
					},
				},
				RegionGameOrderCfg: map[string][]string{
					"TW": {"ff", "luxurydice"},
				},
				RegionPromotionCfg: map[string]*RegionPromotionConfig{
					"TW": {
						Enable: true,
						EnableTimeRange: TimeRange{
							StartTimeStr: mockStartTimeStr,
							EndTimeStr:   mockEndTimeStr,
							StartTime:    st,
							EndTime:      et,
						},
						MsgI18nKey:   "LD_CTA_KEY",
						ActionTarget: "luxurydice",
						VersionControl: VersionControl{
							Enable:  true,
							IOS:     "3.190.0",
							Android: "2.106.0",
						},
					},
				},
			},
			ExpWarn: []string{"gameID fruitfarm provider check failed, 17LiveQ is not an available provider"},
		},
	}

	for _, t := range tests {
		cfg := CommonCfg{}
		res, warns, err := cfg.Check([]byte(t.MockYAML))
		s.Require().Equal(t.ExpErr, err)
		if err == nil {
			cfg.Apply(res)
			s.ElementsMatch(t.ExpWarn, warns)
			s.Equal(t.ExpCfg, cfg, t.Desc)
		}
	}
}

func (s *configSuite) TestExtractAddOn() {
	tests := []struct {
		Desc   string
		Param  confModel.AddonParam
		Config string
		Setup  func()
		ExpRes AddOn
	}{
		{
			Desc: "normal case",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "ios",
					Version: "3.126.0",
				},
			},
			Config: `
enableIconAnimation: true
regionPromotion:
    TW:
        enable: true
        enableTimeRange:
            startTime: "2022-06-02 17:00:00 (GMT+0800)"
            endTime: "2022-06-15 23:59:59 (GMT+0800)"
        msgI18nKey: "LD_CTA_KEY"
        actionTarget: "luxurydice"
        versionControl:
            enable: true
            ios: "3.126.0"
            android: "2.41.0"
games:
  fruitfarm:
    provider: "17Live"
    enable: true
    gameID: "fruitfarm"
    icon: "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png"
    url: "https://17live-game/fruitfarm/"
    webViewBgColor: "#ffecba"
    nameI18nkey: "ff_name_key"
    descI18nkey: "ff_desc_key"
    versionControl:
      enable: false
      ios: "3.126.0"
      android: "2.41.0"
  luxurydice:
    provider: "17Live"
    enable: true
    gameID: "luxurydice"
    icon: "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aea.png"
    url: "https://17live-game/luxurydice/"
    webViewBgColor: "#ffecba"
    nameI18nkey: "ld_name_key"
    descI18nkey: "ld_desc_key"
    versionControl:
      enable: false
      ios: "3.126.0"
      android: "2.41.0"
  soccerxgps:
    provider: "xgps"
    enable: true
    gameID: "soccerxgps"
    icon: "https://cdn.17app.co/21126f4a-4af1-4f79-8fdb-7434d10de535.png"
    getGameUrl: "https://nakama-trade-api-sta.17app.co/api/v1/xgps/user/getGameUrl"
    webViewBgColor: "#ffecba"
    nameI18nkey: "soccerXgps_name_key"
    descI18nkey: "soccerXgps_desc_key"
    versionControl:
      enable: false
      ios: "3.126.0"
      android: "2.41.0"
regionGameOrder:
  TW:
  - "luxurydice"
  - "notexisting"
  - "fruitfarm"
  - "soccerxgps"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }

				s.mockI18n.On("Translate", "tw", "ff_name_key", mock.Anything).Return("Fruit Farm", nil)
				s.mockI18n.On("Translate", "tw", "ff_desc_key", mock.Anything).Return("Test", nil)
				s.mockI18n.On("Translate", "tw", "ld_name_key", mock.Anything).Return("Luxury Dice", nil)
				s.mockI18n.On("Translate", "tw", "ld_desc_key", mock.Anything).Return("LD Desc", nil)
				s.mockI18n.On("Translate", "tw", "soccerXgps_name_key", mock.Anything).Return("17 Soccer", nil)
				s.mockI18n.On("Translate", "tw", "soccerXgps_desc_key", mock.Anything).Return("17Soccer Desc", nil)
			},
			ExpRes: AddOn{
				EnableIconAnimation: true,
				GamesInfo: []GameInfo{
					{
						Provider:       "17Live",
						ID:             "luxurydice",
						Icon:           "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aea.png",
						Url:            "https://17live-game/luxurydice/",
						WebViewBgColor: "#ffecba",
						Name:           "Luxury Dice",
						Desc:           "LD Desc",
					},
					{
						Provider:       "17Live",
						ID:             "fruitfarm",
						Icon:           "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png",
						Url:            "https://17live-game/fruitfarm/",
						WebViewBgColor: "#ffecba",
						Name:           "Fruit Farm",
						Desc:           "Test",
					},
					{
						Provider:       "xgps",
						ID:             "soccerxgps",
						Icon:           "https://cdn.17app.co/21126f4a-4af1-4f79-8fdb-7434d10de535.png",
						GetGameUrl:     "https://nakama-trade-api-sta.17app.co/api/v1/xgps/user/getGameUrl",
						WebViewBgColor: "#ffecba",
						Name:           "17 Soccer",
						Desc:           "17Soccer Desc",
					},
				},
				Promotion: Promotion{
					Enable:     true,
					MsgI18nKey: "LD_CTA_KEY",
					Target:     "luxurydice",
				},
			},
		},
		{
			Desc: "success case: ipRegion(CA) is using the config of the regionGroup(US)",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "CA",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "ios",
					Version: "3.126.0",
				},
			},
			Config: `
enableIconAnimation: true
regionPromotion:
    TW:
        enable: true
        enableTimeRange:
            startTime: "2022-06-02 17:00:00 (GMT+0800)"
            endTime: "2022-06-15 23:59:59 (GMT+0800)"
        msgI18nKey: "FF_CTA_KEY"
        actionTarget: "fruitfarm"
        versionControl:
            enable: true
            ios: "3.126.0"
            android: "2.41.0"
    US:
        enable: true
        enableTimeRange:
            startTime: "2022-06-02 17:00:00 (GMT+0800)"
            endTime: "2022-06-15 23:59:59 (GMT+0800)"
        msgI18nKey: "LD_CTA_KEY"
        actionTarget: "luxurydice"
        versionControl:
            enable: true
            ios: "3.126.0"
            android: "2.41.0"
games:
  fruitfarm:
    provider: "17Live"
    enable: true
    gameID: "fruitfarm"
    icon: "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png"
    url: "https://17live-game/fruitfarm/"
    webViewBgColor: "#ffecba"
    nameI18nkey: "ff_name_key"
    descI18nkey: "ff_desc_key"
    versionControl:
      enable: false
      ios: "3.126.0"
      android: "2.41.0"
regionGameOrder:
  TW:
  - "fruitfarmjp"
  US:
  - "fruitfarm"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
				s.mockI18n.On("Translate", "tw", "ff_name_key", mock.Anything).Return("Fruit Farm", nil)
				s.mockI18n.On("Translate", "tw", "ff_desc_key", mock.Anything).Return("Test", nil)
			},
			ExpRes: AddOn{
				EnableIconAnimation: true,
				GamesInfo: []GameInfo{
					{
						Provider:       "17Live",
						ID:             "fruitfarm",
						Icon:           "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png",
						Url:            "https://17live-game/fruitfarm/",
						WebViewBgColor: "#ffecba",
						Name:           "Fruit Farm",
						Desc:           "Test",
					},
				},
				Promotion: Promotion{
					Enable:     true,
					MsgI18nKey: "LD_CTA_KEY",
					Target:     "luxurydice",
				},
			},
		},
		{
			Desc: "enableIconAnimation: enabled",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "ios",
					Version: "3.126.0",
				},
			},
			Config: `
enableIconAnimation: true
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				EnableIconAnimation: true,
				GamesInfo:           []GameInfo{},
			},
		},
		{
			Desc: "enableIconAnimation: disabled",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "ios",
					Version: "3.126.0",
				},
			},
			Config: `
enableIconAnimation: false
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				EnableIconAnimation: false,
				GamesInfo:           []GameInfo{},
			},
		},

		{
			Desc: "games: Unsupported given language, use defaultlanguage(en) instead",
			Param: confModel.AddonParam{
				Lang: "UK",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "android",
					Version: "2.41.0",
				},
			},
			Config: `
enableIconAnimation: true
games:
  fruitfarm:
    provider: "17Live"
    enable: true
    gameID: "fruitfarm"
    icon: "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png"
    url: "https://17live-game/fruitfarm/"
    webViewBgColor: "#ffecba"
    nameI18nkey: "ff_name_key"
    descI18nkey: "ff_desc_key"
    versionControl:
      enable: false
      ios: "3.126.0"
      android: "2.41.0"
regionGameOrder:
  TW:
  - "fruitfarm"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }

				s.mockI18n.On("Translate", "en", "ff_name_key", mock.Anything).Return("Fruit Farm EN", nil)
				s.mockI18n.On("Translate", "en", "ff_desc_key", mock.Anything).Return("Test EN", nil)
			},
			ExpRes: AddOn{
				EnableIconAnimation: true,
				GamesInfo: []GameInfo{
					{
						Provider:       "17Live",
						ID:             "fruitfarm",
						Icon:           "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png",
						Url:            "https://17live-game/fruitfarm/",
						WebViewBgColor: "#ffecba",
						Name:           "Fruit Farm EN",
						Desc:           "Test EN",
					},
				},
			},
		},
		{
			Desc: "games: no any games enabled in the current IP region",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "android",
					Version: "2.41.0",
				},
			},
			Config: `
games:
  fruitfarm:
    provider: "17Live"
    enable: true
    gameID: "fruitfarm"
    icon: "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png"
    url: "https://17live-game/fruitfarm/"
    webViewBgColor: "#ffecba"
    nameI18nkey: "ff_name_key"
    descI18nkey: "ff_desc_key"
    versionControl:
      enable: false
      ios: "3.126.0"
      android: "2.41.0"
regionGameOrder:
  JP:
  - "fruitfarm"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }

				s.mockI18n.On("Translate", "tw", "ff_name_key", mock.Anything).Return("Fruit Farm", nil)
				s.mockI18n.On("Translate", "tw", "ff_desc_key", mock.Anything).Return("Test", nil)
			},
			ExpRes: AddOn{
				GamesInfo: []GameInfo{},
			},
		},
		{
			Desc: "games: meet minimum iOS version for fruitfarm",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "ios",
					Version: "3.126.0",
				},
			},
			Config: `
games:
  fruitfarm:
    provider: "17Live"
    enable: true
    gameID: "fruitfarm"
    icon: "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png"
    url: "https://17live-game/fruitfarm/"
    webViewBgColor: "#ffecba"
    nameI18nkey: "ff_name_key"
    descI18nkey: "ff_desc_key"
    versionControl:
      enable: true
      ios: "3.126.0"
      android: "2.41.0"
regionGameOrder:
  TW:
  - "fruitfarm"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				GamesInfo: []GameInfo{
					{
						Provider:       "17Live",
						ID:             "fruitfarm",
						Icon:           "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png",
						Url:            "https://17live-game/fruitfarm/",
						WebViewBgColor: "#ffecba",
						Name:           "Fruit Farm",
						Desc:           "Test",
					},
				},
			},
		},
		{
			Desc: "games: meet minimum Android version for fruitfarm",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "android",
					Version: "2.41.0",
				},
			},
			Config: `
games:
  fruitfarm:
    provider: "17Live"
    enable: true
    gameID: "fruitfarm"
    icon: "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png"
    url: "https://17live-game/fruitfarm/"
    webViewBgColor: "#ffecba"
    nameI18nkey: "ff_name_key"
    descI18nkey: "ff_desc_key"
    versionControl:
      enable: true
      ios: "3.126.0"
      android: "2.41.0"
regionGameOrder:
  TW:
  - "fruitfarm"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				GamesInfo: []GameInfo{
					{
						Provider:       "17Live",
						ID:             "fruitfarm",
						Icon:           "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png",
						Url:            "https://17live-game/fruitfarm/",
						WebViewBgColor: "#ffecba",
						Name:           "Fruit Farm",
						Desc:           "Test",
					},
				},
			},
		},
		{
			Desc: "games: not meet minimum iOS version for fruitfarm",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "ios",
					Version: "3.125.0",
				},
			},
			Config: `
games:
  fruitfarm:
    provider: "17Live"
    enable: true
    gameID: "fruitfarm"
    icon: "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png"
    url: "https://17live-game/fruitfarm/"
    webViewBgColor: "#ffecba"
    nameI18nkey: "ff_name_key"
    descI18nkey: "ff_desc_key"
    versionControl:
      enable: true
      ios: "3.126.0"
      android: "2.41.0"
regionGameOrder:
  TW:
  - "fruitfarm"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				GamesInfo: []GameInfo{},
			},
		},
		{
			Desc: "games: not meet minimum Android version for fruitfarm",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "android",
					Version: "2.35.0",
				},
			},
			Config: `
games:
  fruitfarm:
    provider: "17Live"
    enable: true
    gameID: "fruitfarm"
    icon: "http://cdn.17app.co/b509416d-c234-4f03-a4a7-f13c24939aef.png"
    url: "https://17live-game/fruitfarm/"
    webViewBgColor: "#ffecba"
    nameI18nkey: "ff_name_key"
    descI18nkey: "ff_desc_key"
    versionControl:
      enable: true
      ios: "3.126.0"
      android: "2.41.0"
regionGameOrder:
  TW:
  - "fruitfarm"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				GamesInfo: []GameInfo{},
			},
		},
		{
			Desc: "regionPromotion: unsupported IP region",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "US",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "ios",
					Version: "3.126.0",
				},
			},
			Config: `
regionPromotion:
  TW:
    enable: true
    enableTimeRange:
      startTime: "2022-06-02 17:00:00 (GMT+0800)"
      endTime: "2022-06-15 23:59:59 (GMT+0800)"
    msgI18nKey: "LD_CTA_KEY"
    actionTarget: "luxurydice"
    versionControl:
      enable: true
      ios: "3.126.0"
      android: "2.41.0"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				Promotion: Promotion{
					Enable:     false,
					MsgI18nKey: "",
					Target:     "",
				},
				GamesInfo: []GameInfo{},
			},
		},
		{
			Desc: "regionPromotion: promotion not enabled",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "ios",
					Version: "3.126.0",
				},
			},
			Config: `
regionPromotion:
  TW:
    enable: false
    enableTimeRange:
      startTime: "2022-06-02 17:00:00 (GMT+0800)"
      endTime: "2022-06-15 23:59:59 (GMT+0800)"
    msgI18nKey: "LD_CTA_KEY"
    actionTarget: "luxurydice"
    versionControl:
      enable: true
      ios: "3.126.0"
      android: "2.41.0"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				Promotion: Promotion{
					Enable:     false,
					MsgI18nKey: "LD_CTA_KEY",
					Target:     "luxurydice",
				},
				GamesInfo: []GameInfo{},
			},
		},
		{
			Desc: "regionPromotion: not in enableTimeRange",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "ios",
					Version: "3.126.0",
				},
			},
			Config: `
regionPromotion:
  TW:
    enable: false
    enableTimeRange:
      startTime: "2022-06-02 17:00:00 (GMT+0800)"
      endTime: "2022-06-15 23:59:59 (GMT+0800)"
    msgI18nKey: "LD_CTA_KEY"
    actionTarget: "luxurydice"
    versionControl:
      enable: true
      ios: "3.126.0"
      android: "2.41.0"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 5, 1, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				Promotion: Promotion{
					Enable:     false,
					MsgI18nKey: "LD_CTA_KEY",
					Target:     "luxurydice",
				},
				GamesInfo: []GameInfo{},
			},
		},

		{
			Desc: "regionPromotion: not meet minimum iOS version",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "ios",
					Version: "3.125.0",
				},
			},
			Config: `
regionPromotion:
  TW:
    enable: true
    enableTimeRange:
      startTime: "2022-06-02 17:00:00 (GMT+0800)"
      endTime: "2022-06-15 23:59:59 (GMT+0800)"
    msgI18nKey: "LD_CTA_KEY"
    actionTarget: "luxurydice"
    versionControl:
      enable: true
      ios: "3.126.0"
      android: "2.41.0"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				Promotion: Promotion{
					Enable:     false,
					MsgI18nKey: "LD_CTA_KEY",
					Target:     "luxurydice",
				},
				GamesInfo: []GameInfo{},
			},
		},
		{
			Desc: "regionPromotion: not meet minimum Android version",
			Param: confModel.AddonParam{
				Lang: "TW",
				RegionInfo: &regionModel.RegionInfo{
					Region:     "TW",
					RegionByIP: "TW",
				},
				DeviceInfo: &confModel.DeviceInfo{
					Type:    "android",
					Version: "2.35.0",
				},
			},
			Config: `
regionPromotion:
  TW:
    enable: true
    enableTimeRange:
      startTime: "2022-06-02 17:00:00 (GMT+0800)"
      endTime: "2022-06-15 23:59:59 (GMT+0800)"
    msgI18nKey: "LD_CTA_KEY"
    actionTarget: "luxurydice"
    versionControl:
      enable: true
      ios: "3.126.0"
      android: "2.41.0"
`,
			Setup: func() {
				loc, _ := time.LoadLocation("Asia/Taipei")
				timeNow = func() time.Time { return time.Date(2022, 6, 5, 0, 0, 0, 0, loc) }
			},
			ExpRes: AddOn{
				Promotion: Promotion{
					Enable:     false,
					MsgI18nKey: "LD_CTA_KEY",
					Target:     "luxurydice",
				},
				GamesInfo: []GameInfo{},
			},
		},
	}

	for _, test := range tests {
		s.Run(test.Desc, func() {
			if test.Setup != nil {
				test.Setup()
			}

			// load yaml
			data, _, err := commonCfg.Check([]byte(test.Config))
			s.Require().NoError(err)
			commonCfg.Apply(data)

			getRegionGroupConfig = func() *RegionGroupConfig {
				return &RegionGroupConfig{
					RegionGroup: map[string][]string{
						"TW": {"TW"},
						"JP": {"JP"},
						"US": {"US", "CA"},
					},
					DefaultRegionGroup: "TW",
					RegionToRegionGroup: map[string]string{
						"TW": "TW",
						"JP": "JP",
						"US": "US",
						"CA": "US",
					},
					AvailableRegionGroups: []string{"TW", "JP", "US"},
					AvailableRegionGroupMap: map[string]struct{}{
						"TW": {},
						"JP": {},
						"US": {},
					},
				}
			}

			addOn := extractAddOn(mockCTX, test.Param)
			s.Require().Equal(test.ExpRes, addOn)
		})
	}
}

func (s *configSuite) TestWhitelistCfg() {
	tests := []struct {
		Desc     string
		MockYAML string
		ExpCfg   GameCenterCfg
		ExpWarn  []string
		ExpErr   error
	}{
		{
			Desc: "normal case",
			MockYAML: `
canDisableGameCenterWhitelist:
    user_ids:
      - userID1
      - userID2
`,
			ExpCfg: GameCenterCfg{
				CanDisableGameCenterWhitelist: whitelist{
					UserIDs: []string{"userID1", "userID2"},
				},
				canDisableGameCenterWhitelistMap: map[string]bool{
					"userID1": true,
					"userID2": true,
				},
			},
		},
	}

	for _, t := range tests {
		cfg := GameCenterCfg{}
		res, warns, err := cfg.Check([]byte(t.MockYAML))
		s.Require().Equal(t.ExpErr, err)
		if err == nil {
			cfg.Apply(res)
			s.ElementsMatch(t.ExpWarn, warns)
			s.Equal(t.ExpCfg, cfg, t.Desc)
		}
	}
}
