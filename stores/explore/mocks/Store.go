// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import config "github.com/17media/api/models/config"
import ctx "github.com/17media/api/base/ctx"
import explore "github.com/17media/api/stores/explore"
import mock "github.com/stretchr/testify/mock"
import user "github.com/17media/api/models/user"

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// GetExploreDetails provides a mock function with given fields: context, viewerID, labelID, cursor, count, region, deviceInfo
func (_m *Store) GetExploreDetails(context ctx.CTX, viewerID string, labelID string, cursor string, count int, region string, deviceInfo config.DeviceInfo) ([]*explore.Detail, string, error) {
	ret := _m.Called(context, viewerID, labelID, cursor, count, region, deviceInfo)

	var r0 []*explore.Detail
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, int, string, config.DeviceInfo) []*explore.Detail); ok {
		r0 = rf(context, viewerID, labelID, cursor, count, region, deviceInfo)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*explore.Detail)
		}
	}

	var r1 string
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, string, int, string, config.DeviceInfo) string); ok {
		r1 = rf(context, viewerID, labelID, cursor, count, region, deviceInfo)
	} else {
		r1 = ret.Get(1).(string)
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, string, string, string, int, string, config.DeviceInfo) error); ok {
		r2 = rf(context, viewerID, labelID, cursor, count, region, deviceInfo)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GetExplores provides a mock function with given fields: context, labelID, cursor, count, region
func (_m *Store) GetExplores(context ctx.CTX, labelID string, cursor string, count int, region string) ([]*user.UserInfo, string, error) {
	ret := _m.Called(context, labelID, cursor, count, region)

	var r0 []*user.UserInfo
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, int, string) []*user.UserInfo); ok {
		r0 = rf(context, labelID, cursor, count, region)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*user.UserInfo)
		}
	}

	var r1 string
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, int, string) string); ok {
		r1 = rf(context, labelID, cursor, count, region)
	} else {
		r1 = ret.Get(1).(string)
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, string, string, int, string) error); ok {
		r2 = rf(context, labelID, cursor, count, region)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GetFeaturedExplores provides a mock function with given fields: context, viewerID, labelID, cursor, count, region, deviceInfo
func (_m *Store) GetFeaturedExplores(context ctx.CTX, viewerID string, labelID string, cursor string, count int, region string, deviceInfo config.DeviceInfo) ([]*explore.Featured, string, error) {
	ret := _m.Called(context, viewerID, labelID, cursor, count, region, deviceInfo)

	var r0 []*explore.Featured
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, int, string, config.DeviceInfo) []*explore.Featured); ok {
		r0 = rf(context, viewerID, labelID, cursor, count, region, deviceInfo)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*explore.Featured)
		}
	}

	var r1 string
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, string, int, string, config.DeviceInfo) string); ok {
		r1 = rf(context, viewerID, labelID, cursor, count, region, deviceInfo)
	} else {
		r1 = ret.Get(1).(string)
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, string, string, string, int, string, config.DeviceInfo) error); ok {
		r2 = rf(context, viewerID, labelID, cursor, count, region, deviceInfo)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}
