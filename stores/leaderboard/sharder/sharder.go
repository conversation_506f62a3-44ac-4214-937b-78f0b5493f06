package sharder

import (
	"time"

	"github.com/17media/api/base/ctx"
)

// Item is item in the leaderboard
type Item struct {
	ID    string
	Score float64
}

// Service provide realtime leaderboard without hotspot in redis
type Service interface {
	// Incr increase itemID in boardName by delta.
	Incr(context ctx.CTX, itemID, boardName string, delta float64) error

	// Set sets itemID in boardName by score.
	Set(context ctx.CTX, itemID, boardName string, score float64) error

	// Del remove boards
	Del(context ctx.CTX, boardName string) error

	// GetTopN returns top n ranks in boardName.
	GetTopN(context ctx.CTX, n int, boardName string) ([]*Item, error)

	// GetScore gets score of itemID in boardName
	GetScore(context ctx.CTX, itemID, boardName string) (float64, error)

	// Count get the number of elements associcated with the boardName
	Count(context ctx.CTX, boardName string) (int, error)

	// ShardNum returns shard number of this shard
	ShardNum() int

	// TopNumPerShard returns top numbers saved in this shard
	TopNumPerShard() int

	// SetTTL sets ttl of the boardName
	SetTTL(context ctx.CTX, itemID, boardName string, ttl time.Duration) error
}
