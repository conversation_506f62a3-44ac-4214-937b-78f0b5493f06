package invoice

import (
	"fmt"

	"github.com/jmoiron/sqlx"

	"github.com/17media/api/base/ctx"
	invoiceModel "github.com/17media/api/models/invoice"
)

var (
	// ErrNotFound is returned when you can't find something
	ErrNotFound = fmt.Errorf("invoice not found")

	// ErrUnsupportStatus is returned when you trying to update unsupport status
	ErrUnsupportStatus = fmt.Errorf("update unsupport status")
)

// Option is functional parameter to specify invoice option
type Option func(*opt) error

type opt struct {
	tx *sqlx.Tx
}

// WithTransaction specifies sql.Tx if need to execute in the same transaction
func WithTransaction(tx *sqlx.Tx) Option {
	return func(o *opt) error {
		o.tx = tx
		return nil
	}
}

func extractOptions(options ...Option) (*opt, error) {
	o := &opt{}
	for _, option := range options {
		if err := option(o); err != nil {
			return nil, err
		}
	}
	return o, nil
}

// ListCond defines conditions that can be used to list invoices
type ListCond struct {
	CreateFromTimeMs int64
	CreateToTimeMs   int64
}

// Store is invoice interface
type Store interface {
	// Create add new invoices
	Create(context ctx.CTX, inputs []invoiceModel.CreateInvoiceInput, executorID string, options ...Option) ([]*invoiceModel.Invoice, error)
	// Gets returns invoices by ids, return error if invoice not found
	Gets(context ctx.CTX, ids []int64, options ...Option) ([]*invoiceModel.Invoice, error)
	// UpdateStatus update invoice status and returns updated invoice
	UpdateStatus(context ctx.CTX, ids []int64, status invoiceModel.Status, executorID string, options ...Option) ([]invoiceModel.UpdateStatusResult, []*invoiceModel.Invoice, error)
	// List return invoices with give list condition
	List(context ctx.CTX, cursor string, count int, condition ListCond) ([]*invoiceModel.Invoice, string, error)
}
