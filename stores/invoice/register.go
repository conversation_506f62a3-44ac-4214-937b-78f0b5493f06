package invoice

import (
	"database/sql"

	"github.com/17media/dig"

	"github.com/17media/api/setup/dimanager"
	// trigger init
	_ "github.com/17media/api/setup/mysql"
	_ "github.com/17media/api/setup/mysql/mysqlgiftwriter"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of invoice object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		GiftWriter *sql.DB `name:"mysqlGiftWriter"`
	}

	fn := func(p params) Store {
		return New(
			p.GiftWriter,
		)
	}
	m.ProvideConstructor(fn, `invoice`)
}

// GetInvoice returns the invoice object
func GetInvoice(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"invoice"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
