package stubs

import (
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/testutil"
	model "github.com/17media/api/models/award"
)

// StubResult implements RewardDeliveryResult for testing purposes.
type StubResult struct {
	Type model.RewardType
}

func (r StubR<PERSON>ult) RewardType() model.RewardType {
	return r.Type
}

// ProviderAlwaysPass is a stub RewardProvider that always returns success for both Validate and Deliver.
type ProviderAlwaysPass struct{}

func (m *ProviderAlwaysPass) Validate(ctx ctx.CTX, setting model.RewardSetting) error {
	return nil
}
func (m *ProviderAlwaysPass) Deliver(ctx ctx.CTX, setting model.RewardSetting) (model.RewardDeliveryResult, error) {
	return StubResult{Type: setting.Type}, nil
}

// ProviderValidateFail is a stub RewardProvider that always fails validation.
type ProviderValidateFail struct{}

func (m *ProviderValidateFail) Validate(ctx ctx.CTX, setting model.RewardSetting) error {
	return testutil.MockError
}
func (m *ProviderValidateFail) Deliver(ctx ctx.CTX, setting model.RewardSetting) (model.RewardDeliveryResult, error) {
	return StubResult{Type: setting.Type}, nil
}

// ProviderDeliverFail is a stub RewardProvider that always fails on delivery.
type ProviderDeliverFail struct{}

func (m *ProviderDeliverFail) Validate(ctx ctx.CTX, setting model.RewardSetting) error {
	return nil
}
func (m *ProviderDeliverFail) Deliver(ctx ctx.CTX, setting model.RewardSetting) (model.RewardDeliveryResult, error) {
	return nil, testutil.MockError
}
