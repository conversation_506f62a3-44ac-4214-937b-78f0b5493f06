package accounting

import (
	"database/sql"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"sort"
	"strings"
	"testing"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"golang.org/x/text/currency"

	subTopicModel "github.com/17media/gomodel/models/subTopic"

	"github.com/17media/api/base/ctx"
	mdb "github.com/17media/api/base/db"
	bdocker "github.com/17media/api/base/docker"
	mtime "github.com/17media/api/base/time"
	"github.com/17media/api/models"
	accountingM "github.com/17media/api/models/accounting"
	bcrM "github.com/17media/api/models/accounting/billcontractrelation"
	bbfuM "github.com/17media/api/models/bbfu"
	remittanceM "github.com/17media/api/models/bbfu/remittance"
	chatM "github.com/17media/api/models/chat"
	confModel "github.com/17media/api/models/config"
	contractM "github.com/17media/api/models/contract"
	erpM "github.com/17media/api/models/erp"
	exchangeM "github.com/17media/api/models/exchange"
	featureModel "github.com/17media/api/models/feature"
	intraModel "github.com/17media/api/models/intra"
	ledgerM "github.com/17media/api/models/ledger"
	moneyM "github.com/17media/api/models/money"
	officialM "github.com/17media/api/models/official"
	payoutAccountM "github.com/17media/api/models/payoutaccount"
	queueModel "github.com/17media/api/models/queue"
	regionM "github.com/17media/api/models/region"
	userM "github.com/17media/api/models/user"
	"github.com/17media/api/service/cache"
	fcron "github.com/17media/api/service/cron/fake"
	mERP "github.com/17media/api/service/erp/mocks"
	kycConfig "github.com/17media/api/service/kyc/config"
	mkyc "github.com/17media/api/service/kyc/mocks"
	"github.com/17media/api/service/payoutaccount"
	mpayoutaccount "github.com/17media/api/service/payoutaccount/mocks"
	"github.com/17media/api/service/redis/rediscache"
	"github.com/17media/api/service/region"
	mRegion "github.com/17media/api/service/region/mocks"
	"github.com/17media/api/setup/dimanager" // "github.com/17media/api/setup/mysql"
	"github.com/17media/api/stores/accounting/approval"
	mApproval "github.com/17media/api/stores/accounting/approval/mocks"
	"github.com/17media/api/stores/accounting/bill"
	mBill "github.com/17media/api/stores/accounting/bill/mocks"
	bcr "github.com/17media/api/stores/accounting/billcontractrelation"
	mBillRel "github.com/17media/api/stores/accounting/billcontractrelation/mocks"
	"github.com/17media/api/stores/accounting/config"
	mLargePaymentAccount "github.com/17media/api/stores/accounting/largepaymentaccount/mocks"
	ml "github.com/17media/api/stores/accounting/manuallist"
	mml "github.com/17media/api/stores/accounting/manuallist/mocks"
	"github.com/17media/api/stores/accounting/payout"
	mPayout "github.com/17media/api/stores/accounting/payout/mocks"
	mRate "github.com/17media/api/stores/accounting/rate/app/mocks"
	pRate "github.com/17media/api/stores/accounting/rate/pay"
	mpRate "github.com/17media/api/stores/accounting/rate/pay/mocks"
	mSettleup "github.com/17media/api/stores/accounting/settleup/mocks"
	"github.com/17media/api/stores/accounting/testhelper"
	"github.com/17media/api/stores/bbfu/remittance"
	mRemittance "github.com/17media/api/stores/bbfu/remittance/mocks"
	mContract "github.com/17media/api/stores/contract/mocks"
	mExchangeRate "github.com/17media/api/stores/exchange/mocks"
	"github.com/17media/api/stores/intra/comment"
	mComment "github.com/17media/api/stores/intra/comment/mocks"
	"github.com/17media/api/stores/intra/file"
	mIntraFile "github.com/17media/api/stores/intra/file/mocks"
	"github.com/17media/api/stores/intra/user"
	currencyStore "github.com/17media/api/stores/money/currency"
	"github.com/17media/api/stores/money/currency/usd"
	mMoney "github.com/17media/api/stores/money/mocks"
	mStatistic "github.com/17media/api/stores/money/statistic/mocks"
	moneyTest "github.com/17media/api/stores/money/testing"
	mOfficialMsg "github.com/17media/api/stores/official/mocks"
	mOutbox "github.com/17media/api/stores/outbox/mocks"
	mPurchaseStats "github.com/17media/api/stores/purchase/statistics/mocks"
	us "github.com/17media/api/stores/user"
	mUser "github.com/17media/api/stores/user/mocks"
)

const (
	insertAccountingPayoutStmt = `INSERT INTO AccountingPayout (timeMillis,groupID,creatorID,balancerID,billID,agencyType,agencyAccount,agencyReceiptID,currency,rate,amountBill,feeOwner,fee,amountPaid,amountPayable,status,updateTimeMillis,thirdPartyStatus,errorMessage,deduction,payoutTimeMillis) VALUES (:timeMillis,:groupID,:creatorID,:balancerID,:billID,:agencyType,:agencyAccount,:agencyReceiptID,:currency,:rate,:amountBill,:feeOwner,:fee,:amountPaid,:amountPayable,:status,:updateTimeMillis,:thirdPartyStatus,:errorMessage,:deduction,:payoutTimeMillis)`
	insertRefundQueryStr       = "INSERT INTO AccountingRefund (billID,feeCurrency,feeAmount,amountInPayoutCurrency,tradeID,dealingID,fileKey,timeMillis) VALUES (?,?,?,?,?,?,?,?)"
)

var (
	mockCTX              = ctx.Background()
	mockExecutorID       = "tester"
	mockExecutorOpenID   = "testerOpenID"
	mockExecutorRegion   = "TW"
	mockApprovalFileKey  = "approval.csv"
	mockPayoutGroupID    = "groupID"
	mockPayoutBalancerID = "balancer"
	twloc, _             = time.LoadLocation("Asia/Taipei")
	t20171105            = time.Date(2017, time.November, 5, 0, 0, 0, 0, time.UTC)
	settle20171105       = time.Date(2017, time.October, 1, bill.SettlementHour, 0, 0, 0, twloc)
	t20171205            = time.Date(2017, time.December, 5, 0, 0, 0, 0, time.UTC)
	t********            = time.Date(2018, time.March, 1, 0, 0, 0, 0, time.UTC)
	t20190301            = time.Date(2019, time.March, 1, 0, 0, 0, 0, time.UTC)
	t20221012            = time.Date(2022, time.October, 12, 0, 0, 0, 0, time.UTC)
	mockPayoutTypeInfos  = contractM.PayoutTypeInfos{
		Infos: []*contractM.PayoutTypeInfo{
			{
				PayoutType:   int32(accountingM.PayoutType_PAYPAL),
				RegionGroups: []string{"TW"},
				RegionAvailableCurrencies: []*contractM.RegionAvailableCurrencies{
					{
						RegionGroup:     "TW",
						DefaultCurrency: currency.USD.String(),
						Currencies: []string{
							currency.USD.String(),
							currency.TWD.String(),
							currency.HKD.String(),
						},
					},
				},
			},
			{
				PayoutType:   int32(accountingM.PayoutType_BANK_TBB),
				RegionGroups: []string{"TW"},
				RegionAvailableCurrencies: []*contractM.RegionAvailableCurrencies{
					{
						RegionGroup:     "TW",
						DefaultCurrency: currency.TWD.String(),
						Currencies: []string{
							currency.TWD.String(),
						},
					},
				},
				PayeePayoutMap: int32(accountingM.PayeePayoutType_LOCAL_BANK),
			},
			{
				PayoutType:   int32(accountingM.PayoutType_OFFLINE),
				RegionGroups: []string{"TW", "JP"},
				RegionAvailableCurrencies: []*contractM.RegionAvailableCurrencies{
					{
						RegionGroup:     "TW",
						DefaultCurrency: currency.TWD.String(),
						Currencies: []string{
							currency.USD.String(),
						},
					},
					{
						RegionGroup:     "JP",
						DefaultCurrency: currency.USD.String(),
						Currencies: []string{
							currency.USD.String(),
							currency.JPY.String(),
						},
					},
				},
				PayeePayoutMap: int32(accountingM.PayeePayoutType_LOCAL_BANK),
			},
			{
				PayoutType:   int32(accountingM.PayoutType_BANK_SMBC),
				RegionGroups: []string{"JP"},
				RegionAvailableCurrencies: []*contractM.RegionAvailableCurrencies{
					{
						RegionGroup:     "JP",
						DefaultCurrency: currency.USD.String(),
						Currencies: []string{
							currency.USD.String(),
							currency.JPY.String(),
						},
					},
				},
			},
		},
	}
	mockPayoutRates = func() [][]*exchangeM.RateInfo {
		mr := [][]*exchangeM.RateInfo{}
		for i := 0; i < 13; i++ {
			mr = append(mr, []*exchangeM.RateInfo{
				{Region: "TW", FromCurrency: currency.USD, ToCurrency: currency.TWD, Rate: 32, EffectiveTime: time.Time{}, ExecUserID: mockExecutorID},
				{Region: "TW", FromCurrency: currency.USD, ToCurrency: currency.HKD, Rate: 8.5, EffectiveTime: time.Time{}, ExecUserID: mockExecutorID},
				{Region: "TW", FromCurrency: currency.USD, ToCurrency: currency.USD, Rate: 1.0, EffectiveTime: time.Time{}, ExecUserID: mockExecutorID},
			})
		}
		return mr
	}
	mockPayoutAccount = &payoutAccountM.PayoutAccount{
		BankName:     "test-bank-name",
		BankCode:     "test-bank-code",
		BranchName:   "test-bank-branch-name",
		BranchCode:   "test-bank-branch-code",
		PayeeName:    "test-payee-name",
		Account:      "test-account",
		PayeeID:      "test-payee-id-number",
		OwnerID:      "",
		OwnerType:    payoutAccountM.OwnerType_OWNER_TYPE_STREAMER,
		AccountType:  payoutAccountM.AccountType_LOCAL_ACCOUNT,
		FeeOwnerType: payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
	}
	mockPayoutAccountPTA = &payoutAccountM.PayoutAccount{
		BankName:     "test-bank-name",
		BankCode:     "test-bank-code",
		BranchName:   "test-bank-branch-name",
		BranchCode:   "test-bank-branch-code",
		PayeeName:    "test-payee-name",
		Account:      "test-account",
		PayeeID:      "test-payee-id-number",
		AccountType:  payoutAccountM.AccountType_LOCAL_ACCOUNT,
		FeeOwnerType: payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
		OwnerID:      "",
		OwnerType:    payoutAccountM.OwnerType_OWNER_TYPE_AGENCY,
	}
)

type accountingTestSuite struct {
	suite.Suite

	dbHost    string
	dbPort    string
	dbName    string
	db        *sql.DB
	dbx       *sqlx.DB
	redisPort string

	bill         bill.Bill
	intraComment comment.Comment

	mockPayout              *mPayout.Payout
	mockBill                *mBill.Bill
	mockApproval            *mApproval.Store
	mockSettleup            *mSettleup.SettleUp
	mockUser                *mUser.Store
	mockFuncs               *mockFuncs
	mockRegion              *mRegion.Service
	mockBank                *mMoney.Bank
	mockContract            *mContract.Store
	mockIntraFile           *mIntraFile.Store
	mockComment             *mComment.Comment
	mockRate                *mRate.AppCurrencyToLocalCurrencyRate
	mockPayoutRate          *mpRate.PayoutExchangeRate
	mockExchangeRate        *mExchangeRate.Rate
	mockStatistic           *mStatistic.Store
	mockOfficialMsg         *mOfficialMsg.Store
	mockLargePaymentAccount *mLargePaymentAccount.LargePaymentAccount
	mockPurchaseStats       *mPurchaseStats.Store
	mockERP                 *mERP.Service
	mockRemittance          *mRemittance.Store
	mockManualList          *mml.ManualList
	mockKYC                 *mkyc.Service
	mockBillRel             *mBillRel.Store
	mockOutbox              *mOutbox.Store
	mockPayoutAccount       *mpayoutaccount.Service

	im *impl

	manager *dimanager.Manager
}

func (s *accountingTestSuite) SetupSuite() {
	localhost, ports, err := bdocker.RunExtDockers([]string{"mysql8", "redis", "mongo"})
	s.Require().NoError(err)
	s.dbHost = localhost
	s.dbName = "media17gift"
	s.dbPort = ports[0]
	s.redisPort = ports[1]

	testhelper.CreateDb("media17gift", localhost, ports[0])

	s.manager = dimanager.DefaultManager
	s.manager.ProvideString("rds_gift_uri", "root:@tcp("+localhost+":"+s.dbPort+")/media17gift?charset=utf8mb4")
	s.manager.ProvideString("rds_gift_reader_uri", "root:@tcp("+localhost+":"+s.dbPort+")/media17gift?charset=utf8mb4")
	s.manager.ProvideString("rds_high_priority_uri", "root:@tcp("+localhost+":"+s.dbPort+")/media17gift?charset=utf8mb4")
	s.manager.ProvideString("redis_cache_uri", localhost+":"+ports[1])
	s.manager.ProvideString("redis_persist_uri", localhost+":"+ports[1])
	s.manager.ProvideString("redis_cache_money_uri", localhost+":"+ports[1])
	s.manager.ProvideString("mongo_uri", localhost+":"+ports[2])
	s.manager.ProvideString("mongo_db", "17media")
	flag.Set("pubsub_emulator_host", "pubsub_emulator_host")
	rediscache.ConnectRedisCluster(s.manager)

	db, err := sql.Open("mysql", "root:@tcp("+s.dbHost+":"+s.dbPort+")/"+s.dbName)
	if err != nil {
		panic(err)
	}
	s.db = db
	s.dbx = sqlx.NewDb(db, mdb.SQLDriver)

	testhelper.MigrateUp(s.dbName, s.dbHost, s.dbPort)
	moneyTest.CreateBankDbSchema(s.dbName, s.dbHost, s.dbPort)
}

func (s *accountingTestSuite) TearDownSuite() {
	s.NoError(bdocker.RemExtDockers())
}

func (s *accountingTestSuite) SetupTest() {
	s.manager.ClearMock()
	s.mockUser = mUser.RegisterMock(s.manager)
	s.mockRegion = mRegion.RegisterMock(s.manager)
	s.mockContract = mContract.RegisterMock(s.manager)
	s.mockIntraFile = mIntraFile.RegisterMock(s.manager)
	s.mockComment = mComment.RegisterMock(s.manager)
	s.mockRate = mRate.RegisterMock(s.manager)
	s.mockExchangeRate = mExchangeRate.RegisterRateMock(s.manager)
	s.mockStatistic = mStatistic.RegisterMock(s.manager)
	s.mockOfficialMsg = mOfficialMsg.RegisterMock(s.manager)
	s.mockPurchaseStats = mPurchaseStats.RegisterMock(s.manager)
	s.mockERP = mERP.RegisterMock(s.manager)
	s.mockRemittance = mRemittance.RegisterMock(s.manager)
	s.mockManualList = mml.RegisterMock(s.manager)
	fcron.RegisterFake(s.manager)
	s.mockKYC = mkyc.RegisterMock(s.manager)
	s.mockBillRel = mBillRel.RegisterMock(s.manager)
	s.mockPayoutAccount = mpayoutaccount.RegisterMock(s.manager)
	s.mockSettleup = mSettleup.RegisterMock(s.manager)
	s.manager.Compile()
	s.mockOutbox = mOutbox.RegisterMock(s.manager)

	s.mockLargePaymentAccount = &mLargePaymentAccount.LargePaymentAccount{}
	s.bill = bill.GetBill(s.manager)
	s.intraComment = comment.New(s.db)
	s.im = GetAccounting(s.manager).(*impl)
	s.im.largePaymentAccount = s.mockLargePaymentAccount

	TimeNow = time.Now
	s.mockPayout = &mPayout.Payout{}
	s.mockBill = &mBill.Bill{}
	s.mockApproval = &mApproval.Store{}
	s.mockFuncs = &mockFuncs{}
	s.mockBank = &mMoney.Bank{}
	s.mockPayoutRate = &mpRate.PayoutExchangeRate{}
	s.im.payoutRate = s.mockPayoutRate
	getTimeLocation = s.mockFuncs.getTimeLocation
	isPayoutAvailableForERP = s.mockFuncs.isPayoutAvailableForERP
	getFeatureStartTime = s.mockFuncs.getFeatureStartTime
	isFeatureOn = func(_ featureModel.Features, _ *regionM.RegionInfo, _ *confModel.DeviceInfo) bool {
		return true
	}
	pushBillChangedMessage = s.mockFuncs.pushBillChangedMessage
	getAccountType = s.mockFuncs.getAccountType
	parseCreateBillFileFunc = s.mockFuncs.parseCreateBillFile
	getBillLogsFunc = getBillLogs
	getConsumptionTaxRate = func() float64 {
		return 0.1
	}
	enableUseCreateTimeToQueryBill = func() bool {
		return true
	}
	skipWalletBillPayoutIAM = func() bool {
		return true
	}
	getBillTypeSettings = func() []config.BillTypeSetting {
		return []config.BillTypeSetting{
			{
				BillType: accountingM.BillType_BILL_REVENUE,
				AvailableAccountTypeRegionGroups: map[string][]payoutAccountM.AccountType{
					"TW":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"JP":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"US":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"MENA": {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"SG":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"MY":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"HK":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"PH":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"ID":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"TH":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"VN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"MM":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"IN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"CN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"NONE": {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
				},
			},
			{
				BillType: accountingM.BillType_BILL_REWARD,
				AvailableAccountTypeRegionGroups: map[string][]payoutAccountM.AccountType{
					"TW":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"JP":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"US":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"MENA": {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"SG":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"MY":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"HK":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"PH":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"ID":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"TH":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"VN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"MM":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"IN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"CN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"NONE": {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
				},
			},
			{
				BillType: accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
				AvailableAccountTypeRegionGroups: map[string][]payoutAccountM.AccountType{
					"TW":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"JP":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"US":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"MENA": {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"SG":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"MY":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"HK":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"PH":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"ID":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"TH":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"VN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"MM":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"IN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"CN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"NONE": {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
				},
			},
			{
				BillType: accountingM.BillType_BILL_COMBINE_PAY,
				AvailableAccountTypeRegionGroups: map[string][]payoutAccountM.AccountType{
					"TW":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"JP":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"US":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"MENA": {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"SG":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"MY":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"HK":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"PH":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"ID":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"TH":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"VN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"MM":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"IN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"CN":   {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					"NONE": {payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
				},
			},
			{
				BillType: accountingM.BillType_BILL_WALLET,
				AvailableAccountTypeRegionGroups: map[string][]payoutAccountM.AccountType{
					"TW": {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_COMPANY_ACCOUNT},
				},
			},
			{
				BillType: accountingM.BillType_BILL_WALLET_REVENUE,
				AvailableAccountTypeRegionGroups: map[string][]payoutAccountM.AccountType{
					"TW":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_COMPANY_ACCOUNT},
					"JP":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"US":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"MENA": {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"SG":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"MY":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"HK":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"PH":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"ID":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"TH":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"VN":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"MM":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"IN":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"CN":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"NONE": {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
				},
			},
			{
				BillType: accountingM.BillType_BILL_WALLET_REWARD,
				AvailableAccountTypeRegionGroups: map[string][]payoutAccountM.AccountType{
					"TW":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_COMPANY_ACCOUNT},
					"JP":   {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"US":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"MENA": {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"SG":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"MY":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"HK":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"PH":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"ID":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"TH":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"VN":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"MM":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"IN":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"CN":   {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
					"NONE": {payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
				},
			},
		}
	}

	s.mockRegionGetGroup()
	mRegion.MockGetGroupTimeLocation(s.mockRegion)
}

func (s *accountingTestSuite) TearDownTest() {
	testhelper.MigrateDown(s.dbName, s.dbHost, s.dbPort)
	moneyTest.TruncateBankDb(s.dbName, s.dbHost, s.dbPort)
	bdocker.ClearRedis(s.redisPort)

	s.im.lc.Clear()

	s.mockPayout.AssertExpectations(s.T())
	s.mockBill.AssertExpectations(s.T())
	s.mockApproval.AssertExpectations(s.T())
	s.mockUser.AssertExpectations(s.T())
	s.mockFuncs.AssertExpectations(s.T())
	s.mockRegion.AssertExpectations(s.T())
	s.mockBank.AssertExpectations(s.T())
	s.mockContract.AssertExpectations(s.T())
	s.mockIntraFile.AssertExpectations(s.T())
	s.mockComment.AssertExpectations(s.T())
	s.mockRate.AssertExpectations(s.T())
	s.mockExchangeRate.AssertExpectations(s.T())
	s.mockStatistic.AssertExpectations(s.T())
	s.mockOfficialMsg.AssertExpectations(s.T())
	s.mockLargePaymentAccount.AssertExpectations(s.T())
	s.mockPayoutRate.AssertExpectations(s.T())
	s.mockERP.AssertExpectations(s.T())
	s.mockManualList.AssertExpectations(s.T())
	s.mockBillRel.AssertExpectations(s.T())
	s.mockOutbox.AssertExpectations(s.T())
	s.mockPayoutAccount.AssertExpectations(s.T())

	cache.ClearPfx()
}

func (s *accountingTestSuite) mockRegionGetGroup() {
	getRegionGroup = func(_ ctx.CTX, region string) (group string, err error) {
		return kycConfig.DefaultRegionToGroupMap[region], nil
	}
	getRegionRepresentative = func(_ ctx.CTX, group string) (string, error) {
		return kycConfig.DefaultRegionRepresentative[group], nil
	}
	getGroupRegions = func(_ ctx.CTX, group string) (regions []string, err error) {
		return kycConfig.DefaultGroupRegions[group], nil
	}
}

func (s *accountingTestSuite) TestPayoutStatusBackwardCompatiblity() {
	s.Equal("UNKNOWN_PAYOUT_STATUS", accountingM.PayoutStatus_name[0])
	s.Equal("PAYOUT_IN_PROGRESS", accountingM.PayoutStatus_name[1])
	s.Equal("PAYOUT_COMPLETED", accountingM.PayoutStatus_name[2])
	s.Equal("PAYOUT_UNFINISHED", accountingM.PayoutStatus_name[3])
	s.Equal("PAYOUT_REFUNDED", accountingM.PayoutStatus_name[4])

	s.Equal(int32(0), accountingM.PayoutStatus_value["UNKNOWN_PAYOUT_STATUS"])
	s.Equal(int32(1), accountingM.PayoutStatus_value["PAYOUT_IN_PROGRESS"])
	s.Equal(int32(2), accountingM.PayoutStatus_value["PAYOUT_COMPLETED"])
	s.Equal(int32(3), accountingM.PayoutStatus_value["PAYOUT_UNFINISHED"])
	s.Equal(int32(4), accountingM.PayoutStatus_value["PAYOUT_REFUNDED"])
}

func (s *accountingTestSuite) TestMakeApprovalSuccess() {
	s.createTestingData(s.im, s.dbx)

	// revenue bills
	subject := "testMakeApproval"
	targetRegion := "TW"
	loc, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)
	billIDs := []string{"********-b11-orphan"}
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, targetRegion).Return(loc, nil).Twice()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockExecutorID).Return([]models.User{
		{
			UserID: mockExecutorID,
			OpenID: mockExecutorOpenID,
		},
	}, nil).Once()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "u11").Return([]models.User{
		{
			UserID: "u11",
			OpenID: "u11OpenID",
		},
	}, nil).Once()
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)
	s.mockIntraFile.On("Upload", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*file.File")).Return([]string{mockApprovalFileKey}, nil).Once()
	s.mockComment.On("GetComments", mock.AnythingOfType("ctx.CTX"), intraModel.CommentType_BILL_COMMENT_TYPE, mock.AnythingOfType("[]int32"), mock.AnythingOfType("string"), "", 100).Return(nil, "", nil)
	apl, err := s.im.MakeApproval(mockCTX, mockExecutorID, subject, targetRegion, billIDs)
	s.NoError(err)
	s.Equal(subject, apl.Subject)
	s.Equal(1, apl.BillsCount)
	s.Equal("USD", apl.LocalCurrency)
	s.Equal(float64(90000), apl.TotalAmount)
	s.Equal(float64(90000), apl.TotalAmountInLocalCurrency)
	s.Equal(targetRegion, apl.Region)
	s.Equal(accountingM.ApprovalTypeRevenue, apl.Type)
	s.Equal(accountingM.BillType_BILL_REVENUE, apl.BillType)

	aplRows, err := s.im.approval.GetByBillIDs(mockCTX, billIDs)
	s.NoError(err)
	aplRow := aplRows["********-b11-orphan"]
	s.Equal(subject, aplRow.Subject)
	s.Equal(accountingM.ApprovalTypeRevenue, aplRow.Type)

	// monthly income bill
	billIDs = []string{"********-monthly-bill"}
	targetRegion = "JP"
	loc, err = time.LoadLocation("Asia/Tokyo")
	s.NoError(err)
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, targetRegion).Return(loc, nil).Twice()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockExecutorID).Return([]models.User{
		{
			UserID: mockExecutorID,
			OpenID: mockExecutorOpenID,
		},
	}, nil).Once()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "u14").Return([]models.User{
		{
			UserID: "u14",
			OpenID: "u11OpenID",
		},
	}, nil).Once()
	s.mockIntraFile.On("Upload", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*file.File")).Return([]string{mockApprovalFileKey}, nil).Once()
	apl, err = s.im.MakeApproval(mockCTX, mockExecutorID, subject, targetRegion, billIDs)
	s.NoError(err)
	s.Equal(subject, apl.Subject)
	s.Equal(1, apl.BillsCount)
	s.Equal("JPY", apl.LocalCurrency)
	s.Equal(float64(0), apl.TotalAmount)
	s.Equal(float64(1000), apl.TotalAmountInLocalCurrency)
	s.Equal(targetRegion, apl.Region)
	s.Equal(accountingM.ApprovalTypeContractMonthlyIncome, apl.Type)
	s.Equal(accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME, apl.BillType)

	// MENA revenue bills
	subject = "testMakeApproval"
	targetRegion = "AE"
	loc, err = time.LoadLocation("Asia/Dubai")
	s.NoError(err)
	billIDs = []string{"********-AE-orphan", "********-DJ-orphan"}
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, targetRegion).Return(loc, nil).Twice()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockExecutorID).Return([]models.User{
		{
			UserID: mockExecutorID,
			OpenID: mockExecutorOpenID,
		},
	}, nil).Once()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "uAE", "uDJ").Return([]models.User{
		{
			UserID: "uAE",
			OpenID: "uAEOpenID",
		},
		{
			UserID: "uDJ",
			OpenID: "uDJOpenID",
		},
	}, nil).Once()
	s.mockIntraFile.On("Upload", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*file.File")).Return([]string{mockApprovalFileKey}, nil).Once()
	s.mockComment.On("GetComments", mock.AnythingOfType("ctx.CTX"), intraModel.CommentType_BILL_COMMENT_TYPE, mock.AnythingOfType("[]int32"), mock.AnythingOfType("string"), "", 100).Return(nil, "", nil)
	apl, err = s.im.MakeApproval(mockCTX, mockExecutorID, subject, targetRegion, billIDs)
	s.NoError(err)
	s.Equal(subject, apl.Subject)
	s.Equal(2, apl.BillsCount)
	s.Equal("AED", apl.LocalCurrency)
	s.Equal(float64(180000), apl.TotalAmount)
	s.Equal(float64(180000), apl.TotalAmountInLocalCurrency)
	s.Equal(targetRegion, apl.Region)
	s.Equal(accountingM.ApprovalTypeRevenue, apl.Type)
	s.Equal(accountingM.BillType_BILL_REVENUE, apl.BillType)
}

func (s *accountingTestSuite) TestMakeApprovalWithApprovalType() {
	cases := []*struct {
		desc         string
		mockBills    []*bill.DBRow
		subject      string
		region       string
		billIDs      []string
		approvalType accountingM.ApprovalType
		mockFunc     func()
		expResult    *accountingM.ResApprovalLog
		expErr       error
	}{
		{
			desc: "normal case",
			mockBills: []*bill.DBRow{
				{
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "billID1",
					Type:                  accountingM.BillType_BILL_REVENUE,
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					UserID:                "userID1",
					Region:                "TW",
					Amount:                usd.ToNanoUSD(1000),
					TradeID:               "tradeID1",
					DealingID:             "dealingID1",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            mockExecutorID,
					TimeMillis:            mtime.MilliSecond(t********),
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 30900,
				},
				{
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "billID2",
					Type:                  accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					UserID:                "userID1",
					Region:                "TW",
					Amount:                usd.ToNanoUSD(1000),
					TradeID:               "tradeID2",
					DealingID:             "dealingID2",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            mockExecutorID,
					TimeMillis:            mtime.MilliSecond(t********),
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 30900,
				},
			},
			subject:      "testMakeApproval",
			region:       "TW",
			billIDs:      []string{"billID1", "billID2"},
			approvalType: accountingM.ApprovalTypeAgency,
			mockFunc: func() {
				loc, err := time.LoadLocation("Asia/Tokyo")
				s.NoError(err)
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(loc, nil).Twice()
				s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockExecutorID).Return([]models.User{
					{
						UserID: mockExecutorID,
						OpenID: mockExecutorOpenID,
					},
				}, nil).Once()
				s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "userID1").Return([]models.User{
					{
						UserID: "userID1",
						OpenID: "openID1",
					},
				}, nil).Once()
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)
				s.mockComment.On("GetComments", mock.AnythingOfType("ctx.CTX"), intraModel.CommentType_BILL_COMMENT_TYPE, mock.AnythingOfType("[]int32"), mock.AnythingOfType("string"), "", 100).Return(nil, "", nil).Twice()
				s.mockIntraFile.On("Upload", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*file.File")).Return([]string{mockApprovalFileKey}, nil).Once()
			},
			expResult: &accountingM.ResApprovalLog{
				ApprovalID:                 "",
				RateMonth:                  "",
				Subject:                    "testMakeApproval",
				Status:                     accountingM.ApprovalStatus_APPROVAL_SEALED,
				Type:                       accountingM.ApprovalTypeAgency,
				BillType:                   accountingM.BillType_BILL_REVENUE,
				FileKey:                    mockApprovalFileKey,
				BillsCount:                 2,
				TotalAmount:                float64(2000),
				LocalCurrency:              "TWD",
				TotalAmountInLocalCurrency: float64(61800),
				Region:                     "TW",
				CreateUnixTime:             0,
				ApproveUnixTime:            0,
				RejectUnixTime:             0,
			},
			expErr: nil,
		},
	}
	for _, c := range cases {
		for _, b := range c.mockBills {
			_, err := s.dbx.NamedExec(bill.InsertBillQueryStr, b)
			s.NoError(err)
		}
		if c.mockFunc != nil {
			c.mockFunc()
		}

		apl, err := s.im.MakeApproval(mockCTX, mockExecutorID, c.subject, c.region, c.billIDs, accountingM.MakeApprovalWithType(c.approvalType))
		s.Equal(c.expErr, err, c.desc)
		if c.expErr == nil {
			c.expResult.ApprovalID = apl.ApprovalID
			c.expResult.CreateUnixTime = apl.CreateUnixTime
			s.Equal(c.expResult, apl, c.desc)
		}

	}
}

func (s *accountingTestSuite) TestMakeApprovalError() {
	s.createTestingData(s.im, s.dbx)

	subject := "testMakeApproval"
	region := "testRegion"
	billIDs := []string{}

	// empty billIDs
	apl, err := s.im.MakeApproval(mockCTX, mockExecutorID, subject, region, billIDs)
	s.EqualError(err, "not support empty approval")
	s.Nil(apl)

	// bills are not in the same regionGroup with the input region
	region = "TW"
	billIDs = []string{"********-b11-orphan", "********-b12-orphan"}
	apl, err = s.im.MakeApproval(mockCTX, mockExecutorID, subject, region, billIDs)
	s.EqualError(err, "A bill's regionGroup(HK) is not the same with the input regionGroup(TW)")
	s.Nil(apl)
}

func (s *accountingTestSuite) TestGetBillLogsEmpty() {
	// filter voided bill

	region := []string{"TW", "JP"}
	userID := "mockUser"
	stage := accountingM.Stage_BILL_MANAGE_STAGE
	payoutStatus := []accountingM.PayoutStatus{
		accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
		accountingM.PayoutStatus_PAYOUT_SUCCESS,
		accountingM.PayoutStatus_PAYOUT_FAIL,
		accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND,
	}
	filter := accountingM.GetBillLogsFilter{
		TimeType:     accountingM.TimeFilterType_BILL_CREATED_TIME,
		From:         time.Time{},
		To:           time.Now(),
		Stage:        stage,
		PayoutStatus: payoutStatus,
		UserIDs:      []string{userID},
		Region:       region,
	}
	logs, totalCount, cursor, err := s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(0, len(logs))
	s.Equal(0, totalCount)
	s.Equal("", cursor)

	// Find no bill
	s.createTestingData(s.im, s.dbx)
	from := time.Date(2017, time.November, 1, 0, 0, 0, 0, time.UTC)
	to := time.Date(2017, time.November, 30, 0, 0, 0, 0, time.UTC)
	region = []string{"TW", "JP"}
	stage = accountingM.Stage_BILL_MANAGE_STAGE
	payoutStatus = []accountingM.PayoutStatus{}
	filter = accountingM.GetBillLogsFilter{
		TimeType:     accountingM.TimeFilterType_BILL_CREATED_TIME,
		From:         from,
		To:           to,
		Stage:        stage,
		PayoutStatus: payoutStatus,
		Region:       region,
	}
	logs, totalCount, _, err = s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(0, len(logs))
	s.Equal(0, totalCount)
}

func (s *accountingTestSuite) TestGetBillLogsV2() {
	nowMS = func() int64 { return mtime.MilliSecond(time.Date(2020, 4, 1, 0, 0, 0, 0, time.UTC)) }
	mockSnapshot := mtime.MilliSecond(time.Date(2020, 4, 1, 0, 0, 0, 0, time.UTC))
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)
	cases := []*struct {
		desc      string
		filter    accountingM.GetBillLogsFilter
		offset    int
		limit     int
		snapshot  int64
		mockBills []*bill.DBRow
		mockFunc  func()
		// use TradeID to identify the bill we expected, although TradeID should be the same in the same bill
		expTradeIDs   []string
		expTotalCount int
		expSnapshot   int64
		expErr        error
	}{
		{
			desc: "only see bills before snapshot",
			filter: accountingM.GetBillLogsFilter{
				TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
				From:     time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				To:       time.Date(2020, 2, 28, 0, 0, 0, 0, time.UTC),
				Region:   []string{"TW"},
			},
			offset:   0,
			limit:    10,
			snapshot: mockSnapshot,
			mockBills: []*bill.DBRow{
				{
					ID:                    1,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-1",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user1",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-1",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                    2,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-1",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user1",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-2",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 2, 2, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                    3,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-1",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user1",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-3",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 5, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                    4,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-2",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user2",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-4",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					// user can see the bill that is out of from/to time range, but updated before snapshot
					ID:                    5,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-2",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user2",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-5",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 3, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			mockFunc: func() {
				s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{
					"bill-1": {
						&pRate.PayoutRate{FromCurrency: currency.USD, ToCurrency: currency.TWD, Rate: 30},
					},
				}, nil).Once()
				s.mockUser.On("GetPlainUsers", mockCTX, "user1", "user2").Return([]models.User{
					{
						UserID:            "user1",
						PaypalVerifyState: 2,
					},
					{
						UserID:            "user2",
						PaypalVerifyState: 2,
					},
				}, nil).Once()
			},
			expTradeIDs:   []string{"trade-2", "trade-5"},
			expTotalCount: 2,
			expSnapshot:   mockSnapshot,
			expErr:        nil,
		},
		{
			desc: "offset and limit",
			filter: accountingM.GetBillLogsFilter{
				TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
				From:     time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				To:       time.Date(2020, 2, 28, 0, 0, 0, 0, time.UTC),
				Region:   []string{"TW"},
			},
			offset:   2,
			limit:    2,
			snapshot: 0,
			mockBills: []*bill.DBRow{
				{
					ID:                    1,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-1",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user1",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-1",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                    2,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-2",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user1",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-2",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 2, 2, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 2, 2, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                    3,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-3",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user1",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-3",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 2, 3, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 2, 3, 0, 0, 0, 0, time.UTC)),
				},
			},
			mockFunc: func() {
				s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{
					"bill-1": {
						&pRate.PayoutRate{FromCurrency: currency.USD, ToCurrency: currency.TWD, Rate: 30},
					},
				}, nil).Once()
				s.mockUser.On("GetPlainUsers", mockCTX, "user1").Return([]models.User{
					{
						UserID:            "user1",
						PaypalVerifyState: 2,
					},
				}, nil).Once()
			},
			expTradeIDs:   []string{"trade-1"},
			expTotalCount: 3,
			expSnapshot:   mockSnapshot,
			expErr:        nil,
		},
		{
			desc: "empty bill list",
			filter: accountingM.GetBillLogsFilter{
				TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
				From:     time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				To:       time.Date(2020, 2, 28, 0, 0, 0, 0, time.UTC),
				Region:   []string{"TW"},
			},
			offset:   0,
			limit:    10,
			snapshot: mockSnapshot,
			mockBills: []*bill.DBRow{
				{
					ID:                    1,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-1",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user1",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-1",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 3, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 3, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			mockFunc:      func() {},
			expTradeIDs:   []string{},
			expTotalCount: 0,
			expSnapshot:   mockSnapshot,
			expErr:        nil,
		},
		{
			desc: "empty bill list - 2",
			filter: accountingM.GetBillLogsFilter{
				TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
				From:     time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC),
				To:       time.Date(2022, 2, 28, 0, 0, 0, 0, time.UTC),
				Region:   []string{"TW"},
				BillIDs:  []string{},
			},
			offset:   0,
			limit:    10,
			snapshot: 0,
			mockBills: []*bill.DBRow{
				{
					ID:                    1,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-1",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user1",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-1",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 3, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 3, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			mockFunc:      func() {},
			expTradeIDs:   []string{},
			expTotalCount: 0,
			expSnapshot:   mockSnapshot,
			expErr:        nil,
		},
		{
			desc: "filter and sort bills by totalAmountInLocalCurrency",
			filter: accountingM.GetBillLogsFilter{
				TimeType:                         accountingM.TimeFilterType_BILL_CREATED_TIME,
				From:                             time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				To:                               time.Date(2020, 2, 28, 0, 0, 0, 0, time.UTC),
				Region:                           []string{"TW"},
				SortByTotalAmountInLocalCurrency: func() *accountingM.SortingOrder { order := accountingM.SortingOrderDESC; return &order }(),
				TotalAmountInLocalCurrency: &accountingM.AmountFilter{
					From: float64(2000),
					To:   float64(4000),
				},
			},
			offset:   0,
			limit:    10,
			snapshot: mockSnapshot,
			mockBills: []*bill.DBRow{
				{
					ID:                         1,
					PrevID:                     sql.NullInt64{Valid: false},
					IsNew:                      1,
					BillID:                     "bill-1",
					Status:                     accountingM.BillStatus_BILL_ORPHAN,
					Type:                       accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                     "user1",
					Region:                     "TW",
					LocalCurrency:              "TWD",
					AmountInLocalCurrency:      1000,
					TotalAmountInLocalCurrency: 1000,
					TradeID:                    "trade-1",
					PayoutType:                 accountingM.PayoutType_BANK_TBB,
					PayoutAccount:              "<EMAIL>",
					PayoutAccountVerified:      true,
					ExecutorID:                 "tester",
					TimeMillis:                 mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:           mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                         2,
					PrevID:                     sql.NullInt64{Valid: false},
					IsNew:                      1,
					BillID:                     "bill-2",
					Status:                     accountingM.BillStatus_BILL_ORPHAN,
					Type:                       accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                     "user1",
					Region:                     "TW",
					LocalCurrency:              "TWD",
					AmountInLocalCurrency:      4000,
					TotalAmountInLocalCurrency: 4000,
					TradeID:                    "trade-2",
					PayoutType:                 accountingM.PayoutType_BANK_TBB,
					PayoutAccount:              "<EMAIL>",
					PayoutAccountVerified:      true,
					ExecutorID:                 "tester",
					TimeMillis:                 mtime.MilliSecond(time.Date(2020, 2, 2, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:           mtime.MilliSecond(time.Date(2020, 2, 2, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                         3,
					PrevID:                     sql.NullInt64{Valid: false},
					IsNew:                      1,
					BillID:                     "bill-3",
					Status:                     accountingM.BillStatus_BILL_ORPHAN,
					Type:                       accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                     "user1",
					Region:                     "TW",
					LocalCurrency:              "TWD",
					AmountInLocalCurrency:      2000,
					TotalAmountInLocalCurrency: 2000,
					TradeID:                    "trade-3",
					PayoutType:                 accountingM.PayoutType_BANK_TBB,
					PayoutAccount:              "<EMAIL>",
					PayoutAccountVerified:      true,
					ExecutorID:                 "tester",
					TimeMillis:                 mtime.MilliSecond(time.Date(2020, 2, 3, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:           mtime.MilliSecond(time.Date(2020, 2, 3, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                         4,
					PrevID:                     sql.NullInt64{Valid: false},
					IsNew:                      1,
					BillID:                     "bill-4",
					Status:                     accountingM.BillStatus_BILL_ORPHAN,
					Type:                       accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                     "user1",
					Region:                     "TW",
					LocalCurrency:              "TWD",
					AmountInLocalCurrency:      5000,
					TotalAmountInLocalCurrency: 5000,
					TradeID:                    "trade-4",
					PayoutType:                 accountingM.PayoutType_BANK_TBB,
					PayoutAccount:              "<EMAIL>",
					PayoutAccountVerified:      true,
					ExecutorID:                 "tester",
					TimeMillis:                 mtime.MilliSecond(time.Date(2020, 2, 4, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:           mtime.MilliSecond(time.Date(2020, 2, 4, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                         5,
					PrevID:                     sql.NullInt64{Valid: false},
					IsNew:                      1,
					BillID:                     "bill-5",
					Status:                     accountingM.BillStatus_BILL_ORPHAN,
					Type:                       accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                     "user1",
					Region:                     "TW",
					LocalCurrency:              "TWD",
					AmountInLocalCurrency:      3000,
					TotalAmountInLocalCurrency: 3000,
					TradeID:                    "trade-5",
					PayoutType:                 accountingM.PayoutType_BANK_TBB,
					PayoutAccount:              "<EMAIL>",
					PayoutAccountVerified:      true,
					ExecutorID:                 "tester",
					TimeMillis:                 mtime.MilliSecond(time.Date(2020, 2, 5, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:           mtime.MilliSecond(time.Date(2020, 2, 5, 0, 0, 0, 0, time.UTC)),
				},
			},
			mockFunc: func() {
				s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{
					"bill-1": {
						&pRate.PayoutRate{FromCurrency: currency.USD, ToCurrency: currency.TWD, Rate: 30},
					},
				}, nil).Once()
				s.mockUser.On("GetPlainUsers", mockCTX, "user1").Return([]models.User{
					{
						UserID:            "user1",
						PaypalVerifyState: 2,
					},
				}, nil).Once()
			},
			expTradeIDs:   []string{"trade-2", "trade-5", "trade-3"},
			expTotalCount: 3,
			expSnapshot:   mockSnapshot,
			expErr:        nil,
		},
		{
			desc: "filter and sort bills by totalAmountInLocalCurrency.From only",
			filter: accountingM.GetBillLogsFilter{
				TimeType:                         accountingM.TimeFilterType_BILL_CREATED_TIME,
				From:                             time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				To:                               time.Date(2020, 2, 28, 0, 0, 0, 0, time.UTC),
				Region:                           []string{"TW"},
				SortByTotalAmountInLocalCurrency: func() *accountingM.SortingOrder { order := accountingM.SortingOrderDESC; return &order }(),
				TotalAmountInLocalCurrency: &accountingM.AmountFilter{
					From: float64(2000),
				},
			},
			offset:   0,
			limit:    10,
			snapshot: mockSnapshot,
			mockBills: []*bill.DBRow{
				{
					ID:                         1,
					PrevID:                     sql.NullInt64{Valid: false},
					IsNew:                      1,
					BillID:                     "bill-1",
					Status:                     accountingM.BillStatus_BILL_ORPHAN,
					Type:                       accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                     "user1",
					Region:                     "TW",
					LocalCurrency:              "TWD",
					AmountInLocalCurrency:      1000,
					TotalAmountInLocalCurrency: 1000,
					TradeID:                    "trade-1",
					PayoutType:                 accountingM.PayoutType_BANK_TBB,
					PayoutAccount:              "<EMAIL>",
					PayoutAccountVerified:      true,
					ExecutorID:                 "tester",
					TimeMillis:                 mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:           mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                         2,
					PrevID:                     sql.NullInt64{Valid: false},
					IsNew:                      1,
					BillID:                     "bill-2",
					Status:                     accountingM.BillStatus_BILL_ORPHAN,
					Type:                       accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                     "user1",
					Region:                     "TW",
					LocalCurrency:              "TWD",
					AmountInLocalCurrency:      4000,
					TotalAmountInLocalCurrency: 4000,
					TradeID:                    "trade-2",
					PayoutType:                 accountingM.PayoutType_BANK_TBB,
					PayoutAccount:              "<EMAIL>",
					PayoutAccountVerified:      true,
					ExecutorID:                 "tester",
					TimeMillis:                 mtime.MilliSecond(time.Date(2020, 2, 2, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:           mtime.MilliSecond(time.Date(2020, 2, 2, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                         3,
					PrevID:                     sql.NullInt64{Valid: false},
					IsNew:                      1,
					BillID:                     "bill-3",
					Status:                     accountingM.BillStatus_BILL_ORPHAN,
					Type:                       accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                     "user1",
					Region:                     "TW",
					LocalCurrency:              "TWD",
					AmountInLocalCurrency:      2000,
					TotalAmountInLocalCurrency: 2000,
					TradeID:                    "trade-3",
					PayoutType:                 accountingM.PayoutType_BANK_TBB,
					PayoutAccount:              "<EMAIL>",
					PayoutAccountVerified:      true,
					ExecutorID:                 "tester",
					TimeMillis:                 mtime.MilliSecond(time.Date(2020, 2, 3, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:           mtime.MilliSecond(time.Date(2020, 2, 3, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                         4,
					PrevID:                     sql.NullInt64{Valid: false},
					IsNew:                      1,
					BillID:                     "bill-4",
					Status:                     accountingM.BillStatus_BILL_ORPHAN,
					Type:                       accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                     "user1",
					Region:                     "TW",
					LocalCurrency:              "TWD",
					AmountInLocalCurrency:      5000,
					TotalAmountInLocalCurrency: 5000,
					TradeID:                    "trade-4",
					PayoutType:                 accountingM.PayoutType_BANK_TBB,
					PayoutAccount:              "<EMAIL>",
					PayoutAccountVerified:      true,
					ExecutorID:                 "tester",
					TimeMillis:                 mtime.MilliSecond(time.Date(2020, 2, 4, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:           mtime.MilliSecond(time.Date(2020, 2, 4, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                         5,
					PrevID:                     sql.NullInt64{Valid: false},
					IsNew:                      1,
					BillID:                     "bill-5",
					Status:                     accountingM.BillStatus_BILL_ORPHAN,
					Type:                       accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                     "user1",
					Region:                     "TW",
					LocalCurrency:              "TWD",
					AmountInLocalCurrency:      3000,
					TotalAmountInLocalCurrency: 3000,
					TradeID:                    "trade-5",
					PayoutType:                 accountingM.PayoutType_BANK_TBB,
					PayoutAccount:              "<EMAIL>",
					PayoutAccountVerified:      true,
					ExecutorID:                 "tester",
					TimeMillis:                 mtime.MilliSecond(time.Date(2020, 2, 5, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:           mtime.MilliSecond(time.Date(2020, 2, 5, 0, 0, 0, 0, time.UTC)),
				},
			},
			mockFunc: func() {
				s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{
					"bill-1": {
						&pRate.PayoutRate{FromCurrency: currency.USD, ToCurrency: currency.TWD, Rate: 30},
					},
				}, nil).Once()
				s.mockUser.On("GetPlainUsers", mockCTX, "user1").Return([]models.User{
					{
						UserID:            "user1",
						PaypalVerifyState: 2,
					},
				}, nil).Once()
			},
			expTradeIDs:   []string{"trade-4", "trade-2", "trade-5", "trade-3"},
			expTotalCount: 4,
			expSnapshot:   mockSnapshot,
			expErr:        nil,
		},
	}
	for _, c := range cases {
		testhelper.MigrateDown(s.dbName, s.dbHost, s.dbPort)

		for _, b := range c.mockBills {
			res, err := s.dbx.NamedExec(bill.InsertBillQueryStr, b)
			s.NoError(err)
			b.ID, err = res.LastInsertId()
			s.NoError(err)
		}
		if c.mockFunc != nil {
			c.mockFunc()
		}

		logs, totalCount, snapshot, err := s.im.GetBillLogsV2(mockCTX, c.filter, c.offset, c.limit, c.snapshot, GetBillLogsNeedCount())
		s.Equal(c.expErr, err, c.desc)
		s.Equal(c.expTotalCount, totalCount, c.desc)
		s.Equal(c.expSnapshot, snapshot, c.desc)
		s.Equal(len(c.expTradeIDs), len(logs), c.desc)
		for i, log := range logs {
			s.Equal(c.expTradeIDs[i], log.TradeID, c.desc)
		}
	}
}

func (s *accountingTestSuite) TestGetBillIDs() {
	nowMS = func() int64 { return mtime.MilliSecond(time.Date(2020, 4, 1, 0, 0, 0, 0, time.UTC)) }
	mockSnapshot := mtime.MilliSecond(time.Date(2020, 4, 1, 0, 0, 0, 0, time.UTC))
	cases := []*struct {
		desc       string
		filter     accountingM.GetBillLogsFilter
		snapshot   int64
		mockBills  []*bill.DBRow
		mockFunc   func()
		expBillIDs []string
		expErr     error
	}{
		{
			desc: "normal",
			filter: accountingM.GetBillLogsFilter{
				TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
				From:     time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC),
				To:       time.Date(2020, 6, 1, 0, 0, 0, 0, time.UTC),
				Region:   []string{"TW"},
			},
			snapshot: mockSnapshot,
			mockBills: []*bill.DBRow{
				{
					ID:                    1,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-1",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user1",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-1",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 5, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 5, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                    2,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-2",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user2",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-2",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 3, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 3, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			mockFunc:   func() {},
			expBillIDs: []string{"bill-2"},
			expErr:     nil,
		},
		{
			desc: "empty bill",
			filter: accountingM.GetBillLogsFilter{
				TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
				From:     time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC),
				To:       time.Date(2020, 6, 1, 0, 0, 0, 0, time.UTC),
				BillIDs:  []string{},
			},
			snapshot: mockSnapshot,
			mockBills: []*bill.DBRow{
				{
					ID:                    1,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-1",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user1",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-1",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 5, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 5, 1, 0, 0, 0, 0, time.UTC)),
				},
				{
					ID:                    2,
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "bill-2",
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					Type:                  accountingM.BillType_BILL_WALLET_TW_REVENUE,
					UserID:                "user2",
					Region:                "TW",
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 2000,
					TradeID:               "trade-2",
					PayoutType:            accountingM.PayoutType_BANK_TBB,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					ExecutorID:            "tester",
					TimeMillis:            mtime.MilliSecond(time.Date(2020, 3, 1, 0, 0, 0, 0, time.UTC)),
					CreateTimeMillis:      mtime.MilliSecond(time.Date(2020, 3, 1, 0, 0, 0, 0, time.UTC)),
				},
			},
			mockFunc:   func() {},
			expBillIDs: []string{},
			expErr:     nil,
		},
	}

	for _, c := range cases {
		testhelper.MigrateDown(s.dbName, s.dbHost, s.dbPort)

		for _, b := range c.mockBills {
			res, err := s.dbx.NamedExec(bill.InsertBillQueryStr, b)
			s.NoError(err)
			b.ID, err = res.LastInsertId()
			s.NoError(err)
		}
		if c.mockFunc != nil {
			c.mockFunc()
		}
		billIDs, err := s.im.GetBillIDs(mockCTX, c.filter, c.snapshot)
		s.Equal(c.expErr, err, c.desc)
		s.Equal(c.expBillIDs, billIDs, c.desc)
	}
}

func (s *accountingTestSuite) TestGetBillByStage() {
	s.createTestingData(s.im, s.dbx)
	s.mockPayout.On("GetTypeInfos").Return(mockPayoutTypeInfos)
	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{
		"201712-b4-orphan": {
			&pRate.PayoutRate{FromCurrency: currency.USD, ToCurrency: currency.TWD, Rate: 30},
		},
	}, nil)

	s.im.payout = s.mockPayout

	// find one bill in bill manage status
	filter := accountingM.GetBillLogsFilter{
		TimeType:     accountingM.TimeFilterType_BILL_CREATED_TIME,
		From:         time.Date(2017, time.December, 1, 0, 0, 0, 0, time.UTC),
		To:           time.Date(2017, time.December, 31, 0, 0, 0, 0, time.UTC),
		Stage:        accountingM.Stage_BILL_MANAGE_STAGE,
		PayoutStatus: []accountingM.PayoutStatus{},
		Region:       []string{},
	}
	s.mockUser.On("GetPlainUsers", mockCTX, "u4").Return([]models.User{
		{
			UserID:            "u4",
			PaypalVerifyState: 2,
		},
	}, nil).Once()
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	logs, totalCount, cursor, err := s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", cursor)
	s.Equal("201712-b4-orphan", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_ORPHAN, logs[0].Status)
	s.Equal(accountingM.PayoutType_BANK_TBB, logs[0].PayoutType)
	s.Equal("TW", logs[0].Region)
	s.Equal(float64(40000), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[0].PayoutStatus)
	s.Equal(float64(0), logs[0].PayoutFee)
	s.Equal(float64(0), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t20171205.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal("USD", logs[0].LocalCurrency)
	s.Equal(float64(40000), logs[0].AmountInLocalCurrency)
	s.Equal("", logs[0].PayID)
	s.Equal("", logs[0].BalancerID)
	s.Equal("", logs[0].ApprovalID)
	s.Equal("TWD", logs[0].PayoutCurrency)
	s.Equal(float64(30), logs[0].PayoutRate)

	filter = accountingM.GetBillLogsFilter{
		TimeType:     accountingM.TimeFilterType_BILL_CREATED_TIME,
		From:         time.Date(2018, time.March, 1, 0, 0, 0, 0, time.UTC),
		To:           time.Date(2018, time.April, 31, 0, 0, 0, 0, time.UTC),
		Stage:        accountingM.Stage_BILL_MANAGE_STAGE,
		PayoutStatus: []accountingM.PayoutStatus{},
		Region:       []string{},
		Type:         []accountingM.BillType{accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME},
	}
	s.mockUser.On("GetPlainUsers", mockCTX, "u14").Return([]models.User{
		{
			UserID:            "u14",
			PaypalVerifyState: 2,
		},
	}, nil).Once()

	logs, totalCount, cursor, err = s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", cursor)
	s.Equal("********-monthly-bill", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_ORPHAN, logs[0].Status)
	s.Equal("JP", logs[0].Region)
	s.Equal(float64(0), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[0].PayoutStatus)
	s.Equal(float64(0), logs[0].PayoutFee)
	s.Equal(float64(0), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t********.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal("JPY", logs[0].LocalCurrency)
	s.Equal(float64(1000), logs[0].AmountInLocalCurrency)
	s.Equal("", logs[0].PayID)
	s.Equal("", logs[0].BalancerID)
	s.Equal("", logs[0].ApprovalID)

	// find bills in payout status and in progress
	from := time.Date(2017, time.November, 1, 0, 0, 0, 0, time.UTC)
	to := time.Date(2017, time.December, 30, 0, 0, 0, 0, time.UTC)
	region := []string{}
	stage := accountingM.Stage_PAYOUT_STAGE
	payoutStatus := []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_IN_PROGRESS}
	filter = accountingM.GetBillLogsFilter{
		TimeType:     accountingM.TimeFilterType_BILL_CREATED_TIME,
		From:         from,
		To:           to,
		Stage:        stage,
		PayoutStatus: payoutStatus,
		Region:       region,
	}
	s.mockUser.On("GetPlainUsers", mockCTX, "u1", "u2").Return([]models.User{
		{
			UserID:            "u1",
			PaypalVerifyState: 2,
		},
		{
			UserID:            "u2",
			PaypalVerifyState: 2,
		},
	}, nil).Once()
	s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), []string{"201712-b7-in-a3", "201711-b1-in-a1"}).Return([]*payout.DBRow{
		{BillID: "201712-b7-in-a3", DeductionDetail: map[accountingM.DeductionCategory]float64{
			accountingM.DeductionCategory_CONSUMPTION_TAX:  10,
			accountingM.DeductionCategory_HEALTH_INSURANCE: 20,
		}},
		{BillID: "201711-b1-in-a1"},
	}, nil).Once()

	logs, totalCount, cursor, err = s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(2, len(logs))
	s.Equal(2, totalCount)
	s.Equal("", cursor)

	s.Equal("201712-b7-in-a3", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_PAYOUT, logs[0].Status)
	s.Equal("TW", logs[0].Region)
	s.Equal(float64(70000), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_PAYOUT_IN_PROGRESS, logs[0].PayoutStatus)
	s.Equal(float64(0), logs[0].PayoutFee)
	s.Equal(float64(5000), logs[0].PaidAmount)
	s.Equal(float64(64990), logs[0].UnpaidAmount)
	s.Equal(float64(10), logs[0].Deduction)
	s.Equal(t20171205.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal("USD", logs[0].LocalCurrency)
	s.Equal(float64(70000), logs[0].AmountInLocalCurrency)
	s.Equal(mockPayoutGroupID, logs[0].PayID)
	s.Equal("", logs[0].BalancerID)
	s.Equal("a3_201712_approved_approval", logs[0].ApprovalID)
	s.Equal("UNCLAIMED", logs[0].TransactionStatus)
	s.Equal("unclaimed error message", logs[0].PayoutErrorMessage)
	s.Nil(logs[0].Contract)
	s.Equal(float64(0), logs[0].IncomeTax)
	s.Equal(float64(10), logs[0].ConsumptionTax)
	s.Equal(float64(20), logs[0].HealthInsuranceAmount)

	s.Equal("201711-b1-in-a1", logs[1].BillID)
	s.Equal(accountingM.BillStatus_BILL_PAYOUT, logs[1].Status)
	s.Equal("TW", logs[1].Region)
	s.Equal(float64(10000), logs[1].Amount)
	s.Equal(accountingM.PayoutStatus_PAYOUT_IN_PROGRESS, logs[1].PayoutStatus)
	s.Equal(float64(0), logs[1].PayoutFee)
	s.Equal(float64(0), logs[1].PaidAmount)
	s.Equal(float64(9990), logs[1].UnpaidAmount)
	s.Equal(float64(10), logs[1].Deduction)
	s.Equal(t20171105.Unix(), logs[1].CreateUnixTime)
	s.Equal(true, logs[1].PayoutAccountVerified)
	s.Equal("USD", logs[1].LocalCurrency)
	s.Equal(float64(10000), logs[1].AmountInLocalCurrency)
	s.Equal(mockPayoutGroupID, logs[1].PayID)
	s.Equal("", logs[1].BalancerID)
	s.Equal("a1_2017_approvaed_approval", logs[1].ApprovalID)
	s.Equal("", logs[1].TransactionStatus)
	s.Equal("", logs[1].PayoutErrorMessage)
	s.Nil(logs[1].Contract)

	// find one bill in payout status and in refunded or completed
	s.mockUser.On("GetPlainUsers", mockCTX, "u2", "u3").Return([]models.User{
		{
			UserID:            "u2",
			PaypalVerifyState: 2,
		},
		{
			UserID:            "u3",
			PaypalVerifyState: 2,
		},
	}, nil).Once()
	s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), []string{"201711-b2-in-a1", "201711-b3-in-a1"}).Return([]*payout.DBRow{
		{BillID: "201711-b2-in-a1"},
		{BillID: "201711-b3-in-a1"},
	}, nil).Once()

	filter.PayoutStatus = []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_FAIL, accountingM.PayoutStatus_PAYOUT_SUCCESS}
	logs, totalCount, cursor, err = s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(2, len(logs))
	s.Equal(2, totalCount)
	s.Equal("", cursor)

	s.Equal("201711-b2-in-a1", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_PAYOUT, logs[0].Status)
	s.Equal("TW", logs[0].Region)
	s.Equal(float64(20000), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_PAYOUT_SUCCESS, logs[0].PayoutStatus)
	s.Equal(float64(1000), logs[0].PayoutFee)
	s.Equal(float64(20000), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t20171105.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal("USD", logs[0].LocalCurrency)
	s.Equal(float64(20000), logs[0].AmountInLocalCurrency)
	s.Equal("USD", logs[0].PayoutCurrency)
	s.Equal(float64(1.0), logs[0].PayoutRate)
	s.Equal(mockPayoutGroupID, logs[0].PayID)
	s.Equal(mockPayoutBalancerID, logs[0].BalancerID)
	s.Equal("a1_2017_approvaed_approval", logs[0].ApprovalID)
	s.Equal("", logs[0].TransactionStatus)
	s.Equal("", logs[0].PayoutErrorMessage)

	s.Equal("201711-b3-in-a1", logs[1].BillID)
	s.Equal(accountingM.BillStatus_BILL_PAYOUT, logs[1].Status)
	s.Equal("TW", logs[1].Region)
	s.Equal(float64(30000), logs[1].Amount)
	s.Equal(accountingM.PayoutStatus_PAYOUT_FAIL, logs[1].PayoutStatus)
	s.Equal(float64(1000), logs[1].PayoutFee)
	s.Equal(float64(0), logs[1].PaidAmount)
	s.Equal(float64(29000), logs[1].UnpaidAmount)
	s.Equal(t20171105.Unix(), logs[1].CreateUnixTime)
	s.Equal(true, logs[1].PayoutAccountVerified)
	s.Equal("USD", logs[1].LocalCurrency)
	s.Equal(float64(30000), logs[1].AmountInLocalCurrency)
	s.Equal(mockPayoutGroupID, logs[1].PayID)
	s.Equal("", logs[1].BalancerID)
	s.Equal("a1_2017_approvaed_approval", logs[1].ApprovalID)
	s.Equal("FAILED", logs[1].TransactionStatus)
	s.Equal("failed error message", logs[1].PayoutErrorMessage)

	// get all bills including voided
	s.mockUser.On("GetPlainUsers", mockCTX, "u13").Return([]models.User{
		{
			UserID:            "u13",
			PaypalVerifyState: 2,
		},
	}, nil).Once()

	filter = accountingM.GetBillLogsFilter{
		TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
		From:     time.Date(2017, time.November, 4, 0, 0, 0, 0, time.UTC),
		To:       time.Date(2017, time.November, 6, 0, 0, 0, 0, time.UTC),
		Stage:    accountingM.Stage_ALL_STAGE,
		UserIDs:  []string{"u13"},
		Region:   []string{"TW"},
	}
	logs, totalCount, cursor, err = s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", cursor)

	s.Equal("********-voided", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_VOIDED, logs[0].Status)
	s.Equal("TW", logs[0].Region)
	s.Equal(float64(90000), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[0].PayoutStatus)
	s.Equal(float64(0), logs[0].PayoutFee)
	s.Equal(float64(0), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t20171105.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal("USD", logs[0].LocalCurrency)
	s.Equal(float64(90000), logs[0].AmountInLocalCurrency)
	s.Equal("USD", logs[0].PayoutCurrency)
	s.Equal(float64(0), logs[0].PayoutRate)
	s.Equal("", logs[0].PayID)
	s.Equal("", logs[0].BalancerID)
	s.Equal("", logs[0].ApprovalID)
	s.Equal("", logs[0].TransactionStatus)
	s.Equal("", logs[0].PayoutErrorMessage)
}

func (s *accountingTestSuite) TestGetBillByCondition() {
	s.createTestingData(s.im, s.dbx)
	s.mockPayout.On("GetTypeInfos").Return(mockPayoutTypeInfos)
	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{}, nil)
	s.im.payout = s.mockPayout

	// Find bills by region
	filter := accountingM.GetBillLogsFilter{
		TimeType:     accountingM.TimeFilterType_BILL_CREATED_TIME,
		From:         time.Date(2017, time.November, 1, 0, 0, 0, 0, time.UTC),
		To:           time.Date(2017, time.December, 31, 0, 0, 0, 0, time.UTC),
		Stage:        accountingM.Stage_APPROVAL_STAGE,
		PayoutStatus: []accountingM.PayoutStatus{},
		Region:       []string{"JP", "ID"},
	}
	s.mockUser.On("GetPlainUsers", mockCTX, "u5").Return([]models.User{
		{
			UserID:            "u5",
			PaypalVerifyState: 2,
		},
	}, nil).Once()
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	logs, totalCount, cursor, err := s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", cursor)

	s.Equal("201712-b5-in-a2", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_SELECTED, logs[0].Status)
	s.Equal("JP", logs[0].Region)
	s.Equal(float64(50000), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[0].PayoutStatus)
	s.Equal(float64(0), logs[0].PayoutFee)
	s.Equal(float64(0), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t20171205.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal(accountingM.PayoutType_PAYPAL, logs[0].PayoutType)
	s.Equal("<EMAIL>", logs[0].PayoutAccount)
	s.Equal("JPY", logs[0].LocalCurrency)
	s.Equal(float64(5302500), logs[0].AmountInLocalCurrency)
	s.Equal("", logs[0].PayID)
	s.Equal("", logs[0].BalancerID)
	s.Equal("a2_2017_sealed_approval", logs[0].ApprovalID)
	s.Equal("", logs[0].RateMonth)
	s.Equal(float64(5000), logs[0].AmountForConsumptionTaxInLocalCurrency)
	s.Equal(float64(6000), logs[0].AmountForIncomeTaxInLocalCurrency)
	s.Equal("B123456789", logs[0].InvoiceNumber)
	s.Equal(accountingM.DeductionCondition_FREE_INCOME_TAX, logs[0].DeductionCondition)
	s.Equal("streamerRealName", logs[0].StreamerRealName)

	// find bills by payoutFilter
	filter.Stage = accountingM.Stage_PAYOUT_STAGE
	filter.PayoutStatus = []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_FAIL, accountingM.PayoutStatus_PAYOUT_SUCCESS}
	filter.PayoutAccountFilter = []accountingM.PayoutAccountFilter{
		{
			Type:            accountingM.PayoutType_BANK_ESUN,
			AccountVerified: accountingM.VerifiedFilter_VERIFIED,
		},
		{
			Type:            accountingM.PayoutType_OFFLINE,
			AccountVerified: accountingM.VerifiedFilter_UNVERIFIED,
		},
	}
	filter.Region = []string{}
	s.mockUser.On("GetPlainUsers", mockCTX, "u2").Return([]models.User{
		{
			UserID:            "u2",
			PaypalVerifyState: 2,
		},
	}, nil).Once()
	s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), []string{"201711-b2-in-a1"}).Return([]*payout.DBRow{{BillID: "201711-b2-in-a1"}}, nil).Once()

	logs, totalCount, cursor, err = s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", cursor)

	s.Equal("201711-b2-in-a1", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_PAYOUT, logs[0].Status)
	s.Equal("TW", logs[0].Region)
	s.Equal(float64(20000), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_PAYOUT_SUCCESS, logs[0].PayoutStatus)
	s.Equal(float64(1000), logs[0].PayoutFee)
	s.Equal(float64(20000), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t20171105.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal(accountingM.PayoutType_BANK_ESUN, logs[0].PayoutType)
	s.Equal("bank-account", logs[0].PayoutAccount)
	s.Equal(userM.UserType_CONTRACT_USER, logs[0].UserType)
	s.Equal("USD", logs[0].LocalCurrency)
	s.Equal(float64(20000), logs[0].AmountInLocalCurrency)
	s.Equal("USD", logs[0].PayoutCurrency)
	s.Equal(float64(1.0), logs[0].PayoutRate)
	s.Equal(mockPayoutGroupID, logs[0].PayID)
	s.Equal(mockPayoutBalancerID, logs[0].BalancerID)
	s.Equal("a1_2017_approvaed_approval", logs[0].ApprovalID)

	// find bills by userType
	filter.Stage = accountingM.Stage_PAYOUT_STAGE
	filter.PayoutStatus = []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_FAIL, accountingM.PayoutStatus_PAYOUT_SUCCESS}
	filter.PayoutAccountFilter = []accountingM.PayoutAccountFilter{}
	filter.Region = []string{}
	filter.UserType = []userM.UserType{userM.UserType_CONTRACT_USER, userM.UserType_AGENCY}
	s.mockUser.On("GetPlainUsers", mockCTX, "u2", "u3").Return([]models.User{
		{
			UserID:            "u2",
			PaypalVerifyState: 2,
		},
		{
			UserID:            "u3",
			PaypalVerifyState: 2,
		},
	}, nil).Once()
	s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), []string{"201711-b2-in-a1", "201711-b3-in-a1"}).Return([]*payout.DBRow{
		{BillID: "201711-b2-in-a1"},
		{BillID: "201711-b3-in-a1"},
	}, nil).Once()

	logs, totalCount, cursor, err = s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(2, len(logs))
	s.Equal(2, totalCount)
	s.Equal("", cursor)

	s.Equal("201711-b2-in-a1", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_PAYOUT, logs[0].Status)
	s.Equal("TW", logs[0].Region)
	s.Equal(float64(20000), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_PAYOUT_SUCCESS, logs[0].PayoutStatus)
	s.Equal(float64(1000), logs[0].PayoutFee)
	s.Equal(float64(20000), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t20171105.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal(accountingM.PayoutType_BANK_ESUN, logs[0].PayoutType)
	s.Equal("bank-account", logs[0].PayoutAccount)
	s.Equal(userM.UserType_CONTRACT_USER, logs[0].UserType)
	s.Equal("USD", logs[0].LocalCurrency)
	s.Equal(float64(20000), logs[0].AmountInLocalCurrency)
	s.Equal("USD", logs[0].PayoutCurrency)
	s.Equal(float64(1.0), logs[0].PayoutRate)
	s.Equal(mockPayoutGroupID, logs[0].PayID)
	s.Equal(mockPayoutBalancerID, logs[0].BalancerID)
	s.Equal("a1_2017_approvaed_approval", logs[0].ApprovalID)

	s.Equal("201711-b3-in-a1", logs[1].BillID)
	s.Equal(accountingM.BillStatus_BILL_PAYOUT, logs[1].Status)
	s.Equal("TW", logs[1].Region)
	s.Equal(float64(30000), logs[1].Amount)
	s.Equal(accountingM.PayoutStatus_PAYOUT_FAIL, logs[1].PayoutStatus)
	s.Equal(float64(1000), logs[1].PayoutFee)
	s.Equal(float64(0), logs[1].PaidAmount)
	s.Equal(float64(29000), logs[1].UnpaidAmount)
	s.Equal(t20171105.Unix(), logs[1].CreateUnixTime)
	s.Equal(true, logs[1].PayoutAccountVerified)
	s.Equal("USD", logs[1].LocalCurrency)
	s.Equal(float64(30000), logs[1].AmountInLocalCurrency)
	s.Equal(mockPayoutGroupID, logs[1].PayID)
	s.Equal("", logs[1].BalancerID)
	s.Equal("a1_2017_approvaed_approval", logs[1].ApprovalID)
	s.Equal("FAILED", logs[1].TransactionStatus)
	s.Equal("failed error message", logs[1].PayoutErrorMessage)
}

func (s *accountingTestSuite) TestGetBillByApprovedTime() {
	s.createTestingData(s.im, s.dbx)
	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{}, nil)
	s.mockPayout.On("GetTypeInfos").Return(contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{}})
	s.im.payout = s.mockPayout

	filter := accountingM.GetBillLogsFilter{
		TimeType:     accountingM.TimeFilterType_BILL_APPROVED_TIME,
		From:         time.Time{},
		To:           time.Now(),
		Stage:        accountingM.Stage_PAYOUT_STAGE,
		PayoutStatus: []accountingM.PayoutStatus{accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS},
		Region:       []string{"TW"},
	}

	s.mockUser.On("GetPlainUsers", mockCTX, "u6", "u8", "u9").Return([]models.User{
		{
			UserID:            "u6",
			PaypalVerifyState: 2,
		},
		{
			UserID:            "u8",
			PaypalVerifyState: 2,
		},
		{
			UserID:            "u9",
			PaypalVerifyState: 2,
		},
	}, nil).Once()
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	logs, totalCount, cursor, err := s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(3, len(logs))
	s.Equal(3, totalCount)
	s.Equal("", cursor)

	s.Equal("201712-b6-in-a3", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_APPROVED, logs[0].Status)
	s.Equal("TW", logs[0].Region)
	s.Equal(float64(60000), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[0].PayoutStatus)
	s.Equal(float64(0), logs[0].PayoutFee)
	s.Equal(float64(0), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t20171205.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal(float64(60000), logs[0].AmountInLocalCurrency)
	s.Equal("USD", logs[0].LocalCurrency)
	s.Equal("", logs[0].PayID)
	s.Equal("", logs[0].BalancerID)
	s.Equal("a3_201712_approved_approval", logs[0].ApprovalID)
	s.Equal("2017-12", logs[0].RateMonth)

	s.Equal("2017-b8-in-a4-a5", logs[1].BillID)
	s.Equal(accountingM.BillStatus_BILL_APPROVED, logs[1].Status)
	s.Equal("TW", logs[1].Region)
	s.Equal(float64(80000), logs[1].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[1].PayoutStatus)
	s.Equal(float64(0), logs[1].PayoutFee)
	s.Equal(float64(0), logs[1].PaidAmount)
	s.Equal(float64(0), logs[1].UnpaidAmount)
	s.Equal(t20171205.Unix(), logs[1].CreateUnixTime)
	s.Equal(true, logs[1].PayoutAccountVerified)
	s.Equal(float64(80000), logs[1].AmountInLocalCurrency)
	s.Equal("USD", logs[1].LocalCurrency)
	s.Equal("", logs[1].PayID)
	s.Equal("", logs[1].BalancerID)
	s.Equal("a5_recover_a4_approval", logs[1].ApprovalID)
	s.Equal("a5_rate", logs[1].RateMonth)

	s.Equal("2017-b9-in-a4-a5", logs[2].BillID)
	s.Equal(accountingM.BillStatus_BILL_APPROVED, logs[2].Status)
	s.Equal("TW", logs[2].Region)
	s.Equal(float64(90000), logs[2].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[2].PayoutStatus)
	s.Equal(float64(0), logs[2].PayoutFee)
	s.Equal(float64(0), logs[2].PaidAmount)
	s.Equal(float64(0), logs[2].UnpaidAmount)
	s.Equal(t20171205.Unix(), logs[2].CreateUnixTime)
	s.Equal(true, logs[1].PayoutAccountVerified)
	s.Equal(float64(90000), logs[2].AmountInLocalCurrency)
	s.Equal("USD", logs[2].LocalCurrency)
	s.Equal("", logs[2].PayID)
	s.Equal("", logs[2].BalancerID)
	s.Equal("a5_recover_a4_approval", logs[2].ApprovalID)
	s.Equal("a5_rate", logs[2].RateMonth)
}

func (s *accountingTestSuite) TestGetBillByCompleteTime() {
	s.createTestingData(s.im, s.dbx)
	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{}, nil)
	s.im.payout = s.mockPayout
	s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), []string{"201711-b2-in-a1"}).Return([]*payout.DBRow{{BillID: "201711-b2-in-a1"}}, nil).Once()

	filter := accountingM.GetBillLogsFilter{
		TimeType: accountingM.TimeFilterType_BILL_COMPLETE_TIME,
		From:     t20171105,
		To:       t20171205.AddDate(0, 0, 1),
		Stage:    accountingM.Stage_PAYOUT_STAGE,
		Region:   []string{"TW"},
	}

	s.mockUser.On("GetPlainUsers", mockCTX, "u2").Return([]models.User{
		{
			UserID:            "u2",
			PaypalVerifyState: 2,
		},
	}, nil).Once()
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	logs, totalCount, cursor, err := s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", cursor)

	s.Equal("201711-b2-in-a1", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_PAYOUT, logs[0].Status)
	s.Equal("TW", logs[0].Region)
	s.Equal(float64(20000), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_PAYOUT_SUCCESS, logs[0].PayoutStatus)
	s.Equal(float64(1000), logs[0].PayoutFee)
	s.Equal(float64(20000), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t20171105.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal("USD", logs[0].LocalCurrency)
	s.Equal(float64(20000), logs[0].AmountInLocalCurrency)
	s.Equal("USD", logs[0].PayoutCurrency)
	s.Equal(float64(1.0), logs[0].PayoutRate)
	s.Equal(mockPayoutGroupID, logs[0].PayID)
	s.Equal(mockPayoutBalancerID, logs[0].BalancerID)
	s.Equal("a1_2017_approvaed_approval", logs[0].ApprovalID)
	s.Equal("", logs[0].TransactionStatus)
	s.Equal("", logs[0].PayoutErrorMessage)
}

func (s *accountingTestSuite) TestGetBillByApprovedTimeEmpty() {
	s.createTestingData(s.im, s.dbx)

	filter := accountingM.GetBillLogsFilter{
		TimeType:     accountingM.TimeFilterType_BILL_APPROVED_TIME,
		From:         time.Time{},
		To:           time.Date(2015, time.December, 1, 0, 0, 0, 0, time.UTC),
		Stage:        accountingM.Stage_PAYOUT_STAGE,
		PayoutStatus: []accountingM.PayoutStatus{accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS},
		Region:       []string{"TW"},
	}

	logs, totalCount, cursor, err := s.im.GetBillLogs(mockCTX, filter, "", 100, GetBillLogsNeedCount())
	s.NoError(err)
	s.Equal(0, len(logs))
	s.Equal(0, totalCount)
	s.Equal("", cursor)
}

func (s *accountingTestSuite) TestGetBillByStatus() {
	userID := "u1"
	mockBills := []struct {
		br bill.DBRow
		pr payout.DBRow
	}{
		{
			br: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b1",
				Status:                accountingM.BillStatus_BILL_APPROVED,
				UserID:                userID,
				Region:                "TW",
				Amount:                usd.ToNanoUSD(55),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            2000000,
				CreateTimeMillis:      2000000,
				ExecutorID:            mockExecutorID,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 55,
			},
		},
		{
			br: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b2",
				Status:                accountingM.BillStatus_BILL_WRITTEN_OFF,
				UserID:                userID,
				Region:                "TW",
				Amount:                usd.ToNanoUSD(55),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            3000000,
				CreateTimeMillis:      3000000,
				ExecutorID:            mockExecutorID,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 55,
			},
		},
		{
			br: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b3",
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                userID,
				Region:                "TW",
				Amount:                usd.ToNanoUSD(77),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            4000000,
				CreateTimeMillis:      4000000,
				ExecutorID:            mockExecutorID,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 77,
			},
			pr: payout.DBRow{
				TimeMillis:       5000000,
				CreatorID:        mockExecutorID,
				BillID:           "b3",
				Currency:         "USD",
				Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
				UpdateTimeMillis: 5000000,
			},
		},
		{
			br: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b4",
				Status:                accountingM.BillStatus_BILL_SELECTED,
				UserID:                userID,
				Region:                "TW",
				Amount:                usd.ToNanoUSD(88),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            6000000,
				CreateTimeMillis:      6000000,
				ExecutorID:            mockExecutorID,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 88,
			},
		},
		{
			br: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b5",
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                userID,
				Region:                "TW",
				Amount:                usd.ToNanoUSD(99),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            4000000,
				CreateTimeMillis:      4000000,
				ExecutorID:            mockExecutorID,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 99,
			},
			pr: payout.DBRow{
				TimeMillis:       5000000,
				CreatorID:        mockExecutorID,
				BillID:           "b5",
				Currency:         "USD",
				Rate:             1,
				Status:           accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND,
				Fee:              9,
				AmountBill:       99,
				UpdateTimeMillis: 5000000,
			},
		},
	}
	for _, b := range mockBills {
		// create orphan bill
		orphanBill := b.br
		orphanBill.Status = accountingM.BillStatus_BILL_ORPHAN
		_, err := s.dbx.NamedExec(bill.InsertBillQueryStr, orphanBill)
		s.Require().NoError(err)
		_, err = s.dbx.NamedExec(bill.InsertBillQueryStr, b.br)
		s.Require().NoError(err)
		_, err = s.dbx.NamedExec(insertAccountingPayoutStmt, b.pr)
		s.Require().NoError(err)
		if b.pr.Status == accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND {
			_, err = s.dbx.Exec(insertRefundQueryStr, b.br.BillID, b.br.Currency, usd.ToNanoUSD(b.pr.Fee), b.pr.AmountBill-b.pr.Fee, "", "", "", 0)
			s.Require().NoError(err)
		}
	}
	s.mockPayout.On("GetTypeInfos").Return(contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{}})
	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{}, nil)
	s.im.payout = s.mockPayout
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	tests := []struct {
		desc               string
		filterStatus       []accountingM.BillStatus
		filterPayoutStatus []accountingM.PayoutStatus
		expBills           []bill.DBRow
		expPayouts         []payout.DBRow
	}{
		{
			desc: "get approved bills",
			filterStatus: []accountingM.BillStatus{
				accountingM.BillStatus_BILL_APPROVED,
			},
			filterPayoutStatus: []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_IN_PROGRESS},
			expBills:           []bill.DBRow{mockBills[0].br},
			expPayouts:         []payout.DBRow{mockBills[0].pr},
		},
		{
			desc: "get written-off bills",
			filterStatus: []accountingM.BillStatus{
				accountingM.BillStatus_BILL_ORPHAN,
				accountingM.BillStatus_BILL_PAYOUT,
				accountingM.BillStatus_BILL_WRITTEN_OFF,
			},
			filterPayoutStatus: []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_SUCCESS},
			expBills:           []bill.DBRow{mockBills[1].br},
			expPayouts:         []payout.DBRow{mockBills[1].pr},
		},
		{
			desc: "get in progress payout bills",
			filterStatus: []accountingM.BillStatus{
				accountingM.BillStatus_BILL_PAYOUT,
			},
			filterPayoutStatus: []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_IN_PROGRESS},
			expBills:           []bill.DBRow{mockBills[2].br},
			expPayouts:         []payout.DBRow{mockBills[1].pr},
		},
		{
			desc: "get written-off and in progress payout bills",
			filterStatus: []accountingM.BillStatus{
				accountingM.BillStatus_BILL_PAYOUT,
				accountingM.BillStatus_BILL_WRITTEN_OFF,
			},
			filterPayoutStatus: []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_IN_PROGRESS},
			expBills:           []bill.DBRow{mockBills[2].br, mockBills[1].br},
			expPayouts:         []payout.DBRow{mockBills[2].pr, mockBills[1].pr},
		},
		{
			desc: "no bill",
			filterStatus: []accountingM.BillStatus{
				accountingM.BillStatus_BILL_PAYOUT,
			},
			filterPayoutStatus: []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_SUCCESS},
			expBills:           []bill.DBRow{},
		},
		{
			desc:               "get in progress payout bills without status filter",
			filterStatus:       []accountingM.BillStatus{},
			filterPayoutStatus: []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_IN_PROGRESS},
			expBills:           []bill.DBRow{mockBills[2].br},
			expPayouts:         []payout.DBRow{mockBills[2].pr},
		},
		{
			desc:               "get refund payout",
			filterStatus:       []accountingM.BillStatus{},
			filterPayoutStatus: []accountingM.PayoutStatus{accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND},
			expBills:           []bill.DBRow{mockBills[4].br},
			expPayouts:         []payout.DBRow{mockBills[4].pr},
		},
	}

	for _, t := range tests {
		s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*payout.DBRow{}, nil).Maybe()

		context := ctx.WithValue(mockCTX, "testDesc", t.desc)
		expBillCount := len(t.expBills)
		if expBillCount > 0 {
			uniqueUserID := map[string]bool{}
			getPlainUserArgs := []interface{}{mock.AnythingOfType("ctx.CTX")}
			mockUsers := []models.User{}
			for _, b := range t.expBills {
				if uniqueUserID[b.UserID] {
					continue
				}
				uniqueUserID[b.UserID] = true
				getPlainUserArgs = append(getPlainUserArgs, b.UserID)
				mockUsers = append(mockUsers, models.User{UserID: b.UserID})
			}
			s.mockUser.On("GetPlainUsers", getPlainUserArgs...).Return(mockUsers, nil).Once()
		}

		filter := accountingM.GetBillLogsFilter{
			TimeType:     accountingM.TimeFilterType_BILL_CREATED_TIME,
			From:         time.Time{},
			To:           time.Now(),
			Stage:        accountingM.Stage_PAYOUT_STAGE,
			Status:       t.filterStatus,
			PayoutStatus: t.filterPayoutStatus,
		}
		logs, totalCount, cursor, err := s.im.GetBillLogs(context, filter, "", 100, GetBillLogsNeedCount())
		s.NoError(err, t.desc)
		s.Equal(expBillCount, len(logs), t.desc)
		s.Equal(expBillCount, totalCount, t.desc)
		s.Equal("", cursor, t.desc)
		for i, log := range logs {
			b := t.expBills[i]
			p := t.expPayouts[i]
			s.Equal(b.BillID, log.BillID, t.desc)
			s.Equal(b.UserID, log.UserID, t.desc)
			s.Equal(b.Status, log.Status, t.desc)
			s.Equal(b.Region, log.Region, t.desc)
			s.Equal(usd.ToUSD(b.Amount), log.Amount, t.desc)
			if b.Status == accountingM.BillStatus_BILL_WRITTEN_OFF {
				s.Equal(b.TimeMillis/1000, log.WrittenOffUnixTime, t.desc)
				s.Equal(b.ExecutorID, log.WrittenOffExecutorID, t.desc)
			}
			if p.Status == accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND {
				s.Equal(p.Fee, log.RefundFee, t.desc)
				s.Equal(p.Fee, log.RefundFeeInLocalCurrency, t.desc)
			} else {
				s.Equal(float64(0), log.RefundFee, t.desc)
				s.Equal(float64(0), log.RefundFeeInLocalCurrency, t.desc)
			}
		}
	}
}

func (s *accountingTestSuite) TestGetBillByTypes() {
	mockBills := []*bill.Element{
		{
			DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b1",
				Status:                accountingM.BillStatus_BILL_ORPHAN,
				UserID:                "u1",
				UserType:              userM.UserType_CONTRACT_USER,
				Region:                "TW",
				Type:                  accountingM.BillType_BILL_REVENUE,
				Amount:                usd.ToNanoUSD(55),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            2000000,
				CreateTimeMillis:      2000000,
				ExecutorID:            mockExecutorID,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 55,
			},
		},
		{
			DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b2",
				Status:                accountingM.BillStatus_BILL_ORPHAN,
				UserID:                "u2",
				UserType:              userM.UserType_USER,
				Region:                "TW",
				Type:                  accountingM.BillType_BILL_REWARD,
				Amount:                usd.ToNanoUSD(66),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            3000000,
				CreateTimeMillis:      3000000,
				ExecutorID:            mockExecutorID,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 66,
			},
		},
		{
			DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b3",
				Status:                accountingM.BillStatus_BILL_ORPHAN,
				UserID:                "u3",
				UserType:              userM.UserType_CONTRACT_USER,
				Region:                "JP",
				Type:                  accountingM.BillType_BILL_COMBINE_PAY,
				Amount:                0,
				PayoutType:            accountingM.PayoutType_PAYPAL,
				PayoutAccount:         "<EMAIL>",
				PayoutAccountVerified: true,
				TimeMillis:            3000000,
				CreateTimeMillis:      3000000,
				ExecutorID:            mockExecutorID,
				LocalCurrency:         "JPY",
				AmountInLocalCurrency: 30000,
			},
			SubBills: []*bill.Element{
				{DBRow: bill.DBRow{
					BillID:                "b3-1",
					IsNew:                 1,
					Status:                accountingM.BillStatus_BILL_COMBINED,
					UserID:                "u3",
					UserType:              userM.UserType_CONTRACT_USER,
					Region:                "JP",
					Type:                  accountingM.BillType_BILL_REVENUE,
					Currency:              moneyM.Currency_NANO_USD,
					Amount:                100,
					PayoutType:            accountingM.PayoutType_PAYPAL,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					TimeMillis:            3000000,
					CreateTimeMillis:      3000000,
					ExecutorID:            mockExecutorID,
					LocalCurrency:         "JPY",
					AmountInLocalCurrency: 10000,
				}},
				{DBRow: bill.DBRow{
					BillID:                "b3-2",
					IsNew:                 1,
					Status:                accountingM.BillStatus_BILL_COMBINED,
					UserID:                "u3",
					UserType:              userM.UserType_CONTRACT_USER,
					Region:                "JP",
					Type:                  accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
					Amount:                0,
					PayoutType:            accountingM.PayoutType_PAYPAL,
					PayoutAccount:         "<EMAIL>",
					PayoutAccountVerified: true,
					TimeMillis:            3000000,
					CreateTimeMillis:      3000000,
					ExecutorID:            mockExecutorID,
					LocalCurrency:         "JPY",
					AmountInLocalCurrency: 20000,
				}},
			},
		},
	}

	tests := []struct {
		desc        string
		filterTypes []accountingM.BillType
		expBills    []*bill.Element
	}{
		{
			desc: "get revenue bill",
			filterTypes: []accountingM.BillType{
				accountingM.BillType_BILL_REVENUE,
			},
			expBills: []*bill.Element{mockBills[0]},
		},
		{
			desc: "get reward bill",
			filterTypes: []accountingM.BillType{
				accountingM.BillType_BILL_REWARD,
			},
			expBills: []*bill.Element{mockBills[1]},
		},
		{
			desc: "get revenue and reward bills",
			filterTypes: []accountingM.BillType{
				accountingM.BillType_BILL_REVENUE,
				accountingM.BillType_BILL_REWARD,
			},
			expBills: []*bill.Element{mockBills[1], mockBills[0]},
		},
		{
			desc: "get combined bills",
			filterTypes: []accountingM.BillType{
				accountingM.BillType_BILL_COMBINE_PAY,
			},
			expBills: []*bill.Element{mockBills[2]},
		},
	}
	s.mockPayout.On("GetTypeInfos").Return(mockPayoutTypeInfos)
	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{}, nil)
	s.im.payout = s.mockPayout
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	for _, t := range tests {
		context := ctx.WithValue(mockCTX, "testDesc", t.desc)
		_, err := s.dbx.Exec("DELETE FROM Bill")
		s.NoError(err)
		for _, b := range t.expBills {
			_, err := s.dbx.NamedExec(bill.InsertBillQueryStr, b)
			s.NoError(err)
			if len(b.SubBills) == 0 {
				continue
			}
			for _, sb := range b.SubBills {
				_, err := s.dbx.NamedExec(bill.InsertBillQueryStr, sb)
				s.NoError(err)
				_, err = s.dbx.Exec("INSERT INTO `CombinedBillRelation` (`billID`, `subBillID`) VALUES (?, ?)", b.BillID, sb.BillID)
				s.NoError(err)
			}
		}
		expBillCount := len(t.expBills)
		if expBillCount > 0 {
			uniqueUserID := map[string]bool{}
			mockUserIDs := []string{}
			mockUsers := []models.User{}
			for _, b := range t.expBills {
				if uniqueUserID[b.UserID] {
					continue
				}
				uniqueUserID[b.UserID] = true
				mockUserIDs = append(mockUserIDs, b.UserID)
				mockUsers = append(mockUsers, models.User{UserID: b.UserID})
			}
			sort.Strings(mockUserIDs)
			sort.Slice(mockUsers, func(i, j int) bool {
				return mockUsers[i].UserID < mockUsers[j].UserID
			})
			getPlainUserArgs := []interface{}{mock.AnythingOfType("ctx.CTX")}
			for _, u := range mockUserIDs {
				getPlainUserArgs = append(getPlainUserArgs, u)
			}
			s.mockUser.On("GetPlainUsers", getPlainUserArgs...).Return(mockUsers, nil).Once()
		}

		filter := accountingM.GetBillLogsFilter{
			TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
			From:     time.Time{},
			To:       time.Now(),
			Type:     t.filterTypes,
		}
		logs, totalCount, cursor, err := s.im.GetBillLogs(context, filter, "", 100, GetBillLogsNeedCount())
		s.NoError(err, t.desc)
		s.Equal(expBillCount, len(logs), t.desc)
		s.Equal(expBillCount, totalCount, t.desc)
		s.Equal("", cursor, t.desc)
		for i, b := range t.expBills {
			s.Equal(b.BillID, logs[i].BillID, t.desc)
			s.Equal(b.Type, logs[i].Type, t.desc)
			s.Equal(b.UserID, logs[i].UserID, t.desc)
			s.Equal(b.UserType, logs[i].UserType, t.desc)
			s.Equal(b.Status, logs[i].Status, t.desc)
			s.Equal(b.Region, logs[i].Region, t.desc)
			s.Equal(usd.ToUSD(b.Amount), logs[i].Amount, t.desc)
			s.Equal(b.AmountInLocalCurrency, logs[i].AmountInLocalCurrency, t.desc)
			s.Equal(b.LocalCurrency, logs[i].LocalCurrency, t.desc)
			s.Equal(b.UserID, logs[i].Payee.UserID, t.desc)
			if b.Status == accountingM.BillStatus_BILL_WRITTEN_OFF {
				s.Equal(b.TimeMillis/1000, logs[i].WrittenOffUnixTime, t.desc)
				s.Equal(b.ExecutorID, logs[i].WrittenOffExecutorID, t.desc)
			}
			if len(b.SubBills) == 0 {
				continue
			}
			retSubBillMap := map[string]*accountingM.ResBillLog{}
			for _, sb := range logs[i].SubBillLogs {
				retSubBillMap[sb.BillID] = sb
			}
			s.Equal(len(b.SubBills), len(retSubBillMap), t.desc)
			for _, sb := range b.SubBills {
				retSB, ok := retSubBillMap[sb.BillID]
				s.Equal(true, ok, t.desc)
				s.Equal(sb.BillID, retSB.BillID, t.desc)
				s.Equal(sb.Type, retSB.Type, t.desc)
				s.Equal(sb.UserID, retSB.UserID, t.desc)
				s.Equal(sb.UserType, retSB.UserType, t.desc)
				s.Equal(sb.Status, retSB.Status, t.desc)
				s.Equal(sb.Region, retSB.Region, t.desc)
				s.Equal(usd.ToUSD(sb.Amount), retSB.Amount, t.desc)
				s.Equal(sb.AmountInLocalCurrency, retSB.AmountInLocalCurrency, t.desc)
				s.Equal(sb.LocalCurrency, retSB.LocalCurrency, t.desc)
			}
		}
	}
}

func (s *accountingTestSuite) TestGetApprovalBillLogs() {
	s.createTestingData(s.im, s.dbx)
	s.im.comment = s.intraComment
	approvalID := "a3_201712_approved_approval"
	offset := 0
	limit := 100
	s.mockUser.On("GetPlainUsers", mockCTX, "u2", "u6").Return([]models.User{
		{UserID: "u2"}, {UserID: "u6"},
	}, nil).Once()
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)
	logs, totalCount, cursor, err := s.im.GetBillLogsByApprovalID(mockCTX, approvalID, offset, limit, accountingM.GetApprovalBillLogsFilter{
		UserIDs: []string{"u2", "u6"},
	})
	s.NoError(err)
	s.Equal(2, len(logs))
	s.Equal(2, totalCount)
	s.Equal("", cursor)

	s.Equal("201712-b6-in-a3", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_APPROVED, logs[0].Status)
	s.Equal("TW", logs[0].Region)
	s.Equal(float64(60000), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[0].PayoutStatus)
	s.Equal(float64(0), logs[0].PayoutFee)
	s.Equal(float64(0), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t20171205.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal(float64(60000), logs[0].AmountInLocalCurrency)
	s.Equal("USD", logs[0].LocalCurrency)
	s.Equal("", logs[0].PayID)
	s.Equal("", logs[0].BalancerID)
	s.Equal("a3_201712_approved_approval", logs[0].ApprovalID)

	s.Equal("201712-b7-in-a3", logs[1].BillID)
	s.Equal(accountingM.BillStatus_BILL_PAYOUT, logs[1].Status)
	s.Equal("TW", logs[1].Region)
	s.Equal(float64(70000), logs[1].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[1].PayoutStatus)
	s.Equal(float64(0), logs[1].PayoutFee)
	s.Equal(float64(0), logs[1].PaidAmount)
	s.Equal(float64(0), logs[1].UnpaidAmount)
	s.Equal(t20171205.Unix(), logs[1].CreateUnixTime)
	s.Equal(true, logs[1].PayoutAccountVerified)
	s.Equal(float64(70000), logs[1].AmountInLocalCurrency)
	s.Equal("USD", logs[1].LocalCurrency)
	s.Equal("", logs[0].PayID)
	s.Equal("", logs[0].BalancerID)
	s.Equal("a3_201712_approved_approval", logs[1].ApprovalID)

	// use offset and limit
	s.mockUser.On("GetPlainUsers", mockCTX, "u6").Return([]models.User{
		{
			UserID: "u6",
		},
	}, nil).Once()
	logs, totalCount, cursor, err = s.im.GetBillLogsByApprovalID(mockCTX, approvalID, 0, 1, accountingM.GetApprovalBillLogsFilter{
		UserIDs: []string{"u2", "u6"},
	})
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(2, totalCount)
	s.Equal("1", cursor)
	s.Equal("201712-b6-in-a3", logs[0].BillID)

	s.mockUser.On("GetPlainUsers", mockCTX, "u2").Return([]models.User{
		{
			UserID: "u2",
		},
	}, nil).Once()
	logs, totalCount, cursor, err = s.im.GetBillLogsByApprovalID(mockCTX, approvalID, 1, 1, accountingM.GetApprovalBillLogsFilter{
		UserIDs: []string{"u2", "u6"},
	})
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(2, totalCount)
	s.Equal("", cursor)
	s.Equal("201712-b7-in-a3", logs[0].BillID)

	// found zero bill match input userIDs
	logs, totalCount, cursor, err = s.im.GetBillLogsByApprovalID(mockCTX, approvalID, 1, 1, accountingM.GetApprovalBillLogsFilter{
		UserIDs: []string{"not-exist-user-id"},
	})
	s.NoError(err)
	s.Equal(0, len(logs))
	s.Equal(0, totalCount)
	s.Equal("", cursor)

	// Monthly income approval
	billIDs := []string{"********-monthly-bill"}
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "JP").Return(time.UTC, nil).Twice()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "u14").Return([]models.User{
		{
			UserID: "u14",
			OpenID: "u11OpenID",
		},
	}, nil).Once()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockExecutorID).Return([]models.User{
		{
			UserID: mockExecutorID,
			OpenID: mockExecutorOpenID,
		},
	}, nil).Once()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "u14").Return([]models.User{
		{
			UserID: "u14",
			OpenID: "u11OpenID",
		},
	}, nil).Once()
	s.mockIntraFile.On("Upload", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*file.File")).Return([]string{mockApprovalFileKey}, nil).Once()
	apl, err := s.im.MakeApproval(mockCTX, mockExecutorID, "test-approval", "JP", billIDs)
	s.NoError(err)
	s.mockUser.On("GetPlainUsers", mockCTX, "u14").Return([]models.User{
		{
			UserID: "u14",
			OpenID: "u11OpenID",
		},
	}, nil).Once()
	logs, totalCount, cursor, err = s.im.GetBillLogsByApprovalID(mockCTX, apl.ApprovalID, offset, limit, accountingM.GetApprovalBillLogsFilter{
		OpenIDs: []string{"u11OpenID"},
	})
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", cursor)
	s.Equal("********-monthly-bill", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_SELECTED, logs[0].Status)
	s.Equal("JP", logs[0].Region)
	s.Equal(float64(0), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[0].PayoutStatus)
	s.Equal(float64(0), logs[0].PayoutFee)
	s.Equal(float64(0), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t********.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal(float64(1000), logs[0].AmountInLocalCurrency)
	s.Equal("JPY", logs[0].LocalCurrency)
	s.Equal("", logs[0].PayID)
	s.Equal("", logs[0].BalancerID)
	s.Equal(apl.ApprovalID, logs[0].ApprovalID)
	s.Equal(accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME, logs[0].Type)

	// Combined bill approval
	billIDs = []string{"********-combined-bill"}
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "JP").Return(time.UTC, nil).Twice()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockExecutorID).Return([]models.User{
		{
			UserID: mockExecutorID,
			OpenID: mockExecutorOpenID,
		},
	}, nil).Once()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "u15").Return([]models.User{
		{
			UserID: "u14",
			OpenID: "u11OpenID",
		},
	}, nil).Once()
	s.mockIntraFile.On("Upload", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*file.File")).Return([]string{mockApprovalFileKey}, nil).Once()
	apl, err = s.im.MakeApproval(mockCTX, mockExecutorID, "test-approval", "JP", billIDs)
	s.NoError(err)
	s.mockUser.On("GetPlainUsers", mockCTX, "u15").Return([]models.User{
		{
			UserID: "u15",
		},
	}, nil).Once()
	logs, totalCount, cursor, err = s.im.GetBillLogsByApprovalID(mockCTX, apl.ApprovalID, offset, limit, accountingM.GetApprovalBillLogsFilter{})
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", cursor)
	s.Equal("********-combined-bill", logs[0].BillID)
	s.Equal(accountingM.BillStatus_BILL_SELECTED, logs[0].Status)
	s.Equal("JP", logs[0].Region)
	s.Equal(float64(0), logs[0].Amount)
	s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, logs[0].PayoutStatus)
	s.Equal(float64(0), logs[0].PayoutFee)
	s.Equal(float64(0), logs[0].PaidAmount)
	s.Equal(float64(0), logs[0].UnpaidAmount)
	s.Equal(t********.Unix(), logs[0].CreateUnixTime)
	s.Equal(true, logs[0].PayoutAccountVerified)
	s.Equal(float64(13000), logs[0].AmountInLocalCurrency)
	s.Equal("JPY", logs[0].LocalCurrency)
	s.Equal("", logs[0].PayID)
	s.Equal("", logs[0].BalancerID)
	s.Equal(apl.ApprovalID, logs[0].ApprovalID)
	s.Equal(accountingM.BillType_BILL_COMBINE_PAY, logs[0].Type)
	s.Equal(2, len(logs[0].SubBillLogs))
}

func (s *accountingTestSuite) TestGetApprovalLogsEmpty() {
	cursor := ""
	limit := 100
	filter := accountingM.GetApprovalLogsFilter{
		From:   time.Time{},
		To:     time.Now(),
		ID:     "no approval logs",
		Region: []string{},
		Status: []accountingM.ApprovalStatus{accountingM.ApprovalStatus_APPROVAL_APPROVED},
	}
	logs, totalCount, nextCursor, err := s.im.GetApprovalLogs(mockCTX, filter, cursor, limit)
	s.NoError(err)
	s.Equal(0, len(logs))
	s.Equal(0, totalCount)
	s.Equal("", nextCursor)
}

func (s *accountingTestSuite) TestGetApprovalLogsByID() {
	s.createTestingData(s.im, s.dbx)
	s.im.comment = s.intraComment
	approvalID := "a5_recover_a4_approval"
	cursor := ""
	limit := 100
	filter := accountingM.GetApprovalLogsFilter{
		From:   time.Time{},
		To:     time.Now(),
		ID:     approvalID,
		Region: []string{},
		Status: []accountingM.ApprovalStatus{},
	}
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	logs, totalCount, nextCursor, err := s.im.GetApprovalLogs(mockCTX, filter, cursor, limit)
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", nextCursor)
	l := logs[0]
	s.Equal("a5_recover_a4_approval", l.ApprovalID)
	s.Equal("TW", l.Region)
	s.Equal("s5", l.Subject)
	s.Equal(accountingM.ApprovalStatus_APPROVAL_APPROVED, l.Status)
	s.Equal("a5_rate", l.RateMonth)
	s.Equal(2, l.BillsCount)
	s.Equal(float64(170000), l.TotalAmount)
	s.Equal("USD", l.LocalCurrency)
	s.Equal(float64(170000), l.TotalAmountInLocalCurrency)
	s.Equal("approval.csv", l.FileKey)
	s.Equal(t20171205.Unix(), l.CreateUnixTime)
	s.NotEqual(int64(0), l.ApproveUnixTime)
	s.Equal(int64(0), l.RejectUnixTime)
	s.Equal(accountingM.ApprovalTypeRevenue, l.Type)
	s.Equal(accountingM.BillType_BILL_REVENUE, l.BillType)

	// Make approval for mothly bill and get log
	billIDs := []string{"********-monthly-bill"}
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "JP").Return(time.UTC, nil).Twice()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockExecutorID).Return([]models.User{
		{
			UserID: mockExecutorID,
			OpenID: mockExecutorOpenID,
		},
	}, nil).Once()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "u14").Return([]models.User{
		{
			UserID: "u14",
			OpenID: "u11OpenID",
		},
	}, nil).Once()
	s.mockIntraFile.On("Upload", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*file.File")).Return([]string{mockApprovalFileKey}, nil).Once()
	apl, err := s.im.MakeApproval(mockCTX, mockExecutorID, "test-approval", "JP", billIDs)
	s.NoError(err)
	filter.ID = apl.ApprovalID
	filter.To = time.Now()
	logs, totalCount, nextCursor, err = s.im.GetApprovalLogs(mockCTX, filter, cursor, limit)
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", nextCursor)
	l = logs[0]
	s.Equal("JP", l.Region)
	s.Equal("test-approval", l.Subject)
	s.Equal(accountingM.ApprovalStatus_APPROVAL_SEALED, l.Status)
	s.Equal(1, l.BillsCount)
	s.Equal(float64(0), l.TotalAmount)
	s.Equal("JPY", l.LocalCurrency)
	s.Equal(float64(1000), l.TotalAmountInLocalCurrency)
	s.Equal(accountingM.ApprovalTypeContractMonthlyIncome, l.Type)
	s.Equal(accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME, l.BillType)
}

func (s *accountingTestSuite) TestGetApprovalLogsByIdWithWrongStatus() {
	s.createTestingData(s.im, s.dbx)
	// latest a5_recover_a4_approval is approved
	approvalID := "a5_recover_a4_approval"
	cursor := ""
	limit := 100
	filter := accountingM.GetApprovalLogsFilter{
		From:   time.Time{},
		To:     time.Now(),
		ID:     approvalID,
		Region: []string{},
		Status: []accountingM.ApprovalStatus{accountingM.ApprovalStatus_APPROVAL_EDITABLE},
	}
	logs, totalCount, nextCursor, err := s.im.GetApprovalLogs(mockCTX, filter, cursor, limit)
	s.NoError(err)
	s.Equal(0, len(logs))
	s.Equal(0, totalCount)
	s.Equal("", nextCursor)
}

func (s *accountingTestSuite) TestGetApprovalLogsByStatusAndTime() {
	s.createTestingData(s.im, s.dbx)
	s.im.comment = s.intraComment

	// get to 2017.11 and approved, sealed logs
	status := []accountingM.ApprovalStatus{accountingM.ApprovalStatus_APPROVAL_APPROVED, accountingM.ApprovalStatus_APPROVAL_SEALED}
	to := time.Date(2017, time.November, 30, 0, 0, 0, 0, time.UTC)
	cursor := ""
	limit := 100
	filter := accountingM.GetApprovalLogsFilter{
		From:   time.Time{},
		To:     to,
		ID:     "",
		Region: []string{},
		Status: status,
	}

	logs, totalCount, nextCursor, err := s.im.GetApprovalLogs(mockCTX, filter, cursor, limit)
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", nextCursor)

	limit = 1
	l := logs[0]
	s.Equal("a1_2017_approvaed_approval", l.ApprovalID)
	s.Equal("TW", l.Region)
	s.Equal("s1", l.Subject)
	s.Equal(accountingM.ApprovalStatus_APPROVAL_APPROVED, l.Status)
	s.Equal("2017-11", l.RateMonth)
	s.Equal(3, l.BillsCount)
	s.Equal(float64(60000), l.TotalAmount)
	s.Equal("approval.csv", l.FileKey)
	s.Equal(t20171105.Unix(), l.CreateUnixTime)
	s.NotEqual(int64(0), l.ApproveUnixTime)
	s.Equal(int64(0), l.RejectUnixTime)
	s.Equal(accountingM.ApprovalTypeRevenue, l.Type)
	s.Equal(accountingM.BillType_BILL_REVENUE, l.BillType)

	// get 2017.12 and approved, sealed logs
	from := time.Date(2017, time.December, 1, 0, 0, 0, 0, time.UTC)
	to = time.Date(2017, time.December, 31, 0, 0, 0, 0, time.UTC)
	filter.From = from
	filter.To = to

	logs, totalCount, nextCursor, err = s.im.GetApprovalLogs(mockCTX, filter, cursor, limit)
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(3, totalCount)
	s.Equal("1", nextCursor)
	l = logs[0]
	s.Equal("a2_2017_sealed_approval", l.ApprovalID)
	s.Equal("", l.RateMonth)
	s.Equal("JP", l.Region)
	s.Equal("s2", l.Subject)
	s.Equal(accountingM.ApprovalStatus_APPROVAL_SEALED, l.Status)
	s.Equal("", l.RateMonth)
	s.Equal(1, l.BillsCount)
	s.Equal(float64(50000), l.TotalAmount)
	s.Equal("approval.csv", l.FileKey)
	s.Equal(t20171205.Unix(), l.CreateUnixTime)
	s.Equal(int64(0), l.ApproveUnixTime)
	s.Equal(int64(0), l.RejectUnixTime)
	s.Equal(accountingM.ApprovalTypeRevenue, l.Type)
	s.Equal(accountingM.BillType_BILL_REVENUE, l.BillType)

	logs, totalCount, nextCursor, err = s.im.GetApprovalLogs(mockCTX, filter, nextCursor, limit)
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(3, totalCount)
	s.Equal("2", nextCursor)
	l = logs[0]
	s.Equal("a3_201712_approved_approval", l.ApprovalID)
	s.Equal("TW", l.Region)
	s.Equal("s3", l.Subject)
	s.Equal(accountingM.ApprovalStatus_APPROVAL_APPROVED, l.Status)
	s.Equal("2017-12", l.RateMonth)
	s.Equal(2, l.BillsCount)
	s.Equal(float64(130000), l.TotalAmount)
	s.Equal("approval.csv", l.FileKey)
	s.Equal(t20171205.Unix(), l.CreateUnixTime)
	s.NotEqual(int64(0), l.ApproveUnixTime)
	s.Equal(int64(0), l.RejectUnixTime)
	s.Equal(accountingM.ApprovalTypeRevenue, l.Type)
	s.Equal(accountingM.BillType_BILL_REVENUE, l.BillType)

	logs, totalCount, nextCursor, err = s.im.GetApprovalLogs(mockCTX, filter, nextCursor, limit)
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(3, totalCount)
	s.Equal("", nextCursor)
	l = logs[0]
	s.Equal("a5_recover_a4_approval", l.ApprovalID)
	s.Equal("TW", l.Region)
	s.Equal("s5", l.Subject)
	s.Equal(accountingM.ApprovalStatus_APPROVAL_APPROVED, l.Status)
	s.Equal("a5_rate", l.RateMonth)
	s.Equal(2, l.BillsCount)
	s.Equal(float64(170000), l.TotalAmount)
	s.Equal("approval.csv", l.FileKey)
	s.Equal(t20171205.Unix(), l.CreateUnixTime)
	s.NotEqual(int64(0), l.ApproveUnixTime)
	s.Equal(int64(0), l.RejectUnixTime)
	s.Equal(accountingM.ApprovalTypeRevenue, l.Type)
	s.Equal(accountingM.BillType_BILL_REVENUE, l.BillType)

	// get 2017.12 and rejected logs
	status = []accountingM.ApprovalStatus{accountingM.ApprovalStatus_APPROVAL_REJECTED}
	filter.Status = status
	limit = 100
	logs, totalCount, nextCursor, err = s.im.GetApprovalLogs(mockCTX, filter, cursor, limit)
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", nextCursor)
	l = logs[0]
	s.Equal("a4_201712_rejected_approval", l.ApprovalID)
	s.Equal("", l.RateMonth)
	s.Equal("TW", l.Region)
	s.Equal("S4", l.Subject)
	s.Equal(accountingM.ApprovalStatus_APPROVAL_REJECTED, l.Status)
	s.Equal("", l.RateMonth)
	s.Equal(2, l.BillsCount)
	s.Equal(float64(170000), l.TotalAmount)
	s.Equal("approval.csv", l.FileKey)
	s.Equal(t20171205.Unix(), l.CreateUnixTime)
	s.Equal(int64(0), l.ApproveUnixTime)
	s.NotEqual(int64(0), l.RejectUnixTime)
	s.Equal(accountingM.ApprovalTypeRevenue, l.Type)
	s.Equal(accountingM.BillType_BILL_REVENUE, l.BillType)
}

func (s *accountingTestSuite) TestGetApprovalLogsByRegionAndType() {
	s.createTestingData(s.im, s.dbx)
	s.im.comment = s.intraComment

	// get to 2017.11 and approved, sealed logs
	from := time.Date(2017, time.December, 1, 0, 0, 0, 0, time.UTC)
	to := time.Date(2017, time.December, 31, 0, 0, 0, 0, time.UTC)
	region := []string{"JP", "ID"}
	status := []accountingM.ApprovalStatus{}
	cursor := ""
	limit := 100
	filter := accountingM.GetApprovalLogsFilter{
		From:     from,
		To:       to,
		ID:       "",
		Region:   region,
		Status:   status,
		Type:     []accountingM.ApprovalType{accountingM.ApprovalTypeRevenue},
		BillType: []accountingM.BillType{accountingM.BillType_BILL_REVENUE},
	}

	logs, totalCount, nextCursor, err := s.im.GetApprovalLogs(mockCTX, filter, cursor, limit)
	s.NoError(err)
	s.Equal(1, len(logs))
	s.Equal(1, totalCount)
	s.Equal("", nextCursor)

	l := logs[0]
	s.Equal("a2_2017_sealed_approval", l.ApprovalID)
	s.Equal("", l.RateMonth)
	s.Equal("JP", l.Region)
	s.Equal("s2", l.Subject)
	s.Equal(accountingM.ApprovalStatus_APPROVAL_SEALED, l.Status)
	s.Equal("", l.RateMonth)
	s.Equal(1, l.BillsCount)
	s.Equal(float64(50000), l.TotalAmount)
	s.Equal("approval.csv", l.FileKey)
	s.Equal(t20171205.Unix(), l.CreateUnixTime)
	s.Equal(int64(0), l.ApproveUnixTime)
	s.Equal(int64(0), l.RejectUnixTime)
	s.Equal(accountingM.ApprovalTypeRevenue, l.Type)
	s.Equal(accountingM.BillType_BILL_REVENUE, l.BillType)
}

func (s *accountingTestSuite) TestGetApprovalLogsV2() {
	approval.TimeNow = func() time.Time { return (time.Date(2022, 8, 17, 0, 0, 0, 0, time.UTC)) }
	approvalUnixTime := mtime.MilliSecond(time.Date(2022, 8, 17, 0, 0, 0, 0, time.UTC)) / 1000
	toTime := time.Date(2022, time.August, 17, 0, 0, 0, 0, time.UTC)

	type test struct {
		desc            string
		filter          accountingM.GetApprovalLogsFilter
		offset          int
		limit           int
		expApprovalLogs []*accountingM.ResApprovalLog
		expTotalCount   int
		expErr          error
	}

	tests := []test{
		{
			desc: "succ with limit",
			filter: accountingM.GetApprovalLogsFilter{
				From:   time.Time{},
				To:     toTime,
				Region: []string{},
				Status: []accountingM.ApprovalStatus{},
			},
			offset: 0,
			limit:  2,
			expApprovalLogs: []*accountingM.ResApprovalLog{
				{
					ApprovalID:                 "wallet",
					RateMonth:                  "a5_rate",
					Subject:                    "s6",
					Status:                     accountingM.ApprovalStatus_APPROVAL_APPROVED,
					Type:                       accountingM.ApprovalTypeWallet,
					BillType:                   accountingM.BillType_BILL_WALLET,
					FileKey:                    "approval.csv",
					BillsCount:                 1,
					TotalAmount:                1000,
					LocalCurrency:              "USD",
					TotalAmountInLocalCurrency: 1000,
					Region:                     "TW",
					CreateUnixTime:             **********,
					ApproveUnixTime:            approvalUnixTime,
				},
				{
					ApprovalID:                 "wallet2",
					Subject:                    "s6",
					Status:                     accountingM.ApprovalStatus_APPROVAL_SEALED,
					Type:                       accountingM.ApprovalTypeWallet,
					BillType:                   accountingM.BillType_BILL_WALLET,
					FileKey:                    "approval.csv",
					BillsCount:                 1,
					TotalAmount:                2000,
					LocalCurrency:              "USD",
					TotalAmountInLocalCurrency: 2000,
					Region:                     "TW",
					CreateUnixTime:             **********,
				},
			},
			expTotalCount: 8,
			expErr:        nil,
		},
		{
			desc: "succ with offset and limit",
			filter: accountingM.GetApprovalLogsFilter{
				From:   time.Time{},
				To:     toTime,
				Region: []string{},
				Status: []accountingM.ApprovalStatus{},
			},
			offset: 2,
			limit:  2,
			expApprovalLogs: []*accountingM.ResApprovalLog{
				{
					ApprovalID:                 "agency_approval_id_1",
					RateMonth:                  "2019-03",
					Subject:                    "s3",
					Status:                     accountingM.ApprovalStatus_APPROVAL_SEALED,
					Type:                       accountingM.ApprovalTypeAgency,
					BillType:                   accountingM.BillType_BILL_REVENUE,
					FileKey:                    "approval.csv",
					BillsCount:                 1,
					TotalAmount:                80000,
					LocalCurrency:              "USD",
					TotalAmountInLocalCurrency: 80000,
					Region:                     "TW",
					CreateUnixTime:             **********,
				},
				{
					ApprovalID:                 "a2_2017_sealed_approval",
					Subject:                    "s2",
					Status:                     accountingM.ApprovalStatus_APPROVAL_SEALED,
					Type:                       accountingM.ApprovalTypeRevenue,
					BillType:                   accountingM.BillType_BILL_REVENUE,
					FileKey:                    "approval.csv",
					BillsCount:                 1,
					TotalAmount:                50000,
					LocalCurrency:              "JPY",
					TotalAmountInLocalCurrency: 5302500,
					Region:                     "JP",
					CreateUnixTime:             **********,
				},
			},
			expTotalCount: 8,
			expErr:        nil,
		},
		{
			desc: "succ with wildcard search for approvalID or subject",
			filter: accountingM.GetApprovalLogsFilter{
				From:                      time.Time{},
				To:                        toTime,
				Region:                    []string{},
				Status:                    []accountingM.ApprovalStatus{},
				WildcardSearchSubjectOrID: "S", // find out subject or approvalID that contains S(case sensitive)
			},
			offset: 0,
			limit:  2,
			expApprovalLogs: []*accountingM.ResApprovalLog{
				{
					ApprovalID:                 "a4_201712_rejected_approval",
					Subject:                    "S4",
					Status:                     accountingM.ApprovalStatus_APPROVAL_REJECTED,
					Type:                       accountingM.ApprovalTypeRevenue,
					BillType:                   accountingM.BillType_BILL_REVENUE,
					FileKey:                    "approval.csv",
					BillsCount:                 2,
					TotalAmount:                170000,
					LocalCurrency:              "USD",
					TotalAmountInLocalCurrency: 170000,
					Region:                     "TW",
					CreateUnixTime:             **********,
					RejectUnixTime:             approvalUnixTime,
				},
			},
			expTotalCount: 1,
			expErr:        nil,
		},
	}

	s.createTestingData(s.im, s.dbx)
	for _, t := range tests {
		logs, totalCount, err := s.im.GetApprovalLogsV2(mockCTX, t.filter, t.offset, t.limit)
		s.Equal(t.expErr, err, t.desc)
		s.Equal(t.expApprovalLogs, logs, t.desc)
		s.Equal(t.expTotalCount, totalCount, t.desc)
	}
}

func (s *accountingTestSuite) TestSettleBills() {
	mockRemittanceFileKey := "mockRemittanceFileKey"
	mockComment := "mockComment"
	s.im.payout = s.mockPayout
	s.im.bill = s.mockBill

	mockPayoutBillAmount := usd.ToNanoUSD(100)
	mockPayoutDeduction := 1.0
	mockPayoutFee := 10.0
	mockPayoutPaidAmount := 89.0
	// test for using local timezone
	mockTime := time.Date(2021, time.February, 28, 23, 30, 0, 0, time.UTC) // = 2021/3/1 7:30 in TW = 2021/3/1 8:30 in JP
	mockTimeMillis := mtime.MilliSecond(mockTime)
	mockUpdateTime := time.Date(2021, time.March, 31, 10, 30, 0, 0, time.UTC)
	mockUpdateTimeMillis := mtime.MilliSecond(mockUpdateTime)
	mockMessage := "[17LIVE]2021-02月分報酬振込通知\n請求ID：%s\n\n▼配信者情報\n本名：%s\n適格事業者番号：B123456789\n\n▼配信レポート\n税抜金額：91 円\n適用税率：10％\n消費税額：9 円\n税込金額：100 円\n源泉徴収税額：30 円\n振込手数料：10 円\n支払合計金額：89 円\n\n※本メッセージに記載されている報酬に関する情報の第三者への開示は、当社との間で約定された秘密保持義務契約に違反します。本規定は、契約終了後も効力を有します。"
	mockOverseasMessage := "[17LIVE]2021-02月分報酬振込通知\n請求ID：%s\n\n▼配信者情報\n本名：%s\n\n▼配信レポート\n・報酬金額：99 円\n・振込手数料：10 円\n・支払合計金額：89 円\n\n※本メッセージに記載されている報酬に関する情報の第三者への開示は、当社との間で約定された秘密保持義務契約に違反します。本規定は、契約終了後も効力を有します。"
	mockPayoutDeductionDetail := map[accountingM.DeductionCategory]float64{
		accountingM.DeductionCategory_INCOME_TAX:      30,
		accountingM.DeductionCategory_CONSUMPTION_TAX: 10,
	}
	mockBills := []*bill.Element{
		{
			DBRow: bill.DBRow{
				ID:                    1,
				BillID:                "bill-1",
				Region:                "TW",
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                "userID1",
				Amount:                mockPayoutBillAmount,
				PayoutType:            accountingM.PayoutType_BANK_ESUN,
				PayoutAccount:         "userID1TBBAccount",
				PayoutAccountVerified: true,
				ExecutorID:            mockExecutorID,
				DealingID:             "dealingID1",
				TimeMillis:            mockTimeMillis,
				CreateTimeMillis:      mtime.MilliSecond(time.Date(2021, time.March, 31, 0, 30, 0, 0, time.UTC)),
			},
		},
		{
			DBRow: bill.DBRow{
				ID:                    2,
				BillID:                "bill-2",
				Region:                "JP",
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                "userID2",
				Amount:                mockPayoutBillAmount,
				PayeeName:             "user2",
				PayoutType:            accountingM.PayoutType_BANK_SMBC,
				PayoutAccount:         "userID1TBBAccount",
				PayoutAccountVerified: true,
				PayoutBankName:        "test-smbc",
				PayoutBankCode:        "test-123",
				PayoutBankBranchName:  "test-smbc-br",
				PayoutBankBranchCode:  "test-smbc-br-123",
				ExecutorID:            mockExecutorID,
				DealingID:             "dealingID1",
				TimeMillis:            mockTimeMillis,
				CreateTimeMillis:      mockTimeMillis,
				PayToAgency:           false,
				LocalCurrency:         "JPY",
				AmountInLocalCurrency: 100,
				InvoiceNumber:         "B123456789",
			},
		},
		{
			DBRow: bill.DBRow{
				ID:                    3,
				BillID:                "bill-3",
				Region:                "TW",
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                "userID3",
				Amount:                mockPayoutBillAmount,
				PayoutType:            accountingM.PayoutType_BANK_SMBC,
				PayoutAccount:         "userID1TBBAccount",
				PayoutAccountVerified: true,
				PayoutBankName:        "test-smbc",
				PayoutBankCode:        "test-123",
				PayoutBankBranchName:  "test-smbc-br",
				PayoutBankBranchCode:  "test-smbc-br-123",
				ExecutorID:            mockExecutorID,
				DealingID:             "dealingID1",
				TimeMillis:            mockTimeMillis,
				CreateTimeMillis:      mockTimeMillis,
				PayToAgency:           true,
			},
		},
		{
			DBRow: bill.DBRow{
				ID:                    4,
				BillID:                "bill-4",
				Type:                  accountingM.BillType_BILL_WALLET,
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                "userID4",
				Amount:                mockPayoutBillAmount,
				PayoutType:            accountingM.PayoutType_BANK_TBB,
				PayoutAccount:         "userID1TBBAccount",
				PayoutAccountVerified: true,
				PayoutBankName:        "test-smbc",
				PayoutBankCode:        "test-123",
				PayoutBankBranchName:  "test-smbc-br",
				PayoutBankBranchCode:  "test-smbc-br-123",
				ExecutorID:            mockExecutorID,
				DealingID:             "dealingID1",
				TimeMillis:            mockTimeMillis,
				CreateTimeMillis:      mockTimeMillis,
				PayToAgency:           false,
				Region:                "TW",
				LocalCurrency:         "TWD",
				AmountInLocalCurrency: 100,
			},
		},
		{
			DBRow: bill.DBRow{
				ID:                    5,
				BillID:                "bill-5",
				Type:                  accountingM.BillType_BILL_WALLET,
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                "userID5",
				Amount:                mockPayoutBillAmount,
				PayoutType:            accountingM.PayoutType_BANK_TBB,
				PayoutAccount:         "userID1TBBAccount",
				PayoutAccountVerified: true,
				PayoutBankName:        "test-smbc",
				PayoutBankCode:        "test-123",
				PayoutBankBranchName:  "test-smbc-br",
				PayoutBankBranchCode:  "test-smbc-br-123",
				ExecutorID:            mockExecutorID,
				DealingID:             "dealingID1",
				TimeMillis:            mockTimeMillis,
				CreateTimeMillis:      mockTimeMillis,
				PayToAgency:           false,
				Region:                "TW",
				LocalCurrency:         "TWD",
				AmountInLocalCurrency: 100,
			},
		},
		{
			DBRow: bill.DBRow{
				ID:                    6,
				BillID:                "bill-6",
				Region:                "JP",
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                "userID6",
				Amount:                mockPayoutBillAmount,
				PayeeType:             payoutAccountM.PayeeType_USER_FOREIGN,
				PayoutType:            accountingM.PayoutType_BANK_SMBC,
				PayoutAccount:         "userID1TBBAccount",
				PayoutAccountVerified: true,
				PayoutBankName:        "test-smbc",
				PayoutBankCode:        "test-123",
				PayoutBankBranchName:  "test-smbc-br",
				PayoutBankBranchCode:  "test-smbc-br-123",
				ExecutorID:            mockExecutorID,
				DealingID:             "dealingID1",
				TimeMillis:            mockTimeMillis,
				CreateTimeMillis:      mockTimeMillis,
				PayToAgency:           false,
			},
		},
		{
			DBRow: bill.DBRow{
				ID:                    7,
				BillID:                "bill-7",
				Region:                "JP",
				Type:                  accountingM.BillType_BILL_WALLET_REVENUE,
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                "userID7",
				Amount:                mockPayoutBillAmount,
				PayeeType:             payoutAccountM.PayeeType_USER_FOREIGN,
				PayoutType:            accountingM.PayoutType_BANK_SMBC,
				PayoutAccount:         "userID1TBBAccount",
				PayoutAccountVerified: true,
				PayoutBankName:        "test-smbc",
				PayoutBankCode:        "test-123",
				PayoutBankBranchName:  "test-smbc-br",
				PayoutBankBranchCode:  "test-smbc-br-123",
				ExecutorID:            mockExecutorID,
				DealingID:             "dealingID1",
				TimeMillis:            mockTimeMillis,
				CreateTimeMillis:      mockTimeMillis,
				PayToAgency:           false,
			},
		},
	}

	mockNewPayout := []*payout.DBRow{}
	billIDs := []string{}
	mockGetPlainUserInput := []interface{}{mock.AnythingOfType("ctx.CTX")}
	mockGetPlainUserRet := []models.User{}
	mockSendMsg := []officialM.SystemMsgParam{}
	for _, b := range mockBills {
		mockNewPayout = append(mockNewPayout, &payout.DBRow{
			TimeMillis:       mockUpdateTimeMillis,
			GroupID:          mockPayoutGroupID,
			CreatorID:        mockExecutorID,
			BalancerID:       mockExecutorID,
			BillID:           b.BillID,
			AgencyType:       b.PayoutType,
			AgencyAccount:    b.PayoutAccount,
			AgencyReceiptID:  "receiptID",
			Currency:         "USD",
			Rate:             float64(1.0),
			AmountBill:       usd.ToUSD(mockPayoutBillAmount),
			FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_17,
			Fee:              mockPayoutFee,
			AmountPaid:       mockPayoutPaidAmount,
			AmountPayable:    0,
			RemittanceURL:    mockRemittanceFileKey,
			Status:           accountingM.PayoutStatus_PAYOUT_SUCCESS,
			UpdateTimeMillis: mockUpdateTimeMillis,
			PayoutTimeMillis: mockUpdateTimeMillis,
			Deduction:        mockPayoutDeduction,
			DeductionDetail:  mockPayoutDeductionDetail,
		})
		billIDs = append(billIDs, b.BillID)
		mockGetPlainUserInput = append(mockGetPlainUserInput, b.UserID)
		mockGetPlainUserRet = append(mockGetPlainUserRet, models.User{
			UserID: b.UserID,
			OpenID: "openID-" + b.UserID,
		})

		if b.PayoutType == accountingM.PayoutType_BANK_SMBC && !b.PayToAgency && !b.Type.IsWalletType() {
			if b.PayeeType == payoutAccountM.PayeeType_USER_FOREIGN {
				mockSendMsg = append(mockSendMsg, officialM.SystemMsgParam{
					ReceiveUserID: b.UserID,
					Message:       fmt.Sprintf(mockOverseasMessage, b.BillID, b.StreamerRealName),
					Picture:       "",
					ContentType:   chatM.Message_TEXT,
				})
			} else {
				mockSendMsg = append(mockSendMsg, officialM.SystemMsgParam{
					ReceiveUserID: b.UserID,
					Message:       fmt.Sprintf(mockMessage, b.BillID, b.StreamerRealName),
					Picture:       "",
					ContentType:   chatM.Message_TEXT,
				})
			}
		}
		if b.Type.IsWalletType() {
			s.mockOutbox.On("Publish", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), subTopicModel.SubTopics[subTopicModel.KeyBBRichBillStatusChanged].Topic, &accountingM.BillChangedInfo{
				BillID:          b.BillID,
				Status:          b.Status,
				PayoutStatus:    accountingM.PayoutStatus_PAYOUT_SUCCESS,
				TaxAmount:       "1",
				PayoutFeeAmount: "10",
				Remark:          mockComment,
				RefundFeeAmount: "0",
				IncomeTax:       "30",
				ConsumptionTax:  "10",
			}).Return(nil).Once()
		}
	}

	jploc, _ := time.LoadLocation("Asia/Tokyo")
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "JP").Return(jploc, nil)
	s.mockIntraFile.On("Exist", mock.Anything, mock.Anything).Return(true, nil).Once()
	s.mockPayout.On("SettleWithRemittance", mockCTX, mockExecutorID, mock.Anything, mock.Anything).Return(mockNewPayout, mockBills, nil).Once()
	s.mockUser.On("GetPlainUsers", mockGetPlainUserInput...).Return(mockGetPlainUserRet, nil).Once()
	if len(mockSendMsg) > 0 {
		s.mockOfficialMsg.On("SendMulti", mock.AnythingOfType("ctx.CTX"), mockSendMsg, mock.Anything, mock.Anything).Return(nil).Once()
	}
	s.mockComment.On("BatchPost", mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_PAYOUT_SETTLE_COMMENT), billIDs, mock.Anything).Return(nil, nil)
	s.mockFuncs.On("pushBillChangedMessage").Return(true)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	logs, err := s.im.SettleBills(mockCTX, mockExecutorID, mockRemittanceFileKey, billIDs, mockComment)
	s.NoError(err)
	s.Equal(len(mockNewPayout), len(logs))
	for i, log := range logs {
		b := mockBills[i]
		s.Equal(b.CreateTimeMillis/1000, log.CreateUnixTime)
		s.Equal(int64(0), log.ApprovedUnixTime)
		s.Equal(b.BillID, log.BillID)
		s.Equal(b.Type, log.Type)
		s.Equal(b.Status, log.Status)
		s.Equal(b.UserID, log.UserID)
		s.Equal(usd.ToUSD(b.Amount), log.Amount)
		s.Equal(b.Region, log.Region)
		s.Equal(b.PayoutType, log.PayoutType)
		s.Equal(b.PayoutAccount, log.PayoutAccount)
		s.Equal(b.PayoutAccountVerified, log.PayoutAccountVerified)
		s.Equal(accountingM.PayoutStatus_PAYOUT_SUCCESS, log.PayoutStatus)
		s.Equal(mockUpdateTimeMillis/1000, log.PaidUnixTime)
		s.Equal(mockUpdateTimeMillis/1000, log.CompletedUnixTime)
		s.Equal(int64(0), log.RefundedUnixTime)
		s.Equal(mockUpdateTimeMillis/1000, log.UpdatedUnixTime)
		s.Equal("USD", log.PayoutCurrency)
		s.Equal(float64(1.0), log.PayoutRate)
		s.Equal(float64(10), log.PayoutFee)
		s.Equal(float64(89), log.PaidAmount)
		s.Equal(float64(0), log.UnpaidAmount)
		s.Equal(b.UserType, log.UserType)
		s.Equal(mockPayoutGroupID, log.PayID)
		s.Equal(mockExecutorID, log.BalancerID)
		s.Equal(b.UserID, log.Payee.UserID)
		s.Equal(payoutAccountM.FeeOwnerType_FEE_OWNER_17, log.FeeOwner)
		s.Equal(mockRemittanceFileKey, log.RemittanceFileKey)
	}
}

func (s *accountingTestSuite) TestSettleBillsRemittanceNotExist() {
	billIDs := []string{"1"}
	mockRemittanceFileKey := "mockRemittanceFileKey"
	s.mockIntraFile.On("Exist", mock.Anything, mock.Anything).Return(false, errors.New("Exist error")).Once()
	_, err := s.im.SettleBills(mockCTX, mockExecutorID, mockRemittanceFileKey, billIDs, "")
	s.EqualError(err, "remittance fileKey mockRemittanceFileKey not exists(false)(err:Exist error)")
}

func (s *accountingTestSuite) TestSettleBillWithFee() {
	s.im.payout = s.mockPayout
	s.im.bill = s.mockBill
	mockTimeMillis := mtime.MilliSecond(time.Now())
	mockUserID := "user-1"
	mockPayoutBillAmount := usd.ToNanoUSD(100)
	mockRemittanceFileKey := "mockRemittanceFileKey"
	mockComment := "test-comment"
	s.mockFuncs.On("pushBillChangedMessage").Return(true)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	cases := []*struct {
		desc             string
		billID           string
		feeOwner         payoutAccountM.FeeOwnerType
		fee              float64
		paidAmount       float64
		mockBill         *bill.Element
		mockPayout       *payout.DBRow
		mockRemittance   *payout.Remittance
		expectPaidAmount float64
	}{
		{
			desc:       "fee and paid amount",
			billID:     "1",
			feeOwner:   payoutAccountM.FeeOwnerType_FEE_OWNER_17,
			fee:        10,
			paidAmount: 3000,
			mockBill: &bill.Element{
				DBRow: bill.DBRow{
					ID:                    1,
					BillID:                "1",
					Status:                accountingM.BillStatus_BILL_PAYOUT,
					UserID:                mockUserID,
					Amount:                mockPayoutBillAmount,
					PayoutType:            accountingM.PayoutType_BANK_ESUN,
					PayoutAccount:         "userID1TBBAccount",
					PayoutAccountVerified: true,
					ExecutorID:            mockExecutorID,
					DealingID:             "dealingID1",
					TimeMillis:            mockTimeMillis,
					CreateTimeMillis:      mockTimeMillis,
				},
			},
			mockPayout: &payout.DBRow{
				ID:               1,
				TimeMillis:       mockTimeMillis,
				GroupID:          mockPayoutGroupID,
				CreatorID:        mockExecutorID,
				BalancerID:       mockExecutorID,
				BillID:           "1",
				AgencyType:       accountingM.PayoutType_BANK_ESUN,
				AgencyReceiptID:  "receiptID",
				Currency:         "TWD",
				Rate:             float64(30.0),
				AmountBill:       float64(3000),
				FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_17,
				Fee:              10,
				AmountPaid:       3000,
				AmountPayable:    0,
				Deduction:        300,
				RemittanceURL:    mockRemittanceFileKey,
				Status:           accountingM.PayoutStatus_PAYOUT_SUCCESS,
				UpdateTimeMillis: mockTimeMillis,
				PayoutTimeMillis: mockTimeMillis,
			},
			mockRemittance: &payout.Remittance{
				FileKey: mockRemittanceFileKey,
				Receipts: []*payout.RemittanceReceipt{
					{
						BillID: "1",
						WithFee: &payout.RemittanceReceiptWithFee{
							FeeOwner: payoutAccountM.FeeOwnerType_FEE_OWNER_17,
							Fee:      10,
						},
						WithPaidAmount: &payout.RemittanceReceiptWithPaidAmount{
							Amount: 3000,
						},
					},
				},
			},
		},
	}
	for _, c := range cases {
		s.mockIntraFile.On("Exist", mock.Anything, mock.Anything).Return(true, nil).Once()
		s.mockPayout.On("SettleWithRemittance", mock.AnythingOfType("ctx.CTX"), mockExecutorID, c.mockRemittance, mock.Anything).Return([]*payout.DBRow{c.mockPayout}, []*bill.Element{c.mockBill}, nil).Once()
		s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), c.mockBill.UserID).Return([]models.User{
			{
				UserID: c.mockBill.UserID,
			},
		}, nil).Once()
		s.mockComment.On("BatchPost", mock.AnythingOfType("ctx.CTX"), mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_PAYOUT_SETTLE_COMMENT), []string{c.mockBill.BillID}, []string{mockComment}).Return(nil, nil).Once()
		log, err := s.im.SettleBillWithFee(mockCTX, mockExecutorID, mockRemittanceFileKey, c.mockBill.BillID, c.feeOwner, c.fee, c.paidAmount, mockComment)
		s.NoError(err)
		s.Equal(mockTimeMillis/1000, log.CreateUnixTime)
		s.Equal(int64(0), log.ApprovedUnixTime)
		s.Equal(c.mockBill.BillID, log.BillID)
		s.Equal(c.mockBill.Status, log.Status)
		s.Equal(c.mockBill.UserID, log.UserID)
		s.Equal(usd.ToUSD(c.mockBill.Amount), log.Amount)
		s.Equal(c.mockBill.Region, log.Region)
		s.Equal(c.mockBill.PayoutType, log.PayoutType)
		s.Equal(c.mockBill.PayoutAccount, log.PayoutAccount)
		s.Equal(c.mockBill.PayoutAccountVerified, log.PayoutAccountVerified)
		s.Equal(accountingM.PayoutStatus_PAYOUT_SUCCESS, log.PayoutStatus)
		s.Equal(mockTimeMillis/1000, log.PaidUnixTime)
		s.Equal(mockTimeMillis/1000, log.CompletedUnixTime)
		s.Equal(int64(0), log.RefundedUnixTime)
		s.Equal(mockTimeMillis/1000, log.UpdatedUnixTime)
		s.Equal("TWD", log.PayoutCurrency)
		s.Equal(c.mockPayout.Rate, log.PayoutRate)
		s.Equal(c.mockPayout.Fee, log.PayoutFee)
		s.Equal(c.mockPayout.AmountPaid, log.PaidAmount)
		s.Equal(float64(0), log.UnpaidAmount)
		s.Equal(float64(300), log.Deduction)
		s.Equal(c.mockBill.UserType, log.UserType)
		s.Equal(mockExecutorID, log.BalancerID)
		s.Equal(c.mockBill.UserID, log.Payee.UserID)
		s.Equal(payoutAccountM.FeeOwnerType_FEE_OWNER_17, log.FeeOwner)
		s.Equal(mockRemittanceFileKey, log.RemittanceFileKey)
	}
}

func (s *accountingTestSuite) TestSettleBillWithFeeRemittanceNotExist() {
	billID := "1"
	mockRemittanceFileKey := "mockRemittanceFileKey"
	feeOwner := payoutAccountM.FeeOwnerType_FEE_OWNER_17
	fee := 100.0
	s.mockIntraFile.On("Exist", mock.Anything, mock.Anything).Return(false, errors.New("Exist error")).Once()
	_, err := s.im.SettleBillWithFee(mockCTX, mockExecutorID, mockRemittanceFileKey, billID, feeOwner, fee, 0, "")
	s.EqualError(err, "remittance fileKey mockRemittanceFileKey not exists(false)(err:Exist error)")
}

func (s *accountingTestSuite) TestUpdateBillReceipt() {
	mockRemittanceFileKey := "mockRemittanceFileKey"
	mockComment := "mockComment"
	payoutTime := mtime.MilliSecond(time.Date(2018, 1, 1, 0, 0, 0, 0, time.UTC))
	now := mtime.MilliSecond(time.Now())
	mockUserID := "user-1"
	mockPayoutBillAmount := usd.ToNanoUSD(100)
	s.im.payout = s.mockPayout
	s.im.bill = s.mockBill

	cases := []*struct {
		desc              string
		billIDs           []string
		remittanceFileKey string
		mockPayouts       []*payout.DBRow
		mockBills         []*bill.Element
		expErr            error
		expLogs           []*accountingM.ResBillLog
	}{
		{
			desc:              "succ",
			billIDs:           []string{"1"},
			remittanceFileKey: mockRemittanceFileKey,
			mockPayouts: []*payout.DBRow{
				{
					BillID:           "bill-1",
					AgencyType:       accountingM.PayoutType_OFFLINE,
					Currency:         "USD",
					AmountBill:       100,
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_UNKNOWN_TYPE,
					Fee:              0,
					AmountPaid:       100,
					AmountPayable:    0,
					Status:           accountingM.PayoutStatus_PAYOUT_SUCCESS,
					RemittanceURL:    mockRemittanceFileKey,
					UpdateTimeMillis: now,
					PayoutTimeMillis: payoutTime,
				},
				{
					BillID:           "bill-2",
					AgencyType:       accountingM.PayoutType_OFFLINE,
					Currency:         "USD",
					AmountBill:       100,
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_UNKNOWN_TYPE,
					Fee:              0,
					AmountPaid:       100,
					AmountPayable:    0,
					Status:           accountingM.PayoutStatus_PAYOUT_SUCCESS,
					RemittanceURL:    mockRemittanceFileKey,
					UpdateTimeMillis: now,
					PayoutTimeMillis: payoutTime,
				},
			},
			mockBills: []*bill.Element{
				{
					DBRow: bill.DBRow{
						ID:                    1,
						BillID:                "bill-1",
						Status:                accountingM.BillStatus_BILL_PAYOUT,
						UserID:                mockUserID,
						Amount:                mockPayoutBillAmount,
						PayoutType:            accountingM.PayoutType_BANK_ESUN,
						PayoutAccount:         "userID1TBBAccount",
						PayoutAccountVerified: true,
						ExecutorID:            mockExecutorID,
						DealingID:             "dealingID1",
						TimeMillis:            payoutTime,
						CreateTimeMillis:      payoutTime,
					},
				},
				{
					DBRow: bill.DBRow{
						ID:                    1,
						BillID:                "bill-2",
						Status:                accountingM.BillStatus_BILL_PAYOUT,
						UserID:                mockUserID,
						Amount:                mockPayoutBillAmount,
						PayoutType:            accountingM.PayoutType_BANK_ESUN,
						PayoutAccount:         "userID1TBBAccount",
						PayoutAccountVerified: true,
						ExecutorID:            mockExecutorID,
						DealingID:             "dealingID1",
						TimeMillis:            payoutTime,
						CreateTimeMillis:      payoutTime,
					},
				},
			},
			expErr:  nil,
			expLogs: []*accountingM.ResBillLog{},
		},
	}

	for _, c := range cases {
		mockUsers := []models.User{}
		uniqueUserID := map[string]bool{}
		mockUserIDs := []interface{}{}
		mockBillIDs := []string{}
		for _, b := range c.mockBills {
			mockBillIDs = append(mockBillIDs, b.BillID)
			if uniqueUserID[b.UserID] {
				continue
			}
			uniqueUserID[b.UserID] = true
			mockUsers = append(mockUsers, models.User{UserID: b.UserID})
			mockUserIDs = append(mockUserIDs, b.UserID)
		}
		s.mockIntraFile.On("Exist", mock.Anything, mock.Anything).Return(true, nil).Once()

		s.mockPayout.On("UpdateRemittance", mock.Anything, mock.Anything, mock.Anything).Return(c.mockPayouts, c.mockBills, nil).Once()
		args := append([]interface{}{mock.Anything}, mockUserIDs...)
		s.mockUser.On("GetPlainUsers", args...).Return(mockUsers, nil).Once()
		s.mockComment.On("PostTx", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
		s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

		logs, err := s.im.UpdateBillReceipt(mockCTX, mockExecutorID, c.remittanceFileKey, mockBillIDs, mockComment)
		s.Equal(c.expErr, err, c.desc)
		if c.expErr != nil {
			continue
		}
		for i, log := range logs {
			s.Equal(c.mockBills[i].BillID, log.BillID, c.desc)
			s.Equal(c.mockPayouts[i].Status, log.PayoutStatus, c.desc)
			s.Equal(c.mockPayouts[i].PayoutTimeMillis/1000, log.PaidUnixTime, c.desc)
			s.Equal(c.mockPayouts[i].UpdateTimeMillis/1000, log.CompletedUnixTime, c.desc)
			s.Equal(c.mockPayouts[i].UpdateTimeMillis/1000, log.UpdatedUnixTime, c.desc)
			s.Equal(c.mockPayouts[i].GroupID, log.PayID, c.desc)
			s.Equal(c.mockPayouts[i].FeeOwner, log.FeeOwner, c.desc)
			s.Equal(c.mockPayouts[i].Currency, log.PayoutCurrency, c.desc)
			s.Equal(c.mockPayouts[i].Rate, log.PayoutRate, c.desc)
			s.Equal(c.mockPayouts[i].Fee, log.PayoutFee, c.desc)
			s.Equal(c.mockPayouts[i].AmountPaid, log.PaidAmount, c.desc)
			s.Equal(c.mockPayouts[i].AmountPayable, log.UnpaidAmount, c.desc)
			s.Equal(c.mockPayouts[i].Deduction, log.Deduction, c.desc)
			s.Equal(c.mockPayouts[i].RemittanceURL, log.RemittanceFileKey, c.desc)
			s.Equal(c.mockPayouts[i].BalancerID, log.BalancerID, c.desc)
		}
	}
}

func (s *accountingTestSuite) TestLock() {
	s.createTestingData(s.im, s.dbx)

	bills := []string{"lock-test-1", "lock-test-2"}
	userIDs := []string{"u10", "u10"}
	s.mockUser.On("GetPlainUsers", mockCTX, userIDs[0]).Return([]models.User{
		{
			UserID: "u10",
			OpenID: "u10",
		},
	}, nil).Once()
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)
	// Lock
	logs, err := s.im.LockBills(mockCTX, mockExecutorID, bills)
	s.NoError(err)
	s.Equal(2, len(logs))
	for i, l := range logs {
		s.Equal(int64(0), l.ApprovedUnixTime)
		s.Equal(bills[i], l.BillID)
		s.Equal(accountingM.BillStatus_BILL_LOCKED, l.Status)
		s.Equal("u10", l.UserID)
		s.Equal("TW", l.Region)
		s.Equal(float64(90000), l.Amount)
		s.Equal(accountingM.PayoutType_PAYPAL, l.PayoutType)
		s.Equal("<EMAIL>", l.PayoutAccount)
		s.Equal(true, l.PayoutAccountVerified)
		s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, l.PayoutStatus)
		s.Equal("u10", l.Payee.UserID)
		s.Equal("u10", l.Payee.OpenID)
		s.Equal("USD", l.LocalCurrency)
		s.Equal(90000.0, l.AmountInLocalCurrency)
		s.Equal(payoutAccountM.FeeOwnerType_FEE_OWNER_UNKNOWN_TYPE, l.FeeOwner)
		s.Equal("", l.RemittanceFileKey)
		s.Equal(userM.UserType_USER, l.UserType)
	}
}

func (s *accountingTestSuite) TestUnlock() {
	s.createTestingData(s.im, s.dbx)

	bills := []string{"lock-test-1", "lock-test-2"}
	userIDs := []string{"u10", "u10"}
	s.mockUser.On("GetPlainUsers", mockCTX, userIDs[0]).Return([]models.User{
		{
			UserID: "u10",
			OpenID: "u10",
		},
	}, nil).Twice()
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)
	// Lock
	_, err := s.im.LockBills(mockCTX, mockExecutorID, bills)
	s.NoError(err)
	// Unlock
	logs, err := s.im.UnlockBills(mockCTX, mockExecutorID, bills)
	s.NoError(err)
	s.Equal(2, len(logs))
	for i, l := range logs {
		s.Equal(int64(0), l.ApprovedUnixTime)
		s.Equal(bills[i], l.BillID)
		s.Equal(accountingM.BillStatus_BILL_ORPHAN, l.Status)
		s.Equal("u10", l.UserID)
		s.Equal("TW", l.Region)
		s.Equal(float64(90000), l.Amount)
		s.Equal(accountingM.PayoutType_PAYPAL, l.PayoutType)
		s.Equal("<EMAIL>", l.PayoutAccount)
		s.Equal(true, l.PayoutAccountVerified)
		s.Equal(accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS, l.PayoutStatus)
		s.Equal("u10", l.Payee.UserID)
		s.Equal("u10", l.Payee.OpenID)
		s.Equal("USD", l.LocalCurrency)
		s.Equal(90000.0, l.AmountInLocalCurrency)
		s.Equal(payoutAccountM.FeeOwnerType_FEE_OWNER_UNKNOWN_TYPE, l.FeeOwner)
		s.Equal("", l.RemittanceFileKey)
		s.Equal(userM.UserType_USER, l.UserType)
	}
}

func (s *accountingTestSuite) TestLockError() {
	s.createTestingData(s.im, s.dbx)
	bills := []string{"lock-test-1"}
	// UpdateStatus error: invalid billID
	billIDs := []string{"fake-bill-id"}
	logs, err := s.im.LockBills(mockCTX, mockExecutorID, billIDs)
	s.EqualError(err, fmt.Sprintf("GetByBillIDs part of bill logs (%v) not exist", billIDs))
	s.Equal(0, len(logs))
	// UpdateStatus error: invalid status
	err = mdb.Transactx(mockCTX, s.dbx, func(tx *sqlx.Tx) error {
		_, err := s.im.bill.UpdateStatus(mockCTX, tx, mockExecutorID, []string{bills[0]}, accountingM.BillStatus_BILL_SELECTED)
		s.NoError(err)
		return nil
	})
	s.NoError(err)
	logs, err = s.im.LockBills(mockCTX, mockExecutorID, bills)
	s.EqualError(err, fmt.Sprintf("Invalid status transition billID:%s fromStatus:%+v toStatus:%+v", bills[0], accountingM.BillStatus_BILL_SELECTED, accountingM.BillStatus_BILL_LOCKED))
	s.Equal(0, len(logs))
}

func (s *accountingTestSuite) TestUnlockError() {
	s.createTestingData(s.im, s.dbx)
	bills := []string{"lock-test-1"}
	// UpdateStatus error: invalid billID
	billIDs := []string{"fake-bill-id"}
	logs, err := s.im.UnlockBills(mockCTX, mockExecutorID, billIDs)
	s.EqualError(err, fmt.Sprintf("GetByBillIDs part of bill logs (%v) not exist", billIDs))
	s.Equal(0, len(logs))

	// UpdateStatus error: invalid status
	logs, err = s.im.UnlockBills(mockCTX, mockExecutorID, bills)
	s.EqualError(err, "bill status not in locked")
	s.Equal(0, len(logs))
}

func (s *accountingTestSuite) TestVoid() {
	billIDs := []string{"test-bill-1"}
	mockBill := &mBill.Bill{}
	userID := "user"
	user := models.User{UserID: userID}
	mockBill.On("Void", mock.AnythingOfType("ctx.CTX"), mockExecutorID, billIDs).Return([]*bill.Element{{
		DBRow: bill.DBRow{
			BillID: "test-bill-1",
			UserID: userID,
		},
	}}, nil).Once()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), userID).Return([]models.User{user}, nil).Once()
	s.mockFuncs.On("pushBillChangedMessage").Return(true)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	s.im.bill = mockBill
	err := s.im.VoidBills(mockCTX, mockExecutorID, billIDs)
	s.NoError(err)
}

func (s *accountingTestSuite) TestBBRichRemittanceStatusChangedTask() {
	s.im.payout = s.mockPayout
	mockNow := time.Date(2021, time.May, 4, 0, 0, 0, 0, time.UTC)
	mockNowMillis := mtime.MilliSecond(mockNow)

	type testInfo struct {
		desc       string
		remittance interface{} //*remittanceM.Remittance
		mockFunc   func(info *testInfo)
		expErr     error
	}
	cases := []*testInfo{
		{
			desc: "mormal case",
			remittance: &remittanceM.Remittance{
				RemittanceID:      "remittance-id-1",
				Remitter:          accountingM.BillType_BILL_REVENUE.ToRemitter(),
				RemitterID:        "bill-id-1",
				UserID:            "user-id-1",
				Status:            remittanceM.StatusClaimed,
				Amount:            usd.ToNanoUSD(1000),
				ActualAmount:      usd.ToNanoUSD(1000),
				WalletType:        bbfuM.WalletType_TWD_RevenueTW,
				CreateTimeMillis:  0,
				DepositTimeMillis: mockNowMillis,
			},
			mockFunc: func(t *testInfo) {
				remittance := t.remittance.(*remittanceM.Remittance)
				s.mockPayout.On("SettleWithBBRichRemittance", mock.AnythingOfType("ctx.CTX"), []*remittanceM.Remittance{remittance}).Return(
					[]*payout.DBRow{}, []*bill.Element{}, nil,
				).Once()
			},
			expErr: nil,
		},
		{
			desc:       "unmarshal failed",
			remittance: &payout.DBRow{},
			expErr:     fmt.Errorf("invalid remittance"),
		},
		{
			desc: "ignore other status",
			remittance: &remittanceM.Remittance{
				RemittanceID:      "remittance-id-1",
				Remitter:          accountingM.BillType_BILL_REVENUE.ToRemitter(),
				RemitterID:        "bill-id-1",
				UserID:            "user-id-1",
				Status:            remittanceM.StatusClaimable,
				Amount:            usd.ToNanoUSD(1000),
				ActualAmount:      usd.ToNanoUSD(1000),
				WalletType:        bbfuM.WalletType_TWD_RevenueTW,
				CreateTimeMillis:  0,
				DepositTimeMillis: mockNowMillis,
			},
			expErr: nil,
		},
	}

	for _, c := range cases {
		if c.mockFunc != nil {
			c.mockFunc(c)
		}

		data, err := json.Marshal(c.remittance)
		s.Require().NoError(err)
		err = s.im.BBRichRemittanceStatusChangedTask(mockCTX, data, queueModel.CallbackOption{PublishTime: mockNow})
		s.Require().Equal(c.expErr, err, c.desc)
	}
}

func (s *accountingTestSuite) TestCreateWalletBillTask() {
	createBills = s.mockFuncs.createBillsFunc
	now := time.Now()
	publishFailedError := fmt.Errorf("publish failed")
	cases := []*struct {
		desc                string
		createBillInfo      *accountingM.CreateBillInfo
		mockCreateBillTasks []*accountingM.CreateBillTask
		mockFunc            func()
		expBillCount        int
		expErr              error
	}{
		{
			desc: "normal case",
			createBillInfo: &accountingM.CreateBillInfo{
				MessageID:     "trade-id-2",
				SenderType:    accountingM.SenderTypeBBRich,
				UserID:        "user",
				UserType:      userM.UserType_CONTRACT_USER,
				Region:        "NONE",
				BillType:      accountingM.BillType_BILL_WALLET_REWARD,
				Currency:      currency.USD.String(),
				Amount:        currencyStore.FloatToString(1000, 9),
				Fee:           currencyStore.FloatToString(10, 9),
				PayoutAccount: payoutAccountM.PayoutAccount{},
			},
			mockCreateBillTasks: []*accountingM.CreateBillTask{
				{
					SenderType: accountingM.SenderTypeBBRich,
					MessageID:  "trade-id-1",
					BillID:     "bill-id-1",
				},
			},
			mockFunc: func() {
				s.mockFuncs.On("createBillsFunc", s.im, mock.AnythingOfType("ctx.CTX"), "user", mock.AnythingOfType("[]*accounting.CreateBillInput"), mock.Anything).Return(
					[]*accountingM.ResBillLog{},
					nil,
				).Run(func(args mock.Arguments) {
					callback := args.Get(4).(func(*sqlx.Tx, []string, []*bill.Element) error)
					s.mockOutbox.On("Publish", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), subTopicModel.SubTopics[subTopicModel.KeyBBRichBillCreated].Topic, &accountingM.BillCreatedInfo{
						MessageID: "trade-id-2",
						BillID:    "bill-id-2",
					}).Return(nil).Once()
					err := mdb.Transactx(mockCTX, s.dbx, func(tx *sqlx.Tx) error {
						b := &bill.DBRow{
							ID:                    1,
							PrevID:                sql.NullInt64{Valid: false},
							BillID:                "bill-id-2",
							Status:                accountingM.BillStatus_BILL_ORPHAN,
							Type:                  accountingM.BillType_BILL_WALLET_REWARD,
							UserID:                "user",
							UserType:              userM.UserType_CONTRACT_USER,
							Region:                "NONE",
							LocalCurrency:         "USD",
							AmountInLocalCurrency: 1000,
							TimeMillis:            mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
						}
						_, err := tx.NamedExec(bill.InsertBillQueryStr, b)
						s.NoError(err)
						return callback(tx, []string{"bill-id-2"}, nil)
					})
					s.Equal(nil, err)
				}).Once()
			},
			expBillCount: 1,
			expErr:       nil,
		},
		{
			desc: "normal case with AmountForTax",
			createBillInfo: &accountingM.CreateBillInfo{
				MessageID:               "trade-id-2",
				SenderType:              accountingM.SenderTypeBBRich,
				UserID:                  "user",
				UserType:                userM.UserType_CONTRACT_USER,
				Region:                  "JP",
				BillType:                accountingM.BillType_BILL_WALLET_REVENUE,
				Currency:                currency.JPY.String(),
				Amount:                  currencyStore.FloatToString(1000, 9),
				AmountForConsumptionTax: currencyStore.FloatToString(100, 9),
				AmountForIncomeTax:      currencyStore.FloatToString(110, 9),
				Fee:                     currencyStore.FloatToString(0, 9),
				PayoutAccount:           payoutAccountM.PayoutAccount{},
			},
			mockCreateBillTasks: []*accountingM.CreateBillTask{
				{
					SenderType: accountingM.SenderTypeBBRich,
					MessageID:  "trade-id-1",
					BillID:     "bill-id-1",
				},
			},
			mockFunc: func() {
				s.mockFuncs.On("createBillsFunc", s.im, mock.AnythingOfType("ctx.CTX"), "user", []*accountingM.CreateBillInput{
					{
						UserID:                "user",
						UserType:              userM.UserType_CONTRACT_USER,
						Region:                "JP",
						PayoutAccountVerified: true,
						PayoutFeeOwner:        payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
						BillArgs: []*accountingM.CreateBillArg{
							{
								Type:       accountingM.BillType_BILL_WALLET_REVENUE,
								AmountType: accountingM.CreateBillAmountType_LocalCurrency,
								LocalCurrencyAmount: accountingM.LocalCurrencyAmount{
									Currency:               currency.JPY,
									Value:                  1000,
									ExtraFee:               0,
									ValueForConsumptionTax: 100,
									ValueForIncomeTax:      110,
								},
							},
						},
					},
				}, mock.Anything).Return(
					[]*accountingM.ResBillLog{},
					nil,
				).Run(func(args mock.Arguments) {
					callback := args.Get(4).(func(*sqlx.Tx, []string, []*bill.Element) error)
					s.mockOutbox.On("Publish", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), subTopicModel.SubTopics[subTopicModel.KeyBBRichBillCreated].Topic, &accountingM.BillCreatedInfo{
						MessageID: "trade-id-2",
						BillID:    "bill-id-2",
					}).Return(nil).Once()
					err := mdb.Transactx(mockCTX, s.dbx, func(tx *sqlx.Tx) error {
						b := &bill.DBRow{
							ID:                                     1,
							PrevID:                                 sql.NullInt64{Valid: false},
							BillID:                                 "bill-id-2",
							Status:                                 accountingM.BillStatus_BILL_ORPHAN,
							Type:                                   accountingM.BillType_BILL_WALLET_REWARD,
							UserID:                                 "user",
							UserType:                               userM.UserType_CONTRACT_USER,
							Region:                                 "NONE",
							LocalCurrency:                          "USD",
							AmountInLocalCurrency:                  1000,
							AmountForConsumptionTaxInLocalCurrency: 100,
							AmountForIncomeTaxInLocalCurrency:      110,
							TimeMillis:                             mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
						}
						_, err := tx.NamedExec(bill.InsertBillQueryStr, b)
						s.NoError(err)
						return callback(tx, []string{"bill-id-2"}, nil)
					})
					s.Equal(nil, err)
				}).Once()
			},
			expBillCount: 1,
			expErr:       nil,
		},
		{
			desc: "publish failed",
			createBillInfo: &accountingM.CreateBillInfo{
				MessageID:     "trade-id-2",
				SenderType:    accountingM.SenderTypeBBRich,
				UserID:        "user",
				Region:        "NONE",
				BillType:      accountingM.BillType_BILL_WALLET_REWARD,
				Currency:      currency.USD.String(),
				Amount:        currencyStore.FloatToString(1000, 9),
				Fee:           currencyStore.FloatToString(10, 9),
				PayoutAccount: payoutAccountM.PayoutAccount{},
			},
			mockCreateBillTasks: []*accountingM.CreateBillTask{
				{
					SenderType: accountingM.SenderTypeBBRich,
					MessageID:  "trade-id-1",
					BillID:     "bill-id-1",
				},
			},
			mockFunc: func() {
				s.mockFuncs.On("createBillsFunc", s.im, mock.AnythingOfType("ctx.CTX"), "user", mock.AnythingOfType("[]*accounting.CreateBillInput"), mock.Anything).Return(
					[]*accountingM.ResBillLog{},
					publishFailedError,
				).Run(func(args mock.Arguments) {
					callback := args.Get(4).(func(*sqlx.Tx, []string, []*bill.Element) error)
					s.mockOutbox.On("Publish", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), subTopicModel.SubTopics[subTopicModel.KeyBBRichBillCreated].Topic, &accountingM.BillCreatedInfo{
						MessageID: "trade-id-2",
						BillID:    "bill-id-2",
					}).Return(publishFailedError).Once()
					err := mdb.Transactx(mockCTX, s.dbx, func(tx *sqlx.Tx) error {
						b := &bill.DBRow{
							ID:                    1,
							PrevID:                sql.NullInt64{Valid: false},
							BillID:                "bill-id-2",
							Status:                accountingM.BillStatus_BILL_ORPHAN,
							Type:                  accountingM.BillType_BILL_WALLET_REWARD,
							UserID:                "user",
							Region:                "NONE",
							LocalCurrency:         "USD",
							AmountInLocalCurrency: 1000,
							TimeMillis:            mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
						}
						_, err := tx.NamedExec(bill.InsertBillQueryStr, b)
						s.NoError(err)
						return callback(tx, []string{"bill-id-2"}, nil)
					})
					s.Equal(publishFailedError, err)
				}).Once()
			},
			expBillCount: 0,
			expErr:       publishFailedError,
		},
		{
			desc: "task has already been done before, revert transaction, send ack to pubsub",
			createBillInfo: &accountingM.CreateBillInfo{
				MessageID:     "trade-id-1",
				SenderType:    accountingM.SenderTypeBBRich,
				UserID:        "user",
				Region:        "NONE",
				BillType:      accountingM.BillType_BILL_WALLET_REWARD,
				Currency:      currency.USD.String(),
				Amount:        currencyStore.FloatToString(1000, 9),
				Fee:           currencyStore.FloatToString(10, 9),
				PayoutAccount: payoutAccountM.PayoutAccount{},
			},
			mockCreateBillTasks: []*accountingM.CreateBillTask{
				{
					SenderType: accountingM.SenderTypeBBRich,
					MessageID:  "trade-id-1",
					BillID:     "bill-id-1",
				},
			},
			mockFunc: func() {
				s.mockFuncs.On("createBillsFunc", s.im, mock.AnythingOfType("ctx.CTX"), "user", mock.AnythingOfType("[]*accounting.CreateBillInput"), mock.Anything).Return(
					[]*accountingM.ResBillLog{},
					mdb.ErrDuplicateEntry,
				).Run(func(args mock.Arguments) {
					callback := args.Get(4).(func(*sqlx.Tx, []string, []*bill.Element) error)
					err := mdb.Transactx(mockCTX, s.dbx, func(tx *sqlx.Tx) error {
						b := &bill.DBRow{
							ID:                    1,
							PrevID:                sql.NullInt64{Valid: false},
							BillID:                "bill-id-2",
							Status:                accountingM.BillStatus_BILL_ORPHAN,
							Type:                  accountingM.BillType_BILL_WALLET_REWARD,
							UserID:                "user",
							Region:                "NONE",
							LocalCurrency:         "USD",
							AmountInLocalCurrency: 1000,
							TimeMillis:            mtime.MilliSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, time.UTC)),
						}
						_, err := tx.NamedExec(bill.InsertBillQueryStr, b)
						s.NoError(err)
						return callback(tx, []string{"bill-id-2"}, nil)
					})
					s.Equal(mdb.ErrDuplicateEntry, err)
				}).Once()
			},
			expBillCount: 0,
			expErr:       nil,
		},
	}

	for _, c := range cases {
		testhelper.MigrateDown(s.dbName, s.dbHost, s.dbPort)
		for _, task := range c.mockCreateBillTasks {
			_, err := s.dbx.NamedExec(`INSERT INTO CreateBillTask SET senderType=:senderType, messageID=:messageID, billID=:billID`, task)
			s.NoError(err)
		}
		if c.mockFunc != nil {
			c.mockFunc()
		}
		data, err := json.Marshal(c.createBillInfo)
		s.NoError(err)
		err = s.im.CreateWalletBillTask(mockCTX, data, queueModel.CallbackOption{PublishTime: now})
		s.Equal(c.expErr, err, c.desc)

		var billCount int
		err = s.im.writerDB.Get(&billCount, "SELECT count(billID) FROM Bill")
		s.NoError(err)
		s.Equal(c.expBillCount, billCount, c.desc)
	}
	createBills = createBillsFunc
}

func (s *accountingTestSuite) TestSetUsersPayoutLock() {
	mockAdminID := int32(1)
	s.mockUser.On("GetPlainUsers", mockCTX, "user-1").Return([]models.User{{UserID: "user-1"}}, nil).Once()
	s.mockManualList.On("SetPayoutLock", mock.AnythingOfType("ctx.CTX"), mockExecutorID, "user-1", accountingM.PayoutLock_LOCK_ON, mockAdminID).Return(nil).Once()
	results, err := s.im.SetUsersPayoutLock(mockCTX, mockExecutorID, []string{"user-1"}, accountingM.PayoutLock_LOCK_ON, mockAdminID)
	s.NoError(err)
	s.Require().Equal(1, len(results))
	s.Equal(&models.User{UserID: "user-1"}, results[0].User)
	s.Equal(true, results[0].Success)

	s.mockUser.On("GetPlainUsers", mockCTX, "user-1").Return([]models.User{{UserID: "user-1"}}, nil).Once()
	s.mockManualList.On("SetPayoutLock", mock.AnythingOfType("ctx.CTX"), mockExecutorID, "user-1", accountingM.PayoutLock_LOCK_OFF, mockAdminID).Return(ml.ErrNotFound).Once()
	results, err = s.im.SetUsersPayoutLock(mockCTX, mockExecutorID, []string{"user-1"}, accountingM.PayoutLock_LOCK_OFF, mockAdminID)
	s.NoError(err)
	s.Require().Equal(1, len(results))
	s.Equal(&models.User{UserID: "user-1"}, results[0].User)
	s.Equal(true, results[0].Success)
}

func (s *accountingTestSuite) TestUpdateBill() {
	fbAccountType := payoutAccountM.AccountType_FOREIGN_ACCOUNT
	offAccountType := payoutAccountM.AccountType_OFFLINE_ACCOUNT
	bbrichAccountType := payoutAccountM.AccountType_BBRICH_ACCOUNT
	paypalAccountType := payoutAccountM.AccountType_PAYPAL_ACCOUNT
	offPayoutType := accountingM.PayoutType_OFFLINE
	mockBills := []*bill.Element{
		{
			DBRow: bill.DBRow{
				BillID: "update-test-1",
				Status: accountingM.BillStatus_BILL_ORPHAN,
				Type:   accountingM.BillType_BILL_REVENUE,
				UserID: "user-1",
				Region: "TW",
			},
		},
		{
			DBRow: bill.DBRow{
				BillID:   "update-test-2",
				UserType: userM.UserType_CONTRACT_USER,
				Status:   accountingM.BillStatus_BILL_ORPHAN,
				Type:     accountingM.BillType_BILL_REVENUE,
				UserID:   "user-2",
				Region:   "TW",
			},
		},
	}
	mockFBAccount := &payoutAccountM.PayoutAccount{
		Account:     "fb-acount",
		BankName:    "fb-bank",
		BankCode:    "fb-code",
		BranchName:  "fb-branch",
		BranchCode:  "fb-brcode",
		PayeeName:   "fb-payee",
		PayeeType:   payoutAccountM.PayeeType_USER_FOREIGN,
		SwiftCode:   "USD",
		AccountType: payoutAccountM.AccountType_FOREIGN_ACCOUNT,
	}
	mockUnverifiedFBAccount := &payoutAccountM.PayoutAccount{
		Account:     "fb-acount",
		AccountType: payoutAccountM.AccountType_FOREIGN_ACCOUNT,
	}
	fbUpdateFields := map[bill.UpdateFileldName]interface{}{
		bill.PayoutTypeField:            accountingM.PayoutType_BANK_ESUN,
		bill.AccountTypeField:           fbAccountType,
		bill.PayoutAccountField:         mockFBAccount.Account,
		bill.PayoutAccountVerifiedField: true,
		bill.PayoutBankNameField:        mockFBAccount.BankName,
		bill.PayoutBankCodeField:        mockFBAccount.BankCode,
		bill.PayoutBankBranchNameField:  mockFBAccount.BranchName,
		bill.PayoutBankBranchCodeField:  mockFBAccount.BranchCode,
		bill.PayoutBankAddressField:     mockFBAccount.Address,
		bill.PayoutSwiftCodeField:       mockFBAccount.SwiftCode,
		bill.PayeeNameField:             mockFBAccount.PayeeName,
		bill.PayoutFeeOwnerField:        payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
		bill.PayeeTypeField:             payoutAccountM.PayeeType_USER_FOREIGN,
		bill.PayoutSwiftCurrencyField:   mockFBAccount.SwiftCurrency(),
	}
	offUpdateFields := map[bill.UpdateFileldName]interface{}{
		bill.PayoutTypeField:            accountingM.PayoutType_OFFLINE,
		bill.AccountTypeField:           offAccountType,
		bill.PayoutAccountField:         "user-1",
		bill.PayoutAccountVerifiedField: true,
		bill.PayoutFeeOwnerField:        payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
	}
	bbrichUpdateFields := map[bill.UpdateFileldName]interface{}{
		bill.PayoutTypeField:            accountingM.PayoutType_BBFU,
		bill.AccountTypeField:           bbrichAccountType,
		bill.PayoutAccountField:         "user-1",
		bill.PayoutAccountVerifiedField: true,
		bill.PayoutFeeOwnerField:        payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
	}
	bbrichWithContractUpdateFields := map[bill.UpdateFileldName]interface{}{
		bill.PayoutTypeField:            accountingM.PayoutType_BBFU,
		bill.AccountTypeField:           bbrichAccountType,
		bill.PayoutAccountField:         "user-2",
		bill.PayoutAccountVerifiedField: true,
		bill.PayoutFeeOwnerField:        payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
		bill.PayeeTypeField:             payoutAccountM.PayeeType_USER_FOREIGN,
	}

	cases := []*struct {
		desc        string
		billIDs     []string
		update      accountingM.UpdateBillInfo
		mockFunc    func()
		expectError error
	}{
		{
			desc:    "update to foreign bank accountType, v2=true",
			billIDs: []string{"update-test-1", "update-test-2"},
			update: accountingM.UpdateBillInfo{
				AccountType: &fbAccountType,
			},
			mockFunc: func() {
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"update-test-1", "update-test-2"}).Return(mockBills, nil).Once()
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), bcr.Query{BillIDs: []string{"update-test-1"}}, mock.Anything).Return([]*bcrM.BillContractRel{{ContractID: 1}}, nil).Once()
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), bcr.Query{BillIDs: []string{"update-test-2"}}, mock.Anything).Return([]*bcrM.BillContractRel{{ContractID: 2}}, nil).Once()
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), bcr.Query{BillIDs: []string{"update-test-1", "update-test-2"}}).Return([]*bcrM.BillContractRel{
					{BillID: "update-test-1", ContractID: 1},
					{BillID: "update-test-2", ContractID: 2},
				}, nil).Once()
				s.mockContract.On("GetContract", mock.AnythingOfType("ctx.CTX"), int32(1)).Return(&contractM.Contract{}, nil).Once()
				s.mockContract.On("GetContract", mock.AnythingOfType("ctx.CTX"), int32(2)).Return(&contractM.Contract{}, nil).Once()
				s.mockContract.On("GetPayoutAccountsByCategory", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*contract.Contract"), contractM.PayoutCategory_REVENUE).Return(
					[]*payoutAccountM.PayoutAccount{mockFBAccount}, nil).Twice()
				s.mockBill.On("Update", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, []string{"update-test-1"}, fbUpdateFields).Return(mockBills[:1], nil).Once()
				s.mockBill.On("Update", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, []string{"update-test-2"}, fbUpdateFields).Return(mockBills[1:], nil).Once()
				s.mockUser.On("GetPlainUsers", mockCTX, "user-1", "user-2").Return([]models.User{{UserID: "user-1", RegionGroup: "TW"}, {UserID: "user-2"}}, nil).Once()
			},
		},
		{
			desc:    "update to offline accountType",
			billIDs: []string{"update-test-1"},
			update: accountingM.UpdateBillInfo{
				AccountType: &offAccountType,
			},
			mockFunc: func() {
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil).Once()
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"update-test-1"}).Return(mockBills[:1], nil).Once()
				s.mockBill.On("Update", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, []string{"update-test-1"}, offUpdateFields).Return(mockBills[:1], nil).Once()
				s.mockUser.On("GetPlainUsers", mockCTX, "user-1").Return([]models.User{{UserID: "user-1", RegionGroup: "TW"}}, nil).Once()
			},
		},
		{
			desc:    "update to offline payoutType",
			billIDs: []string{"update-test-1"},
			update: accountingM.UpdateBillInfo{
				PayoutType: &offPayoutType,
			},
			mockFunc: func() {
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil).Once()
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"update-test-1"}).Return(mockBills[:1], nil).Once()
				s.mockBill.On("Update", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, []string{"update-test-1"}, offUpdateFields).Return(mockBills[:1], nil).Once()
				s.mockUser.On("GetPlainUsers", mockCTX, "user-1").Return([]models.User{{UserID: "user-1", RegionGroup: "TW"}}, nil).Once()
			},
		},
		{
			desc:    "update to bbrich accountType",
			billIDs: []string{"update-test-1"},
			update: accountingM.UpdateBillInfo{
				AccountType: &bbrichAccountType,
			},
			mockFunc: func() {
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil).Once()
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"update-test-1"}).Return(mockBills[:1], nil).Once()
				s.mockBill.On("Update", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, []string{"update-test-1"}, bbrichUpdateFields).Return(mockBills[:1], nil).Once()
				s.mockUser.On("GetPlainUsers", mockCTX, "user-1").Return([]models.User{{UserID: "user-1", RegionGroup: "TW"}}, nil).Once()
			},
		},
		{
			desc:    "update to bbrich accountType with contract",
			billIDs: []string{"update-test-2"},
			update: accountingM.UpdateBillInfo{
				AccountType: &bbrichAccountType,
			},
			mockFunc: func() {
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), bcr.Query{BillIDs: []string{"update-test-2"}}, mock.Anything).Return([]*bcrM.BillContractRel{{ContractID: 1}}, nil).Once()
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), bcr.Query{BillIDs: []string{"update-test-2"}}).Return([]*bcrM.BillContractRel{{ContractID: 1}}, nil).Once()
				s.mockContract.On("GetContract", mock.AnythingOfType("ctx.CTX"), int32(1)).Return(&contractM.Contract{}, nil).Once()
				s.mockContract.On("GetPayoutAccountsByCategory", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*contract.Contract"), contractM.PayoutCategory_REVENUE).Return(
					[]*payoutAccountM.PayoutAccount{mockFBAccount}, nil).Once()

				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"update-test-2"}).Return(mockBills[1:2], nil).Once()
				s.mockBill.On("Update", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, []string{"update-test-2"}, bbrichWithContractUpdateFields).Return(mockBills[1:2], nil).Once()
				s.mockUser.On("GetPlainUsers", mockCTX, "user-2").Return([]models.User{{UserID: "user-2", RegionGroup: "TW"}}, nil).Once()
			},
		},
		{
			desc:    "fake bill id",
			billIDs: []string{"fake-1"},
			update: accountingM.UpdateBillInfo{
				AccountType: &bbrichAccountType,
			},
			mockFunc: func() {
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"fake-1"}).Return(nil, fmt.Errorf("not exist")).Once()
			},
			expectError: fmt.Errorf("not exist"),
		},
		{
			desc:    "invalid bill status",
			billIDs: []string{"update-test-1"},
			update: accountingM.UpdateBillInfo{
				AccountType: &bbrichAccountType,
			},
			mockFunc: func() {
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"update-test-1"}).Return([]*bill.Element{
					{
						DBRow: bill.DBRow{
							BillID: "update-test-1",
							Status: accountingM.BillStatus_BILL_PAYOUT,
							Type:   accountingM.BillType_BILL_REVENUE,
							UserID: "user-1",
							Region: "TW",
						},
					},
				}, nil).Once()
			},
			expectError: fmt.Errorf("UpdateBills invalid update"),
		},
		{
			desc:    "not supported type",
			billIDs: []string{"update-test-1"},
			update: accountingM.UpdateBillInfo{
				AccountType: &paypalAccountType,
			},
			mockFunc: func() {
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"update-test-1"}).Return(mockBills[:1], nil).Once()
			},
			expectError: fmt.Errorf("not supported accountType/payoutType"),
		},
		{
			desc:    "unverified",
			billIDs: []string{"update-test-1"},
			update: accountingM.UpdateBillInfo{
				AccountType: &fbAccountType,
			},
			mockFunc: func() {
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"update-test-1"}).Return(mockBills[:1], nil).Once()
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), bcr.Query{BillIDs: []string{"update-test-1"}}, mock.Anything).Return([]*bcrM.BillContractRel{{ContractID: 1}}, nil).Once()
				s.mockContract.On("GetContract", mock.AnythingOfType("ctx.CTX"), int32(1)).Return(&contractM.Contract{}, nil).Once()
				s.mockContract.On("GetPayoutAccountsByCategory", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*contract.Contract"), contractM.PayoutCategory_REVENUE).Return(
					[]*payoutAccountM.PayoutAccount{mockUnverifiedFBAccount}, nil).Once()
			},
			expectError: fmt.Errorf("unverified payout account"),
		},
		{
			desc:    "user without contract",
			billIDs: []string{"update-test-3"},
			update: accountingM.UpdateBillInfo{
				AccountType: &fbAccountType,
			},
			mockFunc: func() {
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"update-test-3"}).Return([]*bill.Element{
					{
						DBRow: bill.DBRow{
							BillID: "update-test-3",
							Status: accountingM.BillStatus_BILL_ORPHAN,
							Type:   accountingM.BillType_BILL_REVENUE,
							UserID: "user-3",
							Region: "TW",
						},
					},
				}, nil).Once()
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), bcr.Query{BillIDs: []string{"update-test-3"}}, mock.Anything).Return([]*bcrM.BillContractRel{}, nil).Once()
			},
			expectError: fmt.Errorf("cannot find corresponding contract for bill"),
		},
	}

	for _, c := range cases {
		s.Run(c.desc, func() {
			s.TearDownTest()
			s.SetupTest()

			// use mock bill
			s.im.bill = s.mockBill
			s.mockFuncs.On("getKYCRegion", mock.AnythingOfType("ctx.CTX"), mock.Anything, mock.AnythingOfType("[]string")).Return([]string{""}, nil).Maybe()

			c.mockFunc()
			_, err := s.im.UpdateBills(mockCTX, mockExecutorID, c.billIDs, &c.update)
			s.Require().Equal(c.expectError, err, c.desc)
		})
	}
}

func (s *accountingTestSuite) TestCreatePaymentsFile() {
	// executor region/location mocks
	loc, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorRegion).Return(loc, nil).Once()

	// mock bill
	getBillLogsFunc = s.mockFuncs.getBillLogs
	TimeNow = s.mockFuncs.timeNow
	s.im.bill = s.mockBill
	mockNow := time.Date(2021, time.January, 5, 0, 0, 0, 0, time.UTC)
	billIDs := []string{"201711-b2-in-a1"}

	s.mockFuncs.On("timeNow").Return(mockNow)
	s.mockFuncs.On("getBillLogs", mock.AnythingOfType("ctx.CTX"), mock.Anything, accountingM.GetBillLogsFilter{
		TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
		From:     time.Unix(0, 0),
		To:       mockNow,
		Stage:    accountingM.Stage_PAYOUT_STAGE,
		BillIDs:  billIDs,
	}, "", 2000, mock.Anything).Return([]*accountingM.ResBillLog{
		{
			UserID:                "u2",
			BillID:                "201711-b2-in-a1",
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserType:              userM.UserType_CONTRACT_USER,
			Status:                accountingM.BillStatus_BILL_PAYOUT,
			Region:                "TW",
			Amount:                20000,
			TradeID:               "tradeIDa1",
			PayoutType:            accountingM.PayoutType_BANK_ESUN,
			PayoutAccount:         "bank-account",
			PayoutAccountVerified: true,
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 20000,
		},
	}, 1, "", nil).Once()
	s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.Anything, []string{"201711-b2-in-a1"}).Return([]*bill.Element{{DBRow: bill.DBRow{
		PrevID:                sql.NullInt64{Valid: false},
		BillID:                "201711-b2-in-a1",
		Type:                  accountingM.BillType_BILL_REVENUE,
		UserType:              userM.UserType_CONTRACT_USER,
		Status:                accountingM.BillStatus_BILL_PAYOUT,
		UserID:                "u2",
		Region:                "TW",
		Amount:                usd.ToNanoUSD(20000),
		TradeID:               "tradeIDa1",
		DealingID:             "dealingIDa1b2",
		PayoutType:            accountingM.PayoutType_BANK_ESUN,
		PayoutAccount:         "bank-account",
		PayoutAccountVerified: true,
		ExecutorID:            "tester",
		TimeMillis:            mtime.MilliSecond(t20171105),
		LocalCurrency:         "USD",
		AmountInLocalCurrency: 20000,
	}}}, nil)

	// bill comment mocks
	s.im.payout = s.mockPayout
	mockCursor := "1"
	mockEndCursor := ""
	s.mockComment.On("GetComments", mock.AnythingOfType("ctx.CTX"), intraModel.CommentType_BILL_COMMENT_TYPE, mock.AnythingOfType("[]int32"), billIDs[0], "", 100).Return([]*comment.ResLog{
		{
			ExecutorID:     mockExecutorID,
			Comment:        "comment-1",
			CreateUnixTime: time.Now().Unix(),
		},
	}, mockCursor, nil).Once()
	s.mockComment.On("GetComments", mock.AnythingOfType("ctx.CTX"), intraModel.CommentType_BILL_COMMENT_TYPE, mock.AnythingOfType("[]int32"), billIDs[0], mockCursor, 100).Return([]*comment.ResLog{
		{
			ExecutorID:     mockExecutorID,
			Comment:        "comment-2",
			CreateUnixTime: time.Now().Unix(),
		},
	}, mockEndCursor, nil).Once()
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockExecutorID, mockExecutorID).Return([]models.User{
		{
			UserID: mockExecutorID,
			OpenID: mockExecutorOpenID,
		},
		{
			UserID: mockExecutorID,
			OpenID: mockExecutorOpenID,
		},
	}, nil).Once()

	// intra file mocks
	mockKeys := []string{"fileKey"}
	s.mockIntraFile.On("Upload", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*file.File")).Return(mockKeys, nil).Once()
	res, err := s.im.CreatePaymentsFile(mockCTX, mockExecutorID, billIDs)
	s.NoError(err)
	s.Equal(mockKeys[0], res.Key)
}

func (s *accountingTestSuite) TestRefundWithOption() {
	// use mock bill and mock payout
	s.im.bill = s.mockBill
	s.im.payout = s.mockPayout

	mockUserID := "userID"
	mockBillID := "billID-1"
	mockBillRegion := "TW"
	mockBillAmount := usd.ToNanoUSD(100)
	mockLocalCurrency := "USD"
	mockPayoutCurrency := "USD"
	mockPayoutTimeMillis := mtime.NowMS()
	mockPayoutRate := float64(0.5)
	mockRefundOption := accountingM.RefundOption{
		FileKey: "refundFileKey",
	}
	mockBill := bill.Element{
		DBRow: bill.DBRow{
			BillID:                mockBillID,
			Amount:                mockBillAmount,
			Status:                accountingM.BillStatus_BILL_PAYOUT,
			Region:                mockBillRegion,
			UserID:                mockUserID,
			Currency:              moneyM.Currency_NANO_USD,
			TradeID:               "",
			DealingID:             "",
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 100,
			CreateTimeMillis:      *************,
		},
	}
	mockPayout := payout.DBRow{
		Currency:         mockPayoutCurrency,
		Rate:             mockPayoutRate,
		FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
		Fee:              20,
		AmountBill:       100,
		Status:           accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND,
		TimeMillis:       mockPayoutTimeMillis,
		BalancerID:       mockExecutorID,
		UpdateTimeMillis: mockPayoutTimeMillis,
		PayoutTimeMillis: mockPayoutTimeMillis,
	}
	expLog := accountingM.ResBillLog{
		BillID:                   mockBillID,
		Status:                   accountingM.BillStatus_BILL_PAYOUT,
		Region:                   mockBillRegion,
		UserID:                   mockUserID,
		Amount:                   usd.ToUSD(mockBillAmount),
		LocalCurrency:            mockLocalCurrency,
		AmountInLocalCurrency:    usd.ToUSD(mockBillAmount),
		AmountInPayoutCurrency:   100,
		PayoutStatus:             accountingM.PayoutStatus(accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND),
		PaidUnixTime:             mockPayoutTimeMillis / 1000,
		CompletedUnixTime:        0,
		RefundedUnixTime:         mockPayoutTimeMillis / 1000,
		UpdatedUnixTime:          mockPayoutTimeMillis / 1000,
		PayoutCurrency:           mockPayoutCurrency,
		PayoutRate:               mockPayoutRate,
		FeeOwner:                 payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
		PayoutFee:                20,
		BalancerID:               mockExecutorID,
		RefundFileKey:            mockRefundOption.FileKey,
		RefundFeeInLocalCurrency: 40,
		Payee: accountingM.Payee{
			UserID: mockUserID,
		},
		CreateUnixTime:   **********,
		CreateTimeMillis: *************,
	}
	s.mockIntraFile.On("Exist", mock.AnythingOfType("ctx.CTX"), mockRefundOption.FileKey).Return(true, nil)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	cases := []struct {
		desc     string
		opt      accountingM.RefundOption
		mockFunc func()
		expLog   accountingM.ResBillLog
		expError error
	}{
		{
			desc: "refund normal bill",
			opt:  mockRefundOption,
			mockFunc: func() {
				mockElements := []*bill.Element{{
					DBRow: bill.DBRow{
						BillID: mockBillID,
						Type:   accountingM.BillType_BILL_WALLET,
					},
				}}
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), []string{mockBillID}).Return(mockElements, nil).Once()
			},
			expError: fmt.Errorf("must post a comment when return wallet bill"),
		},
		{
			desc: "refund normal bill",
			opt:  mockRefundOption,
			mockFunc: func() {
				mockElements := []*bill.Element{&mockBill}
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), []string{mockBillID}).Return(mockElements, nil).Once()
				s.mockPayout.On("Refund", mock.AnythingOfType("ctx.CTX"), mockExecutorID, mockBillID, &mockRefundOption).Return(
					&mockPayout, &mockBill, nil).Once()
				s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockUserID).Return([]models.User{
					{
						UserID: mockUserID,
					},
				}, nil).Once()
			},
			expLog: expLog,
		},
		{
			desc: "bill is before billingV2 startDate",
			opt:  mockRefundOption,
			mockFunc: func() {
				mockElements := []*bill.Element{&mockBill}
				// *************: Friday, 31 July 2020 23:00:00 GMT+8
				mockElements[0].CreateTimeMillis = *************
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), []string{mockBillID}).Return(mockElements, nil).Once()
			},
			expError: fmt.Errorf("returning/refunding the legacy bills, created before %s, is forbidden", getActiveDateOfBillingV2()),
		},
	}

	s.mockFuncs.On("pushBillChangedMessage").Return(true)
	for _, c := range cases {
		c.mockFunc()
		l, err := s.im.RefundBill(mockCTX, mockExecutorID, mockBillID, &c.opt)
		s.Require().Equal(c.expError, err, c.desc)
		if c.expError != nil {
			continue
		}
		s.Require().Equal(c.expLog, *l, c.desc)
	}
}

func (s *accountingTestSuite) TestRefundBills() {
	s.im.bill = s.mockBill
	s.im.payout = s.mockPayout
	mockUserID := "userID"
	mockPayoutTimeMillis := mtime.NowMS()

	type testCase struct {
		desc    string
		billIDs []string
		option  *accountingM.BatchRefundOption
		mockFun func(testCase)
		payouts []*payout.DBRow
		bills   []*bill.Element
		expLogs []*accountingM.ResBillLog
		expErr  error
	}

	cases := []testCase{
		{
			desc:    "success",
			billIDs: []string{"1", "2"},
			option: &accountingM.BatchRefundOption{
				FileKey: "file-key-1",
				Comment: "test comment",
			},
			mockFun: func(c testCase) {
				s.mockIntraFile.On("Exist", mock.AnythingOfType("ctx.CTX"), "file-key-1").Return(true, nil)
				for _, billID := range []string{"1", "2"} {
					s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_PAYOUT_REFUND_COMMENT), billID, "test comment").Return(nil, nil)
				}
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), []string{"1", "2"}).Return(c.bills, nil).Once()
				s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockUserID).Return([]models.User{
					{
						UserID: mockUserID,
					},
				}, nil).Once()
				s.mockPayout.On("BatchRefund", mock.AnythingOfType("ctx.CTX"), mockExecutorID, c.billIDs, c.option).Return(c.payouts, c.bills, nil).Once()
			},
			payouts: []*payout.DBRow{
				{
					Currency:         "TWD",
					Rate:             float64(1.1),
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              20,
					AmountBill:       3000,
					Status:           accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND,
					TimeMillis:       mockPayoutTimeMillis,
					BalancerID:       mockExecutorID,
					UpdateTimeMillis: mockPayoutTimeMillis,
					PayoutTimeMillis: mockPayoutTimeMillis,
				},
				{
					Currency:         "TWD",
					Rate:             float64(1.2),
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_17,
					Fee:              20,
					AmountBill:       6000,
					Status:           accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND,
					TimeMillis:       mockPayoutTimeMillis,
					BalancerID:       mockExecutorID,
					UpdateTimeMillis: mockPayoutTimeMillis,
					PayoutTimeMillis: mockPayoutTimeMillis,
				},
			},
			bills: []*bill.Element{
				{
					DBRow: bill.DBRow{
						BillID:                "1",
						Amount:                usd.ToNanoUSD(100),
						Status:                accountingM.BillStatus_BILL_PAYOUT,
						Region:                "TW",
						UserID:                mockUserID,
						Currency:              moneyM.Currency_NANO_USD,
						TradeID:               "",
						DealingID:             "",
						LocalCurrency:         "TWD",
						AmountInLocalCurrency: 3000,
						CreateTimeMillis:      *************,
					},
				},
				{
					DBRow: bill.DBRow{
						BillID:                "2",
						Amount:                usd.ToNanoUSD(200),
						Status:                accountingM.BillStatus_BILL_PAYOUT,
						Region:                "TW",
						UserID:                mockUserID,
						Currency:              moneyM.Currency_NANO_USD,
						TradeID:               "",
						DealingID:             "",
						LocalCurrency:         "TWD",
						AmountInLocalCurrency: 6000,
						CreateTimeMillis:      *************,
					},
				},
			},
			expLogs: []*accountingM.ResBillLog{
				{
					BillID:                   "1",
					Status:                   accountingM.BillStatus_BILL_PAYOUT,
					Region:                   "TW",
					UserID:                   mockUserID,
					Amount:                   float64(100),
					LocalCurrency:            "TWD",
					AmountInLocalCurrency:    3000,
					PayoutStatus:             accountingM.PayoutStatus(accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND),
					PaidUnixTime:             mockPayoutTimeMillis / 1000,
					CompletedUnixTime:        0,
					RefundedUnixTime:         mockPayoutTimeMillis / 1000,
					UpdatedUnixTime:          mockPayoutTimeMillis / 1000,
					PayoutCurrency:           "TWD",
					AmountInPayoutCurrency:   3000,
					PayoutRate:               float64(1.1),
					FeeOwner:                 payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					PayoutFee:                20,
					BalancerID:               mockExecutorID,
					RefundFileKey:            "file-key-1",
					RefundFeeInLocalCurrency: 18,
					Payee: accountingM.Payee{
						UserID: mockUserID,
					},
					CreateUnixTime:   **********,
					CreateTimeMillis: *************,
				},
				{
					BillID:                   "2",
					Status:                   accountingM.BillStatus_BILL_PAYOUT,
					Region:                   "TW",
					UserID:                   mockUserID,
					Amount:                   float64(200),
					LocalCurrency:            "TWD",
					AmountInLocalCurrency:    6000,
					PayoutStatus:             accountingM.PayoutStatus(accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND),
					PaidUnixTime:             mockPayoutTimeMillis / 1000,
					CompletedUnixTime:        0,
					RefundedUnixTime:         mockPayoutTimeMillis / 1000,
					UpdatedUnixTime:          mockPayoutTimeMillis / 1000,
					PayoutCurrency:           "TWD",
					AmountInPayoutCurrency:   6000,
					PayoutRate:               float64(1.2),
					FeeOwner:                 payoutAccountM.FeeOwnerType_FEE_OWNER_17,
					PayoutFee:                20,
					BalancerID:               mockExecutorID,
					RefundFileKey:            "file-key-1",
					RefundFeeInLocalCurrency: 0,
					Payee: accountingM.Payee{
						UserID: mockUserID,
					},
					CreateUnixTime:   **********,
					CreateTimeMillis: *************,
				},
			},
			expErr: nil,
		},
		{
			desc:    "before billingV2 startDate",
			billIDs: []string{"1", "2"},
			option: &accountingM.BatchRefundOption{
				FileKey: "file-key-1",
				Comment: "test comment",
			},
			mockFun: func(c testCase) {
				s.mockIntraFile.On("Exist", mock.AnythingOfType("ctx.CTX"), "file-key-1").Return(true, nil)
				for _, billID := range []string{"1", "2"} {
					s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_PAYOUT_REFUND_COMMENT), billID, "test comment").Return(nil, nil)
				}
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), []string{"1", "2"}).Return(c.bills, nil).Once()
			},
			bills: []*bill.Element{
				{
					DBRow: bill.DBRow{
						BillID:                "1",
						Amount:                usd.ToNanoUSD(100),
						Status:                accountingM.BillStatus_BILL_PAYOUT,
						Region:                "TW",
						UserID:                mockUserID,
						Currency:              moneyM.Currency_NANO_USD,
						TradeID:               "",
						DealingID:             "",
						LocalCurrency:         "TWD",
						AmountInLocalCurrency: 3000,
						CreateTimeMillis:      *************, // *************:  Saturday, 1 August 2020 00:00:00 GMT+8
					},
				},
				{
					DBRow: bill.DBRow{
						BillID:                "2",
						Amount:                usd.ToNanoUSD(200),
						Status:                accountingM.BillStatus_BILL_PAYOUT,
						Region:                "TW",
						UserID:                mockUserID,
						Currency:              moneyM.Currency_NANO_USD,
						TradeID:               "",
						DealingID:             "",
						LocalCurrency:         "TWD",
						AmountInLocalCurrency: 6000,
						CreateTimeMillis:      *************, // *************: Friday, 31 July 2020 23:00:00 GMT+8
					},
				},
			},
			expErr: fmt.Errorf("returning/refunding the legacy bills, created before %s, is forbidden", getActiveDateOfBillingV2()),
		},
	}

	s.mockFuncs.On("pushBillChangedMessage").Return(true)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)
	for _, c := range cases {
		c.mockFun(c)
		logs, err := s.im.RefundBills(mockCTX, mockExecutorID, c.billIDs, c.option)
		s.Require().Equal(c.expErr, err, c.desc)
		if c.expErr == nil {
			for i, log := range logs {
				s.Require().Equal(c.expLogs[i], log, c.desc)
			}
		}
	}
}

func (s *accountingTestSuite) TestGetPayoutTotalAmounts() {
	s.createTestingData(s.im, s.dbx)
	s.im.bill = s.mockBill
	s.im.payout = s.mockPayout
	s.im.approval = s.mockApproval
	TimeNow = s.mockFuncs.timeNow

	mockBills := []*bill.Element{
		{
			DBRow: bill.DBRow{
				BillID: "201711-b1-in-a1",
				Region: "TW",
			},
		},
		{
			DBRow: bill.DBRow{
				BillID: "201711-b2-in-a1",
				Region: "TW",
			},
		},
	}

	billIDs := []string{}
	for _, b := range mockBills {
		billIDs = append(billIDs, b.BillID)
	}
	payoutAmounts := []*accountingM.PayoutAmount{
		{Currency: currency.USD, Amount: 100.0},
		{Currency: currency.TWD, Amount: 2000.0},
	}
	locTPE, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)
	mockNow := time.Date(2018, time.January, 5, 0, 0, 0, 0, time.UTC)

	s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), billIDs).Return(mockBills, nil).Once()
	s.mockFuncs.On("timeNow").Return(mockNow)
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(locTPE, nil).Once()
	s.mockExchangeRate.On("GetMultiByRegion", mock.AnythingOfType("ctx.CTX"), exchangeM.Type_ACCOUNTING_MONTHLY_PAYOUT_TYPE, "TW", mock.AnythingOfType("[]time.Time")).Return(mockPayoutRates(), nil).Once()
	s.mockRate.On("Get", mock.AnythingOfType("ctx.CTX"), "TW", mock.AnythingOfType("time.Time")).Return(currency.USD, 1.0, nil)
	s.mockPayout.On("GetTypeInfos").Return(mockPayoutTypeInfos).Once()
	s.mockApproval.On("RateMonthExists", mock.AnythingOfType("ctx.CTX"), []string{"TW"}, "2017-12").Return(true, nil).Once()
	s.mockPayout.On("GetPayoutAmounts", mock.AnythingOfType("ctx.CTX"), billIDs, mock.AnythingOfType("map[string]accounting.BillRate"), &currency.TWD).Return(payoutAmounts, nil).Once()
	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), billIDs).Return(map[string]pRate.PayoutRates{}, nil).Once()

	expRes := []*accountingM.ResPayoutTotalAmount{
		{Currency: currency.TWD, Amount: 2000.0},
		{Currency: currency.USD, Amount: 100.0},
	}
	res, err := s.im.GetPayoutTotalAmounts(mockCTX, billIDs, &currency.TWD)
	s.NoError(err)
	s.Equal(expRes, res)
}

func (s *accountingTestSuite) TestAddManualAccount() {
	mockAdminID := int32(1)
	cases := []struct {
		description      string
		inputRegion      string
		inputUserID      string
		mockGetUser      *models.User
		mockGetContract  *contractM.Contract
		mockGetAdminUser *intraModel.User
		expectError      error
	}{
		{
			description:      "add a user in same region",
			inputRegion:      "TW",
			inputUserID:      "user-1",
			mockGetUser:      &models.User{UserID: "user-1"},
			mockGetContract:  &contractM.Contract{AgentID: 123},
			mockGetAdminUser: &intraModel.User{Region: "TW"},
		},
		{
			description: "add a user in NONE",
			inputRegion: "TW",
			inputUserID: "user-1",
			mockGetUser: &models.User{UserID: "user-1"},
		},
	}
	for _, c := range cases {
		s.mockManualList.On("Add", mock.AnythingOfType("ctx.CTX"), mockExecutorID, c.inputRegion, c.inputUserID, mockAdminID).Return(nil).Once()

		err := s.im.AddManualAccount(mockCTX, mockExecutorID, c.inputRegion, c.inputUserID, mockAdminID)
		s.Equal(c.expectError, err, c.description)
		if c.expectError != nil {
			continue
		}
	}
}

func (s *accountingTestSuite) TestGetPaymentReport() {
	userID := "u1"
	mockBills := []struct {
		br bill.DBRow
		pr payout.DBRow
	}{
		{
			br: bill.DBRow{
				Type:                  accountingM.BillType_BILL_REVENUE,
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b1",
				Status:                accountingM.BillStatus_BILL_APPROVED,
				UserID:                userID,
				Region:                "TW",
				Amount:                usd.ToNanoUSD(55),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            1200000,
				CreateTimeMillis:      1200000,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 55,
			},
		},
		{
			br: bill.DBRow{
				Type:                  accountingM.BillType_BILL_REVENUE,
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b2",
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                userID,
				Region:                "TW",
				Amount:                usd.ToNanoUSD(66),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            1000000,
				CreateTimeMillis:      1000000,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 66,
			},
			pr: payout.DBRow{
				TimeMillis:       1000000,
				CreatorID:        mockExecutorID,
				BillID:           "b2",
				Currency:         "USD",
				Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
				UpdateTimeMillis: 1000000,
				PayoutTimeMillis: 1000000,
			},
		},
		{
			br: bill.DBRow{
				Type:                  accountingM.BillType_BILL_REVENUE,
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b3",
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                userID,
				Region:                "TW",
				Amount:                usd.ToNanoUSD(77),
				TimeMillis:            800000,
				CreateTimeMillis:      800000,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 77,
			},
			pr: payout.DBRow{
				TimeMillis:       800000,
				CreatorID:        mockExecutorID,
				BillID:           "b3",
				Currency:         "USD",
				Status:           accountingM.PayoutStatus_PAYOUT_SUCCESS,
				UpdateTimeMillis: 800000,
				PayoutTimeMillis: 800000,
			},
		},
		{
			br: bill.DBRow{
				Type:                  accountingM.BillType_BILL_REWARD,
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b4",
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                userID,
				Region:                "TW",
				Amount:                usd.ToNanoUSD(88),
				TimeMillis:            400000,
				CreateTimeMillis:      400000,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 88,
			},
			pr: payout.DBRow{
				TimeMillis:       600000,
				CreatorID:        mockExecutorID,
				BillID:           "b4",
				Currency:         "USD",
				Rate:             1,
				Status:           accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND,
				UpdateTimeMillis: 600000,
				PayoutTimeMillis: 600000,
			},
		},
		{
			br: bill.DBRow{
				Type:                  accountingM.BillType_BILL_REVENUE,
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b5",
				Status:                accountingM.BillStatus_BILL_WRITTEN_OFF,
				UserID:                userID,
				Region:                "TW",
				Amount:                usd.ToNanoUSD(99),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            1400000,
				CreateTimeMillis:      1400000,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 99,
			},
		},
	}
	getPlainUserArgs := []interface{}{mock.AnythingOfType("ctx.CTX")}
	uniqueUserIDs := map[string]bool{}
	users := []models.User{}
	for _, b := range mockBills {
		// create orphan bill
		orphanBill := b.br
		orphanBill.Status = accountingM.BillStatus_BILL_ORPHAN
		_, err := s.dbx.NamedExec(bill.InsertBillQueryStr, orphanBill)
		s.NoError(err)
		_, err = s.dbx.NamedExec(bill.InsertBillQueryStr, b.br)
		s.NoError(err)
		_, err = s.dbx.NamedExec(insertAccountingPayoutStmt, b.pr)
		s.NoError(err)
		if uniqueUserIDs[b.br.UserID] {
			continue
		}
		uniqueUserIDs[b.br.UserID] = true
		getPlainUserArgs = append(getPlainUserArgs, b.br.UserID)
		users = append(users, models.User{UserID: b.br.UserID})
		s.mockUser.On("GetRegionByUserID", mock.AnythingOfType("ctx.CTX"), b.br.UserID).Return(b.br.Region, nil)
	}
	s.mockPayout.On("GetTypeInfos").Return(contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{}})
	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{}, nil)
	s.im.payout = s.mockPayout
	s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*payout.DBRow{}, nil).Maybe()
	s.mockUser.On("GetPlainUsers", getPlainUserArgs...).Return(users, nil)
	// set feature starttime at 1000, filter RevenueToPointLog after 1000
	s.mockFuncs.On("getFeatureStartTime", featureModel.FeatureBabyCoin, mock.AnythingOfType("*region.RegionInfo"), &confModel.DeviceInfo{}).Return(int64(2000))
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	s.mockPurchaseStats.On("GetRevenueToPointLog", mockCTX, userID).Return(
		[]*ledgerM.RevenueToPointLog{
			{
				TimeMs:  2000000, // filter out payment report after timestamp 1000
				NanoUSD: 99990000000,
				Point:   1000,
			},
			{
				TimeMs:  901000,
				NanoUSD: 99990000000,
				Point:   1000,
			},
			{
				TimeMs:  701000,
				NanoUSD: 199990000000,
				Point:   2000,
			},
			{
				TimeMs:  500000,
				NanoUSD: 299990000000,
				Point:   3000,
			},
			{
				TimeMs:  300000,
				NanoUSD: 399990000000,
				Point:   4000,
			},
		}, nil)
	s.mockStatistic.On("GetRevenueToMithLog", mockCTX, userID).Return(
		[]*moneyM.RevenueToMithLog{
			{
				ID:        100,
				Timestamp: 900,
				Revenue:   1,
				Amount:    3.75939849,
			},
			{
				ID:        101,
				Timestamp: 700,
				Revenue:   100,
				Amount:    375.939849,
			},
			{
				ID:        102,
				Timestamp: 501,
				Revenue:   299.99,
				Amount:    1127.7819530151,
			},
			{
				ID:        103,
				Timestamp: 301,
				Revenue:   0.099,
				Amount:    0.0375939849,
			},
		}, nil)

	expPaymentLogs := []*moneyM.PaymentLog{
		{
			Amount:    99,
			Timestamp: 1400,
			Type:      moneyM.LogType_Revenue,
			DetailRevenue: []*moneyM.DetailRevenue{
				{
					Status:    moneyM.DetailRevenueStatus_Billed,
					Timestamp: 1400,
				},
				{
					Status:    moneyM.DetailRevenueStatus_Paid,
					Timestamp: 1400,
				},
			},
		},
		{
			Amount:    55,
			Timestamp: 1200,
			Type:      moneyM.LogType_Revenue,
			DetailRevenue: []*moneyM.DetailRevenue{
				{
					Status:    moneyM.DetailRevenueStatus_Billed,
					Timestamp: 1200,
				},
			},
		},
		{
			Amount:    66,
			Timestamp: 1000,
			Type:      moneyM.LogType_Revenue,
			DetailRevenue: []*moneyM.DetailRevenue{
				{
					Status:    moneyM.DetailRevenueStatus_Billed,
					Timestamp: 1000,
				},
			},
		},
		{
			Amount:          99.99,
			Timestamp:       901,
			Type:            moneyM.LogType_Point,
			DetailRevenue:   []*moneyM.DetailRevenue{},
			DetailTransfer:  1000,
			DetailTransferF: float64(1000),
		},
		{
			Amount:          1,
			Timestamp:       900,
			Type:            moneyM.LogType_Mith,
			DetailRevenue:   []*moneyM.DetailRevenue{},
			DetailTransferF: float64(3.75939849),
		},
		{
			Amount:    77,
			Timestamp: 800,
			Type:      moneyM.LogType_Revenue,
			DetailRevenue: []*moneyM.DetailRevenue{
				{
					Status:    moneyM.DetailRevenueStatus_Billed,
					Timestamp: 800,
				},
				{
					Status:    moneyM.DetailRevenueStatus_Paid,
					Timestamp: 800,
				},
			},
		},
		{
			Amount:          199.99,
			Timestamp:       701,
			Type:            moneyM.LogType_Point,
			DetailRevenue:   []*moneyM.DetailRevenue{},
			DetailTransfer:  2000,
			DetailTransferF: float64(2000),
		},
		{
			Amount:          100,
			Timestamp:       700,
			Type:            moneyM.LogType_Mith,
			DetailRevenue:   []*moneyM.DetailRevenue{},
			DetailTransferF: float64(375.939849),
		},
		{
			Amount:    88,
			Timestamp: 600,
			Type:      moneyM.LogType_Trivia,
			DetailRevenue: []*moneyM.DetailRevenue{
				{
					Status:    moneyM.DetailRevenueStatus_Billed,
					Timestamp: 400,
				},
				{
					Status:    moneyM.DetailRevenueStatus_Paid,
					Timestamp: 600,
				},
				{
					Status:    moneyM.DetailRevenueStatus_Returned,
					Timestamp: 600,
				},
			},
		},
		{
			Amount:          299.99,
			Timestamp:       501,
			Type:            moneyM.LogType_Mith,
			DetailRevenue:   []*moneyM.DetailRevenue{},
			DetailTransferF: float64(1127.7819530151),
		},
		{
			Amount:          299.99,
			Timestamp:       500,
			Type:            moneyM.LogType_Point,
			DetailRevenue:   []*moneyM.DetailRevenue{},
			DetailTransfer:  3000,
			DetailTransferF: float64(3000),
		},
		{
			Amount:          0.099,
			Timestamp:       301,
			Type:            moneyM.LogType_Mith,
			DetailRevenue:   []*moneyM.DetailRevenue{},
			DetailTransferF: float64(0.0375939849),
		},
		{
			Amount:          399.99,
			Timestamp:       300,
			Type:            moneyM.LogType_Point,
			DetailRevenue:   []*moneyM.DetailRevenue{},
			DetailTransfer:  4000,
			DetailTransferF: float64(4000),
		},
	}

	logs := []*moneyM.PaymentLog{}
	cursor := ""
	for {
		pr, err := s.im.GetPaymentReport(mockCTX, userID, cursor, 1000)
		s.NoError(err)
		logs = append(logs, pr.Logs...)

		cursor = pr.Cursor
		if pr.Cursor == "" {
			break
		}
	}

	s.Equal("", cursor)
	s.Equal(len(expPaymentLogs), len(logs))
	for i, l := range expPaymentLogs {
		s.Equal(l, logs[i])
	}
}

func (s *accountingTestSuite) TestNewGetPayments() {
	type dbRefund struct {
		BillID                 string          `db:"billID"`
		FeeCurrency            moneyM.Currency `db:"feeCurrency"`
		FeeAmount              int64           `db:"feeAmount"`
		AmountInPayoutCurrency float64         `db:"amountInPayoutCurrency"`
		TradeID                sql.NullString  `db:"tradeID"`
		DealingID              sql.NullString  `db:"dealingID"`
		FileKey                sql.NullString  `db:"fileKey"`
		TimeMillis             int64           `db:"timeMillis"`
	}
	userID := "paymentUser1"
	userRegion := "TW"
	agentRegion := "US"
	mockBills := []struct {
		br        bill.Element
		pr        payout.DBRow
		refundRow dbRefund
	}{
		{
			br: bill.Element{DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b1",
				Type:                  accountingM.BillType_BILL_REVENUE,
				Status:                accountingM.BillStatus_BILL_ORPHAN,
				UserID:                userID,
				Region:                userRegion,
				Amount:                usd.ToNanoUSD(55),
				TimeMillis:            *************, // balanceID 201806
				CreateTimeMillis:      *************,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 55,
			}},
		},
		{
			br: bill.Element{DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b2",
				Type:                  accountingM.BillType_BILL_REVENUE,
				Status:                accountingM.BillStatus_BILL_APPROVED,
				UserID:                userID,
				Region:                userRegion,
				Amount:                usd.ToNanoUSD(66),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            *************, // balanceID 201805
				CreateTimeMillis:      *************,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 66,
			}},
		},
		{
			br: bill.Element{DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b3",
				Type:                  accountingM.BillType_BILL_REVENUE,
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                userID,
				Region:                userRegion,
				Amount:                usd.ToNanoUSD(108),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            *************, // balanceID 201805
				CreateTimeMillis:      *************,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 108,
			}},
			pr: payout.DBRow{
				TimeMillis:       *************,
				CreatorID:        mockExecutorID,
				BillID:           "b3",
				Currency:         "USD",
				AmountBill:       108,
				Rate:             1,
				Status:           accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND,
				UpdateTimeMillis: *************, // balanceID 201805
			},
			refundRow: dbRefund{
				BillID:                 "b3",
				FeeAmount:              usd.ToNanoUSD(8),
				AmountInPayoutCurrency: 100,
				TimeMillis:             *************,
			},
		},
		{
			br: bill.Element{DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b4",
				Type:                  accountingM.BillType_BILL_REVENUE,
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                userID,
				Region:                userRegion,
				Amount:                usd.ToNanoUSD(77),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            *************, // balanceID 201804
				CreateTimeMillis:      *************,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 77,
			}},
			pr: payout.DBRow{
				TimeMillis:       *************,
				CreatorID:        mockExecutorID,
				BillID:           "b4",
				Currency:         "USD",
				Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
				UpdateTimeMillis: *************, // balanceID 201804
			},
		},
		{
			br: bill.Element{DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b5",
				Type:                  accountingM.BillType_BILL_REVENUE,
				Status:                accountingM.BillStatus_BILL_WRITTEN_OFF,
				UserID:                userID,
				Region:                userRegion,
				Amount:                usd.ToNanoUSD(88),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				TimeMillis:            *************, // balanceID 201804
				CreateTimeMillis:      *************,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 88,
			}},
		},
		{
			br: bill.Element{
				DBRow: bill.DBRow{
					PrevID:                sql.NullInt64{Valid: false},
					IsNew:                 1,
					BillID:                "b6",
					Type:                  accountingM.BillType_BILL_COMBINE_PAY,
					Status:                accountingM.BillStatus_BILL_ORPHAN,
					UserID:                userID,
					Region:                userRegion,
					LocalCurrency:         "USD",
					AmountInLocalCurrency: 100,
					TimeMillis:            *************, // balanceID 201806
					CreateTimeMillis:      *************,
				},
				SubBills: []*bill.Element{
					{DBRow: bill.DBRow{
						IsNew:            1,
						PrevID:           sql.NullInt64{Valid: false},
						BillID:           "b6-1",
						Type:             accountingM.BillType_BILL_REVENUE,
						Status:           accountingM.BillStatus_BILL_COMBINED,
						UserID:           userID,
						Region:           userRegion,
						Amount:           usd.ToNanoUSD(30),
						TimeMillis:       *************, // balanceID 201806
						CreateTimeMillis: *************,
					}},
					{DBRow: bill.DBRow{
						IsNew:                 1,
						PrevID:                sql.NullInt64{Valid: false},
						BillID:                "b6-2",
						Type:                  accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						Status:                accountingM.BillStatus_BILL_COMBINED,
						UserID:                userID,
						Region:                userRegion,
						LocalCurrency:         "USD",
						AmountInLocalCurrency: 70,
						TimeMillis:            *************, // balanceID 201806
						CreateTimeMillis:      *************,
					}},
				},
			},
		},
		{
			br: bill.Element{DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b7",
				Type:                  accountingM.BillType_BILL_REVENUE,
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                userID,
				Region:                userRegion,
				Amount:                usd.ToNanoUSD(100),
				PayoutType:            accountingM.PayoutType_VERIFIED_NANO_USD,
				TimeMillis:            *************, // balanceID 201806
				CreateTimeMillis:      *************,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 100,
			}},
			pr: payout.DBRow{
				TimeMillis:       *************,
				CreatorID:        mockExecutorID,
				BillID:           "b7",
				Currency:         "USD",
				Status:           accountingM.PayoutStatus_PAYOUT_SUCCESS,
				UpdateTimeMillis: *************, // balanceID 201806
			},
		},
		{
			br: bill.Element{DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b8",
				Type:                  accountingM.BillType_BILL_REVENUE,
				Status:                accountingM.BillStatus_BILL_PAYOUT,
				UserID:                userID,
				Region:                userRegion,
				Amount:                usd.ToNanoUSD(100),
				PayoutType:            accountingM.PayoutType_VERIFIED_NANO_USD,
				TimeMillis:            *************, // balanceID 201806
				CreateTimeMillis:      *************,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 100,
			}},
			pr: payout.DBRow{
				TimeMillis:       *************,
				CreatorID:        mockExecutorID,
				BillID:           "b8",
				Currency:         "USD",
				Status:           accountingM.PayoutStatus_PAYOUT_FAIL,
				UpdateTimeMillis: *************, // balanceID 201806
			},
		},
		{
			br: bill.Element{DBRow: bill.DBRow{
				PrevID:                sql.NullInt64{Valid: false},
				IsNew:                 1,
				BillID:                "b9",
				Type:                  accountingM.BillType_BILL_REVENUE,
				Status:                accountingM.BillStatus_BILL_ORPHAN,
				UserID:                userID,
				Region:                userRegion,
				Amount:                usd.ToNanoUSD(10),
				TimeMillis:            *************, // balanceID ********
				CreateTimeMillis:      *************,
				LocalCurrency:         "USD",
				AmountInLocalCurrency: 10,
			}},
		},
	}
	// get local currency mock
	s.mockRate.On("Get", mock.AnythingOfType("ctx.CTX"), agentRegion, mock.Anything).Return(currency.USD, 1.0, nil)
	// get user region mock
	s.mockUser.On("GetRegion", mockCTX, &models.User{UserID: userID}).Return(userRegion, nil).Once()
	// bill mocks
	uniqueUserIDs := map[string]bool{}
	getPlainUserArgs := []interface{}{mock.AnythingOfType("ctx.CTX")}
	users := []models.User{}
	for _, b := range mockBills {
		// create orphan bill
		orphanBill := b.br.DBRow
		orphanBill.Status = accountingM.BillStatus_BILL_ORPHAN
		_, err := s.dbx.NamedExec(bill.InsertBillQueryStr, orphanBill)
		s.NoError(err)
		_, err = s.dbx.NamedExec(bill.InsertBillQueryStr, b.br.DBRow)
		s.NoError(err)
		// create sub bill
		for _, sb := range b.br.SubBills {
			_, err = s.dbx.NamedExec(bill.InsertBillQueryStr, sb.DBRow)
			s.NoError(err)
			_, err = s.dbx.Exec("INSERT INTO `CombinedBillRelation` (`billID`, `subBillID`) VALUES (?, ?)", b.br.DBRow.BillID, sb.DBRow.BillID)
			s.NoError(err)
		}
		_, err = s.dbx.NamedExec(insertAccountingPayoutStmt, b.pr)
		s.NoError(err)
		if b.refundRow.BillID != "" {
			_, err := s.dbx.NamedExec(payout.InsertRefundQueryStr, b.refundRow)
			s.NoError(err)
		}
		s.NoError(err)
		if uniqueUserIDs[b.br.UserID] {
			continue
		}
		uniqueUserIDs[b.br.UserID] = true
		getPlainUserArgs = append(getPlainUserArgs, b.br.UserID)
		users = append(users, models.User{UserID: b.br.UserID})
	}
	s.im.payout = s.mockPayout
	s.mockPayout.On("GetTypeInfos").Return(contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{}})
	s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*payout.DBRow{}, nil).Maybe()
	s.mockUser.On("GetPlainUsers", getPlainUserArgs...).Return(users, nil).Once()
	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{}, nil)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	// revenue mocks
	s.mockContract.On("QueryRevenue", mockCTX, &models.User{UserID: userID}, userRegion).Return(
		[]*moneyM.YearMonthAmount{
			{YM: &moneyM.YearMonth{Year: 2018, Month: 6}, AggregatedAmount: usd.ToNanoUSD(600)},
			{YM: &moneyM.YearMonth{Year: 2018, Month: 5}, AggregatedAmount: usd.ToNanoUSD(800)},
			{YM: &moneyM.YearMonth{Year: 2018, Month: 3}, AggregatedAmount: usd.ToNanoUSD(1000)},
			{YM: &moneyM.YearMonth{Year: 2021, Month: 2}, AggregatedAmount: usd.ToNanoUSD(10)},
		}, nil).Once()

	// revenue to point mocks
	s.mockPurchaseStats.On("GetRevenueToPointLog", mockCTX, userID).Return(
		[]*ledgerM.RevenueToPointLog{
			{
				TimeMs:  1520360089000, // 201803
				NanoUSD: 99990000000,
				Point:   1000,
			},
			{
				TimeMs:  1525630489000, // 201805
				NanoUSD: 199990000000,
				Point:   2000,
			},
		}, nil).Once()

	// revenue to mith mocks
	s.mockStatistic.On("GetRevenueToMithLog", mockCTX, userID).Return(
		[]*moneyM.RevenueToMithLog{
			{
				ID:        100,
				Timestamp: 1520360089,
				Revenue:   1,
				Amount:    3.75939849,
			},
			{
				ID:        101,
				Timestamp: 1525235097,
				Revenue:   100,
				Amount:    375.939849,
			},
			{
				ID:        102,
				Timestamp: 1525235097,
				Revenue:   299.99,
				Amount:    1127.7819530151,
			},
			{
				ID:        103,
				Timestamp: 1528015219,
				Revenue:   0.099,
				Amount:    0.0375939849,
			},
		}, nil)
	loc, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)
	s.mockFuncs.On("getTimeLocation", mockCTX, mock.Anything, agentRegion).Return(loc, nil)
	expPayments := contractM.NewResPayments{
		TotalRevenue:         2410.0, // 600 + 800 + 1000 + 10
		TotalVerifiedRevenue: 100.0,
		TotalWrittenOff:      88.0,
		TotalRefundFee:       8.0,
		TotalPayment:         778.06,  // 99.99 + 1 + 77 + 100 + 299.99 + 199.99 + 0.099
		UnpaidRevenue:        1435.94, // 2400 - 88 - 778.06 - 8 - 100 + 10
		ActiveRevenue:        1174.94, // 1425.94 - 55 - 66 - 30 - 100
		Currency:             "USD",
		MonthRevenuePayments: []*contractM.MonthRevenuePayments{
			{
				Period:  "201803",
				Revenue: 1000,
				Payments: []*contractM.NewPayment{
					{
						ID:             "",
						Amount:         99.99,
						Region:         "NONE",
						Status:         "transferred",
						PaidTime:       1520360089,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_TRANSFER.String(),
					},
					{
						ID:             "100",
						Amount:         1,
						Region:         "NONE",
						Status:         "transferred",
						PaidTime:       1520360089,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_MITH.String(),
					},
				},
			},
			{
				Period:  "201804",
				Revenue: 0,
				Payments: []*contractM.NewPayment{
					{
						ID:             "b4",
						Amount:         77,
						Region:         "TW",
						Status:         "in progress",
						PaidTime:       1525235097,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_PAYOUT.String(),
					},
					{
						ID:             "b5",
						Amount:         88,
						Region:         "TW",
						Status:         "written off",
						PaidTime:       1525509619,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_PAYOUT.String(),
					},
				},
			},
			{
				Period:  "201805",
				Revenue: 800,
				Payments: []*contractM.NewPayment{
					{
						ID:             "b2",
						Amount:         66,
						Region:         "TW",
						Status:         "unpaid",
						PaidTime:       0,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_PAYOUT.String(),
					},
					{
						ID:             "101",
						Amount:         100,
						Region:         "NONE",
						Status:         "transferred",
						PaidTime:       1525235097,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_MITH.String(),
					},
					{
						ID:             "102",
						Amount:         299.99,
						Region:         "NONE",
						Status:         "transferred",
						PaidTime:       1525235097,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_MITH.String(),
					},
					{
						ID:             "",
						Amount:         199.99,
						Region:         "NONE",
						Status:         "transferred",
						PaidTime:       1525630489,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_TRANSFER.String(),
					},
					{
						ID:             "b3",
						Amount:         108,
						Region:         "TW",
						Status:         "refunded",
						PaidTime:       1528015219,
						RefundedAmount: 100,
						RefundedFee:    8,
						Category:       moneyM.Category_PAYOUT.String(),
					},
				},
			},
			{
				Period:  "201806",
				Revenue: 600,
				Payments: []*contractM.NewPayment{
					{
						ID:             "b1",
						Amount:         55,
						Region:         "TW",
						Status:         "billed",
						PaidTime:       0,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_PAYOUT.String(),
					},
					{
						ID:             "b6",
						Amount:         30,
						Region:         "TW",
						Status:         "billed",
						PaidTime:       0,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_PAYOUT.String(),
						CombinedIncome: 100,
						MonthlyIncome:  70,
					},
					{
						ID:             "103",
						Amount:         0.099,
						Region:         "NONE",
						Status:         "transferred",
						PaidTime:       1528015219,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_MITH.String(),
					},
					{
						ID:             "b7",
						Amount:         100,
						Region:         "TW",
						Status:         "verified",
						PaidTime:       1530505497,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_PAYOUT.String(),
					},
					{
						ID:             "b8",
						Amount:         100,
						Region:         "TW",
						Status:         "failed",
						PaidTime:       1530505497,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_PAYOUT.String(),
					},
				},
			},
			{
				Period:  "202102",
				Revenue: 10,
				Payments: []*contractM.NewPayment{
					{
						ID:             "b9",
						Amount:         10,
						Region:         "TW",
						Status:         "billed",
						PaidTime:       0,
						RefundedAmount: 0,
						RefundedFee:    0,
						Category:       moneyM.Category_PAYOUT.String(),
					},
				},
			},
		},
	}

	resPayments, err := s.im.NewGetPayments(mockCTX, &models.User{UserID: userID}, agentRegion, contractM.CurrencyType_CONTRACT)
	s.Require().NoError(err)
	s.Require().Equal(expPayments.TotalRevenue, resPayments.TotalRevenue)
	s.Require().Equal(expPayments.TotalVerifiedRevenue, resPayments.TotalVerifiedRevenue)
	s.Require().Equal(expPayments.TotalWrittenOff, resPayments.TotalWrittenOff)
	s.Require().Equal(expPayments.TotalRefundFee, resPayments.TotalRefundFee)
	s.Require().Equal(expPayments.TotalPayment, resPayments.TotalPayment)
	s.Require().Equal(expPayments.UnpaidRevenue, resPayments.UnpaidRevenue)
	s.Require().Equal(expPayments.ActiveRevenue, resPayments.ActiveRevenue)
	s.Require().Equal(len(expPayments.MonthRevenuePayments), len(resPayments.MonthRevenuePayments))
	for i, ps := range expPayments.MonthRevenuePayments {
		s.Equal(ps, resPayments.MonthRevenuePayments[i])
		for j, p := range ps.Payments {
			s.Equal(p, resPayments.MonthRevenuePayments[i].Payments[j])
		}
	}
}

func (s *accountingTestSuite) TestWriteOffBills() {
	// use mock bill and mock payout
	s.im.bill = s.mockBill
	s.im.bank = s.mockBank
	mockBills := []*bill.Element{
		{
			DBRow: bill.DBRow{
				BillID:     "b1",
				UserID:     "u1",
				Status:     accountingM.BillStatus_UNKNOWN_BILL_STATUS,
				Region:     "TW",
				Amount:     usd.ToNanoUSD(100),
				TimeMillis: 10000,
				DealingID:  "d1",
			},
		},
		{
			DBRow: bill.DBRow{
				BillID:     "b2",
				UserID:     "u2",
				Status:     accountingM.BillStatus_UNKNOWN_BILL_STATUS,
				Region:     "TW",
				Amount:     usd.ToNanoUSD(200),
				TimeMillis: 20000,
				DealingID:  "d2",
			},
		},
	}

	writeOffExecutorID := "writeOffExecutor"
	billIDs := []string{}
	writtenOffBills := []*bill.Element{} // bills returned by bill.UpdateStatus
	getPlainUserArgs := []interface{}{mock.AnythingOfType("ctx.CTX")}
	mockUsers := []models.User{}
	for _, b := range mockBills {
		billIDs = append(billIDs, b.BillID)
		writtenOffBill := *b
		writtenOffBill.TimeMillis = b.TimeMillis + 50000
		writtenOffBill.Status = accountingM.BillStatus_BILL_WRITTEN_OFF
		writtenOffBills = append(writtenOffBills, &writtenOffBill)
		getPlainUserArgs = append(getPlainUserArgs, b.UserID)
		mockUsers = append(mockUsers, models.User{UserID: b.UserID})
	}

	s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), (*sqlx.Tx)(nil), billIDs).Return(mockBills, nil).Once()
	s.mockBill.On("UpdateStatus", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), writeOffExecutorID, billIDs, accountingM.BillStatus_BILL_WRITTEN_OFF).Return(writtenOffBills, nil).Once()
	s.mockBill.On("Settle", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]*bill.Element"), true, mock.AnythingOfType("func(*sqlx.Tx, int64) error")).Return(nil).Run(func(args mock.Arguments) {
		arg := args.Get(3).(func(*sqlx.Tx, int64) error)
		s.NoError(mdb.Transactx(mockCTX, s.dbx, func(tx *sqlx.Tx) error {
			return arg(tx, 0)
		}))
	}).Once()
	s.mockUser.On("GetPlainUsers", getPlainUserArgs...).Return(mockUsers, nil).Times(1)
	s.mockFuncs.On("pushBillChangedMessage").Return(true)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	logs, err := s.im.WriteOffBills(mockCTX, writeOffExecutorID, billIDs)
	s.NoError(err)
	for i, l := range logs {
		s.Equal(writtenOffBills[i].BillID, l.BillID)
		s.Equal(writtenOffBills[i].UserID, l.UserID)
		s.Equal(writtenOffBills[i].Status, l.Status)
		s.Equal(writtenOffBills[i].Region, l.Region)
		s.Equal(usd.ToUSD(writtenOffBills[i].Amount), l.Amount)
		s.Equal(writtenOffBills[i].TimeMillis/1000, l.WrittenOffUnixTime)
		s.Equal(writeOffExecutorID, l.WrittenOffExecutorID)
	}
}

func (s *accountingTestSuite) TestAmountsTotal() {
	amountsInNanoUSD := []int64{
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		142720811868,
		111559137347,
		331359819657,
		107836845232,
		178871028189,
		730335349613,
		140553404183,
		327656482598,
		118716591145,
		253402537884,
		126741866528,
		100218258091,
		105116563861,
		100137278058,
		151294557879,
		257051820703,
		101430157992,
		314638150596,
		109355856010,
		133694495966,
		210680480370,
		116283344854,
		164189338245,
		156261230605,
		183935939369,
		120198689194,
		112714235669,
		155085697541,
		113999012203,
		137560997590,
		103404041719,
		141210210684,
		126147827831,
		144320174404,
		140681491940,
		152484196972,
		221561801557,
		112889195047,
		117999653976,
		136561562305,
		267942689263,
		185605155871,
		102461418674,
		223535258267,
		211715519776,
		124703040887,
		193016370671,
		134134273830,
		108496893362,
		150840754361,
		174830721282,
		138791055409,
		116118296798,
		162862735447,
		101668657592,
		102499699979,
		106764194047,
		122347343426,
		172070806209,
		170248633149,
		101906217862,
		123253345707,
		110667828400,
		151742932458,
		109214219744,
		123272847948,
		111912932746,
		112851581881,
		181691208409,
		203390650185,
		227170804181,
		125924389281,
		157230718629,
		119764446748,
		124394654526,
		113758678464,
		111924512560,
		110902818280,
		104203749962,
		105884665630,
		137996973101,
		119998778626,
		308307393092,
		108226819121,
		123051243330,
		105963079358,
		511652758538,
		144608223146,
		127585424939,
		143195667772,
		107573220931,
		139118403026,
		131684807511,
		118989846175,
		113660967578,
		115509416826,
		123225458358,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
		************,
	}

	billAmounts := &amounts{}
	for _, a := range amountsInNanoUSD {
		billAmounts.append(usd.ToUSD(a))
	}
	s.Equal(26056.14, billAmounts.total(moneyM.Currency_NANO_USD.String()))
	s.Equal(26055.33, billAmounts.total(currency.USD.String()))
	s.Equal(25967.0, billAmounts.total(currency.TWD.String()))
}

func (s *accountingTestSuite) TestGetMonthlyPayoutRates() {
	mockNow := time.Date(2018, time.January, 5, 0, 0, 0, 0, time.UTC)
	mockNowRateMonth := mockNow.AddDate(0, -1, 0).Format("2006-01")
	ts := []time.Time{
		mockNow,
		time.Date(2018, time.January, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.December, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.November, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.October, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.September, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.August, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.July, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.June, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.May, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.April, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.March, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
		time.Date(2017, time.February, 1, 0, 0, 0, 0, time.UTC).Add(-time.Nanosecond),
	}
	mockRates := [][]*exchangeM.RateInfo{}
	expRateInfos := []*accountingM.PayoutRateInfo{}
	twdCurrencySince := time.Date(2017, 4, 1, 0, 0, 0, 0, time.UTC)
	for _, t := range ts {
		tmpDate := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, time.UTC).AddDate(0, -1, 0)
		var rates []*exchangeM.RateInfo
		var localCur currency.Unit
		var rateRet []accountingM.Rate
		if tmpDate.Before(twdCurrencySince) {
			rates = []*exchangeM.RateInfo{
				{Region: "TW", FromCurrency: currency.USD, ToCurrency: currency.USD, Rate: 1.0, EffectiveTime: t, CreateTime: t, ExecUserID: mockExecutorID},
				{Region: "TW", FromCurrency: currency.USD, ToCurrency: currency.TWD, Rate: 32, EffectiveTime: t, CreateTime: t, ExecUserID: mockExecutorID},
				{Region: "TW", FromCurrency: currency.USD, ToCurrency: currency.HKD, Rate: 8.5, EffectiveTime: t, CreateTime: t, ExecUserID: mockExecutorID},
			}
			localCur = currency.USD
			rateRet = []accountingM.Rate{
				{Currency: currency.TWD, Rate: 32},
				{Currency: currency.USD, Rate: 1.0},
			}
		} else {
			rates = []*exchangeM.RateInfo{
				{Region: "TW", FromCurrency: currency.TWD, ToCurrency: currency.USD, Rate: 0.03333, EffectiveTime: t, CreateTime: t, ExecUserID: mockExecutorID},
				{Region: "TW", FromCurrency: currency.TWD, ToCurrency: currency.TWD, Rate: 1, EffectiveTime: t, CreateTime: t, ExecUserID: mockExecutorID},
				{Region: "TW", FromCurrency: currency.TWD, ToCurrency: currency.HKD, Rate: 4, EffectiveTime: t, CreateTime: t, ExecUserID: mockExecutorID},
			}
			localCur = currency.TWD
			rateRet = []accountingM.Rate{
				{Currency: currency.TWD, Rate: 1},
				{Currency: currency.USD, Rate: 0.03333},
			}
		}

		mockRates = append(mockRates, rates)

		rateMonth := tmpDate.Format("2006-01")
		expRateInfos = append(expRateInfos, &accountingM.PayoutRateInfo{
			Region:             "TW",
			RegionGroup:        "TW",
			Month:              rateMonth,
			Currency:           localCur,
			RateInfos:          rateRet,
			ExecutorID:         mockExecutorID,
			LastUpdateUnixTime: t.Unix(),
			Status:             accountingM.PayoutRateStatus_CONFIRMED_RATE_STATUS,
		})
		s.mockRate.On("Get", mock.AnythingOfType("ctx.CTX"), "TW", tmpDate).Return(localCur, 1.0, nil).Once()
	}

	payoutTypeInfos := contractM.PayoutTypeInfos{
		Infos: []*contractM.PayoutTypeInfo{
			{
				RegionAvailableCurrencies: []*contractM.RegionAvailableCurrencies{
					{
						RegionGroup:     "TW",
						DefaultCurrency: currency.USD.String(),
						Currencies: []string{
							currency.USD.String(),
							currency.TWD.String(),
						},
					},
				},
			},
		},
	}

	tests := []struct {
		desc                 string
		regions              []string
		getTimeLocRet        []interface{}
		mockGetMultiByRegion bool
		getMultiByRegionRet  []interface{}
		mockGetTypesInfos    bool
		getTypesInfosRet     []interface{}
		mockRateMonthExists  bool
		rateMonthExistsRet   []interface{}
	}{
		{
			desc:                 "confirmed",
			regions:              []string{"TW"},
			getTimeLocRet:        []interface{}{time.UTC, nil},
			mockGetMultiByRegion: true,
			getMultiByRegionRet:  []interface{}{mockRates, nil},
			mockGetTypesInfos:    true,
			getTypesInfosRet:     []interface{}{payoutTypeInfos},
			mockRateMonthExists:  true,
			rateMonthExistsRet:   []interface{}{true, nil},
		},
	}

	TimeNow = s.mockFuncs.timeNow
	s.im.approval = s.mockApproval
	s.im.payout = s.mockPayout
	for _, t := range tests {
		s.mockFuncs.On("timeNow").Return(mockNow).Once()
		s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(t.getTimeLocRet...).Once()
		if t.mockGetMultiByRegion {
			s.mockExchangeRate.On("GetMultiByRegion", mock.AnythingOfType("ctx.CTX"), exchangeM.Type_ACCOUNTING_MONTHLY_PAYOUT_TYPE, t.regions[0], ts).Return(t.getMultiByRegionRet...).Once()
		}
		if t.mockGetTypesInfos {
			s.mockPayout.On("GetTypeInfos").Return(t.getTypesInfosRet...).Once()
		}
		if t.mockRateMonthExists {
			s.mockApproval.On("RateMonthExists", mock.AnythingOfType("ctx.CTX"), t.regions, mockNowRateMonth).Return(t.rateMonthExistsRet...).Once()
		}

		infos, err := s.im.GetMonthlyPayoutRates(mockCTX, t.regions)
		s.NoError(err)
		s.Equal(expRateInfos, infos)
	}
}

func (s *accountingTestSuite) TestGetBillRates() {
	TimeNow = s.mockFuncs.timeNow
	s.im.approval = s.mockApproval
	s.im.payout = s.mockPayout

	region := "TW"
	mockBillID := "b1"
	tests := []struct {
		desc                           string
		billLocalCurrency              currency.Unit
		approvalRateMonth              string
		setCurrentMonthRateUnConfirmed bool
		mockPayoutRates                map[string]pRate.PayoutRates
		expBillRates                   map[string]accountingM.BillRate
		expError                       error
	}{
		{
			desc:                           "success",
			billLocalCurrency:              currency.USD,
			approvalRateMonth:              "2017-09",
			setCurrentMonthRateUnConfirmed: false,
			mockPayoutRates:                map[string]pRate.PayoutRates{},
			expBillRates: map[string]accountingM.BillRate{
				mockBillID: {
					LocalCurrency:     currency.USD,
					LocalCurrencyRate: 1,
					PayoutRates: map[currency.Unit]float64{
						currency.TWD: 32,
						currency.HKD: 8.5,
						currency.USD: 1,
					},
				},
			},
			expError: nil,
		},
		{
			desc:                           "get rate from PayoutExchangeRate",
			billLocalCurrency:              currency.USD,
			approvalRateMonth:              "",
			setCurrentMonthRateUnConfirmed: false,
			mockPayoutRates: map[string]pRate.PayoutRates{
				mockBillID: {
					&pRate.PayoutRate{
						FromCurrency: currency.USD,
						ToCurrency:   currency.TWD,
						Rate:         32,
					},
					&pRate.PayoutRate{
						FromCurrency: currency.USD,
						ToCurrency:   currency.USD,
						Rate:         1,
					},
					&pRate.PayoutRate{
						FromCurrency: currency.USD,
						ToCurrency:   currency.HKD,
						Rate:         8.5,
					},
				},
			},
			expBillRates: map[string]accountingM.BillRate{
				mockBillID: {
					LocalCurrency:     currency.USD,
					LocalCurrencyRate: 1,
					PayoutRates: map[currency.Unit]float64{
						currency.TWD: 32,
						currency.HKD: 8.5,
						currency.USD: 1,
					},
				},
			},
			expError: nil,
		},
		{
			desc:                           "rate is not confirmed status",
			billLocalCurrency:              currency.USD,
			approvalRateMonth:              "2017-12",
			setCurrentMonthRateUnConfirmed: true,
			mockPayoutRates:                map[string]pRate.PayoutRates{},
			expBillRates:                   nil,
			expError:                       fmt.Errorf("rate month:%v of billID:%v rates not found", "2017-12", mockBillID),
		},
		{
			desc:                           "payout rate is not found",
			billLocalCurrency:              currency.USD,
			approvalRateMonth:              "",
			setCurrentMonthRateUnConfirmed: false,
			mockPayoutRates:                map[string]pRate.PayoutRates{},
			expBillRates:                   nil,
			expError:                       fmt.Errorf("cannot get payout rate"),
		},
		{
			desc:                           "month rate not found",
			billLocalCurrency:              currency.USD,
			approvalRateMonth:              "2016-11",
			setCurrentMonthRateUnConfirmed: false,
			mockPayoutRates:                map[string]pRate.PayoutRates{},
			expBillRates:                   nil,
			expError:                       fmt.Errorf("rate month:%v of billID:%v rates not found", "2016-11", mockBillID),
		},
	}

	mockBillCreateTimeMillis := int64(99999)
	mockBills := []*bill.Element{
		{
			DBRow: bill.DBRow{
				BillID:           mockBillID,
				Region:           region,
				CreateTimeMillis: mockBillCreateTimeMillis,
			},
		},
	}

	for _, t := range tests {
		// mock for payout rates
		rateMonth := "2017-12"
		mockNow := time.Date(2018, time.January, 5, 0, 0, 0, 0, time.UTC)
		s.mockFuncs.On("timeNow").Return(mockNow).Once()
		s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, region).Return(time.UTC, nil).Once()
		s.mockPayout.On("GetTypeInfos").Return(mockPayoutTypeInfos).Once()
		if t.setCurrentMonthRateUnConfirmed {
			mockRates := mockPayoutRates()
			mockRates[0][0].EffectiveTime = time.Date(2017, time.December, 5, 0, 0, 0, 0, time.UTC)
			s.mockExchangeRate.On("GetMultiByRegion", mock.AnythingOfType("ctx.CTX"), exchangeM.Type_ACCOUNTING_MONTHLY_PAYOUT_TYPE, region, mock.Anything).Return(mockRates, nil).Once()
			s.mockApproval.On("RateMonthExists", mock.AnythingOfType("ctx.CTX"), []string{region}, rateMonth).Return(false, nil).Once()
		} else {
			mockRates := mockPayoutRates()
			s.mockExchangeRate.On("GetMultiByRegion", mock.AnythingOfType("ctx.CTX"), exchangeM.Type_ACCOUNTING_MONTHLY_PAYOUT_TYPE, region, mock.Anything).Return(mockRates, nil).Once()
			s.mockApproval.On("RateMonthExists", mock.AnythingOfType("ctx.CTX"), []string{region}, rateMonth).Return(true, nil).Once()
		}
		mockApprovalInfo := map[string]approvalInfo{
			mockBillID: {
				ApprovalID: "a1",
				RateMonth:  t.approvalRateMonth,
			},
		}
		s.mockRate.ExpectedCalls = nil
		s.mockRate.On("Get", mock.AnythingOfType("ctx.CTX"), region, mock.AnythingOfType("time.Time")).Return(t.billLocalCurrency, 1.0, nil)
		s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(t.mockPayoutRates, nil).Once()

		billRates, err := s.im.getBillRates(mockCTX, mockBills, mockApprovalInfo)
		s.Equal(t.expError, err, t.desc)
		s.Equal(t.expBillRates, billRates)
	}
}

func (s *accountingTestSuite) TestSetBillDailyPayoutRate() {
	mockNow := time.Date(2019, time.April, 1, 0, 0, 0, 0, time.UTC)
	TimeNow = s.mockFuncs.timeNow
	s.im.payout = s.mockPayout
	s.mockPayout.On("GetTypeInfos").Return(mockPayoutTypeInfos)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)
	s.createTestingData(s.im, s.dbx)

	tests := []*struct {
		desc          string
		inputBillIDs  []string
		payoutBillIDs map[string]bool
		inputRate     accountingM.Rate
		mockFunc      func()
		expError      error
	}{
		{
			desc: "succ",
			inputBillIDs: []string{
				"wallet-3",
				"wallet-2",
				"wallet-1",
			},
			payoutBillIDs: map[string]bool{"wallet-1": true},
			inputRate: accountingM.Rate{
				Currency: currency.TWD,
				Rate:     31,
			},
			mockFunc: func() {
				s.mockFuncs.On("timeNow").Return(mockNow).Once()
				s.mockPayoutRate.On("Set", mock.AnythingOfType("ctx.CTX"), mockExecutorID, []string{"TW"}, pRate.PayoutRates{
					&pRate.PayoutRate{
						FromCurrency: currency.USD,
						ToCurrency:   currency.TWD,
						Rate:         31,
						Type: pRate.RateType{
							Type: exchangeM.Type_ACCOUNTING_DAILY_PAYOUT_TYPE,
							DailyTypeInfo: &pRate.DailyRateTypeInfo{
								Time: mockNow,
							},
						},
					},
				}).Return(nil).Once()
				s.mockPayoutRate.On("SetBillRatesByType", mock.AnythingOfType("ctx.CTX"), mockExecutorID, mock.AnythingOfType("[]*bill.Element"),
					pRate.RateType{
						Type: exchangeM.Type_ACCOUNTING_DAILY_PAYOUT_TYPE,
						DailyTypeInfo: &pRate.DailyRateTypeInfo{
							Time: mockNow,
						},
					},
				).Return(nil).Once()
				s.mockPayout.On("Patch", mock.AnythingOfType("ctx.CTX"), mockExecutorID, []string{"wallet-1"}, payout.PatchFieldValues{
					PayoutRate: &payout.PatchPayoutRate{
						FromCurrency: currency.USD,
						ToCurrency:   currency.TWD,
						Rate:         31,
					},
				}).Return(nil, nil).Once()
				s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{
					"wallet-2": {
						&pRate.PayoutRate{
							FromCurrency: currency.USD,
							ToCurrency:   currency.TWD,
							Rate:         31,
							Type: pRate.RateType{
								Type: exchangeM.Type_ACCOUNTING_DAILY_PAYOUT_TYPE,
								DailyTypeInfo: &pRate.DailyRateTypeInfo{
									Time: mockNow,
								},
							},
						},
					},
					"wallet-3": {
						&pRate.PayoutRate{
							FromCurrency: currency.USD,
							ToCurrency:   currency.TWD,
							Rate:         31,
							Type: pRate.RateType{
								Type: exchangeM.Type_ACCOUNTING_DAILY_PAYOUT_TYPE,
								DailyTypeInfo: &pRate.DailyRateTypeInfo{
									Time: mockNow,
								},
							},
						},
					},
					"wallet-1": {
						&pRate.PayoutRate{
							FromCurrency: currency.USD,
							ToCurrency:   currency.TWD,
							Rate:         31,
							Type: pRate.RateType{
								Type: exchangeM.Type_ACCOUNTING_DAILY_PAYOUT_TYPE,
								DailyTypeInfo: &pRate.DailyRateTypeInfo{
									Time: mockNow,
								},
							},
						},
					},
				}, nil).Once()
				s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*payout.DBRow{}, nil).Once()

				s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "u16").Return([]models.User{
					{
						UserID:            "u16",
						PaypalVerifyState: 2,
					},
				}, nil)
			},
		},
		{
			desc: "failed due to wrong bill type",
			inputBillIDs: []string{
				"********-b13-orphan",
			},
			inputRate: accountingM.Rate{
				Currency: currency.TWD,
				Rate:     31,
			},
			expError: fmt.Errorf("im.SetBillDailyPayoutRate bills must be in wallet type"),
		},
	}

	for _, t := range tests {
		if t.mockFunc != nil {
			t.mockFunc()
		}
		logs, err := s.im.SetBillDailyPayoutRate(mockCTX, mockExecutorID, t.inputBillIDs, t.inputRate)
		if t.expError != nil {
			s.Equal(t.expError, err, t.desc)
			continue
		}
		for i, log := range logs {
			s.Equal(log.BillID, t.inputBillIDs[i], t.desc)
			// Temporarily we don't check payout bills
			if t.payoutBillIDs[log.BillID] {
				continue
			}
			s.Equal(t.inputRate.Currency.String(), log.PayoutCurrency, t.desc, log.BillID)
			s.Equal(t.inputRate.Rate, log.PayoutRate, t.desc, log.BillID)
		}
	}
}

func (s *accountingTestSuite) TestGetQueryContractTime() {
	loc, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)
	testCases := []struct {
		time    time.Time
		region  string
		expTime int64
	}{
		{
			time:    t20171105,
			region:  "NONE",
			expTime: mtime.MilliSecond(settle20171105),
		},
		{
			time:    time.Date(2018, time.September, 10, 0, 0, 0, 0, loc),
			region:  "TW",
			expTime: mtime.MilliSecond(time.Date(2018, time.August, 1, bill.SettlementHour, 0, 0, 0, loc)),
		},
		{
			time:    time.Date(2018, time.September, 30, 23, 0, 0, 0, loc),
			region:  "JP",
			expTime: mtime.MilliSecond(time.Date(2018, time.September, 1, bill.SettlementHour, 0, 0, 0, loc)),
		},
	}
	for _, c := range testCases {
		t, err := s.im.getQueryContractTime(mockCTX, c.time, c.region)
		s.NoError(err)
		s.Equal(c.expTime, t)
	}
}

func (s *accountingTestSuite) TestGetPaymentActionConfig() {
	getPaymentActions = func(billType accountingM.BillType, payoutType accountingM.PayoutType, group string) []string {
		if billType.IsWalletType() {
			if payoutType == accountingM.PayoutType_BANK_SMBC {
				return []string{"SUBMIT_TO_PAY"}
			}
			return []string{"DOWNLOAD_REPORT"}
		}
		return []string{"DOWNLOAD_PAYOUT_FILE"}
	}
	getDefaultPaymentActions = func(billType accountingM.BillType, _ string) []string {
		if billType.IsWalletType() {
			return []string{"MARK_AS_REFUND"}
		}
		return []string{"MARK_AS_WRITTEN_OFF"}
	}
	originalBillTypeSettings := getBillTypeSettings
	getBillTypeSettings = func() []config.BillTypeSetting {
		return []config.BillTypeSetting{
			{
				BillType: accountingM.BillType_BILL_REVENUE,
				AvailableAccountTypeRegionGroups: map[string][]payoutAccountM.AccountType{
					"TW":           {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT},
					"JP":           {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					bill.NonRegion: {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
				},
			},
			{
				BillType: accountingM.BillType_BILL_WALLET_NONE_REVENUE,
				AvailableAccountTypeRegionGroups: map[string][]payoutAccountM.AccountType{
					bill.NonRegion: {payoutAccountM.AccountType_LOCAL_ACCOUNT},
				},
			},
			{
				BillType: accountingM.BillType_BILL_WALLET_REWARD,
				AvailableAccountTypeRegionGroups: map[string][]payoutAccountM.AccountType{
					"TW":           {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT},
					"JP":           {payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
					bill.NonRegion: {payoutAccountM.AccountType_OFFLINE_ACCOUNT},
				},
			},
		}
	}
	expConfigs := []*accountingM.PaymentActionConfig{
		{
			RegionGroup: "JP",
			BillTypeConfigs: []*accountingM.PaymentActionBillTypeConfig{
				{
					BillType: accountingM.BillType_BILL_REVENUE,
					AccountTypeConfigs: []*accountingM.PaymentActionAccountTypeConfig{
						{
							AccountType: payoutAccountM.AccountType_LOCAL_ACCOUNT,
							Actions:     []string{"DOWNLOAD_PAYOUT_FILE"},
						},
						{
							AccountType: payoutAccountM.AccountType_OFFLINE_ACCOUNT,
							Actions:     []string{"DOWNLOAD_PAYOUT_FILE"},
						},
						{
							AccountType: payoutAccountM.AccountType_BBRICH_ACCOUNT,
							Actions:     []string{"DOWNLOAD_PAYOUT_FILE"},
						},
						{
							AccountType: payoutAccountM.AccountType_UNKNOWN_ACCOUNT_TYPE,
							Actions:     []string{"MARK_AS_WRITTEN_OFF"},
						},
					},
				},
				{
					BillType: accountingM.BillType_BILL_WALLET_REWARD,
					AccountTypeConfigs: []*accountingM.PaymentActionAccountTypeConfig{
						{
							AccountType: payoutAccountM.AccountType_LOCAL_ACCOUNT,
							Actions:     []string{"SUBMIT_TO_PAY"},
						},
						{
							AccountType: payoutAccountM.AccountType_OFFLINE_ACCOUNT,
							Actions:     []string{"DOWNLOAD_REPORT"},
						},
						{
							AccountType: payoutAccountM.AccountType_BBRICH_ACCOUNT,
							Actions:     []string{"DOWNLOAD_REPORT"},
						},
						{
							AccountType: payoutAccountM.AccountType_UNKNOWN_ACCOUNT_TYPE,
							Actions:     []string{"MARK_AS_REFUND"},
						},
					},
				},
			},
		},
		{
			RegionGroup: bill.NonRegion,
			BillTypeConfigs: []*accountingM.PaymentActionBillTypeConfig{
				{
					BillType: accountingM.BillType_BILL_REVENUE,
					AccountTypeConfigs: []*accountingM.PaymentActionAccountTypeConfig{
						{
							AccountType: payoutAccountM.AccountType_OFFLINE_ACCOUNT,
							Actions:     []string{"DOWNLOAD_PAYOUT_FILE"},
						},
						{
							AccountType: payoutAccountM.AccountType_UNKNOWN_ACCOUNT_TYPE,
							Actions:     []string{"MARK_AS_WRITTEN_OFF"},
						},
					},
				},
				{
					BillType: accountingM.BillType_BILL_WALLET_NONE_REVENUE,
					AccountTypeConfigs: []*accountingM.PaymentActionAccountTypeConfig{
						{
							AccountType: payoutAccountM.AccountType_LOCAL_ACCOUNT,
							Actions:     []string{"DOWNLOAD_REPORT"},
						},
						{
							AccountType: payoutAccountM.AccountType_UNKNOWN_ACCOUNT_TYPE,
							Actions:     []string{"MARK_AS_REFUND"},
						},
					},
				},
				{
					BillType: accountingM.BillType_BILL_WALLET_REWARD,
					AccountTypeConfigs: []*accountingM.PaymentActionAccountTypeConfig{
						{
							AccountType: payoutAccountM.AccountType_OFFLINE_ACCOUNT,
							Actions:     []string{"DOWNLOAD_REPORT"},
						},
						{
							AccountType: payoutAccountM.AccountType_UNKNOWN_ACCOUNT_TYPE,
							Actions:     []string{"MARK_AS_REFUND"},
						},
					},
				},
			},
		},
		{
			RegionGroup: "TW",
			BillTypeConfigs: []*accountingM.PaymentActionBillTypeConfig{
				{
					BillType: accountingM.BillType_BILL_REVENUE,
					AccountTypeConfigs: []*accountingM.PaymentActionAccountTypeConfig{
						{
							AccountType: payoutAccountM.AccountType_LOCAL_ACCOUNT,
							Actions:     []string{"DOWNLOAD_PAYOUT_FILE"},
						},
						{
							AccountType: payoutAccountM.AccountType_FOREIGN_ACCOUNT,
							Actions:     []string{"DOWNLOAD_PAYOUT_FILE"},
						},
						{
							AccountType: payoutAccountM.AccountType_UNKNOWN_ACCOUNT_TYPE,
							Actions:     []string{"MARK_AS_WRITTEN_OFF"},
						},
					},
				},
				{
					BillType: accountingM.BillType_BILL_WALLET_REWARD,
					AccountTypeConfigs: []*accountingM.PaymentActionAccountTypeConfig{
						{
							AccountType: payoutAccountM.AccountType_LOCAL_ACCOUNT,
							Actions:     []string{"DOWNLOAD_REPORT"},
						},
						{
							AccountType: payoutAccountM.AccountType_FOREIGN_ACCOUNT,
							Actions:     []string{"DOWNLOAD_REPORT"},
						},
						{
							AccountType: payoutAccountM.AccountType_UNKNOWN_ACCOUNT_TYPE,
							Actions:     []string{"MARK_AS_REFUND"},
						},
					},
				},
			},
		},
	}
	configs, err := s.im.GetPaymentActionConfig(mockCTX)
	s.Equal(nil, err)
	s.Equal(len(expConfigs), len(configs))
	for i, expConfig := range expConfigs {
		s.Equal(expConfig, configs[i])
	}
	getPaymentActions = config.GetPaymentActions
	getBillTypeSettings = originalBillTypeSettings
}

func (s *accountingTestSuite) TestGetPayoutConfigs() {
	bankConfig := &contractM.PayoutTypeInfo{
		PayoutType:   int32(accountingM.PayoutType_BANK_TBB),
		Name:         "台灣企銀",
		RegionGroups: []string{"TW"},
		RegionAvailableCurrencies: []*contractM.RegionAvailableCurrencies{
			{
				RegionGroup:     "TW",
				DefaultCurrency: currency.TWD.String(),
				Currencies: []string{
					currency.TWD.String(),
				},
			},
		},
	}
	bankAccountTypeInfo := &contractM.AccountTypeInfo{
		AccountType:               payoutAccountM.AccountType_LOCAL_ACCOUNT,
		RegionGroups:              bankConfig.RegionGroups,
		RegionAvailableCurrencies: bankConfig.RegionAvailableCurrencies,
	}
	offlineConfig := &contractM.PayoutTypeInfo{
		PayoutType:   int32(accountingM.PayoutType_OFFLINE),
		Name:         "OFFLINE",
		RegionGroups: []string{"TW", "MENA", "PH", "ID", "TH", "VN", "MM", "IN", "CN"},
		RegionAvailableCurrencies: []*contractM.RegionAvailableCurrencies{
			{
				RegionGroup:     "TW",
				DefaultCurrency: currency.USD.String(),
				Currencies: []string{
					currency.USD.String(),
					currency.TWD.String(),
					currency.HKD.String(),
				},
			},
			{
				RegionGroup:     "MENA",
				DefaultCurrency: currency.USD.String(),
				Currencies: []string{
					currency.USD.String(),
				},
			},
			{
				RegionGroup:     "PH",
				DefaultCurrency: currency.USD.String(),
				Currencies: []string{
					currency.USD.String(),
				},
			},
			{
				RegionGroup:     "ID",
				DefaultCurrency: currency.USD.String(),
				Currencies: []string{
					currency.USD.String(),
				},
			},
			{
				RegionGroup:     "TH",
				DefaultCurrency: currency.USD.String(),
				Currencies: []string{
					currency.USD.String(),
				},
			},
			{
				RegionGroup:     "VN",
				DefaultCurrency: currency.USD.String(),
				Currencies: []string{
					currency.USD.String(),
				},
			},
			{
				RegionGroup:     "MM",
				DefaultCurrency: currency.USD.String(),
				Currencies: []string{
					currency.USD.String(),
				},
			},
			{
				RegionGroup:     "IN",
				DefaultCurrency: currency.INR.String(),
				Currencies: []string{
					currency.INR.String(),
				},
			},
			{
				RegionGroup:     "CN",
				DefaultCurrency: currency.CNY.String(),
				Currencies: []string{
					currency.CNY.String(),
				},
			},
		},
	}
	offlineAccountTypeInfo := &contractM.AccountTypeInfo{
		AccountType:               payoutAccountM.AccountType_OFFLINE_ACCOUNT,
		RegionGroups:              offlineConfig.RegionGroups,
		RegionAvailableCurrencies: offlineConfig.RegionAvailableCurrencies,
	}
	paypalConfig := &contractM.PayoutTypeInfo{
		PayoutType:   int32(accountingM.PayoutType_PAYPAL),
		Name:         "PAYPAL",
		RegionGroups: []string{"TW", "HK"},
		RegionAvailableCurrencies: []*contractM.RegionAvailableCurrencies{
			{
				RegionGroup:     "TW",
				DefaultCurrency: currency.TWD.String(),
				Currencies: []string{
					currency.TWD.String(),
					currency.USD.String(),
				},
			},
			{
				RegionGroup:     "HK",
				DefaultCurrency: currency.HKD.String(),
				Currencies: []string{
					currency.HKD.String(),
				},
			},
		},
	}
	paypalAccountTypeInfo := &contractM.AccountTypeInfo{
		AccountType:               payoutAccountM.AccountType_PAYPAL_ACCOUNT,
		RegionGroups:              paypalConfig.RegionGroups,
		RegionAvailableCurrencies: paypalConfig.RegionAvailableCurrencies,
	}
	mockNow := time.Date(2018, time.January, 5, 0, 0, 0, 0, time.UTC)

	TimeNow = s.mockFuncs.timeNow
	s.im.payout = s.mockPayout

	tests := []struct {
		desc                string
		inputRegions        []string
		mockPayoutTypeInfos contractM.PayoutTypeInfos
		mockRegionCurrency  []currency.Unit
		expConfigs          []*accountingM.ResRegionPayoutConfig
		expErr              error
	}{
		{
			desc:                "success get TW",
			inputRegions:        []string{"TW"},
			mockPayoutTypeInfos: contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{paypalConfig, bankConfig, offlineConfig}},
			mockRegionCurrency:  []currency.Unit{currency.USD},
			expConfigs: []*accountingM.ResRegionPayoutConfig{
				{
					RegionGroup:      "TW",
					Regions:          []string{"TW"},
					PayoutTypeInfos:  contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{bankConfig, offlineConfig}},
					AccountTypeInfos: contractM.AccountTypeInfos{Infos: []*contractM.AccountTypeInfo{paypalAccountTypeInfo, bankAccountTypeInfo, offlineAccountTypeInfo}},
					BillTypeInfos: []accountingM.BillTypeInfo{
						{
							Type:          accountingM.BillType_BILL_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_TBB, accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_TBB, accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeReward},
						},
						{
							Type:          accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_TBB, accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeContractMonthlyIncome},
						},
						{
							Type:          accountingM.BillType_BILL_COMBINE_PAY,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_TBB, accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeCombinePay, accountingM.ApprovalTypeAgency},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_TBB, accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_COMPANY_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWallet},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_TBB, accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_COMPANY_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_TBB, accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_LOCAL_ACCOUNT, payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_COMPANY_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletReward},
						},
					},
					LocalCurrency: "USD",
				},
			},
		},
		{
			desc:                "success get MENA",
			inputRegions:        []string{"AE"},
			mockPayoutTypeInfos: contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{paypalConfig, bankConfig, offlineConfig}},
			mockRegionCurrency:  []currency.Unit{currency.USD},
			expConfigs: []*accountingM.ResRegionPayoutConfig{
				{
					RegionGroup:      "MENA",
					Regions:          []string{"AE", "DJ"},
					PayoutTypeInfos:  contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{offlineConfig}},
					AccountTypeInfos: contractM.AccountTypeInfos{Infos: []*contractM.AccountTypeInfo{offlineAccountTypeInfo}},
					BillTypeInfos: []accountingM.BillTypeInfo{
						{
							Type:          accountingM.BillType_BILL_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeReward},
						},
						{
							Type:          accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeContractMonthlyIncome},
						},
						{
							Type:          accountingM.BillType_BILL_COMBINE_PAY,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeCombinePay, accountingM.ApprovalTypeAgency},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletReward},
						},
					},
					LocalCurrency: "USD",
				},
			},
		},
		{
			desc:                "success get PH",
			inputRegions:        []string{"PH"},
			mockPayoutTypeInfos: contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{paypalConfig, bankConfig, offlineConfig}},
			mockRegionCurrency:  []currency.Unit{currency.USD},
			expConfigs: []*accountingM.ResRegionPayoutConfig{
				{
					RegionGroup:      "PH",
					Regions:          []string{"PH"},
					PayoutTypeInfos:  contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{offlineConfig}},
					AccountTypeInfos: contractM.AccountTypeInfos{Infos: []*contractM.AccountTypeInfo{offlineAccountTypeInfo}},
					BillTypeInfos: []accountingM.BillTypeInfo{
						{
							Type:          accountingM.BillType_BILL_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeReward},
						},
						{
							Type:          accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeContractMonthlyIncome},
						},
						{
							Type:          accountingM.BillType_BILL_COMBINE_PAY,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeCombinePay, accountingM.ApprovalTypeAgency},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletReward},
						},
					},
					LocalCurrency: "USD",
				},
			},
		},
		{
			desc:                "success get ID",
			inputRegions:        []string{"ID"},
			mockPayoutTypeInfos: contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{paypalConfig, bankConfig, offlineConfig}},
			mockRegionCurrency:  []currency.Unit{currency.USD},
			expConfigs: []*accountingM.ResRegionPayoutConfig{
				{
					RegionGroup:      "ID",
					Regions:          []string{"ID"},
					PayoutTypeInfos:  contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{offlineConfig}},
					AccountTypeInfos: contractM.AccountTypeInfos{Infos: []*contractM.AccountTypeInfo{offlineAccountTypeInfo}},
					BillTypeInfos: []accountingM.BillTypeInfo{
						{
							Type:          accountingM.BillType_BILL_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeReward},
						},
						{
							Type:          accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeContractMonthlyIncome},
						},
						{
							Type:          accountingM.BillType_BILL_COMBINE_PAY,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeCombinePay, accountingM.ApprovalTypeAgency},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletReward},
						},
					},
					LocalCurrency: "USD",
				},
			},
		},
		{
			desc:                "success get TH",
			inputRegions:        []string{"TH"},
			mockPayoutTypeInfos: contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{paypalConfig, bankConfig, offlineConfig}},
			mockRegionCurrency:  []currency.Unit{currency.USD},
			expConfigs: []*accountingM.ResRegionPayoutConfig{
				{
					RegionGroup:      "TH",
					Regions:          []string{"TH"},
					PayoutTypeInfos:  contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{offlineConfig}},
					AccountTypeInfos: contractM.AccountTypeInfos{Infos: []*contractM.AccountTypeInfo{offlineAccountTypeInfo}},
					BillTypeInfos: []accountingM.BillTypeInfo{
						{
							Type:          accountingM.BillType_BILL_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeReward},
						},
						{
							Type:          accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeContractMonthlyIncome},
						},
						{
							Type:          accountingM.BillType_BILL_COMBINE_PAY,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeCombinePay, accountingM.ApprovalTypeAgency},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletReward},
						},
					},
					LocalCurrency: "USD",
				},
			},
		},
		{
			desc:                "success get VN",
			inputRegions:        []string{"VN"},
			mockPayoutTypeInfos: contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{paypalConfig, bankConfig, offlineConfig}},
			mockRegionCurrency:  []currency.Unit{currency.USD},
			expConfigs: []*accountingM.ResRegionPayoutConfig{
				{
					RegionGroup:      "VN",
					Regions:          []string{"VN"},
					PayoutTypeInfos:  contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{offlineConfig}},
					AccountTypeInfos: contractM.AccountTypeInfos{Infos: []*contractM.AccountTypeInfo{offlineAccountTypeInfo}},
					BillTypeInfos: []accountingM.BillTypeInfo{
						{
							Type:          accountingM.BillType_BILL_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeReward},
						},
						{
							Type:          accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeContractMonthlyIncome},
						},
						{
							Type:          accountingM.BillType_BILL_COMBINE_PAY,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeCombinePay, accountingM.ApprovalTypeAgency},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletReward},
						},
					},
					LocalCurrency: "USD",
				},
			},
		},
		{
			desc:                "success get MM",
			inputRegions:        []string{"MM"},
			mockPayoutTypeInfos: contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{paypalConfig, bankConfig, offlineConfig}},
			mockRegionCurrency:  []currency.Unit{currency.USD},
			expConfigs: []*accountingM.ResRegionPayoutConfig{
				{
					RegionGroup:      "MM",
					Regions:          []string{"MM"},
					PayoutTypeInfos:  contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{offlineConfig}},
					AccountTypeInfos: contractM.AccountTypeInfos{Infos: []*contractM.AccountTypeInfo{offlineAccountTypeInfo}},
					BillTypeInfos: []accountingM.BillTypeInfo{
						{
							Type:          accountingM.BillType_BILL_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeReward},
						},
						{
							Type:          accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeContractMonthlyIncome},
						},
						{
							Type:          accountingM.BillType_BILL_COMBINE_PAY,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeCombinePay, accountingM.ApprovalTypeAgency},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletReward},
						},
					},
					LocalCurrency: "USD",
				},
			},
		},
		{
			desc:                "success get IN",
			inputRegions:        []string{"IN"},
			mockPayoutTypeInfos: contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{paypalConfig, bankConfig, offlineConfig}},
			mockRegionCurrency:  []currency.Unit{currency.INR},
			expConfigs: []*accountingM.ResRegionPayoutConfig{
				{
					RegionGroup:      "IN",
					Regions:          []string{"IN"},
					PayoutTypeInfos:  contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{offlineConfig}},
					AccountTypeInfos: contractM.AccountTypeInfos{Infos: []*contractM.AccountTypeInfo{offlineAccountTypeInfo}},
					BillTypeInfos: []accountingM.BillTypeInfo{
						{
							Type:          accountingM.BillType_BILL_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeReward},
						},
						{
							Type:          accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeContractMonthlyIncome},
						},
						{
							Type:          accountingM.BillType_BILL_COMBINE_PAY,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeCombinePay, accountingM.ApprovalTypeAgency},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletReward},
						},
					},
					LocalCurrency: "INR",
				},
			},
		},
		{
			desc:                "success get CN",
			inputRegions:        []string{"CN"},
			mockPayoutTypeInfos: contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{paypalConfig, bankConfig, offlineConfig}},
			mockRegionCurrency:  []currency.Unit{currency.CNY},
			expConfigs: []*accountingM.ResRegionPayoutConfig{
				{
					RegionGroup:      "CN",
					Regions:          []string{"CN"},
					PayoutTypeInfos:  contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{offlineConfig}},
					AccountTypeInfos: contractM.AccountTypeInfos{Infos: []*contractM.AccountTypeInfo{offlineAccountTypeInfo}},
					BillTypeInfos: []accountingM.BillTypeInfo{
						{
							Type:          accountingM.BillType_BILL_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeReward},
						},
						{
							Type:          accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeContractMonthlyIncome},
						},
						{
							Type:          accountingM.BillType_BILL_COMBINE_PAY,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_OFFLINE, accountingM.PayoutType_BBFU},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_OFFLINE_ACCOUNT, payoutAccountM.AccountType_BBRICH_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeCombinePay, accountingM.ApprovalTypeAgency},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REVENUE,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletRevenue},
						},
						{
							Type:          accountingM.BillType_BILL_WALLET_REWARD,
							PayoutTypes:   []accountingM.PayoutType{accountingM.PayoutType_BANK_ESUN, accountingM.PayoutType_OFFLINE},
							AccountTypes:  []payoutAccountM.AccountType{payoutAccountM.AccountType_FOREIGN_ACCOUNT, payoutAccountM.AccountType_OFFLINE_ACCOUNT},
							ApprovalTypes: []accountingM.ApprovalType{accountingM.ApprovalTypeWalletReward},
						},
					},
					LocalCurrency: "CNY",
				},
			},
		},
	}

	for _, t := range tests {
		s.Run(t.desc, func() {
			s.mockPayout.On("GetTypeInfos").Return(t.mockPayoutTypeInfos).Once()
			s.mockFuncs.On("timeNow").Return(mockNow).Once()
			for i, r := range t.inputRegions {
				s.mockRate.On("Get", mock.AnythingOfType("ctx.CTX"), r, mockNow).Return(t.mockRegionCurrency[i], 1.0, nil).Once()
			}
			configs, err := s.im.GetPayoutConfigs(mockCTX, t.inputRegions)
			s.Equal(t.expErr, err, t.desc)
			if t.expErr != nil {
				return
			}
			s.Equal(len(t.expConfigs), len(configs), t.desc)
			for i, c := range t.expConfigs {
				s.Equal(c.Region, configs[i].Region, t.desc)
				s.Equal(c.RegionGroup, configs[i].RegionGroup, t.desc)
				s.Equal(c.Regions, configs[i].Regions, t.desc)
				s.Equal(len(c.PayoutTypeInfos.Infos), len(configs[i].PayoutTypeInfos.Infos), t.desc)
				s.Equal(len(c.AccountTypeInfos.Infos), len(configs[i].AccountTypeInfos.Infos), t.desc)
				s.Equal(c.BillTypeInfos, configs[i].BillTypeInfos, t.desc)
				s.Equal(c.LocalCurrency, configs[i].LocalCurrency, t.desc)
				s.Equal(len(pRate.DefaultOneUSDToGivenCurrencyAmount), len(configs[i].SafeRates), t.desc)
			}
		})
	}
}

func (s *accountingTestSuite) TestCreatePayoutFile() {
	loc, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)
	mockURL := "mock-url"
	mockBillID := "mock-bill"
	mockRegion := "TW"
	mockUserID1 := "user-1"
	mockPayoutType := accountingM.PayoutType_BANK_SMBC

	s.im.bill = s.mockBill
	s.im.payout = s.mockPayout

	cases := []*struct {
		desc      string
		mockFuncs func()
		expErr    error
	}{
		{
			desc: "succ",
			mockFuncs: func() {
				s.mockFuncs.On("getTimeLocation", mockCTX, mock.Anything, mockRegion).Return(loc, nil).Once()
				s.mockBill.On("GetByBillIDs", mockCTX, (*sqlx.Tx)(nil), []string{mockBillID}).Return([]*bill.Element{
					{DBRow: bill.DBRow{Region: mockRegion, UserID: mockUserID1, BillID: mockBillID}},
				}, nil).Twice()
				s.mockUser.On("GetPlainUsers", mockCTX, mockUserID1).Return([]models.User{
					{UserID: mockUserID1},
				}, nil).Once()
				s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil).Once()
				s.mockPayout.On("CreatePayFile", mockCTX, mockExecutorID, mock.AnythingOfType("[]*payout.CreatePayFileInput")).Return(mockPayoutType, mockURL, nil).Once()
				s.mockComment.On("GetComments", mock.AnythingOfType("ctx.CTX"), intraModel.CommentType_BILL_COMMENT_TYPE, mock.AnythingOfType("[]int32"), mockBillID, "", 100).Return(nil, "", nil).Once()
			},
		},
	}
	for _, c := range cases {
		c.mockFuncs()
		t, url, err := s.im.CreatePayoutFile(mockCTX, mockExecutorID, []string{mockBillID})
		s.Equal(c.expErr, err, c.desc)
		if c.expErr != nil {
			continue
		}
		s.Equal(mockPayoutType, t, c.desc)
		s.Equal(mockURL, url, c.desc)
	}
}

func (s *accountingTestSuite) TestbankTradeDetailGetter() {
	// prepare mock data
	tradeDetailGetterPagingCnt = 1 // set to 1 so we can test paging
	mockUserID1 := "mockUserID1"
	mockUserID2 := "mockUserID2"

	// mock for user's currency amount
	moneyTest.Add(mockCTX, s.dbx, moneyM.Currency_NANO_USD, mockUserID1, usd.ToNanoUSD(600))
	moneyTest.Add(mockCTX, s.dbx, moneyM.Currency_NANO_USD, mockUserID2, usd.ToNanoUSD(600))
	mockNonExistTradeID := "tradeIDnonexist"

	// mock bills belongs to multiple trades
	repeatedTradeCnt := 3
	mockTradeIDs := []string{mockNonExistTradeID}
	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID1).Return([]models.User{
		{
			UserID: mockUserID1,
		},
	}, nil)

	// mock payout
	s.im.payout = s.mockPayout
	s.mockPayout.On("GetTypeInfos").Return(contractM.PayoutTypeInfos{Infos: []*contractM.PayoutTypeInfo{}})
	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{}, nil)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	for i := 1; i <= repeatedTradeCnt; i++ {
		bills, err := s.bill.CreateBills(mockCTX, mockExecutorID, []*bill.CreateBillInfo{
			{
				UserID:                mockUserID1,
				Type:                  accountingM.BillType_BILL_REVENUE,
				UserType:              userM.UserType_USER,
				Currency:              moneyM.Currency_NANO_USD,
				Amount:                usd.ToNanoUSD(float64(100 * i)),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				PayoutAccount:         "<EMAIL>",
				PayoutAccountVerified: true,
				Region:                "TW",
				LocalCurrency:         currency.USD,
				AmountInLocalCurrency: 100,
			},
			{
				UserID:                mockUserID2,
				Type:                  accountingM.BillType_BILL_REVENUE,
				UserType:              userM.UserType_USER,
				Currency:              moneyM.Currency_NANO_USD,
				Amount:                usd.ToNanoUSD(float64(100.0 * i)),
				PayoutType:            accountingM.PayoutType_PAYPAL,
				PayoutAccount:         "<EMAIL>",
				PayoutAccountVerified: true,
				Region:                "TW",
				LocalCurrency:         currency.USD,
				AmountInLocalCurrency: 100,
			},
		}, func(tx *sqlx.Tx, bills []*bill.Element) error {
			return nil
		})
		s.Require().NoError(err)
		mockTradeIDs = append(mockTradeIDs, bills[0].TradeID)
	}

	tradeDetails, err := s.im.bankTradeDetailGetter(mockCTX, mockUserID1, mockTradeIDs)
	s.NoError(err)
	s.Equal(repeatedTradeCnt, len(tradeDetails), "count of tradeDetail should be the same with valid tradeID count")
	for _, tradeDetail := range tradeDetails {
		s.Equal(1, len(tradeDetail.BillPayoutDetail.Infos), "count of BillPayoutDetail.Infos is 1")
	}
}

func (s *accountingTestSuite) TestApproveApproval() {
	s.createTestingData(s.im, s.dbx)

	s.im.approval = s.mockApproval
	s.im.payoutRate = s.mockPayoutRate
	TimeNow = s.mockFuncs.timeNow
	mockNow := time.Date(2019, time.May, 5, 0, 0, 0, 0, time.UTC)
	s.mockFuncs.On("timeNow").Return(mockNow)
	loc, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorRegion).Return(loc, nil)
	s.mockRate.On("Get", mock.AnythingOfType("ctx.CTX"), "TW", mock.Anything).Return(currency.USD, float64(1), nil)

	cases := []*struct {
		desc              string
		approvalID        string
		rateMonth         string
		mockRow           *approval.DBRow
		mockApprovedBills []*bill.Element
		mockResultBills   []*bill.Element
		mockFunc          func()
		expError          error
	}{
		{
			desc:       "approval with rateMonth",
			approvalID: "a3_201712_approved_approval",
			rateMonth:  "2019-03",
			mockRow: &approval.DBRow{
				ApprovalID: "a3_201712_approved_approval",
				Region:     "TW",
				Subject:    "s3",
				Status:     accountingM.ApprovalStatus_APPROVAL_APPROVED,
				RateMonth:  sql.NullString{Valid: true, String: "2019-03"},
				TimeMillis: mtime.MilliSecond(mockNow),
			},
			mockApprovedBills: []*bill.Element{
				{DBRow: bill.DBRow{Amount: usd.ToNanoUSD(60000), LocalCurrency: "USD", AmountInLocalCurrency: 60000}},
			},
			mockResultBills: []*bill.Element{
				{DBRow: bill.DBRow{Amount: usd.ToNanoUSD(60000), LocalCurrency: "USD", AmountInLocalCurrency: 60000}},
			},
		},
		{
			desc:       "approval without rateMonth",
			approvalID: "wallet2",
			mockRow: &approval.DBRow{
				ApprovalID: "wallet2",
				Region:     "TW",
				Subject:    "s6",
				Status:     accountingM.ApprovalStatus_APPROVAL_APPROVED,
				TimeMillis: mtime.MilliSecond(mockNow),
			},
			mockApprovedBills: []*bill.Element{
				{DBRow: bill.DBRow{Amount: usd.ToNanoUSD(2000), LocalCurrency: "USD", AmountInLocalCurrency: 2000}},
			},
			mockResultBills: []*bill.Element{
				{DBRow: bill.DBRow{Amount: usd.ToNanoUSD(2000), LocalCurrency: "USD", AmountInLocalCurrency: 2000}},
			},
		},
		{
			desc:       "approve agency approval",
			approvalID: "agency_approval_id_1",
			rateMonth:  "2019-03",
			mockRow: &approval.DBRow{
				ApprovalID: "agency_approval_id_1",
				Type:       accountingM.ApprovalTypeAgency,
				Region:     "TW",
				Subject:    "s3",
				Status:     accountingM.ApprovalStatus_APPROVAL_APPROVED,
				RateMonth:  sql.NullString{Valid: true, String: "2019-03"},
				TimeMillis: mtime.MilliSecond(mockNow),
			},
			mockApprovedBills: []*bill.Element{
				{DBRow: bill.DBRow{Amount: usd.ToNanoUSD(60000), LocalCurrency: "USD", AmountInLocalCurrency: 60000}},
			},
			mockResultBills: []*bill.Element{
				{
					DBRow: bill.DBRow{UserID: "agencyAdminID", BillID: "agency-bill-id", Region: "TW", Amount: usd.ToNanoUSD(60000), LocalCurrency: "USD", AmountInLocalCurrency: 60000},
					SubBills: []*bill.Element{
						{
							DBRow: bill.DBRow{BillID: "sub-bill-id1"},
						},
						{
							DBRow: bill.DBRow{BillID: "sub-bill-id2"},
						},
						{
							DBRow: bill.DBRow{BillID: "sub-bill-id3"},
						},
					},
				},
			},
			mockFunc: func() {
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(twloc, nil)
			},
		},
	}

	for _, c := range cases {
		if c.mockFunc != nil {
			c.mockFunc()
		}
		s.mockApproval.On("Approve", mock.AnythingOfType("ctx.CTX"), mockExecutorID, c.approvalID, c.rateMonth, mock.Anything).Return(c.mockRow, c.mockApprovedBills, c.mockResultBills, nil).Once()
		if c.rateMonth != "" {
			rateT, err := time.Parse(rateMonthFormat, c.rateMonth)
			s.NoError(err, c.desc)
			s.mockPayoutRate.On("SetBillRatesByType", mock.AnythingOfType("ctx.CTX"), mockExecutorID, mock.Anything,
				pRate.RateType{
					Type: exchangeM.Type_ACCOUNTING_MONTHLY_PAYOUT_TYPE,
					MonthlyTypeInfo: &pRate.MonthlyRateTypeInfo{
						Year:  rateT.Year(),
						Month: rateT.Month(),
					},
				},
				mock.Anything,
			).Return(nil).Once()
			s.mockApproval.On("RateMonthExists", mock.AnythingOfType("ctx.CTX"), []string{"TW"}, mock.AnythingOfType("string")).Return(false, nil).Once()
			s.mockExchangeRate.On("GetMultiByRegion", mock.AnythingOfType("ctx.CTX"), exchangeM.Type_ACCOUNTING_MONTHLY_PAYOUT_TYPE, "TW", mock.AnythingOfType("[]time.Time")).Return(mockPayoutRates(), nil).Once()
		}
		s.mockApproval.On("GetCreateTimeMillis", mock.AnythingOfType("ctx.CTX"), c.approvalID).Return(mtime.MilliSecond(mockNow), nil).Once()

		log, err := s.im.ApproveApproval(mockCTX, mockExecutorID, c.approvalID, c.rateMonth)
		s.Equal(c.expError, err, c.desc)
		if err != nil {
			continue
		}
		s.Equal(c.mockRow.ApprovalID, log.ApprovalID, c.desc)
		s.Equal(c.mockRow.RateMonth.String, log.RateMonth, c.desc)
	}
}

func (s *accountingTestSuite) TestSetMonthlyPayoutRates() {
	s.im.approval = s.mockApproval
	s.im.payout = s.mockPayout
	s.im.payoutRate = s.mockPayoutRate

	s.mockPayout.On("GetTypeInfos").Return(mockPayoutTypeInfos)

	loc, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorRegion).Return(loc, nil)
	TimeNow = s.mockFuncs.timeNow
	mockNow := time.Date(2019, time.May, 5, 0, 0, 0, 0, time.UTC)
	previousMonth := time.Date(2019, time.April, 1, 0, 0, 0, 0, time.UTC)
	s.mockFuncs.On("timeNow").Return(mockNow)
	s.mockRate.On("Get", mock.AnythingOfType("ctx.CTX"), "TW", mock.Anything).Return(currency.USD, float64(1), nil)
	s.mockApproval.On("RateMonthExists", mock.AnythingOfType("ctx.CTX"), []string{"TW"}, "2019-04").Return(false, nil).Once()
	rateType := pRate.RateType{
		Type: exchangeM.Type_ACCOUNTING_MONTHLY_PAYOUT_TYPE,
		MonthlyTypeInfo: &pRate.MonthlyRateTypeInfo{
			Year:  previousMonth.Year(),
			Month: previousMonth.Month(),
		},
	}
	s.mockPayoutRate.On("Set", mock.AnythingOfType("ctx.CTX"), mockExecutorID, []string{"TW"},
		pRate.PayoutRates{
			&pRate.PayoutRate{
				FromCurrency: currency.USD,
				ToCurrency:   currency.USD,
				Rate:         1,
				Type:         rateType,
			},
			&pRate.PayoutRate{
				FromCurrency: currency.USD,
				ToCurrency:   currency.TWD,
				Rate:         30,
				Type:         rateType,
			},
			&pRate.PayoutRate{
				FromCurrency: currency.USD,
				ToCurrency:   currency.HKD,
				Rate:         7,
				Type:         rateType,
			},
		}).Return(nil).Once()

	info, err := s.im.SetMonthlyPayoutRates(mockCTX, []string{"TW"}, mockExecutorID, []accountingM.Rate{
		{Currency: currency.USD, Rate: 1},
		{Currency: currency.TWD, Rate: 30},
		{Currency: currency.HKD, Rate: 7},
	})
	s.NoError(err)
	s.Equal("TW", info.Region)
	s.Equal(currency.USD, info.Currency)
	s.Equal(mockExecutorID, info.ExecutorID)
	s.Equal(accountingM.PayoutRateStatus_MUTABLE_RATE_STATUS, info.Status)
	s.Equal(3, len(info.RateInfos))
}

func (s *accountingTestSuite) TestGetAvailableAccountType() {
	s.mockFuncs.On("getAccountType", "TW").Return([]payoutAccountM.AccountType{
		payoutAccountM.AccountType_LOCAL_ACCOUNT,
		payoutAccountM.AccountType_FOREIGN_ACCOUNT,
		payoutAccountM.AccountType_OFFLINE_ACCOUNT,
		payoutAccountM.AccountType_BBRICH_ACCOUNT,
		payoutAccountM.AccountType_COMPANY_ACCOUNT,
	})
	s.mockFuncs.On("getAccountType", "JP").Return([]payoutAccountM.AccountType{
		payoutAccountM.AccountType_LOCAL_ACCOUNT,
		payoutAccountM.AccountType_OFFLINE_ACCOUNT,
		payoutAccountM.AccountType_BBRICH_ACCOUNT,
	})
	s.mockFuncs.On("getAccountType", "MENA").Return([]payoutAccountM.AccountType{
		payoutAccountM.AccountType_OFFLINE_ACCOUNT,
		payoutAccountM.AccountType_BBRICH_ACCOUNT,
		payoutAccountM.AccountType_FOREIGN_ACCOUNT,
	})

	types := s.im.GetAvailableAccountType(mockCTX, "TW")
	s.Equal([]payoutAccountM.AccountType{
		payoutAccountM.AccountType_LOCAL_ACCOUNT,
		payoutAccountM.AccountType_FOREIGN_ACCOUNT,
		payoutAccountM.AccountType_OFFLINE_ACCOUNT,
		payoutAccountM.AccountType_BBRICH_ACCOUNT,
		payoutAccountM.AccountType_COMPANY_ACCOUNT,
	}, types, "TW")

	types = s.im.GetAvailableAccountType(mockCTX, "JP")
	s.Equal([]payoutAccountM.AccountType{
		payoutAccountM.AccountType_LOCAL_ACCOUNT,
		payoutAccountM.AccountType_OFFLINE_ACCOUNT,
		payoutAccountM.AccountType_BBRICH_ACCOUNT,
	}, types, "JP")

	types = s.im.GetAvailableAccountType(mockCTX, "AE")
	s.Equal([]payoutAccountM.AccountType{
		payoutAccountM.AccountType_OFFLINE_ACCOUNT,
		payoutAccountM.AccountType_BBRICH_ACCOUNT,
		payoutAccountM.AccountType_FOREIGN_ACCOUNT,
	}, types, "AE")
}

func (s *accountingTestSuite) TestWriteAccountPayments() {
	loc, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)
	t := time.Date(2019, 10, 1, 0, 0, 0, 0, loc)

	cases := []*struct {
		desc         string
		mockBills    []*bill.Element
		mockPayouts  []*payout.DBRow
		mockFunc     func()
		expectStatus []apPaymentStatus
	}{
		{
			desc: "succ",
			mockBills: []*bill.Element{
				{
					DBRow: bill.DBRow{
						BillID:           "bill-id-1",
						UserID:           "user-id-1",
						PayeeID:          "payee-id-1",
						UserType:         userM.UserType_CONTRACT_USER,
						Type:             accountingM.BillType_BILL_WALLET,
						Region:           "TW",
						PayoutType:       accountingM.PayoutType_BBFU,
						LocalCurrency:    "TWD",
						CreateTimeMillis: mtime.MilliSecond(t),
					},
				},
			},
			mockPayouts: []*payout.DBRow{
				{
					BillID:          "bill-id-1",
					TimeMillis:      mtime.MilliSecond(t),
					Status:          accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					Currency:        "TWD",
					AmountBill:      3000,
					AmountPayable:   2980,
					Fee:             10,
					Deduction:       10,
					DeductionDetail: map[accountingM.DeductionCategory]float64{accountingM.DeductionCategory_INCOME_TAX: 10},
					Rate:            1,
				},
			},
			mockFunc: func() {
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(loc, nil).Once()
				s.mockApproval.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), []string{"bill-id-1"}).Return(map[string]*approval.DBRow{
					"bill-id-1": {Subject: "mock-subject"},
				}, nil).Once()
				s.mockFuncs.On("isPayoutAvailableForERP").Return(true).Once()
				s.mockERP.On("CreateAccountPayments", mockCTX, []*erpM.AccountPayment{
					{
						BalanceID:       "PAY-bill-id-1",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-1",
						UserID:          "user-id-1",
						PayeeID:         "payee-id-1",
						BillType:        "BILL_WALLET",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: t,
						PaymentType:     "BBFU",
						PaymentAmount:   2990,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "In progress",
						Unpaid:          2980,
						PaymentFee:      10,
						ExchangeRate:    1,
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
					},
					{
						BalanceID:       "DED-bill-id-1",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-1",
						UserID:          "user-id-1",
						PayeeID:         "payee-id-1",
						BillType:        "BILL_WALLET",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: t,
						PaymentType:     "BBFU",
						PaymentAmount:   10,
						DeductionTax:    10,
						ExchangeRate:    1,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "In progress",
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
					},
				}).Return([]*erpM.CreateResp{{Complete: true}, {Complete: true}}, nil).Once()
			},
			expectStatus: []apPaymentStatus{apPaymentStatusComplete},
		},
		{
			desc: "erp fail",
			mockBills: []*bill.Element{
				{
					DBRow: bill.DBRow{
						BillID:           "bill-id-2",
						UserID:           "user-id-2",
						PayeeID:          "payee-id-2",
						UserType:         userM.UserType_CONTRACT_USER,
						Type:             accountingM.BillType_BILL_WALLET,
						Region:           "TW",
						PayoutType:       accountingM.PayoutType_BBFU,
						LocalCurrency:    "TWD",
						CreateTimeMillis: mtime.MilliSecond(t),
					},
				},
			},
			mockPayouts: []*payout.DBRow{
				{
					BillID:          "bill-id-2",
					TimeMillis:      mtime.MilliSecond(t),
					Status:          accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					Currency:        "TWD",
					AmountBill:      3000,
					AmountPayable:   2980,
					Fee:             10,
					Deduction:       10,
					DeductionDetail: map[accountingM.DeductionCategory]float64{accountingM.DeductionCategory_INCOME_TAX: 10},
					Rate:            1,
				},
			},
			mockFunc: func() {
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(loc, nil).Once()
				s.mockApproval.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), []string{"bill-id-2"}).Return(map[string]*approval.DBRow{
					"bill-id-2": {Subject: "mock-subject"},
				}, nil).Once()
				s.mockFuncs.On("isPayoutAvailableForERP").Return(true).Once()
				s.mockERP.On("CreateAccountPayments", mockCTX, []*erpM.AccountPayment{
					{
						BalanceID:       "PAY-bill-id-2",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-2",
						UserID:          "user-id-2",
						PayeeID:         "payee-id-2",
						BillType:        "BILL_WALLET",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: t,
						PaymentType:     "BBFU",
						PaymentAmount:   2990,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "In progress",
						Unpaid:          2980,
						PaymentFee:      10,
						ExchangeRate:    1,
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
					},
					{
						BalanceID:       "DED-bill-id-2",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-2",
						UserID:          "user-id-2",
						PayeeID:         "payee-id-2",
						BillType:        "BILL_WALLET",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: t,
						PaymentType:     "BBFU",
						PaymentAmount:   10,
						DeductionTax:    10,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "In progress",
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
						ExchangeRate:    1,
					},
				}).Return(nil, fmt.Errorf("mock error")).Once()
			},
			expectStatus: []apPaymentStatus{apPaymentStatusUnknown},
		},
		{
			desc: "response completed is false",
			mockBills: []*bill.Element{
				{
					DBRow: bill.DBRow{
						BillID:           "bill-id-3",
						UserID:           "user-id-3",
						PayeeID:          "payee-id-3",
						UserType:         userM.UserType_CONTRACT_USER,
						Type:             accountingM.BillType_BILL_WALLET,
						Region:           "TW",
						PayoutType:       accountingM.PayoutType_BBFU,
						LocalCurrency:    "TWD",
						CreateTimeMillis: mtime.MilliSecond(t),
					},
				},
				{
					DBRow: bill.DBRow{
						BillID:           "bill-id-4",
						UserID:           "user-id-4",
						PayeeID:          "payee-id-4",
						UserType:         userM.UserType_CONTRACT_USER,
						Type:             accountingM.BillType_BILL_WALLET,
						Region:           "TW",
						PayoutType:       accountingM.PayoutType_BBFU,
						LocalCurrency:    "TWD",
						CreateTimeMillis: mtime.MilliSecond(t),
					},
				},
			},
			mockPayouts: []*payout.DBRow{
				{
					BillID:          "bill-id-3",
					TimeMillis:      mtime.MilliSecond(t),
					Status:          accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					Currency:        "TWD",
					AmountBill:      3000,
					AmountPayable:   2980,
					Fee:             10,
					Deduction:       0,
					DeductionDetail: map[accountingM.DeductionCategory]float64{},
					Rate:            1,
				},
				{
					BillID:          "bill-id-4",
					TimeMillis:      mtime.MilliSecond(t),
					Status:          accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					Currency:        "TWD",
					AmountBill:      3000,
					AmountPayable:   2980,
					Fee:             10,
					Deduction:       10,
					DeductionDetail: map[accountingM.DeductionCategory]float64{accountingM.DeductionCategory_INCOME_TAX: 10},
					Rate:            1,
				},
			},
			mockFunc: func() {
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(loc, nil).Times(2)
				s.mockApproval.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), []string{"bill-id-3"}).Return(map[string]*approval.DBRow{
					"bill-id-3": {Subject: "mock-subject"},
				}, nil).Once()
				s.mockApproval.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), []string{"bill-id-4"}).Return(map[string]*approval.DBRow{
					"bill-id-4": {Subject: "mock-subject"},
				}, nil).Once()
				s.mockFuncs.On("isPayoutAvailableForERP").Return(true).Times(2)
				s.mockERP.On("CreateAccountPayments", mockCTX, []*erpM.AccountPayment{
					{
						BalanceID:       "PAY-bill-id-3",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-3",
						UserID:          "user-id-3",
						PayeeID:         "payee-id-3",
						BillType:        "BILL_WALLET",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: t,
						PaymentType:     "BBFU",
						PaymentAmount:   3000,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "In progress",
						Unpaid:          2980,
						PaymentFee:      10,
						ExchangeRate:    1,
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
					},
					{
						BalanceID:       "PAY-bill-id-4",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-4",
						UserID:          "user-id-4",
						PayeeID:         "payee-id-4",
						BillType:        "BILL_WALLET",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: t,
						PaymentType:     "BBFU",
						PaymentAmount:   2990,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "In progress",
						Unpaid:          2980,
						PaymentFee:      10,
						ExchangeRate:    1,
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
					},
					{
						BalanceID:       "DED-bill-id-4",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-4",
						UserID:          "user-id-4",
						PayeeID:         "payee-id-4",
						BillType:        "BILL_WALLET",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: t,
						PaymentType:     "BBFU",
						PaymentAmount:   10,
						DeductionTax:    10,
						ExchangeRate:    1,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "In progress",
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
					},
				}).Return([]*erpM.CreateResp{{Complete: true}, {Complete: false}, {Complete: true}}, nil).Once()
			},
			expectStatus: []apPaymentStatus{apPaymentStatusComplete, apPaymentStatusFailed},
		},
		{
			desc: "erp disabled",
			mockBills: []*bill.Element{
				{
					DBRow: bill.DBRow{
						BillID:        "bill-id-3",
						UserID:        "user-id-3",
						PayeeID:       "payee-id-3",
						UserType:      userM.UserType_CONTRACT_USER,
						Type:          accountingM.BillType_BILL_WALLET,
						Region:        "TW",
						PayoutType:    accountingM.PayoutType_BBFU,
						LocalCurrency: "TWD",
					},
				},
			},
			mockFunc: func() {
				s.mockFuncs.On("isPayoutAvailableForERP").Return(false).Once()
			},
		},
		{
			desc: "payout currency not equal to local currency",
			mockBills: []*bill.Element{
				{
					DBRow: bill.DBRow{
						BillID:                "bill-id-5",
						UserID:                "user-id-1",
						PayeeID:               "payee-id-1",
						UserType:              userM.UserType_CONTRACT_USER,
						Type:                  accountingM.BillType_BILL_WALLET,
						Region:                "TW",
						PayoutType:            accountingM.PayoutType_BANK_ESUN,
						LocalCurrency:         "TWD",
						AmountInLocalCurrency: 83730,
						CreateTimeMillis:      mtime.MilliSecond(t),
					},
				},
			},
			mockPayouts: []*payout.DBRow{
				{
					BillID:          "bill-id-5",
					TimeMillis:      mtime.MilliSecond(t),
					Status:          accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					Currency:        "USD",
					AmountBill:      3000,
					AmountPayable:   2390,
					Fee:             10,
					Deduction:       600,
					DeductionDetail: map[accountingM.DeductionCategory]float64{accountingM.DeductionCategory_INCOME_TAX: 600},
					Rate:            0.********,
				},
			},
			mockFunc: func() {
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(loc, nil).Once()
				s.mockApproval.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), []string{"bill-id-5"}).Return(map[string]*approval.DBRow{
					"bill-id-5": {Subject: "mock-subject"},
				}, nil).Once()
				s.mockFuncs.On("isPayoutAvailableForERP").Return(true).Once()
				s.mockERP.On("CreateAccountPayments", mockCTX, []*erpM.AccountPayment{
					{
						BalanceID:       "PAY-bill-id-5",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-5",
						UserID:          "user-id-1",
						PayeeID:         "payee-id-1",
						BillType:        "BILL_WALLET",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: t,
						PaymentType:     "BANK_ESUN",
						PaymentAmount:   2400,
						PayoutCurrency:  "USD",
						Note:            "mock-subject",
						Status:          "In progress",
						Unpaid:          2390,
						PaymentFee:      10,
						ExchangeRate:    0.********,
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
					},
					{
						BalanceID:       "DED-bill-id-5",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-5",
						UserID:          "user-id-1",
						PayeeID:         "payee-id-1",
						BillType:        "BILL_WALLET",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: t,
						PaymentType:     "BANK_ESUN",
						PaymentAmount:   16746,
						DeductionTax:    16746,
						ExchangeRate:    1,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "In progress",
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
					},
				}).Return([]*erpM.CreateResp{{Complete: true}, {Complete: true}}, nil).Once()
			},
			expectStatus: []apPaymentStatus{apPaymentStatusComplete},
		},
	}

	for _, c := range cases {
		s.TearDownTest()
		s.SetupTest()
		s.im.bill = s.mockBill
		s.im.approval = s.mockApproval
		c.mockFunc()
		err := s.im.writeAPPayments(mockCTX, c.mockBills, c.mockPayouts)
		s.Require().NoError(err, c.desc)

		if len(c.expectStatus) == 0 {
			continue
		}

		// Query DB
		for i, b := range c.mockBills {
			var rows []*apPaymentRow
			if err := s.im.writerDB.Select(&rows, "SELECT status FROM AccountingPayablePayment WHERE billID=?", b.BillID); err != nil {
				s.Require().NoError(err, c.desc)
			}
			s.Require().Equal(1, len(rows), c.desc)
			s.Equal(c.expectStatus[i], rows[0].Status, c.desc)
		}
	}
}

func (s *accountingTestSuite) TestRetryAPPayment() {
	mockT := time.Date(2019, 10, 1, 0, 0, 0, 0, time.UTC)
	t := mtime.MilliSecond(mockT)
	now := time.Date(2019, 10, 2, 0, 0, 0, 0, time.UTC)
	TimeNow = s.mockFuncs.timeNow
	s.mockFuncs.On("timeNow").Return(now)
	loc, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)

	mockBillID := "bill-id-1"
	mockBills := []*bill.Element{
		{
			DBRow: bill.DBRow{
				BillID:        mockBillID,
				Type:          accountingM.BillType_BILL_REVENUE,
				Region:        "TW",
				PayoutType:    accountingM.PayoutType_BBFU,
				LocalCurrency: "TWD",
			},
		},
	}
	inProgressPayout := &payout.DBRow{
		BillID:          mockBillID,
		TimeMillis:      t,
		Status:          accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
		Currency:        "TWD",
		AmountBill:      3000,
		AmountPayable:   2980,
		Fee:             10,
		Deduction:       10,
		Rate:            1,
		DeductionDetail: map[accountingM.DeductionCategory]float64{accountingM.DeductionCategory_INCOME_TAX: 10},
	}
	completePayout := &payout.DBRow{
		BillID:          mockBillID,
		TimeMillis:      t,
		Status:          accountingM.PayoutStatus_PAYOUT_SUCCESS,
		Currency:        "TWD",
		AmountBill:      3000,
		AmountPaid:      2980,
		Fee:             10,
		Deduction:       10,
		Rate:            1,
		DeductionDetail: map[accountingM.DeductionCategory]float64{accountingM.DeductionCategory_INCOME_TAX: 10},
	}
	s.im.bill = s.mockBill
	s.im.approval = s.mockApproval
	s.im.payout = s.mockPayout

	cases := []*struct {
		desc          string
		mockAPPayment apPaymentRow
		mockFunc      func()
		expectPayment apPaymentRow
	}{
		{
			desc: "unknown->completed",
			mockAPPayment: apPaymentRow{
				BillID:           mockBillID,
				Status:           apPaymentStatusUnknown,
				CreateTimeMillis: t,
				UpdateTimeMillis: t,
			},
			mockFunc: func() {
				s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), (*sqlx.Tx)(nil), []string{mockBillID}).Return(mockBills, nil).Twice()
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(loc, nil).Once()
				s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), []string{mockBillID}).Return([]*payout.DBRow{inProgressPayout}, nil).Once()
				s.mockApproval.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), []string{mockBillID}).Return(map[string]*approval.DBRow{
					mockBillID: {Subject: "mock-subject"},
				}, nil).Once()
				s.mockERP.On("CreateAccountPayments", mock.AnythingOfType("ctx.CTX"), []*erpM.AccountPayment{
					{
						BalanceID:       "PAY-bill-id-1",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-1",
						BillType:        "BILL_REVENUE",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: mockT.In(loc),
						PaymentType:     "BBFU",
						PaymentAmount:   2990,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "In progress",
						Unpaid:          2980,
						PaymentFee:      10,
						ExchangeRate:    1,
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
					},
					{
						BalanceID:       "DED-bill-id-1",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-1",
						BillType:        "BILL_REVENUE",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: mockT.In(loc),
						PaymentType:     "BBFU",
						PaymentAmount:   10,
						DeductionTax:    10,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "In progress",
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
						ExchangeRate:    1,
					},
				}).Return([]*erpM.CreateResp{{Complete: true}, {Complete: true}}, nil).Once()
			},
			expectPayment: apPaymentRow{
				BillID:           mockBillID,
				Status:           apPaymentStatusComplete,
				CreateTimeMillis: t,
				UpdateTimeMillis: mtime.MilliSecond(now),
			},
		},
		{
			desc: "unknown->complete",
			mockAPPayment: apPaymentRow{
				BillID:           mockBillID,
				Status:           apPaymentStatusUnknown,
				CreateTimeMillis: t,
				UpdateTimeMillis: t,
			},
			mockFunc: func() {
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(loc, nil).Once()
				s.mockPayout.On("Get", mock.AnythingOfType("ctx.CTX"), []string{mockBillID}).Return([]*payout.DBRow{completePayout}, nil).Once()
				s.mockApproval.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), []string{mockBillID}).Return(map[string]*approval.DBRow{
					mockBillID: {Subject: "mock-subject"},
				}, nil).Once()
				s.mockERP.On("CreateAccountPayments", mock.AnythingOfType("ctx.CTX"), []*erpM.AccountPayment{
					{
						BalanceID:       "PAY-bill-id-1",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-1",
						BillType:        "BILL_REVENUE",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: mockT.In(loc),
						PaymentType:     "BBFU",
						PaymentAmount:   2990,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "Completed",
						Paid:            2980,
						PaymentFee:      10,
						ExchangeRate:    1,
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
					},
					{
						BalanceID:       "DED-bill-id-1",
						CurrencyCode:    "TWD",
						BillID:          "bill-id-1",
						BillType:        "BILL_REVENUE",
						ContractRegion:  "TW",
						UserRegion:      "TW",
						TransactionDate: mockT.In(loc),
						PaymentType:     "BBFU",
						PaymentAmount:   10,
						DeductionTax:    10,
						PayoutCurrency:  "TWD",
						Note:            "mock-subject",
						Status:          "Completed",
						FeeOwnerType:    "UNKNOWN_PAYOUT_FEE_OWNER",
						ExchangeRate:    1,
					},
				}).Return([]*erpM.CreateResp{{Complete: true}, {Complete: true}}, nil).Once()
			},
			expectPayment: apPaymentRow{
				BillID:           mockBillID,
				Status:           apPaymentStatusComplete,
				CreateTimeMillis: t,
				UpdateTimeMillis: mtime.MilliSecond(now),
			},
		},
	}
	for _, c := range cases {
		// insert db
		_, err := s.im.writerDB.Exec("INSERT INTO AccountingPayablePayment (billID, status, createTimeMillis, updateTimeMillis) VALUES (?,?,?,?) ",
			c.mockAPPayment.BillID, c.mockAPPayment.Status, c.mockAPPayment.CreateTimeMillis, c.mockAPPayment.UpdateTimeMillis,
		)
		s.Require().NoError(err)
		c.mockFunc()
		s.im.retryAPPayments(mockCTX)

		// check db
		var p apPaymentRow
		err = s.im.writerDB.Get(&p, "SELECT billID, status, createTimeMillis, updateTimeMillis FROM AccountingPayablePayment WHERE billID=?", c.mockAPPayment.BillID)
		s.Require().NoError(err)
		s.Equal(c.expectPayment, p, c.desc)

		// truncate db
		if _, err := s.db.Exec("TRUNCATE TABLE `AccountingPayablePayment`;"); err != nil {
			panic(err)
		}
	}
}

func (s *accountingTestSuite) TestUpdateAPPayment() {
	t := mtime.MilliSecond(time.Date(2019, 10, 1, 0, 0, 0, 0, time.UTC))
	now := time.Date(2019, 10, 2, 0, 0, 0, 0, time.UTC)
	TimeNow = s.mockFuncs.timeNow
	s.mockFuncs.On("timeNow").Return(now)
	s.im.bill = s.mockBill
	s.im.payout = s.mockPayout

	cases := []*struct {
		desc          string
		mockPayment   apPaymentRow
		mockFunc      func()
		expectPayment apPaymentRow
	}{
		{
			desc: "complete",
			mockPayment: apPaymentRow{
				BillID:           "bill-1",
				Status:           apPaymentStatusInProgress,
				CreateTimeMillis: t,
				UpdateTimeMillis: t,
			},
			mockFunc: func() {
				s.mockERP.On("GetAccountPayment", mockCTX, "PAY-bill-1").Return(erpM.AccountPayment{
					BillID: "bill-1",
					Status: accountingM.PayoutStatus_PAYOUT_SUCCESS.ERPStatusString(),
				}, nil).Once()
				s.mockPayout.On("SettleWithRemittance", mock.AnythingOfType("ctx.CTX"), settleAPPaymentExecutorID, mock.Anything, mock.Anything).Return(
					[]*payout.DBRow{{}}, []*bill.Element{{DBRow: bill.DBRow{UserID: "user-1"}}}, nil,
				).Once()
				s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "user-1").Return([]models.User{
					{
						UserID: "user-1",
					},
				}, nil).Once()
			},
			expectPayment: apPaymentRow{
				BillID:           "bill-1",
				Status:           apPaymentStatusComplete,
				CreateTimeMillis: t,
				UpdateTimeMillis: mtime.MilliSecond(now),
			},
		},
		{
			desc: "refund",
			mockPayment: apPaymentRow{
				BillID:           "bill-2",
				Status:           apPaymentStatusInProgress,
				CreateTimeMillis: t,
				UpdateTimeMillis: t,
			},
			mockFunc: func() {
				s.mockERP.On("GetAccountPayment", mockCTX, "PAY-bill-2").Return(erpM.AccountPayment{
					BillID:        "bill-2",
					Status:        accountingM.PayoutStatus_PAYOUT_FAIL_AND_REFUND.ERPStatusString(),
					Returned:      100,
					PaymentAmount: 100,
				}, nil).Once()
				s.mockPayout.On("Refund", mock.AnythingOfType("ctx.CTX"), settleAPPaymentExecutorID, "bill-2", mock.Anything).Return(&payout.DBRow{}, &bill.Element{DBRow: bill.DBRow{UserID: "user-1"}}, nil).Once()
			},
			expectPayment: apPaymentRow{
				BillID:           "bill-2",
				Status:           apPaymentStatusReturned,
				CreateTimeMillis: t,
				UpdateTimeMillis: mtime.MilliSecond(now),
			},
		},
	}
	s.mockFuncs.On("pushBillChangedMessage").Return(true)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	for _, c := range cases {
		// insert db
		_, err := s.im.writerDB.Exec("INSERT INTO AccountingPayablePayment (billID, status, createTimeMillis, updateTimeMillis) VALUES (?,?,?,?) ",
			c.mockPayment.BillID, c.mockPayment.Status, c.mockPayment.CreateTimeMillis, c.mockPayment.UpdateTimeMillis,
		)
		s.Require().NoError(err)
		c.mockFunc()
		s.im.updateAPPayments(mockCTX)

		// truncate db
		if _, err := s.db.Exec("TRUNCATE TABLE `AccountingPayablePayment`;"); err != nil {
			panic(err)
		}
	}
}

func (s *accountingTestSuite) TestSyncPayoutStatus() {
	now := time.Date(2020, 1, 18, 0, 0, 0, 0, time.UTC)
	oneMonthAgo := time.Date(2019, 12, 18, 0, 0, 0, 0, time.UTC)
	nowMs := mtime.MilliSecond(now)
	TimeNow = s.mockFuncs.timeNow
	s.mockFuncs.On("timeNow").Return(now)

	mockGroupID := "groupID"
	mockCreatorID := "creatorID"
	mockQuery := remittance.Query{
		Remitters: []remittanceM.Remitter{
			remittanceM.Remitter17Revenue,
			remittanceM.Remitter17Reward,
			remittanceM.Remitter17ContractMonthlyIncome,
			remittanceM.Remitter17CombinePay,
		},
		Statuses: []remittanceM.Status{remittanceM.StatusClaimed},
		DepositTimeMsRange: &remittance.TimeRangeMs{
			From: mtime.MilliSecond(oneMonthAgo),
			To:   nowMs,
		},
	}

	s.mockPayoutRate.On("GetBillRates", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("[]string")).Return(map[string]pRate.PayoutRates{}, nil)
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	cases := []*struct {
		desc       string
		bills      []*bill.DBRow
		payouts    []*payout.DBRow
		mockFunc   func()
		expPayouts []*payout.DBRow
	}{
		{
			desc: "the number of bill logs is less than the number of remittances",
			mockFunc: func() {
				testhelper.MigrateDown(s.dbName, s.dbHost, s.dbPort)
				s.mockRemittance.On("List", mock.AnythingOfType("ctx.CTX"), mockQuery).Return(
					[]*remittanceM.Remittance{
						{
							RemitterID:        "bill-id-1",
							UserID:            "user-id-1",
							DepositTimeMillis: nowMs,
						},
						{
							RemitterID:        "old-bill",
							UserID:            "old-user",
							DepositTimeMillis: 0,
						},
					},
					int64(0), int64(0), nil,
				).Once()
				s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), "user-id-1").Return(
					[]models.User{
						{
							UserID: "user-id-1",
							OpenID: "user-id-1",
						},
					}, nil,
				).Once()
			},
			bills: []*bill.DBRow{
				{
					PrevID:                sql.NullInt64{Valid: false},
					BillID:                "bill-id-1",
					Type:                  accountingM.BillType_BILL_REVENUE,
					UserType:              userM.UserType_USER,
					Status:                accountingM.BillStatus_BILL_PAYOUT,
					UserID:                "user-id-1",
					Region:                "TW",
					Amount:                usd.ToNanoUSD(10000),
					TradeID:               "trade-id-1",
					DealingID:             "dealing-id-1",
					PayoutType:            accountingM.PayoutType_BBFU,
					PayoutAccount:         "user-id-1",
					PayoutAccountVerified: true,
					ExecutorID:            mockCreatorID,
					TimeMillis:            0,
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 10000,
				},
				{
					PrevID:                sql.NullInt64{Valid: false},
					BillID:                "bill-id-2",
					Type:                  accountingM.BillType_BILL_REVENUE,
					UserType:              userM.UserType_USER,
					Status:                accountingM.BillStatus_BILL_PAYOUT,
					UserID:                "user-id-2",
					Region:                "TW",
					Amount:                usd.ToNanoUSD(10000),
					TradeID:               "trade-id-2",
					DealingID:             "dealing-id-2",
					PayoutType:            accountingM.PayoutType_BBFU,
					PayoutAccount:         "user-id-2",
					PayoutAccountVerified: true,
					ExecutorID:            mockCreatorID,
					TimeMillis:            0,
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 10000,
				},
				{
					PrevID:                sql.NullInt64{Valid: false},
					BillID:                "bill-id-3",
					Type:                  accountingM.BillType_BILL_REVENUE,
					UserType:              userM.UserType_USER,
					Status:                accountingM.BillStatus_BILL_PAYOUT,
					UserID:                "user-id-3",
					Region:                "TW",
					Amount:                usd.ToNanoUSD(10000),
					TradeID:               "trade-id-3",
					DealingID:             "dealing-id-3",
					PayoutType:            accountingM.PayoutType_BBFU,
					PayoutAccount:         "user-id-3",
					PayoutAccountVerified: true,
					ExecutorID:            mockCreatorID,
					TimeMillis:            0,
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 10000,
				},
			},
			payouts: []*payout.DBRow{
				{
					ID:               1,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-1",
					AgencyType:       accountingM.PayoutType_BBFU,
					AgencyAccount:    "user-id-1",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-1",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
				{
					ID:               2,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-2",
					AgencyType:       accountingM.PayoutType_BBFU,
					AgencyAccount:    "user-id-2",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-2",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
				{
					ID:               3,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-3",
					AgencyType:       accountingM.PayoutType_BBFU,
					AgencyAccount:    "user-id-3",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-3",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
			},
			expPayouts: []*payout.DBRow{
				{
					ID:               1,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BalancerID:       "user-id-1",
					BillID:           "bill-id-1",
					AgencyType:       accountingM.PayoutType_BBFU,
					AgencyAccount:    "user-id-1",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-1",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_PAYOUT_SUCCESS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: nowMs,
					PayoutTimeMillis: nowMs,
				},
				{
					ID:               2,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-2",
					AgencyType:       accountingM.PayoutType_BBFU,
					AgencyAccount:    "user-id-2",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-2",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
				{
					ID:               3,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-3",
					AgencyType:       accountingM.PayoutType_BBFU,
					AgencyAccount:    "user-id-3",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-3",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
			},
		},
		{
			desc: "wrong bill status, wrong payout status and wrong payout type",
			mockFunc: func() {
				testhelper.MigrateDown(s.dbName, s.dbHost, s.dbPort)
				s.mockRemittance.On("List", mock.AnythingOfType("ctx.CTX"), mockQuery).Return(
					[]*remittanceM.Remittance{
						{
							RemitterID:        "bill-id-1",
							UserID:            "user-id-1",
							DepositTimeMillis: nowMs,
						},
						{
							RemitterID:        "bill-id-2",
							UserID:            "user-id-2",
							DepositTimeMillis: nowMs,
						},
						{
							RemitterID:        "bill-id-3",
							UserID:            "user-id-3",
							DepositTimeMillis: nowMs,
						},
					},
					int64(0), int64(0), nil,
				).Once()
			},
			bills: []*bill.DBRow{
				{
					PrevID:                sql.NullInt64{Valid: false},
					BillID:                "bill-id-1",
					Type:                  accountingM.BillType_BILL_REVENUE,
					UserType:              userM.UserType_USER,
					Status:                accountingM.BillStatus_UNKNOWN_BILL_STATUS,
					UserID:                "user-id-1",
					Region:                "TW",
					Amount:                usd.ToNanoUSD(10000),
					TradeID:               "trade-id-1",
					DealingID:             "dealing-id-1",
					PayoutType:            accountingM.PayoutType_BBFU,
					PayoutAccount:         "user-id-1",
					PayoutAccountVerified: true,
					ExecutorID:            mockCreatorID,
					TimeMillis:            0,
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 10000,
				},
				{
					PrevID:                sql.NullInt64{Valid: false},
					BillID:                "bill-id-2",
					Type:                  accountingM.BillType_BILL_REVENUE,
					UserType:              userM.UserType_USER,
					Status:                accountingM.BillStatus_BILL_PAYOUT,
					UserID:                "user-id-2",
					Region:                "TW",
					Amount:                usd.ToNanoUSD(10000),
					TradeID:               "trade-id-2",
					DealingID:             "dealing-id-2",
					PayoutType:            accountingM.PayoutType_BBFU,
					PayoutAccount:         "user-id-2",
					PayoutAccountVerified: true,
					ExecutorID:            mockCreatorID,
					TimeMillis:            0,
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 10000,
				},
				{
					PrevID:                sql.NullInt64{Valid: false},
					BillID:                "bill-id-3",
					Type:                  accountingM.BillType_BILL_REVENUE,
					UserType:              userM.UserType_USER,
					Status:                accountingM.BillStatus_BILL_PAYOUT,
					UserID:                "user-id-3",
					Region:                "TW",
					Amount:                usd.ToNanoUSD(10000),
					TradeID:               "trade-id-3",
					DealingID:             "dealing-id-3",
					PayoutType:            accountingM.PayoutType_UNKNOWN_PAYOUT_TYPE,
					PayoutAccount:         "user-id-3",
					PayoutAccountVerified: true,
					ExecutorID:            mockCreatorID,
					TimeMillis:            0,
					LocalCurrency:         "TWD",
					AmountInLocalCurrency: 10000,
				},
			},
			payouts: []*payout.DBRow{
				{
					ID:               1,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-1",
					AgencyType:       accountingM.PayoutType_BBFU,
					AgencyAccount:    "user-id-1",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-1",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
				{
					ID:               2,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-2",
					AgencyType:       accountingM.PayoutType_BBFU,
					AgencyAccount:    "user-id-2",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-2",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
				{
					ID:               3,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-3",
					AgencyType:       accountingM.PayoutType_UNKNOWN_PAYOUT_TYPE,
					AgencyAccount:    "user-id-3",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-3",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
			},
			expPayouts: []*payout.DBRow{
				{
					ID:               1,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-1",
					AgencyType:       accountingM.PayoutType_BBFU,
					AgencyAccount:    "user-id-1",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-1",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
				{
					ID:               2,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-2",
					AgencyType:       accountingM.PayoutType_BBFU,
					AgencyAccount:    "user-id-2",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-2",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_UNKNOWN_PAYOUT_STATUS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
				{
					ID:               3,
					TimeMillis:       0,
					GroupID:          mockGroupID,
					CreatorID:        mockCreatorID,
					BillID:           "bill-id-3",
					AgencyType:       accountingM.PayoutType_UNKNOWN_PAYOUT_TYPE,
					AgencyAccount:    "user-id-3",
					Rate:             1,
					Currency:         currency.TWD.String(),
					AmountBill:       10000,
					AgencyReceiptID:  "remittance-id-3",
					FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
					Fee:              0,
					AmountPaid:       0,
					AmountPayable:    10000,
					Deduction:        0,
					Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
					ThirdPartyStatus: "",
					ErrorMessage:     sql.NullString{Valid: false},
					UpdateTimeMillis: 0,
					PayoutTimeMillis: 0,
				},
			},
		},
	}

	for _, c := range cases {
		if c.mockFunc != nil {
			c.mockFunc()
		}
		for _, b := range c.bills {
			_, err := s.dbx.NamedExec(bill.InsertBillQueryStr, b)
			s.NoError(err)
		}
		for _, p := range c.payouts {
			_, err := s.dbx.NamedExec(insertAccountingPayoutStmt, p)
			s.NoError(err)
		}

		// sync
		s.im.syncPayoutStatus(mockCTX)

		// select result
		payouts := []*payout.DBRow{}
		err := s.dbx.Select(&payouts,
			`SELECT id,timeMillis,groupID,creatorID,COALESCE(balancerID, "") as balancerID,
			billID,agencyType,agencyAccount,agencyReceiptID,currency,
			rate,amountBill,feeOwner,fee,amountPaid,
			amountPayable,COALESCE(remittanceURL, "") as remittanceURL,status,updateTimeMillis,thirdPartyStatus,
			errorMessage,deduction,payoutTimeMillis
			FROM AccountingPayout
			ORDER BY id`,
		)
		s.NoError(err)
		for i, expPayout := range c.expPayouts {
			s.Equal(expPayout, payouts[i], c.desc)
		}
	}
}

func (s *accountingTestSuite) TestCombine() {
	s.im.bill = s.mockBill
	billIDs := []string{"test-1", "test-2"}
	s.mockBill.On("Combine", mockCTX, mock.AnythingOfType("*sqlx.Tx"), mockExecutorID, bill.CombineInput{
		bill.CombinedBillIDs(billIDs),
	}).Return([]*bill.Element{{DBRow: bill.DBRow{UserID: "user-1", BillID: "combinebill-1"}}}, nil).Once()
	s.mockUser.On("GetPlainUsers", mockCTX, "user-1").Return([]models.User{
		{
			UserID: "user-1",
			Name:   "user-1",
		},
	}, nil).Once()
	s.mockBillRel.On("List", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return([]*bcrM.BillContractRel{}, nil)

	logs, err := s.im.CombineBills(mockCTX, mockExecutorID, []accountingM.CombinedBillIDs{accountingM.CombinedBillIDs(billIDs)})
	s.NoError(err)
	s.Equal(1, len(logs))
	for _, l := range logs {
		s.Equal("combinebill-1", l.BillID)
		s.Equal("user-1", l.UserID)
	}
}

func (s *accountingTestSuite) TestCreateBillWithFile() {
	mockFileKey := "mock-file-key"
	mockNow := time.Date(2020, time.December, 15, 0, 0, 0, 0, time.UTC)
	mockNowMS := mtime.MilliSecond(mockNow)
	nowMS = func() int64 { return mockNowMS }

	type test struct {
		desc          string
		region        string
		autoCombine   bool
		mockFunc      func()
		expExtraBills []*accountingM.ExtraBillDBRow
		expectError   error
	}

	tests := []test{
		{
			desc:        "succ",
			region:      "TW",
			autoCombine: false,
			mockFunc: func() {
				s.mockIntraFile.On("Download", mock.AnythingOfType("ctx.CTX"), mockFileKey).Return(&file.File{Reader: strings.NewReader("")}, nil).Once()
				s.mockFuncs.On("parseCreateBillFile", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW", []byte{}).Return([]createBillRow{
					{
						userType:      userM.UserType_CONTRACT_USER,
						userID:        "user-a",
						billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountType:   payoutAccountM.AccountType_LOCAL_ACCOUNT,
						amount:        100,
						localCurrency: currency.TWD,
						comment:       "comment-1",
						contractID:    10,
					},
					{
						userType:      userM.UserType_AGENCY,
						userID:        "1",
						billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountType:   payoutAccountM.AccountType_FOREIGN_ACCOUNT,
						amount:        101,
						localCurrency: currency.TWD,
						comment:       "comment-2",
					},
					{
						userType:      userM.UserType_CONTRACT_USER,
						userID:        "user-a",
						billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountType:   payoutAccountM.AccountType_LOCAL_ACCOUNT,
						amount:        200,
						localCurrency: currency.TWD,
						comment:       "comment-3",
						contractID:    10,
					},
				}, nil).Once()
				// streamer's payout account
				s.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					Owners:      &payoutaccount.StreamerOwners{UserIDs: []string{"user-a"}},
					AccountType: payoutAccountM.AccountType_LOCAL_ACCOUNT,
				}).Return([]*payoutAccountM.PayoutAccount{
					mockPayoutAccount,
					mockPayoutAccountPTA,
				}, nil).Twice()
				// agency's payout account
				s.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					Owners:      &payoutaccount.AgencyOwners{AdminIDs: []int32{1}},
					AccountType: payoutAccountM.AccountType_FOREIGN_ACCOUNT,
				}).Return([]*payoutAccountM.PayoutAccount{}, nil).Once()
				// create bill
				expInput := []*accountingM.CreateBillInput{
					{
						UserID:                "1",
						UserType:              userM.UserType_AGENCY,
						Region:                "TW",
						PayoutType:            accountingM.PayoutType_OFFLINE,
						AccountType:           payoutAccountM.AccountType_OFFLINE_ACCOUNT,
						PayoutAccount:         "1",
						PayoutAccountVerified: true,
						PayoutOwnerType:       payoutAccountM.OwnerType_OWNER_TYPE_AGENCY,
						PayoutOwnerID:         "1",
						PayoutFeeOwner:        payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
						PayToAgency:           true,
						BillArgs: []*accountingM.CreateBillArg{
							{
								Type:       accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
								AmountType: accountingM.CreateBillAmountType_LocalCurrency,
								LocalCurrencyAmount: accountingM.LocalCurrencyAmount{
									Currency: currency.TWD,
									Value:    101,
								},
							},
						},
					},
					{
						UserID:                "user-a",
						UserType:              userM.UserType_CONTRACT_USER,
						Region:                "TW",
						PayoutType:            accountingM.PayoutType_BANK_TBB,
						AccountType:           mockPayoutAccount.AccountType,
						PayoutAccount:         mockPayoutAccount.Account,
						PayoutAccountVerified: true,
						PayoutBankName:        mockPayoutAccount.BankName,
						PayoutBankCode:        mockPayoutAccount.BankCode,
						PayoutBankBranchName:  mockPayoutAccount.BranchName,
						PayoutBankBranchCode:  mockPayoutAccount.BranchCode,
						PayoutOwnerType:       payoutAccountM.OwnerType_OWNER_TYPE_STREAMER,
						PayoutFeeOwner:        mockPayoutAccount.FeeOwnerType,
						PayeeName:             mockPayoutAccount.PayeeName,
						PayeeID:               mockPayoutAccount.PayeeID,
						BillArgs: []*accountingM.CreateBillArg{
							{
								Type:       accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
								AmountType: accountingM.CreateBillAmountType_LocalCurrency,
								LocalCurrencyAmount: accountingM.LocalCurrencyAmount{
									Currency: currency.TWD,
									Value:    100,
								},
							},
						},
						ContractID: 10,
					},
					{
						UserID:                "user-a",
						UserType:              userM.UserType_CONTRACT_USER,
						Region:                "TW",
						PayoutType:            accountingM.PayoutType_BANK_TBB,
						AccountType:           mockPayoutAccount.AccountType,
						PayoutAccount:         mockPayoutAccount.Account,
						PayoutAccountVerified: true,
						PayoutBankName:        mockPayoutAccount.BankName,
						PayoutBankCode:        mockPayoutAccount.BankCode,
						PayoutBankBranchName:  mockPayoutAccount.BranchName,
						PayoutBankBranchCode:  mockPayoutAccount.BranchCode,
						PayoutOwnerType:       payoutAccountM.OwnerType_OWNER_TYPE_STREAMER,
						PayoutFeeOwner:        mockPayoutAccount.FeeOwnerType,
						PayeeName:             mockPayoutAccount.PayeeName,
						PayeeID:               mockPayoutAccount.PayeeID,
						BillArgs: []*accountingM.CreateBillArg{
							{
								Type:       accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
								AmountType: accountingM.CreateBillAmountType_LocalCurrency,
								LocalCurrencyAmount: accountingM.LocalCurrencyAmount{
									Currency: currency.TWD,
									Value:    200,
								},
							},
						},
						ContractID: 10,
					},
				}
				expBillID := map[string][]string{
					"user-a": {"mock-bill-1", "mock-bill-3"},
					"1":      {"mock-bill-2"},
				}
				createCall := s.mockSettleup.On("CreateBills", mock.AnythingOfType("ctx.CTX"), mockExecutorID, mock.Anything, mock.Anything).Once()
				createCall.RunFn = func(mockArgs mock.Arguments) {
					input := mockArgs.Get(2).([]*accountingM.CreateBillInput)
					s.Require().Equal(len(expInput), len(input))

					expUserInput := map[string][]*accountingM.CreateBillInput{}
					for _, ipt := range expInput {
						expUserInput[ipt.UserID] = append(expUserInput[ipt.UserID], ipt)
					}
					userIptCount := map[string]int{}
					retBillIDs := []string{}
					retBills := []*accountingM.ResBillLog{}
					for _, ipt := range input {
						iptCount := userIptCount[ipt.UserID]
						s.Equal(expUserInput[ipt.UserID][iptCount], ipt)
						retBillIDs = append(retBillIDs, expBillID[ipt.UserID][iptCount])
						retBills = append(retBills, &accountingM.ResBillLog{BillID: expBillID[ipt.UserID][iptCount]})
						userIptCount[ipt.UserID]++
					}

					f := mockArgs.Get(3).(func(tx *sqlx.Tx, billIDs []string, bills []*bill.Element) error)
					err := mdb.Transactx(mockCTX, s.dbx, func(tx *sqlx.Tx) error {
						return f(tx, retBillIDs, nil)
					})
					s.NoError(err)

					createCall.ReturnArguments = mock.Arguments{retBills, nil}
				}
				// post comments
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "mock-bill-1", "comment-1").Return(nil, nil).Once()
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "mock-bill-2", "comment-2").Return(nil, nil).Once()
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "mock-bill-3", "comment-3").Return(nil, nil).Once()
			},
			expExtraBills: []*accountingM.ExtraBillDBRow{
				{
					BillID:           "mock-bill-2",
					CreateTimeMillis: mockNowMS,
				},
				{
					BillID:           "mock-bill-1",
					CreateTimeMillis: mockNowMS,
				},
				{
					BillID:           "mock-bill-3",
					CreateTimeMillis: mockNowMS,
				},
			},
		},
		{
			desc:        "auto combine",
			region:      "TW",
			autoCombine: true,
			mockFunc: func() {
				s.mockIntraFile.On("Download", mock.AnythingOfType("ctx.CTX"), mockFileKey).Return(&file.File{Reader: strings.NewReader("")}, nil).Once()
				s.mockFuncs.On("parseCreateBillFile", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW", []byte{}).Return([]createBillRow{
					{
						userType:      userM.UserType_CONTRACT_USER,
						userID:        "user-a",
						billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountType:   payoutAccountM.AccountType_LOCAL_ACCOUNT,
						amount:        100,
						localCurrency: currency.TWD,
						comment:       "comment-1",
					},
					{
						userType:      userM.UserType_AGENCY,
						userID:        "1",
						billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountType:   payoutAccountM.AccountType_FOREIGN_ACCOUNT,
						amount:        101,
						localCurrency: currency.TWD,
						comment:       "comment-2",
					},
				}, nil).Once()

				// auto combine
				loc, err := time.LoadLocation("Asia/Taipei")
				s.NoError(err)
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(loc, nil)
				TimeNow = s.mockFuncs.timeNow
				s.mockFuncs.On("timeNow").Return(mockNow).Once()
				getBillLogsFunc = s.mockFuncs.getBillLogs
				s.mockFuncs.On("getBillLogs", mock.AnythingOfType("ctx.CTX"), mock.Anything, accountingM.GetBillLogsFilter{
					TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
					From:     time.Date(2020, time.December, 1, 0, 0, 0, 0, twloc),
					To:       mockNow.In(twloc),
					Stage:    accountingM.Stage_BILL_MANAGE_STAGE,
					Type: []accountingM.BillType{
						accountingM.BillType_BILL_REVENUE,
						accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountingM.BillType_BILL_COMBINE_PAY,
					},
					UserType: []userM.UserType{userM.UserType_CONTRACT_USER},
					Region:   []string{"TW"},
					UserIDs:  []string{"user-a"},
				}, "", 1000, mock.Anything).Return([]*accountingM.ResBillLog{
					{
						UserID:      "user-a",
						BillID:      "cur-bill-1",
						ContractIDs: map[int32]bool{1: true},
					},
					{
						UserID:        "user-a",
						BillID:        "cur-bill-2",
						ContractIDs:   map[int32]bool{2: true, 3: true},
						AccountType:   payoutAccountM.AccountType_OFFLINE_ACCOUNT,
						PayoutAccount: "user-a",
					},
					{
						UserID: "user-a",
						BillID: "cur-bill-3",
					},
				}, 3, "", nil).Once()
				s.mockFuncs.On("getBillLogs", mock.AnythingOfType("ctx.CTX"), mock.Anything, accountingM.GetBillLogsFilter{
					TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
					From:     time.Date(2020, time.December, 1, 0, 0, 0, 0, twloc),
					To:       mockNow.In(twloc),
					Stage:    accountingM.Stage_BILL_MANAGE_STAGE,
					Type: []accountingM.BillType{
						accountingM.BillType_BILL_REVENUE,
						accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountingM.BillType_BILL_COMBINE_PAY,
					},
					UserType: []userM.UserType{userM.UserType_AGENCY},
					Region:   []string{"TW"},
					UserIDs:  []string{"1"},
				}, "", 1000, mock.Anything).Return([]*accountingM.ResBillLog{}, 0, "", nil).Once()

				// agency's payout account
				s.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					Owners:      &payoutaccount.AgencyOwners{AdminIDs: []int32{1}},
					AccountType: payoutAccountM.AccountType_FOREIGN_ACCOUNT,
				}).Return([]*payoutAccountM.PayoutAccount{}, nil).Once()

				// create bill & combine bill
				expInput := []*accountingM.CreateBillInput{
					{
						UserID:                "user-a",
						UserType:              userM.UserType_CONTRACT_USER,
						Region:                "TW",
						PayoutType:            accountingM.PayoutType_OFFLINE,
						AccountType:           payoutAccountM.AccountType_OFFLINE_ACCOUNT,
						PayoutAccount:         "user-a",
						PayoutAccountVerified: true,
						BillArgs: []*accountingM.CreateBillArg{
							{
								Type:       accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
								AmountType: accountingM.CreateBillAmountType_LocalCurrency,
								LocalCurrencyAmount: accountingM.LocalCurrencyAmount{
									Currency: currency.TWD,
									Value:    100,
								},
							},
						},
					},
					{
						UserID:                "1",
						UserType:              userM.UserType_AGENCY,
						Region:                "TW",
						PayoutType:            accountingM.PayoutType_OFFLINE,
						AccountType:           payoutAccountM.AccountType_OFFLINE_ACCOUNT,
						PayoutAccount:         "1",
						PayoutAccountVerified: true,
						PayoutOwnerType:       payoutAccountM.OwnerType_OWNER_TYPE_AGENCY,
						PayoutOwnerID:         "1",
						PayoutFeeOwner:        payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
						PayToAgency:           true,
						BillArgs: []*accountingM.CreateBillArg{
							{
								Type:       accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
								AmountType: accountingM.CreateBillAmountType_LocalCurrency,
								LocalCurrencyAmount: accountingM.LocalCurrencyAmount{
									Currency: currency.TWD,
									Value:    101,
								},
							},
						},
					},
				}
				expBillID := map[userM.UserType]map[string]string{
					userM.UserType_CONTRACT_USER: {"user-a": "mock-bill-1"},
					userM.UserType_AGENCY:        {"1": "mock-bill-2"},
				}
				createCall := s.mockSettleup.On("CreateBills", mock.AnythingOfType("ctx.CTX"), mockExecutorID, mock.Anything, mock.Anything).Once()
				createCall.RunFn = func(mockArgs mock.Arguments) {
					input := mockArgs.Get(2).([]*accountingM.CreateBillInput)
					s.Require().Equal(len(expInput), len(input))

					expUserInput := map[userM.UserType]map[string]*accountingM.CreateBillInput{}
					for _, ipt := range expInput {
						if _, ok := expUserInput[ipt.UserType]; !ok {
							expUserInput[ipt.UserType] = map[string]*accountingM.CreateBillInput{}
						}
						expUserInput[ipt.UserType][ipt.UserID] = ipt
					}
					retBillIDs := []string{}
					retBills := []*accountingM.ResBillLog{}
					for _, ipt := range input {
						userInput, ok := expUserInput[ipt.UserType]
						s.Require().Equal(true, ok)
						s.Equal(userInput[ipt.UserID], ipt)
						retBillIDs = append(retBillIDs, expBillID[ipt.UserType][ipt.UserID])
						retBills = append(retBills, &accountingM.ResBillLog{BillID: expBillID[ipt.UserType][ipt.UserID]})
					}

					f := mockArgs.Get(3).(func(tx *sqlx.Tx, billIDs []string, bills []*bill.Element) error)
					err := mdb.Transactx(mockCTX, s.dbx, func(tx *sqlx.Tx) error {
						return f(tx, retBillIDs, nil)
					})
					s.NoError(err)

					createCall.ReturnArguments = mock.Arguments{retBills, nil}
				}
				s.im.bill = s.mockBill
				s.mockBill.On("Combine", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID,
					bill.CombineInput{
						bill.CombinedBillIDs{"cur-bill-1", "cur-bill-2", "cur-bill-3", "mock-bill-1"},
					},
					mock.Anything,
				).Return([]*bill.Element{
					{
						DBRow: bill.DBRow{UserID: "user-a", BillID: "combined-bill-id-1"},
					},
				}, nil).Once()

				// post comments
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "combined-bill-id-1", "comment-1").Return(nil, nil).Once()
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "mock-bill-1", "comment-1").Return(nil, nil).Once()
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "mock-bill-2", "comment-2").Return(nil, nil).Once()
			},
			expExtraBills: []*accountingM.ExtraBillDBRow{
				{
					BillID:           "combined-bill-id-1",
					CreateTimeMillis: mockNowMS,
				},
				{
					BillID:           "mock-bill-2",
					CreateTimeMillis: mockNowMS,
				},
			},
		},
		{
			desc:        "auto combine and duplicate user inputs",
			region:      "TW",
			autoCombine: true,
			mockFunc: func() {
				s.mockIntraFile.On("Download", mock.AnythingOfType("ctx.CTX"), mockFileKey).Return(&file.File{Reader: strings.NewReader("")}, nil).Once()
				s.mockFuncs.On("parseCreateBillFile", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW", []byte{}).Return([]createBillRow{
					{
						userType:      userM.UserType_CONTRACT_USER,
						userID:        "user-a",
						billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountType:   payoutAccountM.AccountType_LOCAL_ACCOUNT,
						amount:        100,
						localCurrency: currency.TWD,
						comment:       "comment-1",
					},
					{
						userType:      userM.UserType_CONTRACT_USER,
						userID:        "user-a",
						billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountType:   payoutAccountM.AccountType_FOREIGN_ACCOUNT,
						amount:        200,
						localCurrency: currency.TWD,
						comment:       "comment-2",
					},
				}, nil).Once()

				// auto combine
				loc, err := time.LoadLocation("Asia/Taipei")
				s.NoError(err)
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(loc, nil)
				TimeNow = s.mockFuncs.timeNow
				s.mockFuncs.On("timeNow").Return(mockNow).Once()
				getBillLogsFunc = s.mockFuncs.getBillLogs
				s.mockFuncs.On("getBillLogs", mock.AnythingOfType("ctx.CTX"), mock.Anything, accountingM.GetBillLogsFilter{
					TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
					From:     time.Date(2020, time.December, 1, 0, 0, 0, 0, twloc),
					To:       mockNow.In(twloc),
					Stage:    accountingM.Stage_BILL_MANAGE_STAGE,
					Type: []accountingM.BillType{
						accountingM.BillType_BILL_REVENUE,
						accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountingM.BillType_BILL_COMBINE_PAY,
					},
					UserType: []userM.UserType{userM.UserType_CONTRACT_USER},
					Region:   []string{"TW"},
					UserIDs:  []string{"user-a"},
				}, "", 1000, mock.Anything).Return([]*accountingM.ResBillLog{
					{
						UserID:      "user-a",
						BillID:      "cur-bill-1",
						ContractIDs: map[int32]bool{1: true},
					},
					{
						UserID:        "user-a",
						BillID:        "cur-bill-2",
						ContractIDs:   map[int32]bool{2: true, 3: true},
						AccountType:   payoutAccountM.AccountType_OFFLINE_ACCOUNT,
						PayoutAccount: "user-a",
					},
					{
						UserID: "user-a",
						BillID: "cur-bill-3",
					},
				}, 3, "", nil).Once()

				// create bill & combine bill
				expInput := []*accountingM.CreateBillInput{
					{
						UserID:                "user-a",
						UserType:              userM.UserType_CONTRACT_USER,
						Region:                "TW",
						PayoutType:            accountingM.PayoutType_OFFLINE,
						AccountType:           payoutAccountM.AccountType_OFFLINE_ACCOUNT,
						PayoutAccount:         "user-a",
						PayoutAccountVerified: true,
						BillArgs: []*accountingM.CreateBillArg{
							{
								Type:       accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
								AmountType: accountingM.CreateBillAmountType_LocalCurrency,
								LocalCurrencyAmount: accountingM.LocalCurrencyAmount{
									Currency: currency.TWD,
									Value:    100,
								},
							},
							{
								Type:       accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
								AmountType: accountingM.CreateBillAmountType_LocalCurrency,
								LocalCurrencyAmount: accountingM.LocalCurrencyAmount{
									Currency: currency.TWD,
									Value:    200,
								},
							},
						},
					},
				}
				expBillID := map[userM.UserType]map[string]string{
					userM.UserType_CONTRACT_USER: {"user-a": "mock-bill-1"},
				}
				createCall := s.mockSettleup.On("CreateBills", mock.AnythingOfType("ctx.CTX"), mockExecutorID, mock.Anything, mock.Anything).Once()
				createCall.RunFn = func(mockArgs mock.Arguments) {
					input := mockArgs.Get(2).([]*accountingM.CreateBillInput)
					s.Require().Equal(len(expInput), len(input))

					expUserInput := map[userM.UserType]map[string]*accountingM.CreateBillInput{}
					for _, ipt := range expInput {
						if _, ok := expUserInput[ipt.UserType]; !ok {
							expUserInput[ipt.UserType] = map[string]*accountingM.CreateBillInput{}
						}
						expUserInput[ipt.UserType][ipt.UserID] = ipt
					}
					retBillIDs := []string{}
					retBills := []*accountingM.ResBillLog{}
					for _, ipt := range input {
						userInput, ok := expUserInput[ipt.UserType]
						s.Require().Equal(true, ok)
						s.Equal(userInput[ipt.UserID], ipt)
						retBillIDs = append(retBillIDs, expBillID[ipt.UserType][ipt.UserID])
						retBills = append(retBills, &accountingM.ResBillLog{BillID: expBillID[ipt.UserType][ipt.UserID]})
					}

					f := mockArgs.Get(3).(func(tx *sqlx.Tx, billIDs []string, bills []*bill.Element) error)
					err := mdb.Transactx(mockCTX, s.dbx, func(tx *sqlx.Tx) error {
						return f(tx, retBillIDs, nil)
					})
					s.NoError(err)

					createCall.ReturnArguments = mock.Arguments{retBills, nil}
				}
				s.im.bill = s.mockBill
				s.mockBill.On("Combine", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID,
					bill.CombineInput{
						bill.CombinedBillIDs{"cur-bill-1", "cur-bill-2", "cur-bill-3", "mock-bill-1"},
					},
					mock.Anything,
				).Return([]*bill.Element{
					{
						DBRow: bill.DBRow{UserID: "user-a", BillID: "combined-bill-id-1"},
					},
				}, nil).Once()

				// post comments
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "combined-bill-id-1", "comment-1").Return(nil, nil).Once()
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "mock-bill-1", "comment-1").Return(nil, nil).Once()
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "combined-bill-id-1", "comment-2").Return(nil, nil).Once()
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "mock-bill-1", "comment-2").Return(nil, nil).Once()
			},
			expExtraBills: []*accountingM.ExtraBillDBRow{
				{
					BillID:           "combined-bill-id-1",
					CreateTimeMillis: mockNowMS,
				},
			},
		},
		{
			desc:        "auto combine and duplicate user inputs without existing bill",
			region:      "TW",
			autoCombine: true,
			mockFunc: func() {
				s.mockIntraFile.On("Download", mock.AnythingOfType("ctx.CTX"), mockFileKey).Return(&file.File{Reader: strings.NewReader("")}, nil).Once()
				s.mockFuncs.On("parseCreateBillFile", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW", []byte{}).Return([]createBillRow{
					{
						userType:      userM.UserType_CONTRACT_USER,
						userID:        "user-a",
						billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountType:   payoutAccountM.AccountType_LOCAL_ACCOUNT,
						amount:        100,
						localCurrency: currency.TWD,
						comment:       "comment-1",
					},
					{
						userType:      userM.UserType_CONTRACT_USER,
						userID:        "user-a",
						billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountType:   payoutAccountM.AccountType_FOREIGN_ACCOUNT,
						amount:        200,
						localCurrency: currency.TWD,
						comment:       "comment-2",
					},
				}, nil).Once()

				// auto combine
				loc, err := time.LoadLocation("Asia/Taipei")
				s.NoError(err)
				s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, "TW").Return(loc, nil)
				TimeNow = s.mockFuncs.timeNow
				s.mockFuncs.On("timeNow").Return(mockNow).Once()
				getBillLogsFunc = s.mockFuncs.getBillLogs
				s.mockFuncs.On("getBillLogs", mock.AnythingOfType("ctx.CTX"), mock.Anything, accountingM.GetBillLogsFilter{
					TimeType: accountingM.TimeFilterType_BILL_CREATED_TIME,
					From:     time.Date(2020, time.December, 1, 0, 0, 0, 0, twloc),
					To:       mockNow.In(twloc),
					Stage:    accountingM.Stage_BILL_MANAGE_STAGE,
					Type: []accountingM.BillType{
						accountingM.BillType_BILL_REVENUE,
						accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
						accountingM.BillType_BILL_COMBINE_PAY,
					},
					UserType: []userM.UserType{userM.UserType_CONTRACT_USER},
					Region:   []string{"TW"},
					UserIDs:  []string{"user-a"},
				}, "", 1000, mock.Anything).Return([]*accountingM.ResBillLog{}, 0, "", nil).Once()

				s.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					Owners:      &payoutaccount.StreamerOwners{UserIDs: []string{"user-a"}},
					AccountType: payoutAccountM.AccountType_LOCAL_ACCOUNT,
				}).Return([]*payoutAccountM.PayoutAccount{
					mockPayoutAccount,
					mockPayoutAccountPTA,
				}, nil).Once()

				// create bill & combine bill
				expInput := []*accountingM.CreateBillInput{
					{
						UserID:                "user-a",
						UserType:              userM.UserType_CONTRACT_USER,
						Region:                "TW",
						PayoutType:            accountingM.PayoutType_BANK_TBB,
						AccountType:           mockPayoutAccount.AccountType,
						PayoutAccount:         mockPayoutAccount.Account,
						PayoutAccountVerified: true,
						PayoutBankName:        mockPayoutAccount.BankName,
						PayoutBankCode:        mockPayoutAccount.BankCode,
						PayoutBankBranchName:  mockPayoutAccount.BranchName,
						PayoutBankBranchCode:  mockPayoutAccount.BranchCode,
						PayoutOwnerType:       payoutAccountM.OwnerType_OWNER_TYPE_STREAMER,
						PayoutFeeOwner:        mockPayoutAccount.FeeOwnerType,
						PayeeName:             mockPayoutAccount.PayeeName,
						PayeeID:               mockPayoutAccount.PayeeID,
						BillArgs: []*accountingM.CreateBillArg{
							{
								Type:       accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
								AmountType: accountingM.CreateBillAmountType_LocalCurrency,
								LocalCurrencyAmount: accountingM.LocalCurrencyAmount{
									Currency: currency.TWD,
									Value:    100,
								},
							},
							{
								Type:       accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
								AmountType: accountingM.CreateBillAmountType_LocalCurrency,
								LocalCurrencyAmount: accountingM.LocalCurrencyAmount{
									Currency: currency.TWD,
									Value:    200,
								},
							},
						},
					},
				}
				expBillID := map[userM.UserType]map[string]string{
					userM.UserType_CONTRACT_USER: {"user-a": "mock-bill-1"},
				}
				createCall := s.mockSettleup.On("CreateBills", mock.AnythingOfType("ctx.CTX"), mockExecutorID, mock.Anything, mock.Anything).Once()
				createCall.RunFn = func(mockArgs mock.Arguments) {
					input := mockArgs.Get(2).([]*accountingM.CreateBillInput)
					s.Require().Equal(len(expInput), len(input))

					expUserInput := map[userM.UserType]map[string]*accountingM.CreateBillInput{}
					for _, ipt := range expInput {
						if _, ok := expUserInput[ipt.UserType]; !ok {
							expUserInput[ipt.UserType] = map[string]*accountingM.CreateBillInput{}
						}
						expUserInput[ipt.UserType][ipt.UserID] = ipt
					}
					retBillIDs := []string{}
					retBills := []*accountingM.ResBillLog{}
					for _, ipt := range input {
						userInput, ok := expUserInput[ipt.UserType]
						s.Require().Equal(true, ok)
						s.Equal(userInput[ipt.UserID], ipt)
						retBillIDs = append(retBillIDs, expBillID[ipt.UserType][ipt.UserID])
						retBills = append(retBills, &accountingM.ResBillLog{BillID: expBillID[ipt.UserType][ipt.UserID]})
					}

					f := mockArgs.Get(3).(func(tx *sqlx.Tx, billIDs []string, bills []*bill.Element) error)
					err := mdb.Transactx(mockCTX, s.dbx, func(tx *sqlx.Tx) error {
						return f(tx, retBillIDs, nil)
					})
					s.NoError(err)

					createCall.ReturnArguments = mock.Arguments{retBills, nil}
				}

				// post comments
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "mock-bill-1", "comment-1").Return(nil, nil).Once()
				s.mockComment.On("PostTx", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorID, intraModel.CommentType_BILL_COMMENT_TYPE, int32(accountingM.CommentSubType_BILL_MANAGE_STAGE_COMMENT), "mock-bill-1", "comment-2").Return(nil, nil).Once()
			},
			expExtraBills: []*accountingM.ExtraBillDBRow{
				{
					BillID:           "mock-bill-1",
					CreateTimeMillis: mockNowMS,
				},
			},
		},
	}

	for _, t := range tests {
		s.TearDownTest()
		s.SetupTest()
		t.mockFunc()

		_, err := s.im.CreateBillWithFile(mockCTX, mockExecutorID, t.region, mockFileKey, t.autoCombine)
		s.Require().Equal(t.expectError, err, t.desc)
		if t.expectError != nil {
			continue
		}

		// check db ExtraBill
		extraBills, err := s.getExtraBills()
		s.Require().NoError(err, t.desc)
		s.Require().ElementsMatch(t.expExtraBills, extraBills, t.desc)
	}
}

func (s *accountingTestSuite) TestIsExtraBillApproval() {
	type test struct {
		desc           string
		mockApprovalID string
		expRes         bool
		expErr         error
	}

	tests := []test{
		{
			desc:           "succ, return true when approval contains extra bill",
			mockApprovalID: "a9_has_extra_bill",
			expRes:         true,
			expErr:         nil,
		},
		{
			desc:           "succ, return false when approval does not contain extra bill",
			mockApprovalID: "wallet2",
			expRes:         false,
			expErr:         nil,
		},
	}

	s.createTestingData(s.im, s.dbx)
	for _, t := range tests {
		res, err := s.im.IsExtraBillApproval(mockCTX, t.mockApprovalID)
		s.Require().Equal(t.expErr, err, t.desc)
		s.Require().Equal(t.expRes, res, t.desc)
	}
}

func (s *accountingTestSuite) TestParseCreateBillFile() {
	loc, err := time.LoadLocation("Asia/Taipei")
	s.NoError(err)
	s.mockFuncs.On("getTimeLocation", mock.AnythingOfType("ctx.CTX"), mock.Anything, mockExecutorRegion).Return(loc, nil)
	s.mockRate.On("Get", mock.AnythingOfType("ctx.CTX"), "TW", mock.AnythingOfType("time.Time")).Return(currency.TWD, 30.0, nil)

	cases := []struct {
		desc        string
		region      string
		content     []byte
		mockFunc    func()
		expectError error
		expectRows  []createBillRow
	}{
		{
			desc:    "succ (streamer + agency)",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nAgency,<EMAIL>,Monthly,Offline,10000,test-comment\nContractedStreamer,user-a,Monthly,Streamer-Local,100,\n"),
			mockFunc: func() {
				s.mockUser.On("GetPartialFieldsUsers", mock.AnythingOfType("ctx.CTX"), mock.Anything, "user-a").Run(func(args mock.Arguments) {
					*args[1].(*[]partialUser) = []partialUser{
						{UserID: "user-a", OpenID: ""},
					}
				}).Return(nil).Once()
				s.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), "<EMAIL>").Return(&intraModel.User{Region: "TW", ID: 1}, nil).Once()
				// check streamr's contract
				s.mockContract.On("GetContractsWithoutTermsByUserIDs", mock.AnythingOfType("ctx.CTX"), []string{"user-a"}, mock.Anything).Return(map[string][]*contractM.Contract{
					"user-a": {
						{Id: 1}, {Id: 2},
					},
				}, nil).Once()
				s.mockUser.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), &contractM.Contract{Id: 2}).Return("TW", nil).Once()
			},
			expectRows: []createBillRow{
				{
					userType:      userM.UserType_AGENCY,
					userID:        "1",
					billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
					accountType:   payoutAccountM.AccountType_OFFLINE_ACCOUNT,
					amount:        10000,
					localCurrency: currency.TWD,
					comment:       "test-comment",
				},
				{
					userType:      userM.UserType_CONTRACT_USER,
					userID:        "user-a",
					billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
					accountType:   payoutAccountM.AccountType_LOCAL_ACCOUNT,
					amount:        100,
					localCurrency: currency.TWD,
					comment:       "",
					contractID:    2,
				},
			},
		},
		{
			desc:    "skip blank line",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\n,,,,,\nAgency,<EMAIL>,Monthly,Offline,10000,test-comment\nContractedStreamer,user-a,Monthly,Streamer-Local,100,\n"),
			mockFunc: func() {
				s.mockUser.On("GetPartialFieldsUsers", mock.AnythingOfType("ctx.CTX"), mock.Anything, "user-a").Run(func(args mock.Arguments) {
					*args[1].(*[]partialUser) = []partialUser{
						{UserID: "user-a", OpenID: ""},
					}
				}).Return(nil).Once()
				s.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), "<EMAIL>").Return(&intraModel.User{Region: "TW", ID: 1}, nil).Once()
				// check streamr's contract
				s.mockContract.On("GetContractsWithoutTermsByUserIDs", mock.AnythingOfType("ctx.CTX"), []string{"user-a"}, mock.Anything).Return(map[string][]*contractM.Contract{
					"user-a": {
						{Id: 1}, {Id: 2},
					},
				}, nil).Once()
				s.mockUser.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), &contractM.Contract{Id: 2}).Return("TW", nil).Once()
			},
			expectRows: []createBillRow{
				{
					userType:      userM.UserType_AGENCY,
					userID:        "1",
					billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
					accountType:   payoutAccountM.AccountType_OFFLINE_ACCOUNT,
					amount:        10000,
					localCurrency: currency.TWD,
					comment:       "test-comment",
				},
				{
					userType:      userM.UserType_CONTRACT_USER,
					userID:        "user-a",
					billType:      accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
					accountType:   payoutAccountM.AccountType_LOCAL_ACCOUNT,
					amount:        100,
					localCurrency: currency.TWD,
					comment:       "",
					contractID:    2,
				},
			},
		},
		{
			desc:    "invalid user type",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nUser,<EMAIL>,Monthly,Offline,10000,test-comment\n"),
			mockFunc: func() {
			},
			expectError: fmt.Errorf("invalid user type"),
		},
		{
			desc:    "invalid agency email",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nAgency,<EMAIL>,Monthly,Offline,10000,test-comment\nContractedStreamer,user-a,Monthly,Streamer-Local,100,\n"),
			mockFunc: func() {
				s.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), "<EMAIL>").Return(nil, user.ErrUserNotExist).Once()
			},
			expectError: fmt.Errorf("invalid agency mail"),
		},
		{
			desc:    "invalid agency region",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nAgency,<EMAIL>,Monthly,Offline,10000,test-comment\nContractedStreamer,user-a,Monthly,Streamer-Local,100,\n"),
			mockFunc: func() {
				s.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), "<EMAIL>").Return(&intraModel.User{Region: "JP", ID: 1}, nil).Once()
			},
			expectError: fmt.Errorf("the agency not in this region"),
		},
		{
			desc:    "invalid streamer",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nContractedStreamer,user-a,Monthly,Streamer-Local,100,\n"),
			mockFunc: func() {
				s.mockUser.On("GetPartialFieldsUsers", mock.AnythingOfType("ctx.CTX"), mock.Anything, "user-a").Run(func(args mock.Arguments) {
				}).Return(us.ErrSomeUsersNotFound).Once()
			},
			expectError: fmt.Errorf("invalid user id"),
		},
		{
			desc:    "invalid streamer region",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nContractedStreamer,user-a,Monthly,Streamer-Local,100,\n"),
			mockFunc: func() {
				s.mockUser.On("GetPartialFieldsUsers", mock.AnythingOfType("ctx.CTX"), mock.Anything, "user-a").Run(func(args mock.Arguments) {
					*args[1].(*[]partialUser) = []partialUser{
						{UserID: "user-a", OpenID: ""},
					}
				}).Return(nil).Once()
				// no contract
				s.mockContract.On("GetContractsWithoutTermsByUserIDs", mock.AnythingOfType("ctx.CTX"), []string{"user-a"}, mock.Anything).Return(map[string][]*contractM.Contract{
					"user-a": {},
				}, nil).Once()
			},
			expectError: fmt.Errorf("the user not in this region"),
		},
		{
			desc:    "invalid streamer region",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nContractedStreamer,user-a,Monthly,Streamer-Local,100,\n"),
			mockFunc: func() {
				s.mockUser.On("GetPartialFieldsUsers", mock.AnythingOfType("ctx.CTX"), mock.Anything, "user-a").Run(func(args mock.Arguments) {
					*args[1].(*[]partialUser) = []partialUser{
						{UserID: "user-a", OpenID: ""},
					}
				}).Return(nil).Once()
				s.mockContract.On("GetContractsWithoutTermsByUserIDs", mock.AnythingOfType("ctx.CTX"), []string{"user-a"}, mock.Anything).Return(map[string][]*contractM.Contract{
					"user-a": {
						{Id: 1},
					},
				}, nil).Once()
				s.mockUser.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), &contractM.Contract{Id: 1}).Return("JP", nil).Once()
			},
			expectError: fmt.Errorf("the user not in this region"),
		},
		{
			desc:    "invalid bill type",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nAgency,<EMAIL>,Revenue,Offline,10000,test-comment\n"),
			mockFunc: func() {
			},
			expectError: fmt.Errorf("invalid bill type"),
		},
		{
			desc:    "invalid account type",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nAgency,<EMAIL>,Monthly,Streamer-Local,10000,test-comment\n"),
			mockFunc: func() {
				s.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), "<EMAIL>").Return(&intraModel.User{Region: "TW", ID: 1}, nil).Once()
			},
			expectError: fmt.Errorf("invalid account type"),
		},
		{
			desc:    "invalid account type",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nAgency,<EMAIL>,Monthly,Paypal,10000,test-comment\n"),
			mockFunc: func() {
				s.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), "<EMAIL>").Return(&intraModel.User{Region: "TW", ID: 1}, nil).Once()
			},
			expectError: fmt.Errorf("invalid account type"),
		},
		{
			desc:    "wrong format of amount",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nAgency,<EMAIL>,Monthly,Offline,10x,test-comment\n"),
			mockFunc: func() {
				s.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), "<EMAIL>").Return(&intraModel.User{Region: "TW", ID: 1}, nil).Once()
			},
			expectError: fmt.Errorf("wrong format of amount"),
		},
		{
			desc:    "unsupported amount",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nAgency,<EMAIL>,Monthly,Offline,10000.1,test-comment\n"),
			mockFunc: func() {
				s.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), "<EMAIL>").Return(&intraModel.User{Region: "TW", ID: 1}, nil).Once()
			},
			expectError: fmt.Errorf("unsupported amount"),
		},
		{
			desc:    "unsupported amount",
			region:  "TW",
			content: []byte("User Type,UserID/Account mail,Bill Type,Account Type,Amount,Comment\nAgency,<EMAIL>,Monthly,Offline,-1,test-comment\n"),
			mockFunc: func() {
				s.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), "<EMAIL>").Return(&intraModel.User{Region: "TW", ID: 1}, nil).Once()
			},
			expectError: fmt.Errorf("unsupported amount"),
		},
	}

	for _, c := range cases {
		c.mockFunc()
		rows, err := parseCreateBillFile(mockCTX, s.im, c.region, c.content)
		s.Require().Equal(c.expectError, err, c.desc)
		if c.expectError != nil {
			continue
		}
		s.Equal(c.expectRows, rows, c.desc)
	}
}

func (s *accountingTestSuite) TestGetComments() {
	s.im.bill = s.mockBill
	mockBillID := "mock-bill"
	limit := 100
	s.mockComment.On("GetComments", mock.AnythingOfType("ctx.CTX"), intraModel.CommentType_BILL_COMMENT_TYPE, []int32{3}, mockBillID, "", limit).Return([]*comment.ResLog{
		{
			ExecutorID:     mockExecutorID,
			Comment:        "comment-2",
			CreateUnixTime: time.Now().Unix(),
		},
	}, "", nil).Once()
	s.mockBill.On("GetByBillIDs", mock.AnythingOfType("ctx.CTX"), (*sqlx.Tx)(nil), []string{mockBillID}).Return(nil, nil).Once()

	cs, nx, err := s.im.GetComments(mockCTX, mockBillID, "", limit, &GetCommentsFilter{
		SubTypes: []accountingM.CommentSubType{accountingM.CommentSubType_BILL_PAYOUT_STAGE_COMMENT},
	})
	s.NoError(err)
	s.Equal("", nx)
	s.Equal(1, len(cs))
	s.Equal("comment-2", cs[0].Comment)
}

func (s *accountingTestSuite) createTestingData(im *impl, db *sqlx.DB) {
	mockBills := []*bill.DBRow{
		// 2017.11 3 bills are in `payout in progress`, `payout complete`, and `payout refunded`, and in 1 approval
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "201711-b1-in-a1",
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserType:              userM.UserType_USER,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "u1",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(10000),
			TradeID:               "tradeIDa1",
			DealingID:             "dealingIDa1b1",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20171105),
			CreateTimeMillis:      mtime.MilliSecond(t20171105),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 10000,
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "201711-b2-in-a1",
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserType:              userM.UserType_CONTRACT_USER,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "u2",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(20000),
			TradeID:               "tradeIDa1",
			DealingID:             "dealingIDa1b2",
			PayoutType:            accountingM.PayoutType_BANK_ESUN,
			PayoutAccount:         "bank-account",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20171105),
			CreateTimeMillis:      mtime.MilliSecond(t20171105),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 20000,
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "201711-b3-in-a1",
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserType:              userM.UserType_CONTRACT_USER,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "u3",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(30000),
			TradeID:               "tradeIDa1",
			DealingID:             "dealingIDa1b3",
			PayoutType:            accountingM.PayoutType_BANK_TBB,
			PayoutAccount:         "bank-account-2",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20171105),
			CreateTimeMillis:      mtime.MilliSecond(t20171105),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 30000,
		},
		// 201712 1 bills are in `orphan`
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "201712-b4-orphan",
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserType:              userM.UserType_CONTRACT_USER,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "u4",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(40000),
			TradeID:               "tradeIDb4",
			DealingID:             "dealingIDb4",
			PayoutType:            accountingM.PayoutType_BANK_TBB,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20171205),
			CreateTimeMillis:      mtime.MilliSecond(t20171205),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 40000,
			PayoutFeeOwner:        payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
		},
		// 201712 1 bills are in `selected` and in 1 approval
		// region is JP
		{
			PrevID:                                 sql.NullInt64{Valid: false},
			IsNew:                                  1,
			BillID:                                 "201712-b5-in-a2",
			Status:                                 accountingM.BillStatus_BILL_ORPHAN,
			Type:                                   accountingM.BillType_BILL_REVENUE,
			UserID:                                 "u5",
			Region:                                 "JP",
			Amount:                                 usd.ToNanoUSD(50000),
			TradeID:                                "tradeIDa2",
			DealingID:                              "dealingIDa2b5",
			StreamerRealName:                       "streamerRealName",
			PayoutType:                             accountingM.PayoutType_PAYPAL,
			PayoutAccount:                          "<EMAIL>",
			PayoutAccountVerified:                  true,
			ExecutorID:                             "tester",
			TimeMillis:                             mtime.MilliSecond(t20171205),
			CreateTimeMillis:                       mtime.MilliSecond(t20171205),
			LocalCurrency:                          "JPY",
			AmountInLocalCurrency:                  5302500,
			AmountForConsumptionTaxInLocalCurrency: 5000,
			AmountForIncomeTaxInLocalCurrency:      6000,
			InvoiceNumber:                          "B123456789",
			DeductionCondition:                     accountingM.DeductionCondition_FREE_INCOME_TAX,
		},

		// 201712 1 bill is in `approved`, 1 bill is in `payout in progress` and in 1 approval
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "201712-b6-in-a3",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserID:                "u6",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(60000),
			TradeID:               "tradeIDa3",
			DealingID:             "dealingIDa3b6",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20171205),
			CreateTimeMillis:      mtime.MilliSecond(t20171205),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 60000,
		},
		// same user u2
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "201712-b7-in-a3",
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserType:              userM.UserType_CONTRACT_USER,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "u2",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(70000),
			TradeID:               "tradeIDa3",
			DealingID:             "dealingIDa3b7",
			PayoutType:            accountingM.PayoutType_BANK_TBB,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20171205),
			CreateTimeMillis:      mtime.MilliSecond(t20171205),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 70000,
		},

		// append to a4_201712_rejected_approval, reject and append to a5_recover_a4_approval and approved
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "2017-b8-in-a4-a5",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserID:                "u8",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(80000),
			TradeID:               "tradeIDa4",
			DealingID:             "dealingIDb8",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20171205),
			CreateTimeMillis:      mtime.MilliSecond(t20171205),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 80000,
		},
		// append to a4_201712_rejected_approval, reject and append to a5_recover_a4_approval and approved
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "2017-b9-in-a4-a5",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserID:                "u9",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDa4",
			DealingID:             "dealingIDb9",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20171205),
			CreateTimeMillis:      mtime.MilliSecond(t20171205),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 90000,
		},
		// lock/unlock sample
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "lock-test-1",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserID:                "u10",
			UserType:              userM.UserType_USER,
			Region:                "TW",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDlock1",
			DealingID:             "dealingIDlock1",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 90000,
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "lock-test-2",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserID:                "u10",
			UserType:              userM.UserType_USER,
			Region:                "TW",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDlock2",
			DealingID:             "dealingIDlock2",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 90000,
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "update-test-1",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserID:                "u11",
			UserType:              userM.UserType_USER,
			Region:                "TW",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDtest1",
			DealingID:             "dealingIDtest1",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 90000,
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "update-test-2",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserID:                "u11",
			UserType:              userM.UserType_USER,
			Region:                "TW",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDtest2",
			DealingID:             "dealingIDtest2",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 90000,
		},
		// for test make approval
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "********-b11-orphan",
			Type:                  accountingM.BillType_BILL_REVENUE,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "u11",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDb11",
			DealingID:             "dealingIDb11",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 90000,
		},
		// for test make approval
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "********-b12-orphan",
			Type:                  accountingM.BillType_BILL_REVENUE,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "u12",
			Region:                "HK",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDb12",
			DealingID:             "dealingIDb12",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
			LocalCurrency:         "HKD",
			AmountInLocalCurrency: 681750,
		},
		// for test make approval
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "********-b13-orphan",
			Type:                  accountingM.BillType_BILL_REWARD,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "u11",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDb13",
			DealingID:             "dealingIDb13",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 90000,
		},
		// for test make approval
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "********-AE-orphan",
			Type:                  accountingM.BillType_BILL_REVENUE,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "uAE",
			Region:                "AE",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDbAE",
			DealingID:             "dealingIDbAE",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
			LocalCurrency:         "AED",
			AmountInLocalCurrency: 90000,
		},
		// for test make approval
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "********-DJ-orphan",
			Type:                  accountingM.BillType_BILL_REVENUE,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "uDJ",
			Region:                "DJ",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDbDJ",
			DealingID:             "dealingIDbDJ",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
			LocalCurrency:         "AED",
			AmountInLocalCurrency: 90000,
		},
		// voided bill
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "********-voided",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserID:                "u13",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(90000),
			TradeID:               "tradeIDvoid",
			DealingID:             "dealingIDvoid",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20171105),
			CreateTimeMillis:      mtime.MilliSecond(t20171105),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 90000,
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "********-monthly-bill",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
			UserID:                "u14",
			Region:                "JP",
			Amount:                0,
			LocalCurrency:         "JPY",
			AmountInLocalCurrency: 1000,
			TradeID:               "tradeIDmonthly",
			DealingID:             "dealingIDmonthly",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "********-combined-revenue",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserID:                "u15",
			Region:                "JP",
			Amount:                100,
			Currency:              moneyM.Currency_NANO_USD,
			LocalCurrency:         "JPY",
			AmountInLocalCurrency: 12000,
			TradeID:               "tradeIDcombined",
			DealingID:             "dealingIDcombined",
			PayoutType:            accountingM.PayoutType_OFFLINE,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "********-combined-monthly",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_CONTRACT_MONTHLY_INCOME,
			UserID:                "u15",
			Region:                "JP",
			Amount:                0,
			LocalCurrency:         "JPY",
			AmountInLocalCurrency: 1000,
			TradeID:               "tradeIDcombinedmonthly",
			DealingID:             "dealingIDcombinedmonthly",
			PayoutType:            accountingM.PayoutType_OFFLINE,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "wallet-1",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_WALLET,
			UserID:                "u16",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(1000),
			Currency:              moneyM.Currency_WALLET_NANO_USD,
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 1000,
			TradeID:               "tradeID",
			DealingID:             "dealingID",
			PayoutType:            accountingM.PayoutType_BANK_TBB,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20190301),
			CreateTimeMillis:      mtime.MilliSecond(t20190301),
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "wallet-2",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_WALLET,
			UserID:                "u16",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(2000),
			Currency:              moneyM.Currency_WALLET_NANO_USD,
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 2000,
			TradeID:               "tradeID",
			DealingID:             "dealingID",
			PayoutType:            accountingM.PayoutType_BANK_TBB,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "wallet-3",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_WALLET,
			UserID:                "u16",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(3000),
			Currency:              moneyM.Currency_WALLET_NANO_USD,
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 3000,
			TradeID:               "tradeID",
			DealingID:             "dealingID",
			PayoutType:            accountingM.PayoutType_BANK_TBB,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t********),
			CreateTimeMillis:      mtime.MilliSecond(t********),
		},
		// append to agency_approval_id_1
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "pay_to_agency_bill",
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserID:                "u8",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(80000),
			TradeID:               "tradeIDa4",
			DealingID:             "dealingIDb8",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			PayToAgency:           true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20171205),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 80000,
		},
		// test IsExtraBillApproval 2 bills(1 is extra bill) in one approval(a9)
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "202210-b1-in-a9-is-extra",
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserType:              userM.UserType_USER,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "u17",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(10000),
			TradeID:               "tradeIDa9",
			DealingID:             "dealingIDa9b1",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20221012),
			CreateTimeMillis:      mtime.MilliSecond(t20221012),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 10000,
		},
		{
			PrevID:                sql.NullInt64{Valid: false},
			IsNew:                 1,
			BillID:                "202210-b2-in-a9",
			Type:                  accountingM.BillType_BILL_REVENUE,
			UserType:              userM.UserType_USER,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			UserID:                "u18",
			Region:                "TW",
			Amount:                usd.ToNanoUSD(10000),
			TradeID:               "tradeIDa9",
			DealingID:             "dealingIDa9b2",
			PayoutType:            accountingM.PayoutType_PAYPAL,
			PayoutAccount:         "<EMAIL>",
			PayoutAccountVerified: true,
			ExecutorID:            "tester",
			TimeMillis:            mtime.MilliSecond(t20221012),
			CreateTimeMillis:      mtime.MilliSecond(t20221012),
			LocalCurrency:         "USD",
			AmountInLocalCurrency: 20000,
		},
	}

	for _, b := range mockBills {
		res, err := db.NamedExec(bill.InsertBillQueryStr, b)
		if err != nil {
			panic(err)
		}
		b.ID, err = res.LastInsertId()
		if err != nil {
			panic(err)
		}
	}

	combinedBillMap := map[string][]*bill.DBRow{
		"********-combined-bill": {mockBills[20], mockBills[21]},
	}
	for key, bills := range combinedBillMap {
		var totalAmount float64
		for _, b := range bills {
			totalAmount += b.AmountInLocalCurrency
			_, err := db.Exec("INSERT INTO `CombinedBillRelation` (`billID`, `subBillID`) VALUES (?, ?)", key, b.BillID)
			if err != nil {
				panic(err)
			}
			b.Status = accountingM.BillStatus_BILL_COMBINED
			b.PrevID = sql.NullInt64{Valid: true, Int64: b.ID}
			_, err = db.Exec("UPDATE Bill SET isNew = 0 WHERE id = ?", b.PrevID)
			if err != nil {
				panic(err)
			}
			res, err := db.NamedExec(bill.InsertBillQueryStr, b)
			if err != nil {
				panic(err)
			}
			b.ID, err = res.LastInsertId()
			if err != nil {
				panic(err)
			}
		}
		combineBill := &bill.DBRow{
			IsNew:                 1,
			BillID:                key,
			Status:                accountingM.BillStatus_BILL_ORPHAN,
			Type:                  accountingM.BillType_BILL_COMBINE_PAY,
			UserID:                bills[0].UserID,
			Region:                bills[0].Region,
			Amount:                0,
			LocalCurrency:         bills[0].LocalCurrency,
			AmountInLocalCurrency: totalAmount,
			PayoutType:            bills[0].PayoutType,
			PayoutAccount:         bills[0].PayoutAccount,
			PayoutAccountVerified: bills[0].PayoutAccountVerified,
			ExecutorID:            bills[0].ExecutorID,
			TimeMillis:            bills[0].TimeMillis,
			CreateTimeMillis:      bills[0].CreateTimeMillis,
		}
		_, err := db.NamedExec(bill.InsertBillQueryStr, combineBill)
		if err != nil {
			panic(err)
		}
	}

	mockApprovals := []approval.DBRow{
		{
			PrevID:     sql.NullInt64{Valid: false},
			ApprovalID: "a1_2017_approvaed_approval",
			Type:       accountingM.ApprovalTypeRevenue,
			Region:     "TW",
			Subject:    "s1",
			Status:     accountingM.ApprovalStatus_APPROVAL_EDITABLE,
			ExecutorID: "tester1",
			TimeMillis: mtime.MilliSecond(t20171105),
		},
		{
			PrevID:     sql.NullInt64{Valid: false},
			ApprovalID: "a2_2017_sealed_approval",
			Type:       accountingM.ApprovalTypeRevenue,
			Region:     "JP",
			Subject:    "s2",
			Status:     accountingM.ApprovalStatus_APPROVAL_EDITABLE,
			ExecutorID: "tester2",
			TimeMillis: mtime.MilliSecond(t20171205),
		},
		{
			PrevID:     sql.NullInt64{Valid: false},
			ApprovalID: "a3_201712_approved_approval",
			Type:       accountingM.ApprovalTypeRevenue,
			Region:     "TW",
			Subject:    "s3",
			Status:     accountingM.ApprovalStatus_APPROVAL_EDITABLE,
			ExecutorID: "tester3",
			TimeMillis: mtime.MilliSecond(t20171205),
		},
		{
			PrevID:     sql.NullInt64{Valid: false},
			ApprovalID: "a4_201712_rejected_approval",
			Type:       accountingM.ApprovalTypeRevenue,
			Region:     "TW",
			Subject:    "S4",
			Status:     accountingM.ApprovalStatus_APPROVAL_EDITABLE,
			ExecutorID: "tester4",
			TimeMillis: mtime.MilliSecond(t20171205),
		},
		{
			PrevID:     sql.NullInt64{Valid: false},
			ApprovalID: "a5_recover_a4_approval",
			Type:       accountingM.ApprovalTypeRevenue,
			Region:     "TW",
			Subject:    "s5",
			Status:     accountingM.ApprovalStatus_APPROVAL_EDITABLE,
			ExecutorID: "tester5",
			TimeMillis: mtime.MilliSecond(t20171205),
		},
		{
			PrevID:     sql.NullInt64{Valid: false},
			ApprovalID: "wallet",
			Type:       accountingM.ApprovalTypeWallet,
			Region:     "TW",
			Subject:    "s6",
			Status:     accountingM.ApprovalStatus_APPROVAL_EDITABLE,
			ExecutorID: "tester5",
			TimeMillis: mtime.MilliSecond(t********),
		},
		{
			PrevID:     sql.NullInt64{Valid: false},
			ApprovalID: "wallet2",
			Type:       accountingM.ApprovalTypeWallet,
			Region:     "TW",
			Subject:    "s6",
			Status:     accountingM.ApprovalStatus_APPROVAL_EDITABLE,
			ExecutorID: "tester5",
			TimeMillis: mtime.MilliSecond(t********),
		},
		{
			PrevID:     sql.NullInt64{Valid: false},
			ApprovalID: "agency_approval_id_1",
			Type:       accountingM.ApprovalTypeAgency,
			Region:     "TW",
			Subject:    "s3",
			Status:     accountingM.ApprovalStatus_APPROVAL_EDITABLE,
			RateMonth:  sql.NullString{Valid: true, String: "2019-03"},
			TimeMillis: mtime.MilliSecond(t********),
		},
		{
			PrevID:     sql.NullInt64{Valid: false},
			ApprovalID: "a9_has_extra_bill",
			Type:       accountingM.ApprovalTypeRevenue,
			Region:     "TW",
			Subject:    "s9",
			Status:     accountingM.ApprovalStatus_APPROVAL_EDITABLE,
			ExecutorID: "tester1",
			TimeMillis: mtime.MilliSecond(t20221012),
		},
	}

	for _, a := range mockApprovals {
		res, err := db.NamedExec(approval.InsertApprovalQueryStr, a)
		if err != nil {
			panic(err)
		}

		a.ID, err = res.LastInsertId()
		if err != nil {
			panic(err)
		}
	}

	// a1_2017_approvaed_approval
	_, _, err := im.approval.Append(mockCTX, nil, "tester", "a1_2017_approvaed_approval", []string{"201711-b1-in-a1", "201711-b2-in-a1", "201711-b3-in-a1"})
	if err != nil {
		panic(err)
	}
	_, _, err = im.approval.Seal(mockCTX, nil, "sealer", "a1_2017_approvaed_approval", mockApprovalFileKey)
	if err != nil {
		panic(err)
	}
	_, _, _, err = im.approval.Approve(mockCTX, "approver", "a1_2017_approvaed_approval", "2017-11")
	if err != nil {
		panic(err)
	}

	// a2_2017_sealed_approval
	_, _, err = im.approval.Append(mockCTX, nil, "tester", "a2_2017_sealed_approval", []string{"201712-b5-in-a2"})
	if err != nil {
		panic(err)
	}
	_, _, err = im.approval.Seal(mockCTX, nil, "sealer", "a2_2017_sealed_approval", mockApprovalFileKey)
	if err != nil {
		panic(err)
	}

	// a3_201712_approved_approval
	_, _, err = im.approval.Append(mockCTX, nil, "tester", "a3_201712_approved_approval", []string{"201712-b6-in-a3", "201712-b7-in-a3"})
	if err != nil {
		panic(err)
	}
	_, _, err = im.approval.Seal(mockCTX, nil, "sealer", "a3_201712_approved_approval", mockApprovalFileKey)
	if err != nil {
		panic(err)
	}
	_, _, _, err = im.approval.Approve(mockCTX, "approver", "a3_201712_approved_approval", "2017-12")
	if err != nil {
		panic(err)
	}

	// a4_201712_rejected_approval and aproval-test-a2
	_, _, err = im.approval.Append(mockCTX, nil, "tester", "a4_201712_rejected_approval", []string{"2017-b8-in-a4-a5", "2017-b9-in-a4-a5"})
	if err != nil {
		panic(err)
	}
	_, _, err = im.approval.Seal(mockCTX, nil, "sealer", "a4_201712_rejected_approval", mockApprovalFileKey)
	if err != nil {
		panic(err)
	}
	_, _, err = im.approval.Reject(mockCTX, "rejecter", "a4_201712_rejected_approval")
	if err != nil {
		panic(err)
	}

	// append, seal and approve rejected bills in a24_201712_rejected_approval to a5_recover_a4_approval
	_, _, err = im.approval.Append(mockCTX, nil, "tester2", "a5_recover_a4_approval", []string{"2017-b8-in-a4-a5", "2017-b9-in-a4-a5"})
	if err != nil {
		panic(err)
	}
	_, _, err = im.approval.Seal(mockCTX, nil, "sealer2", "a5_recover_a4_approval", mockApprovalFileKey)
	if err != nil {
		panic(err)
	}
	_, _, _, err = im.approval.Approve(mockCTX, "approver2", "a5_recover_a4_approval", "a5_rate")
	if err != nil {
		panic(err)
	}

	// append, seal and approve rejected bills in a24_201712_rejected_approval to a5_recover_a4_approval
	_, _, err = im.approval.Append(mockCTX, nil, "tester2", "wallet", []string{"wallet-1"})
	if err != nil {
		panic(err)
	}
	_, _, err = im.approval.Seal(mockCTX, nil, "sealer2", "wallet", mockApprovalFileKey)
	if err != nil {
		panic(err)
	}
	// TODO fix
	_, _, _, err = im.approval.Approve(mockCTX, "approver2", "wallet", "a5_rate")
	if err != nil {
		panic(err)
	}

	_, _, err = im.approval.Append(mockCTX, nil, "tester", "wallet2", []string{"wallet-2"})
	if err != nil {
		panic(err)
	}
	_, _, err = im.approval.Seal(mockCTX, nil, "sealer", "wallet2", mockApprovalFileKey)
	if err != nil {
		panic(err)
	}

	_, _, err = im.approval.Append(mockCTX, nil, "tester", "agency_approval_id_1", []string{"pay_to_agency_bill"})
	if err != nil {
		panic(err)
	}
	_, _, err = im.approval.Seal(mockCTX, nil, "sealer", "agency_approval_id_1", mockApprovalFileKey)
	if err != nil {
		panic(err)
	}

	// a9(contains extra bill) append and seal
	_, _, err = im.approval.Append(mockCTX, nil, "tester", "a9_has_extra_bill", []string{"202210-b1-in-a9-is-extra", "202210-b2-in-a9"})
	if err != nil {
		panic(err)
	}
	_, _, err = im.approval.Seal(mockCTX, nil, "sealer", "a9_has_extra_bill", mockApprovalFileKey)
	if err != nil {
		panic(err)
	}

	// insert extra bill to ExtraBill
	mockExtraBillDBRows := []*accountingM.ExtraBillDBRow{
		{
			BillID:           "202210-b1-in-a9-is-extra",
			CreateTimeMillis: mtime.MilliSecond(t20221012),
		},
	}
	for _, r := range mockExtraBillDBRows {
		res, err := db.NamedExec(insertExtraBillStmt, r)
		if err != nil {
			panic(err)
		}

		r.ID, err = res.LastInsertId()
		if err != nil {
			panic(err)
		}
	}

	// s.mockFuncs.On("goroutineGo", mock.Anything).Run(func(args mock.Arguments) {
	// 	arg := args.Get(0).(func())
	// 	arg()
	// })
	groupID := "groupID"
	payExecutorID := "payer"
	mockPayouts := []payout.DBRow{
		{
			TimeMillis:       mtime.MilliSecond(t20171205),
			GroupID:          groupID,
			CreatorID:        payExecutorID,
			BillID:           "201711-b1-in-a1",
			AgencyType:       accountingM.PayoutType_BANK_TBB,
			AgencyAccount:    "<EMAIL>",
			Rate:             1,
			Currency:         currency.USD.String(),
			AmountBill:       10000,
			AgencyReceiptID:  "receipt-1",
			FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
			Fee:              0,
			AmountPaid:       0,
			AmountPayable:    9990,
			Deduction:        10,
			Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
			ThirdPartyStatus: "",
			UpdateTimeMillis: mtime.MilliSecond(t20171205),
			ErrorMessage:     sql.NullString{Valid: false},
		},
		{
			TimeMillis:       mtime.MilliSecond(t20171205),
			GroupID:          groupID,
			CreatorID:        payExecutorID,
			BillID:           "201711-b3-in-a1",
			AgencyType:       accountingM.PayoutType_BANK_TBB,
			AgencyAccount:    "bank-account-2",
			Rate:             1,
			Currency:         currency.USD.String(),
			AmountBill:       30000,
			AgencyReceiptID:  "receipt-2",
			FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
			Fee:              1000,
			AmountPaid:       0,
			AmountPayable:    29000,
			Status:           accountingM.PayoutStatus_PAYOUT_FAIL,
			ThirdPartyStatus: "FAILED",
			UpdateTimeMillis: mtime.MilliSecond(t20171205),
			ErrorMessage:     sql.NullString{Valid: true, String: "failed error message"},
		},
		{
			TimeMillis:       mtime.MilliSecond(t20171205),
			GroupID:          groupID,
			CreatorID:        payExecutorID,
			BillID:           "201712-b7-in-a3",
			AgencyType:       accountingM.PayoutType_BANK_TBB,
			AgencyAccount:    "<EMAIL>",
			Rate:             1,
			Currency:         currency.USD.String(),
			AmountBill:       70000,
			AgencyReceiptID:  "receipt-3",
			FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
			Fee:              0,
			AmountPaid:       5000,
			AmountPayable:    64990,
			Deduction:        10,
			Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
			ThirdPartyStatus: "UNCLAIMED",
			UpdateTimeMillis: mtime.MilliSecond(t20171205),
			ErrorMessage:     sql.NullString{Valid: true, String: "unclaimed error message"},
		},
		{
			TimeMillis:       mtime.MilliSecond(t20171205),
			GroupID:          mockPayoutGroupID,
			CreatorID:        mockExecutorID,
			BalancerID:       mockPayoutBalancerID,
			BillID:           "201711-b2-in-a1",
			AgencyType:       accountingM.PayoutType_BANK_ESUN,
			AgencyAccount:    "bank-account",
			Currency:         "USD",
			Rate:             float64(1.0),
			AmountBill:       20000,
			FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
			Fee:              float64(1000),
			AmountPaid:       20000,
			Status:           accountingM.PayoutStatus_PAYOUT_SUCCESS,
			UpdateTimeMillis: mtime.MilliSecond(t20171205),
		},
		{
			TimeMillis:       mtime.MilliSecond(t20171205),
			GroupID:          mockPayoutGroupID,
			CreatorID:        mockExecutorID,
			BalancerID:       mockPayoutBalancerID,
			BillID:           "wallet-1",
			AgencyType:       accountingM.PayoutType_BANK_TBB,
			AgencyAccount:    "u16",
			Currency:         "TWD",
			Rate:             30.0,
			AmountBill:       30000,
			FeeOwner:         payoutAccountM.FeeOwnerType_FEE_OWNER_USER,
			Fee:              float64(100),
			AmountPaid:       29900,
			Status:           accountingM.PayoutStatus_PAYOUT_IN_PROGRESS,
			UpdateTimeMillis: mtime.MilliSecond(t20171205),
		},
	}
	// New pay
	if err = mdb.Transactx(mockCTX, db, func(tx *sqlx.Tx) error {
		for _, p := range mockPayouts {
			bills, err := s.bill.GetByBillIDs(mockCTX, tx, []string{p.BillID})
			if err != nil {
				return err
			}
			b := bills[0]
			b.PrevID.Int64 = b.ID
			b.Status = accountingM.BillStatus_BILL_PAYOUT
			b.TimeMillis = p.UpdateTimeMillis
			b.IsNew = 1
			_, err = db.Exec("UPDATE Bill SET isNew = 0 WHERE id = ?", b.PrevID)
			if err != nil {
				panic(err)
			}
			_, err = db.NamedExec(bill.InsertBillQueryStr, b)
			if err != nil {
				return err
			}
			res, err := db.NamedExec(insertAccountingPayoutStmt, p)
			if err != nil {
				return err
			}

			p.ID, err = res.LastInsertId()
			if err != nil {
				return err
			}
		}
		return nil
	}); err != nil {
		panic(err)
	}

	// Update bill
	if err = mdb.Transactx(mockCTX, db, func(tx *sqlx.Tx) error {
		_, err = s.bill.UpdateStatus(mockCTX, tx, mockExecutorID, []string{"********-voided"}, accountingM.BillStatus_BILL_VOIDED)
		return err
	}); err != nil {
		panic(err)
	}
}

func TestSuite(t *testing.T) {
	suite.Run(t, new(accountingTestSuite))
}

func (s *accountingTestSuite) getExtraBills() (ebs []*accountingM.ExtraBillDBRow, err error) {
	const sqlStr = `
		SELECT billID, createTimeMillis
		FROM ExtraBill
	`
	err = s.dbx.Select(&ebs, sqlStr)
	return ebs, err
}

type mockFuncs struct {
	mock.Mock
}

func (m *mockFuncs) timeNow() time.Time {
	ret := m.Called()
	return ret.Get(0).(time.Time)
}

func (m *mockFuncs) isPayoutAvailableForERP(region string, payoutType accountingM.PayoutType) bool {
	ret := m.Called()
	return ret.Get(0).(bool)
}

func (m *mockFuncs) getTimeLocation(context ctx.CTX, rs region.Service, region string) (location *time.Location, err error) {
	ret := m.Called(context, rs, region)
	return ret.Get(0).(*time.Location), ret.Error(1)
}

func (m *mockFuncs) getFeatureStartTime(feature featureModel.Features, regionInfo *regionM.RegionInfo, deviceInfo *confModel.DeviceInfo) int64 {
	ret := m.Called(feature, regionInfo, deviceInfo)
	return ret.Get(0).(int64)
}

func (m *mockFuncs) createBillsFunc(im *impl, context ctx.CTX, executorID string, createInputs []*accountingM.CreateBillInput, callback func(*sqlx.Tx, []string, []*bill.Element) error) ([]*accountingM.ResBillLog, error) {
	ret := m.Called(im, context, executorID, createInputs, callback)
	return ret.Get(0).([]*accountingM.ResBillLog), ret.Error(1)
}

func (m *mockFuncs) pushBillChangedMessage() bool {
	ret := m.Called()
	return ret.Get(0).(bool)
}

func (m *mockFuncs) getAccountType(group string) []payoutAccountM.AccountType {
	ret := m.Called(group)
	return ret.Get(0).([]payoutAccountM.AccountType)
}

func (m *mockFuncs) parseCreateBillFile(context ctx.CTX, im *impl, region string, content []byte) ([]createBillRow, error) {
	ret := m.Called(context, im, region, content)
	return ret.Get(0).([]createBillRow), ret.Error(1)
}

func (m *mockFuncs) getBillLogs(context ctx.CTX, im *impl, filter accountingM.GetBillLogsFilter, cursor string, limit int, options ...GetBillLogsOption) (logs []*accountingM.ResBillLog, totalCount int, nextCursor string, err error) {
	ret := m.Called(context, im, filter, cursor, limit, options)
	return ret.Get(0).([]*accountingM.ResBillLog), ret.Get(1).(int), ret.Get(2).(string), ret.Error(3)
}
