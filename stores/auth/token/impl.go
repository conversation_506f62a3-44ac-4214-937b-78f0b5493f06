package token

import (
	jwtlib "github.com/dgrijalva/jwt-go"

	btime "github.com/17media/api/base/time"
	"github.com/17media/logrus"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models"
	"github.com/17media/api/stores/auth/token/config"
)

var (
	getUUIDv4 = models.GetUUIDv4
	timeNow   = btime.TimeNow

	getRefreshExpireTime = config.GetRefreshTokenExpireTime
	getAccessExpireTime  = config.GetAccessTokenExpireTime
	isTokenRevoked       = config.IsTokenRevoked
	isInWhitelist        = config.IsInWhitelist
)

type impl struct {
	refreshKey   []byte
	jwtAccessKey []byte
}

// New Creates an implementation of token Store
func New(refreshKey string, jwtAccessKey string) Store {
	im := impl{
		refreshKey:   []byte(refreshKey),
		jwtAccessKey: []byte(jwtAccessKey),
	}
	return &im
}

func (im *impl) issue(context ctx.CTX, claims *Claims, key interface{}) (string, error) {
	token := jwtlib.NewWithClaims(jwtlib.SigningMethodHS512, claims)
	return token.SignedString(key)
}

func (im *impl) Issue(context ctx.CTX, userID string) (string, string, string, error) {

	now := timeNow()

	tokenID, err := getUUIDv4()
	if err != nil {
		return "", "", "", err
	}

	private := privateClaims{
		UserID: userID,
	}

	refreshExpireTime := getRefreshExpireTime()
	accessExpireTime := getAccessExpireTime()

	refreshClaims := Claims{
		StandardClaims: jwtlib.StandardClaims{
			IssuedAt:  now.Unix(),
			Id:        tokenID,
			ExpiresAt: now.Add(refreshExpireTime).Unix(),
			Subject:   subRefreshToken,
		},
		privateClaims: private,
	}

	refreshTokenStr, err := im.issue(context, &refreshClaims, im.refreshKey)
	if err != nil {
		return "", "", "", err
	}

	accessClaims := Claims{
		StandardClaims: jwtlib.StandardClaims{
			IssuedAt:  now.Unix(),
			Id:        tokenID,
			ExpiresAt: now.Add(accessExpireTime).Unix(),
			Subject:   subAccessToken,
		},
		privateClaims: private,
	}

	accessTokenStr, err := im.issue(context, &accessClaims, im.jwtAccessKey)
	if err != nil {
		return "", "", "", err
	}

	return tokenID, refreshTokenStr, accessTokenStr, nil
}

func (im *impl) Validate(context ctx.CTX, tokenString, ip string) (*Claims, error) {

	claims := Claims{}

	keyFunc := func(token *jwtlib.Token) (interface{}, error) {
		if claims.Subject == subRefreshToken {
			return im.refreshKey, nil
		}
		if claims.Subject == subAccessToken {
			return im.jwtAccessKey, nil
		}
		return nil, ErrSubjectInvalid
	}

	_, err := jwtlib.ParseWithClaims(tokenString, &claims, keyFunc)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err":    err,
			"token":  tokenString,
			"userID": claims.UserID,
		}).Error("token validate error")
		if err == ErrTokenExpired && isInWhitelist(ip) {
			return &claims, nil
		}
		return &claims, err
	}

	return &claims, nil
}

func (im *impl) Refresh(context ctx.CTX, token string) (*Claims, string, string, error) {
	// refresh token needs unexpired token
	tokenClaims, err := im.Validate(context, token, "")
	if err != nil {
		return nil, "", "", err
	}
	if tokenClaims.Subject != subRefreshToken {
		return nil, "", "", ErrTokenInvalid
	}

	now := timeNow()

	refreshExpireTime := getRefreshExpireTime()
	accessExpireTime := getAccessExpireTime()

	private := privateClaims{
		UserID: tokenClaims.UserID,
	}

	refreshClaims := Claims{
		StandardClaims: jwtlib.StandardClaims{
			IssuedAt:  now.Unix(),
			Id:        tokenClaims.Id,
			ExpiresAt: now.Add(refreshExpireTime).Unix(),
			Subject:   subRefreshToken,
		},
		privateClaims: private,
	}

	refreshTokenStr, err := im.issue(context, &refreshClaims, im.refreshKey)
	if err != nil {
		return nil, "", "", err
	}

	accessClaims := Claims{
		StandardClaims: jwtlib.StandardClaims{
			IssuedAt:  now.Unix(),
			Id:        tokenClaims.Id,
			ExpiresAt: now.Add(accessExpireTime).Unix(),
			Subject:   subAccessToken,
		},
		privateClaims: private,
	}

	accessTokenStr, err := im.issue(context, &accessClaims, im.jwtAccessKey)
	if err != nil {
		return nil, "", "", err
	}

	return tokenClaims, refreshTokenStr, accessTokenStr, nil
}

// Valid method that determines if the token is invalid for any supported reason
// Valid() will be called by ParseWithClaims
// if return error type is not jwtlib.ValidationError, jwtlib will wrap the error
func (c Claims) Valid() error {
	// check expire
	now := timeNow()

	if now.Unix() > c.StandardClaims.ExpiresAt {
		return ErrTokenExpired
	}

	if isTokenRevoked(c.UserID, c.StandardClaims.IssuedAt) {
		return ErrTokenInvalid
	}

	return nil
}
