package password

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/crypto/bcrypt"

	"github.com/17media/api/base/ctx"
	bdocker "github.com/17media/api/base/docker"
	"github.com/17media/api/models"
	"github.com/17media/api/models/keys"
	"github.com/17media/api/service/kv"
	"github.com/17media/api/service/queryv2"
	"github.com/17media/api/service/redis/rediscache"
	msms "github.com/17media/api/service/sms/mocks"
	"github.com/17media/api/setup/dimanager"
)

var (
	mCTX = ctx.Background()
)

type mockFuncs struct {
	mock.Mock
}

type passwordSuite struct {
	suite.Suite
	manager   *dimanager.Manager
	mongo     queryv2.Mongo
	mongoPort string
	redisPort string
	sms       *msms.Service
	pw        *impl
	kv        kv.KV
}

func TestStatisticSuite(t *testing.T) {
	suite.Run(t, new(passwordSuite))
}

func (s *passwordSuite) SetupSuite() {
	localhost, ports, err := bdocker.RunExtDockers([]string{"redis", "mongo"})
	s.Require().NoError(err)
	s.redisPort = ports[0]
	s.mongoPort = ports[1]

	s.manager = dimanager.DefaultManager
	s.manager.ProvideString("redis_cache_uri", localhost+":"+ports[0])
	s.manager.ProvideString("redis_cache_user_uri", localhost+":"+ports[0])
	s.manager.ProvideString("redis_persist_uri", localhost+":"+ports[0])
	s.manager.ProvideString("mongo_uri", localhost+":"+ports[1])
	s.manager.ProvideString("mongo_db", "17media")
	rediscache.ConnectRedisCluster(s.manager)
}

func (s *passwordSuite) TearDownSuite() {
}

func (s *passwordSuite) SetupTest() {
	s.manager.ClearMock()
	s.manager.Compile()

	s.mongo = queryv2.GetQueryV2WithoutFillNil(s.manager)
	s.sms = &msms.Service{}
	s.kv = kv.GetKv(s.manager)
	s.pw = New(s.mongo, s.sms, s.kv).(*impl)
}

func (s *passwordSuite) TearDownTest() {
	s.sms.AssertExpectations(s.T())
	s.NoError(bdocker.ClearMongo(s.mongoPort))
	s.NoError(bdocker.ClearRedis(s.redisPort))
}

func (s *passwordSuite) TestGetResetPasswordTokenWithInvalidParameter() {
	_, err := s.pw.GetResetPasswordToken(mCTX, "", "", "", "", "")
	s.Require().EqualError(err, "parameter_error")

	// valid country calling code
	_, err = s.pw.GetResetPasswordToken(mCTX, "", "886", "", "", "")
	s.Require().EqualError(err, "parameter_error")

	// valid local phone number
	_, err = s.pw.GetResetPasswordToken(mCTX, "", "886", "12345678", "", "")
	s.Require().EqualError(err, "parameter_error")

	// valid openID
	_, err = s.pw.GetResetPasswordToken(mCTX, "openID", "886", "12345678", "", "")
	s.Require().EqualError(err, "parameter_error")

	// valid verification code
	_, err = s.pw.GetResetPasswordToken(mCTX, "openID", "886", "12345678", "0246", "")
	s.Require().EqualError(err, "parameter_error")

	// valid request ID
	s.sms.On("VerifyCode", mock.AnythingOfType("ctx.CTX"), "requestID", "0246", "886", "12345678").Return(fmt.Errorf("NotMatch")).Once()
	_, err = s.pw.GetResetPasswordToken(mCTX, "openID", "886", "12345678", "0246", "requestID")
	s.Require().EqualError(err, "NotMatch")
}

func (s *passwordSuite) TestGetResetPasswordTokenWithOpenIDNotFound() {
	s.sms.On("VerifyCode", mock.AnythingOfType("ctx.CTX"), "requestID", "0246", "886", "12345678").Return(nil).Once()
	_, err := s.pw.GetResetPasswordToken(mCTX, "openID", "886", "12345678", "0246", "requestID")
	s.Require().EqualError(err, "openID_not_existed")
}

func (s *passwordSuite) TestGetResetPasswordTokenWithPhoneNotPaired() {
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
		"userID":                  "userID",
		keys.FieldOpenID.String(): "openID",
		"countryCallingCode":      "886",
	}))

	s.sms.On("VerifyCode", mock.AnythingOfType("ctx.CTX"), "requestID", "0246", "886", "12345678").Return(nil)
	_, err := s.pw.GetResetPasswordToken(mCTX, "openID", "886", "12345678", "0246", "requestID")
	s.Require().EqualError(err, "openID_phoneNumber_not_paired")

	_, err = s.pw.GetResetPasswordToken(mCTX, "openID", "886", "12345678", "0246", "requestID")
	s.Require().EqualError(err, "openID_phoneNumber_not_paired")
	s.TearDownTest()
}

func (s *passwordSuite) TestGetResetPasswordTokenOK() {
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
		"userID":                   "userID",
		keys.FieldOpenID.String():  "openID",
		"countryCallingCode":       "886",
		"localPhoneNumber":         "12345678",
		"internationalPhoneNumber": "88612345678",
	}))
	s.sms.On("VerifyCode", mock.AnythingOfType("ctx.CTX"), "requestID", "0246", "886", "12345678").Return(nil).Once()
	res, err := s.pw.GetResetPasswordToken(mCTX, "openID", "886", "12345678", "0246", "requestID")
	s.Require().NoError(err)
	s.Require().NotEmpty(res)
	s.TearDownTest()

	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
		"userID":                   "userID",
		keys.FieldOpenID.String():  "openID🥱",
		"countryCallingCode":       "886",
		"localPhoneNumber":         "12345678",
		"internationalPhoneNumber": "88612345678",
	}))
	s.sms.On("VerifyCode", mock.AnythingOfType("ctx.CTX"), "requestID", "0246", "886", "12345678").Return(nil).Once()
	res, err = s.pw.GetResetPasswordToken(mCTX, "openID🥱", "886", "12345678", "0246", "requestID")
	s.Require().NoError(err)
	s.Require().NotEmpty(res)
	s.TearDownTest()
}

func (s *passwordSuite) TestGetResetPasswordTokenLowerCaseOK() {
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
		"userID":                   "userID",
		keys.FieldOpenID.String():  "openid",
		"countryCallingCode":       "886",
		"localPhoneNumber":         "12345678",
		"internationalPhoneNumber": "88612345678",
	}))
	s.sms.On("VerifyCode", mock.AnythingOfType("ctx.CTX"), "requestID", "0246", "886", "12345678").Return(nil).Once()
	res, err := s.pw.GetResetPasswordToken(mCTX, "openID", "886", "12345678", "0246", "requestID")
	s.Require().NoError(err)
	s.Require().NotEmpty(res)
	s.TearDownTest()
}

func (s *passwordSuite) TestGetResetPasswordTokenLowerCaseNotFound() {
	s.sms.On("VerifyCode", mock.AnythingOfType("ctx.CTX"), "requestID", "0246", "886", "12345678").Return(nil).Once()
	_, err := s.pw.GetResetPasswordToken(mCTX, "openid", "886", "12345678", "0246", "requestID")
	s.Require().EqualError(err, "openID_not_existed")
}

func (s *passwordSuite) TestResetPasswordWithInvalidParameter() {
	err := s.pw.ResetPassword(mCTX, "", "", "")
	s.Require().EqualError(err, "parameter_error")

	// valid password token
	err = s.pw.ResetPassword(mCTX, "resetToken", "", "")
	s.Require().EqualError(err, "parameter_error")

	// valid new password
	err = s.pw.ResetPassword(mCTX, "resetToken", "iamlegend", "")
	s.Require().EqualError(err, "parameter_error")

	// valid confirm password
	err = s.pw.ResetPassword(mCTX, "resetToken", "iamlegend", "confirm")
	s.Require().EqualError(err, "new_password_confirm_error")
}

func (s *passwordSuite) TestResetPasswordWithDBError() {
	// not found
	err := s.pw.ResetPassword(mCTX, "resetToken", "iamlegend", "iamlegend")
	s.Require().EqualError(err, "resetPasswordToken_invalid")

	// token used
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabResetPasswordTokenRecord, bson.M{
		"resetPasswordToken": "resetToken",
		"isUsed":             1,
	}))
	err = s.pw.ResetPassword(mCTX, "resetToken", "iamlegend", "iamlegend")
	s.Require().EqualError(err, "resetPasswordToken_used")
	s.TearDownTest()

	// expired
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabResetPasswordTokenRecord, bson.M{
		"resetPasswordToken": "resetToken",
		"isUsed":             0,
		"ExpireTime":         0,
	}))
	err = s.pw.ResetPassword(mCTX, "resetToken", "iamlegend", "iamlegend")
	s.Require().EqualError(err, "resetPasswordToken_timeout")
	s.TearDownTest()
}

func (s *passwordSuite) TestResetPasswordWithExceedLimit() {

	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabResetPasswordTokenRecord, bson.M{
		"resetPasswordToken": "resetToken",
		"isUsed":             0,
		"expireTime":         time.Now().Unix() + 86400,
		"userID":             "userID-2",
	}))
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
		"userID": "userID",
	}))
	err := s.pw.ResetPassword(mCTX, "resetToken", "iamlegend", "iamlegend")
	s.Require().Equal(err, kv.ErrNotFound)
	s.TearDownTest()

	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabResetPasswordTokenRecord, bson.M{
		"resetPasswordToken": "resetToken",
		"isUsed":             0,
		"expireTime":         time.Now().Unix() + 86400,
		"userID":             "userID",
	}))
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
		"userID":                "userID",
		"lastResetPasswordTime": time.Now().Unix(),
	}))
	err = s.pw.ResetPassword(mCTX, "resetToken", "iamlegend", "iamlegend")
	s.Require().EqualError(err, "maximum_count_exceeds")
	s.TearDownTest()

	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabResetPasswordTokenRecord, bson.M{
		"resetPasswordToken": "resetToken",
		"isUsed":             0,
		"expireTime":         time.Now().Unix() + 86400,
		"userID":             "userID",
	}))
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
		"userID":                "userID",
		"lastResetPasswordTime": 0,
	}))
	err = s.pw.ResetPassword(mCTX, "resetToken", "iamlegend", "iamlegend")
	s.Require().NoError(err)
	s.TearDownTest()
}

func (s *passwordSuite) TestResetPasswordOK() {
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabResetPasswordTokenRecord, bson.M{
		"resetPasswordToken": "resetToken",
		"isUsed":             0,
		"expireTime":         time.Now().Unix() + 86400,
		"userID":             "userID",
	}))
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
		"userID": "userID",
	}))

	err := s.pw.ResetPassword(mCTX, "resetToken", "iamlegend", "iamlegend")
	s.Require().NoError(err)
}

func (s *passwordSuite) TestChangePasswordWithInvalidParameter() {
	s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
		"userID":         "userID",
		"passwordBcrypt": "123",
	}))
	err := s.pw.ChangePassword(mCTX, "userID", "123", "123")
	s.Require().EqualError(err, "password_wrong")
}

func (s *passwordSuite) TestChangePassword() {
	tests := []struct {
		desc        string
		userID      string
		dbPassword  string
		oldPassword string
		newPassword string
		expErr      error
		mockFunc    func()
	}{
		{
			desc:        "ok",
			userID:      "mUserID",
			dbPassword:  "old1",
			oldPassword: "old1",
			newPassword: "new1",
			mockFunc: func() {
				b, err := bcrypt.GenerateFromPassword([]byte("old1"), bcrypt.DefaultCost)
				s.NoError(err)
				mDbPasswordBcrypt := string(b)
				s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
					keys.FieldUserID.String():    "mUserID",
					keys.FieldPasswordB.String(): mDbPasswordBcrypt,
				}))
			},
			expErr: nil,
		},
		{
			desc:        "password wrong",
			userID:      "mUserID",
			dbPassword:  "old1",
			oldPassword: "old3",
			newPassword: "new3",
			mockFunc: func() {
				b, err := bcrypt.GenerateFromPassword([]byte("old1"), bcrypt.DefaultCost)
				s.NoError(err)
				mDbPasswordBcrypt := string(b)
				s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
					keys.FieldUserID.String():    "mUserID",
					keys.FieldPasswordB.String(): mDbPasswordBcrypt,
				}))
			},
			expErr: fmt.Errorf("password_wrong"),
		},
	}

	for _, t := range tests {
		if t.mockFunc != nil {
			t.mockFunc()
		}
		// check ChangePassword's err
		err := s.pw.ChangePassword(mCTX, t.userID, t.oldPassword, t.newPassword)
		s.Equal(t.expErr, err, t.desc)
		if err == nil {
			user := &models.User{}
			s.Require().NoError(s.kv.Get(mCTX, keys.TabUser, t.userID, user))
			s.Require().NoError(bcrypt.CompareHashAndPassword([]byte(user.PasswordBcrypt), []byte(t.newPassword)))
		}
		s.TearDownTest()
	}
}

func (s *passwordSuite) TestGetUserInfoByOpenID() {
	tests := []struct {
		desc     string
		openID   string
		expErr   error
		mockFunc func()
	}{
		{
			desc:   "ok",
			openID: "openID",
			expErr: nil,
			mockFunc: func() {
				s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
					"userID": "userID",
					"openID": "openID",
				}))
			},
		},
		{
			desc:   "ok-lowercase",
			openID: "openID",
			expErr: nil,
			mockFunc: func() {
				s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
					"userID": "userID",
					"openID": "openid",
				}))
			},
		},
		{
			desc:   "ok-pureOpenID",
			openID: "openID",
			expErr: nil,
			mockFunc: func() {
				s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
					"userID":     "userID",
					"openID":     "openid-xxx",
					"pureOpenID": "openID",
				}))
			},
		},
		{
			desc:   "ok-pureOpenID-lowercase",
			openID: "openID",
			expErr: nil,
			mockFunc: func() {
				s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
					"userID":     "userID",
					"openID":     "openid-xxx",
					"pureOpenID": "openid",
				}))
			},
		},
		{
			desc:   "not found case",
			openID: "openID",
			expErr: queryv2.ErrNotFound,
			mockFunc: func() {
				s.Require().NoError(s.mongo.Insert(mCTX, keys.TabUser, bson.M{
					"userID":     "userID",
					"openID":     "openid-xxx",
					"pureOpenID": "openid-zzz",
				}))
			},
		},
	}

	for _, t := range tests {
		if t.mockFunc != nil {
			t.mockFunc()
		}
		_, err := s.pw.getUserInfoByOpenID(mCTX, t.openID)
		s.Equal(t.expErr, err, t.desc)
		s.TearDownTest()
	}
}
