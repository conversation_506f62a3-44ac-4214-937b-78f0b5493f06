package genrelive

import (
	"errors"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models"
	model "github.com/17media/api/models/genrelive"
	regionModel "github.com/17media/api/models/region"
	sectionModel "github.com/17media/api/models/section"
	adminHelper "github.com/17media/api/stores/admin/helper"
)

var (
	ErrorCategoryNotSupported = errors.New("categoryID not supported")
	ErrorNoPinnedStream       = errors.New("no pinned stream")
)

type SortArg struct {
	orderAsc bool
	mlUserID string
	sortType model.SortType
}

// SortByML specifies we will get ml sorting by specific userID
func SortByML(userID string) SortArg {
	return SortArg{
		mlUserID: userID,
		sortType: model.SortTypeML,
	}
}

// SortByAch means getting streams sorted by achievement value. orderAsc = true then sort from low to high
func SortByAch(orderAsc bool) SortArg {
	return SortArg{
		orderAsc: orderAsc,
		sortType: model.SortTypeAch,
	}
}

// SortByViewer means getting streams sorted by viewer. orderAsc = true then sort from low to high
func SortByViewer(orderAsc bool) SortArg {
	return SortArg{
		orderAsc: orderAsc,
		sortType: model.SortTypeViewer,
	}
}

// SortByNewbie means getting streams sorted by newbie.
func SortByNewbie() SortArg {
	return SortArg{
		sortType: model.SortTypeNewbie,
	}
}

// Store is the interface of genrelive store
type Store interface {
	GetSortOptions() []*model.Sort
	GetCategoryOptions(context ctx.CTX, regionInfo *regionModel.RegionInfo) ([]*model.Category, error)
	// GetCategoryInfos gets category infos with subcategories
	GetCategoryInfos(context ctx.CTX, region, lang string, categoryIDs []string) ([]*model.CategoryInfo, error)
	// GetSubcategoryStreams gets subcategory related streams
	GetSubcategoryStreams(context ctx.CTX, region, categoryID, subcategoryID, cursor string, count int) ([]model.Container, string, error)
	// UseCoverPhoto check streams need to use cover photo to display at client slide
	UseCoverPhoto(categoryID string) bool

	GetTopBanners(context ctx.CTX, userID string, regionInfo *regionModel.RegionInfo) (grids []*sectionModel.Grid, err error)
	// note: GetLabelStreams for genrelive always exclude offline stream
	GetLabelStreams(context ctx.CTX, regionInfo *regionModel.RegionInfo, labelID, cursor string, count int, arg SortArg) ([]model.Container, string, error)
	GetAudioStreams(context ctx.CTX, regionInfo *regionModel.RegionInfo, cursor string, count int, arg SortArg) ([]model.Container, string, error)
	GetLatestStreams(context ctx.CTX, regionInfo *regionModel.RegionInfo, cursor string, count int, arg SortArg) ([]model.Container, string, error)
	GetGroupCallStreams(context ctx.CTX, regionInfo *regionModel.RegionInfo, cursor string, count int, arg SortArg) ([]model.Container, string, error)
	// ML recommend for specific user
	GetMLRecommendStreams(context ctx.CTX, regionInfo *regionModel.RegionInfo, userID, cursor string, count int, arg SortArg) ([]model.Container, string, error)
	GetPKStreams(context ctx.CTX, regionInfo *regionModel.RegionInfo, cursor string, count int, arg SortArg) ([]model.Container, string, error)
	GetNewestStreams(context ctx.CTX, regionInfo *regionModel.RegionInfo, cursor string, count int, arg SortArg) ([]model.Container, string, error)
	FilterToppestStream(context ctx.CTX, regionInfo *regionModel.RegionInfo, streams []model.Container) ([]model.Container, error)
	InjectSubtabDisplayName(context ctx.CTX, streams []model.Container, userID, region, language string, registerTime int64)

	GetOnAirStreams(context ctx.CTX, regionInfo *regionModel.RegionInfo) []*models.StreamInfoExt

	PinToToppest(context ctx.CTX, regionGroup, streamID, userID string, opts ...adminHelper.Option) error
	CancelPinToToppest(context ctx.CTX, regionGroup, userID string, opts ...adminHelper.Option) error

	GetInterestCategory(context ctx.CTX, regionInfo *regionModel.RegionInfo, interestTags []string) (*model.Category, error)
}
