package feed

import (
	"fmt"
	"math/rand"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/17media/config"

	bdocker "github.com/17media/api/base/docker"
	"github.com/17media/api/models"
	"github.com/17media/api/models/keys"
	rmodel "github.com/17media/api/models/region"
	fcron "github.com/17media/api/service/cron/fake"
	mkv "github.com/17media/api/service/kv/mocks"
	"github.com/17media/api/service/queryv2"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/service/redis/rediscache"
	"github.com/17media/api/setup/dimanager"
	mclip "github.com/17media/api/stores/clip/mocks"
	mfollow "github.com/17media/api/stores/follow/mocks"
	mlive "github.com/17media/api/stores/live/mocks"
	mpost "github.com/17media/api/stores/post/mocks"
	mschedule "github.com/17media/api/stores/schedule/mocks"
	muser "github.com/17media/api/stores/user/mocks"
)

var (
	mUser1  = models.User{OpenID: "openid-1"}
	mUser2  = models.User{OpenID: "openid-2"}
	mUser3  = models.User{OpenID: "openid-3"}
	mRegion = rmodel.RegionInfo{Region: "TW", RegionByIP: "TW"}
)

type celeSuite struct {
	suite.Suite

	user          *muser.Store
	cron          *fcron.Cron
	cfg           config.Client
	celebrityUIDs celebrityUIDConf

	localhost string
	mongoPort string
	redisPort string
	query     queryv2.Mongo
	redis     redis.Service

	im      *impl
	manager *dimanager.Manager
}

func (s *celeSuite) SetupSuite() {
	localhost, ports, err := bdocker.RunExtDockers([]string{"mongo", "redis"})
	s.Require().NoError(err)
	s.localhost = localhost
	s.mongoPort = ports[0]
	s.redisPort = ports[1]

	s.manager = dimanager.DefaultManager
	s.manager.ProvideString("redis_cache_uri", localhost+":"+s.redisPort)
	s.manager.ProvideString("mongo_uri", localhost+":"+s.mongoPort)
	s.manager.ProvideString("mongo_db", "17media")
	rediscache.ConnectRedisCluster(s.manager)
}

func (s *celeSuite) TearDownSuite() {
	s.NoError(bdocker.RemExtDockers())
}

func (s *celeSuite) SetupTest() {
	s.manager.ClearMock()
	mfollow.RegisterMock(s.manager)
	mlive.RegisterMock(s.manager)
	mschedule.RegisterMock(s.manager)
	mpost.RegisterMock(s.manager)
	mclip.RegisterMock(s.manager)
	mkv.RegisterMock(s.manager)
	s.user = muser.RegisterMock(s.manager)
	s.cron = fcron.RegisterFake(s.manager)
	s.manager.Compile()

	s.query = queryv2.GetQueryV2(s.manager)

	// Generate some posts from db
	s.writePosts("u4", 0)  // u4 has 0 posts
	s.writePosts("u5", 3)  // u5 has 3 posts
	s.writePosts("u6", 10) // u6 has 10 posts

	setConfig()

	s.im = GetFeed(s.manager).(*impl)
	s.im.updateCelebrityPosts(mockCTX)
}

func (s *celeSuite) TearDownTest() {
	s.user.AssertExpectations(s.T())

	s.NoError(bdocker.ClearMongo(s.mongoPort))
	s.NoError(bdocker.ClearRedis(s.redisPort))
}

func (s *celeSuite) TestGetAllCelebrities() {
	cases := []struct {
		msg      string
		catID    string
		language string
		limit    int
		expect   *models.Celebrities
		isErr    bool
	}{
		{
			msg:      "limit too low",
			catID:    "",
			language: "EN",
			limit:    0,
			expect:   nil,
			isErr:    true,
		}, {
			msg:      "limit too high",
			catID:    "",
			language: "EN",
			limit:    maxCount + 1,
			expect:   nil,
			isErr:    true,
		}, {
			msg:      "normal case",
			catID:    "",
			language: "EN",
			limit:    3,
			expect: &models.Celebrities{
				Celebrities: []models.Celebrity{
					models.Celebrity{ID: "star", Name: "star", Users: []models.User{mUser1, mUser2, mUser3}},
					models.Celebrity{ID: "star2", Name: "star2", Users: []models.User{mUser1, mUser2, mUser3}},
				},
			},
			isErr: false,
		}, {
			msg:      "limit > elements",
			catID:    "",
			language: "EN",
			limit:    4,
			expect: &models.Celebrities{
				Celebrities: []models.Celebrity{
					models.Celebrity{ID: "star", Name: "star", Users: []models.User{mUser1, mUser2, mUser3}},
					models.Celebrity{ID: "star2", Name: "star2", Users: []models.User{mUser1, mUser2, mUser3}},
				},
			},
			isErr: false,
		}, {
			msg:      "single category",
			catID:    "star3",
			language: "EN",
			limit:    3,
			expect: &models.Celebrities{
				Celebrities: []models.Celebrity{
					models.Celebrity{ID: "star3", Name: "star3", Users: []models.User{mUser1, mUser2, mUser3}},
				},
			},
			isErr: false,
		},
	}
	s.user.On("GetIDs", mockCTX, mockUserID, "u1", "u2", "u3").Return(
		[]models.User{mUser1, mUser2, mUser3}, nil)
	s.user.On("GetIDs", mockCTX, mockUserID, "u4", "u5", "u6").Return(
		[]models.User{mUser1, mUser2, mUser3}, nil)

	for _, c := range cases {
		cele, err := s.im.GetCelebrities(mockCTX, mockUserID, c.catID, c.language, c.limit, &mRegion)
		s.Equal(c.isErr, err != nil, c.msg)
		s.Equal(c.expect, cele, c.msg)
	}
}

func (s *celeSuite) TestGetCelebrityPostsLimitLow() {
	_, err := s.im.GetCelebrityPosts(mockCTX, mockUserID, "star", "", 0, &mRegion)
	s.Error(err)
}

func (s *celeSuite) TestGetCelebrityPostsLimitHigh() {
	_, err := s.im.GetCelebrityPosts(mockCTX, mockUserID, "star", "", maxCount+1, &mRegion)
	s.Error(err)
}

func (s *celeSuite) TestGetCelebrityPostsBadCategory() {
	_, err := s.im.GetCelebrityPosts(mockCTX, mockUserID, "no-such-category", "", 2, &mRegion)
	s.Error(err)
}

func (s *celeSuite) TestGetCelebrityPostsBadCursor() {
	_, err := s.im.GetCelebrityPosts(mockCTX, mockUserID, "star", "bad-cursor", 2, &mRegion)
	s.Error(err)
}

func (s *celeSuite) TestGetCelebrityPostsNoCursor() {
	s.user.On("GetIDs", mockCTX, mockUserID, "u4", "u5").Return(
		[]models.User{mUser1, mUser2}, nil)

	p, err := s.im.GetCelebrityPosts(mockCTX, mockUserID, "star2", "", 2, &mRegion)
	s.NoError(err)
	s.Equal(&models.CelebrityPosts{
		ID: "star2",
		UserPosts: []models.UserPost{
			{User: mUser1, Posts: []models.Post{}},
			{User: mUser2, Posts: []models.Post{genPost("u5", 2), genPost("u5", 1), genPost("u5", 0)}},
		},
		Cursor: "2",
	}, p)
}

func (s *celeSuite) TestGetCelebrityPostsWithCursor() {
	s.user.On("GetIDs", mockCTX, mockUserID, "u5", "u6").Return(
		[]models.User{mUser2, mUser3}, nil)

	p, err := s.im.GetCelebrityPosts(mockCTX, mockUserID, "star2", "1", 10, &mRegion)
	s.NoError(err)
	s.Equal(&models.CelebrityPosts{
		ID: "star2",
		UserPosts: []models.UserPost{
			{User: mUser2, Posts: []models.Post{genPost("u5", 2), genPost("u5", 1), genPost("u5", 0)}},
			{User: mUser3, Posts: []models.Post{genPost("u6", 9), genPost("u6", 8), genPost("u6", 7)}},
		},
	}, p)
}

func (s *celeSuite) writePosts(uid string, numPosts int) {
	for i := 0; i < numPosts; i++ {
		err := s.query.Upsert(
			mockCTX,
			keys.TabPost,
			bson.M{"postID": strconv.Itoa(rand.Int())},
			genPost(uid, i),
		)
		s.NoError(err)
	}
}

func genPost(uid string, index int) models.Post {
	return models.Post{UserID: uid, PostID: strconv.Itoa(index), Timestamp: int32(index)}
}

func setConfig() {
	getCfg = func(id string, userRegion *rmodel.RegionInfo) (interface{}, error) {
		if id == celebrityConfigID {
			return celebrityConf{
				HiddenCategory: map[string]struct{}{
					"star3": {},
				},
			}, nil
		} else if id == celebrityListConfigID {
			return celebrityUIDConf{
				&celebrityCategory{
					CatID:   "star",
					UserIDs: []string{"u1", "u2", "u3"},
				},
				&celebrityCategory{
					CatID:   "star2",
					UserIDs: []string{"u4", "u5", "u6"},
				},
				&celebrityCategory{
					CatID:   "star3",
					UserIDs: []string{"u1", "u2", "u3"},
				},
			}, nil
		}
		return nil, fmt.Errorf("id %v is invalid", id)
	}
}

func TestCeleSuite(t *testing.T) {
	suite.Run(t, new(celeSuite))
}
