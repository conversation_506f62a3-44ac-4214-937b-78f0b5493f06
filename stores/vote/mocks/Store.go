// Code generated by mockery v2.1.0. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	mock "github.com/stretchr/testify/mock"

	vote "github.com/17media/api/models/vote"
)

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// CloseVote provides a mock function with given fields: context, voteType, srcID
func (_m *Store) CloseVote(context ctx.CTX, voteType vote.VoteType, srcID string) error {
	ret := _m.Called(context, voteType, srcID)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, vote.VoteType, string) error); ok {
		r0 = rf(context, voteType, srcID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DoVote provides a mock function with given fields: context, voteType, srcID, votes
func (_m *Store) DoVote(context ctx.CTX, voteType vote.VoteType, srcID string, votes []*vote.DoVote) (*vote.ResVoteInfo, error) {
	ret := _m.Called(context, voteType, srcID, votes)

	var r0 *vote.ResVoteInfo
	if rf, ok := ret.Get(0).(func(ctx.CTX, vote.VoteType, string, []*vote.DoVote) *vote.ResVoteInfo); ok {
		r0 = rf(context, voteType, srcID, votes)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vote.ResVoteInfo)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, vote.VoteType, string, []*vote.DoVote) error); ok {
		r1 = rf(context, voteType, srcID, votes)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EndVote provides a mock function with given fields: context, voteType, srcID
func (_m *Store) EndVote(context ctx.CTX, voteType vote.VoteType, srcID string) (*vote.ResVoteInfo, error) {
	ret := _m.Called(context, voteType, srcID)

	var r0 *vote.ResVoteInfo
	if rf, ok := ret.Get(0).(func(ctx.CTX, vote.VoteType, string) *vote.ResVoteInfo); ok {
		r0 = rf(context, voteType, srcID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vote.ResVoteInfo)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, vote.VoteType, string) error); ok {
		r1 = rf(context, voteType, srcID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetVoteCurrentResult provides a mock function with given fields: context, voteType, srcID
func (_m *Store) GetVoteCurrentResult(context ctx.CTX, voteType vote.VoteType, srcID string) (*vote.ResVoteInfo, error) {
	ret := _m.Called(context, voteType, srcID)

	var r0 *vote.ResVoteInfo
	if rf, ok := ret.Get(0).(func(ctx.CTX, vote.VoteType, string) *vote.ResVoteInfo); ok {
		r0 = rf(context, voteType, srcID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vote.ResVoteInfo)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, vote.VoteType, string) error); ok {
		r1 = rf(context, voteType, srcID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetVoteData provides a mock function with given fields: context, voteType, srcID, withVoteCounts
func (_m *Store) GetVoteData(context ctx.CTX, voteType vote.VoteType, srcID string, withVoteCounts bool) (*vote.VoteStatus, []int32, error) {
	ret := _m.Called(context, voteType, srcID, withVoteCounts)

	var r0 *vote.VoteStatus
	if rf, ok := ret.Get(0).(func(ctx.CTX, vote.VoteType, string, bool) *vote.VoteStatus); ok {
		r0 = rf(context, voteType, srcID, withVoteCounts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vote.VoteStatus)
		}
	}

	var r1 []int32
	if rf, ok := ret.Get(1).(func(ctx.CTX, vote.VoteType, string, bool) []int32); ok {
		r1 = rf(context, voteType, srcID, withVoteCounts)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]int32)
		}
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, vote.VoteType, string, bool) error); ok {
		r2 = rf(context, voteType, srcID, withVoteCounts)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// SendTriviaVoteData provides a mock function with given fields: context, roomID
func (_m *Store) SendTriviaVoteData(context ctx.CTX, roomID string) {
	_m.Called(context, roomID)
}

// StartVote provides a mock function with given fields: context, voteType, srcID, options
func (_m *Store) StartVote(context ctx.CTX, voteType vote.VoteType, srcID string, options []string) error {
	ret := _m.Called(context, voteType, srcID, options)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, vote.VoteType, string, []string) error); ok {
		r0 = rf(context, voteType, srcID, options)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}
