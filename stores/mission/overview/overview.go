package overview

import (
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models"
	confModel "github.com/17media/api/models/config"
	model "github.com/17media/api/models/mission/overview"
)

// Store is the interface of mission overview store
type Store interface {
	// List will get all mission overview including baggage, dailyquest, and referralProject
	List(context ctx.CTX, userInfo *models.User, region, language string, deviceInfo confModel.DeviceInfo, entranceType model.EntranceType) ([]*model.ResMissionOverview, error)
}
