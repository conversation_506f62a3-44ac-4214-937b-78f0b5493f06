package trivia

import (
	"crypto/sha256"
	"database/sql"
	"encoding/json"
	"flag"
	"fmt"
	"math"
	"strconv"
	"testing"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"
	yaml "gopkg.in/yaml.v2"

	voteModel "github.com/17media/poll/models/vote"

	"github.com/17media/api/base/ctx"
	bdocker "github.com/17media/api/base/docker"
	"github.com/17media/api/base/encoding"
	"github.com/17media/api/base/env"
	"github.com/17media/api/base/log"
	"github.com/17media/api/base/validator"
	"github.com/17media/api/models"
	"github.com/17media/api/models/chat"
	exModel "github.com/17media/api/models/exchange"
	i18nModel "github.com/17media/api/models/i18n"
	"github.com/17media/api/models/keys"
	lmodel "github.com/17media/api/models/leaderboard"
	missionModel "github.com/17media/api/models/mission"
	moneyModel "github.com/17media/api/models/money"
	pgModel "github.com/17media/api/models/program"
	queueModel "github.com/17media/api/models/queue"
	regionModel "github.com/17media/api/models/region"
	model "github.com/17media/api/models/trivia"
	userModel "github.com/17media/api/models/user"
	"github.com/17media/api/service/cache"
	"github.com/17media/api/service/kv"
	"github.com/17media/api/service/localcache/compound"
	"github.com/17media/api/service/localcache/primitive"
	mmessenger "github.com/17media/api/service/messengerv2/mocks"
	"github.com/17media/api/service/queryv2"
	mpublisher "github.com/17media/api/service/queue/mocks"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/service/redis/rediscache"
	regionSrv "github.com/17media/api/service/region"
	mregion "github.com/17media/api/service/region/mocks"
	"github.com/17media/api/service/relation"
	mrelationv2 "github.com/17media/api/service/relationv2/mocks"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/setup/mysql/mysqlgiftwriter"
	mcontract "github.com/17media/api/stores/contract/mocks"
	mexchange "github.com/17media/api/stores/exchange/mocks"
	"github.com/17media/api/stores/leaderboard"
	mleaderboard "github.com/17media/api/stores/leaderboard/mocks"
	mlevel "github.com/17media/api/stores/level/mocks"
	mliveHelper "github.com/17media/api/stores/live/helper/mocks"
	mmLevel "github.com/17media/api/stores/mlevel/mocks"
	mmoney "github.com/17media/api/stores/money/mocks"
	moneyTest "github.com/17media/api/stores/money/testing"
	mofficial "github.com/17media/api/stores/official/mocks"
	"github.com/17media/api/stores/program"
	mprogram "github.com/17media/api/stores/program/mocks"
	triviaConfig "github.com/17media/api/stores/trivia/config"
	muser "github.com/17media/api/stores/user/mocks"
	mvoteAdapter "github.com/17media/api/stores/vote/adapter/mocks"
)

func init() {
	flag.Set("jwt_private_key", privateKey)
	flag.Set("jwt_public_key", publicKey)
}

var (
	mockCTX = ctx.Background()

	mockUserID       = "aaa-bbb-ccc"
	mockUserID2      = "bbb-bbb-bbb"
	mockUserIDOnTV   = "ccc-bbb-aaa"
	mockOpenID       = "aaa"
	mockOpenID2      = "bbb"
	mockHostID       = "QA"
	mockHostID2      = "QA2"
	mockReferralCode = "referralCode"
	mockGameStatus   = model.GameStatus{
		ID:     17,
		Status: model.Status_QUIZSTART,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 1,
		},
	}
	mockTrivia = model.TriviaGame{
		HostID:        mockUserID,
		QuizCountDown: 1,
		Currency:      "NTD",
		Region:        "TW",
		TotalReward:   float32(100),
		Type:          model.TriviaType_TRIVIA,
	}
	mockQuiz1 = model.TriviaQuiz{
		Description: "question 1",
		Options: []string{
			"option 1",
			"option 2",
			"option 3",
		},
		Answer: int32(0),
		Level:  model.QuizLevel_EASY,
		Tag:    "history",
	}
	mockQuiz2 = model.TriviaQuiz{
		Description: "question 2",
		Options: []string{
			"option 1",
			"option 2",
			"option 3",
		},
		Answer: int32(1),
		Level:  model.QuizLevel_SUPERHARD,
		Tag:    "science",
	}
	mockPicture = "picture"
	testConf    = triviaConfig.TriviaConfig{}
	privateKey  = `
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************	`
	publicKey = `
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwNzbxt6CaM/CK6Sk06ou
QIiVcqWIe6cvyuXtI+cSXxDMuLPps1azHKod969/rmt3oeZtP+l14QlQ6XDtmLRn
17eZ2HWPI/WsZUVUJaNb6TPywV2z5AhbER31TW9Fsmd7aCZo8fMbzUGDmsrerdYv
jxy7SVj6C3IhO8KpaF3cSzHXWoZY9wf1zm6lbsxuwg2H+wo0vEIe2gSkS6RvSqD7
3MFdMY4toLqFYgJEOeNYsg1xXfHaya0AkIVzfxqr+jzH6rRPZ0DbvclLztxg74TN
tOSxLadA0ACAPPspEZpHQYOHlaU6ccnFYK/Nq77oh4QqEkMubvGQyat0OmiyKPPJ
KQIDAQAB
-----END PUBLIC KEY-----
	`
)

type triviaSuite struct {
	suite.Suite
	im              *impl
	redis           redis.Service
	db              *sqlx.DB
	query           queryv2.Mongo
	kv              kv.KV
	relation        relation.Service
	store           Store
	local           string
	redisPort       string
	mongoPort       string
	mysqlPort       string
	mockleaderboard *mleaderboard.Leaderboard
	mockRegion      *mregion.Service
	mockUser        *muser.Store
	mockMessenger   *mmessenger.Service
	mockBank        *mmoney.Bank
	mockLiveHelper  *mliveHelper.Helper
	mockExchange    *mexchange.Store
	mockOfficial    *mofficial.Store
	mockContract    *mcontract.Store
	mockLevel       *mlevel.Level
	mockVoteAdapter *mvoteAdapter.Adapter
	mockMLevel      *mmLevel.Store
	mockPublisher   *mpublisher.Publisher
	mockProgram     *mprogram.Store
	mockFuncs       *mockFuncs
	manager         *dimanager.Manager
}

func (s *triviaSuite) prepareData(name, localhost, port string) {
	db, err := sql.Open("mysql", "root:@tcp("+localhost+":"+port+")/")
	if err != nil {
		panic(err)
	}
	defer db.Close()
	_, err = db.Exec("CREATE DATABASE " + name)
	if err != nil {
		panic(err)
	}

	_, err = db.Exec("USE " + name)
	if err != nil {
		panic(err)
	}

	// create bank related dbs
	moneyTest.CreateBankDbSchema(name, localhost, port)

	if _, err = db.Exec(
		`CREATE TABLE CurrencyTable (
			id BIGINT(20) NOT NULL AUTO_INCREMENT,
			currency varchar(32) NOT NULL,
			PRIMARY KEY (id)
		) ENGINE='innodb' DEFAULT CHARSET=utf8mb4;`); err != nil {
		panic(err)
	}

	if _, err = db.Exec(
		`INSERT INTO CurrencyTable (currency) VALUES
		("NANO_USD"), ("NTD"), ("JPY"), ("HKD"), ("USD");`); err != nil {
		panic(err)
	}

	if _, err = db.Exec(
		`CREATE TABLE TriviaGameResult (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			game_id bigint(20) NOT NULL,
			user_id varchar(50) NOT NULL,
			reward bigint(20) NOT NULL,
			currency_id bigint(20) NOT NULL,
			gametime bigint(20) NOT NULL,
			tradeID varchar(27) NOT NULL DEFAULT '',
			dealingID varchar(27) NOT NULL DEFAULT '',
			PRIMARY KEY (id),
			UNIQUE KEY Game_User (game_id, user_id),
			KEY game_id (game_id),
			KEY user_id (user_id),
			KEY currency_id (currency_id),
			KEY gametime (gametime),
			CONSTRAINT TriviaGameResult_ibfk_1 FOREIGN KEY (currency_id) REFERENCES CurrencyTable (id) ON UPDATE CASCADE
		) ENGINE=InnoDB AUTO_INCREMENT=638965 DEFAULT CHARSET=utf8mb4;`); err != nil {
		panic(err)
	}
}

type mockFuncs struct {
	mock.Mock
}

func (m *mockFuncs) currentTime() int64 {
	ret := m.Called()
	return ret.Get(0).(int64)
}

func (m *mockFuncs) translate(language, key string, params ...*i18nModel.Param) (string, error) {
	_ca := []interface{}{language, key}

	for _i := range params {
		_ca = append(_ca, params[_i])
	}
	ret := m.Called(_ca...)

	return ret.String(0), ret.Error(1)
}

func (m *mockFuncs) randInt31n(value int32) int32 {
	ret := m.Called(value)
	return ret.Get(0).(int32)
}

func (s *triviaSuite) SetupSuite() {
	localhost, ports, err := bdocker.RunExtDockers([]string{"redis", "etcd", "mysql8", "mongo"})
	s.Require().NoError(err)
	s.local = localhost
	s.redisPort = ports[0]
	s.mysqlPort = ports[2]
	s.mongoPort = ports[3]

	event = func(context ctx.CTX, logName string, data map[string]interface{}, opts ...log.OptionFunc) {
		return
	}

	flag.Set("pubsub_emulator_host", "hi")

	s.manager = dimanager.DefaultManager
	s.manager.ProvideString("redis_cache_uri", localhost+":"+ports[0])
	s.manager.ProvideString("redis_cache_user_uri", localhost+":"+ports[0])
	s.manager.ProvideString("redis_persist_uri", localhost+":"+ports[0])
	s.manager.ProvideString("etcd_hosts", "http://"+localhost+":"+ports[1])
	s.manager.ProvideString("rds_gift_uri", "root:@tcp("+localhost+":"+ports[2]+")/media17gift?charset=utf8mb4")
	s.manager.ProvideString("rds_gift_reader_uri", "root:@tcp("+localhost+":"+ports[2]+")/media17gift?charset=utf8mb4")
	s.manager.ProvideString("rds_high_priority_uri", "root:@tcp("+localhost+":"+ports[2]+")/media17gift?charset=utf8mb4")
	s.manager.ProvideString("mongo_uri", localhost+":"+ports[3])
	s.manager.ProvideString("mongo_db", "17media")
	rediscache.ConnectRedisCluster(s.manager)
}

func (s *triviaSuite) TearDownSuite() {
	s.NoError(bdocker.RemExtDockers())
}

func TestTriviaSuite(t *testing.T) {
	// Skip trivia unit tests for not used
	t.Skip()
	suite.Run(t, new(triviaSuite))
}

func (s *triviaSuite) SetupTest() {
	s.manager.ClearMock()
	s.mockLiveHelper = mliveHelper.RegisterMock(s.manager)
	s.mockOfficial = mofficial.RegisterMock(s.manager)
	s.mockleaderboard = mleaderboard.RegisterMock(s.manager)
	s.mockVoteAdapter = mvoteAdapter.RegisterMock(s.manager)
	s.mockProgram = mprogram.RegisterMock(s.manager)
	s.mockContract = mcontract.RegisterMock(s.manager)
	s.mockLevel = mlevel.RegisterMock(s.manager)
	s.mockUser = muser.RegisterMock(s.manager)
	s.mockExchange = mexchange.RegisterMock(s.manager)
	s.mockRegion = mregion.RegisterMock(s.manager)
	s.mockMessenger = mmessenger.RegisterMock(s.manager)
	s.mockBank = mmoney.RegisterBankMock(s.manager)
	mrelationv2.RegisterMock(s.manager)
	s.manager.Compile()

	s.prepareData("media17gift", s.local, s.mysqlPort)

	s.mockPublisher = &mpublisher.Publisher{}
	s.redis = rediscache.GetRedisCache(s.manager)
	s.kv = kv.GetKv(s.manager)
	s.query = queryv2.GetQueryV2(s.manager)
	s.relation = relation.GetRelation(s.manager)
	s.db = sqlx.NewDb(mysqlgiftwriter.GetMySQLGiftWriter(s.manager), "mysql")
	s.im = GetTrivia(s.manager).(*impl)
	s.im.missionPublisher = s.mockPublisher
	s.im.preparationPublisher = s.mockPublisher
	s.im.answerPublisher = s.mockPublisher
	s.im.leaderboardPublisher = s.mockPublisher
	s.im.saveAndPayPublisher = s.mockPublisher
	s.im.useMedalPublisher = s.mockPublisher
	s.im.prepareUseMedalPublisher = s.mockPublisher
	s.im.correctLeaderboardPublisher = s.mockPublisher
	s.im.drawResultPublisher = s.mockPublisher

	s.mockFuncs = &mockFuncs{}
	currentTime = s.mockFuncs.currentTime
	translate = s.mockFuncs.translate
	randInt31n = s.mockFuncs.randInt31n

	isTriviaHost = func(userID string, triviaType model.TriviaType) (bool, string) {
		return true, "TW"
	}

	mockCTX = ctx.WithValues(mockCTX, map[string]interface{}{
		"deviceType": "IOS",
		"version":    "2.06",
	})

	quizCountDown = 10

	testConf = triviaConfig.TriviaConfig{
		HostID:     mockUserID,
		HostIDOnTV: mockUserID2,
		HostInfos: map[string]triviaConfig.HostInfo{
			mockUserID:  triviaConfig.HostInfo{TriviaType: model.TriviaType_TRIVIA},
			mockUserID2: triviaConfig.HostInfo{TriviaType: model.TriviaType_TRIVIA_ON_TV},
		},
	}
	getCfg = func(region string) triviaConfig.TriviaConfig {
		return testConf
	}
}

func (s *triviaSuite) TearDownTest() {
	s.mockFuncs.AssertExpectations(s.T())
	s.mockUser.AssertExpectations(s.T())
	s.mockleaderboard.AssertExpectations(s.T())
	s.mockPublisher.AssertExpectations(s.T())
	s.mockLiveHelper.AssertExpectations(s.T())
	s.mockOfficial.AssertExpectations(s.T())
	s.mockVoteAdapter.AssertExpectations(s.T())
	s.mockContract.AssertExpectations(s.T())
	s.mockLevel.AssertExpectations(s.T())
	s.mockUser.AssertExpectations(s.T())
	s.mockExchange.AssertExpectations(s.T())
	s.mockRegion.AssertExpectations(s.T())

	primitive.GetPrimitive(s.manager).ClearLocal(mockCTX)
	compound.GetLocalPersistent(s.manager).ClearLocal(mockCTX)
	err := bdocker.ClearRedis(s.redisPort)
	s.NoError(err)
	err = bdocker.ClearMongo(s.mongoPort)
	s.NoError(err)
	err = bdocker.ClearMySQL(s.mysqlPort)
	s.NoError(err)
	cache.ClearPfx()
}

func (s *triviaSuite) TestInsertTriviaSuccess() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)

	game := &model.TriviaGame{
		ID:            0,
		StartTime:     int64(1234567),
		TotalReward:   200.00,
		Currency:      "NTD",
		Region:        "TW",
		Type:          model.TriviaType_TRIVIA,
		TriviaQuizIDs: []int64{1},
	}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	tr := &model.TriviaGame{}
	s.NoError(s.kv.Get(mockCTX, keys.TabTriviaGames, int64(1), tr))
	s.Equal(float32(200.00), tr.TotalReward)
	s.Equal("NTD", tr.Currency)
	s.Equal("TW", tr.Region)
	s.Equal(model.TriviaType_TRIVIA, tr.Type)
	s.Equal("aaa-bbb-ccc", tr.HostID)
}

func (s *triviaSuite) TestInsertTriviaOnTVSuccess() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID2).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)

	game := &model.TriviaGame{
		ID:            0,
		StartTime:     int64(1234567),
		TotalReward:   200.00,
		Currency:      "NTD",
		Region:        "TW",
		Type:          model.TriviaType_TRIVIA_ON_TV,
		TriviaQuizIDs: []int64{1},
		SubQuizzes: []*model.TriviaSubQuiz{
			&model.TriviaSubQuiz{
				MainQuizID: int64(1),
				SubQuizIDs: []int64{2, 3},
			},
		},
	}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	tr := &model.TriviaGame{}
	s.NoError(s.kv.Get(mockCTX, keys.TabTriviaGames, int64(1), tr))
	s.Equal(float32(200.00), tr.TotalReward)
	s.Equal("NTD", tr.Currency)
	s.Equal("TW", tr.Region)
	s.Equal(model.TriviaType_TRIVIA_ON_TV, tr.Type)
	s.Equal("bbb-bbb-bbb", tr.HostID)

	relations := []model.TriviaRelation{}
	_, err = s.relation.GetList(mockCTX, keys.TabTriviaRelations, relation.To, strconv.FormatInt(int64(1), 10), getRelationNum, 0, &relations)
	s.NoError(err)
	s.Len(relations, 3)
	s.Equal("3", relations[0].QuizID)
	s.Equal("1", relations[0].GameID)
	s.Equal(model.QuizType_SUB, relations[0].Type)
	s.Equal("2", relations[1].QuizID)
	s.Equal("1", relations[1].GameID)
	s.Equal(model.QuizType_SUB, relations[1].Type)
	s.Equal("1", relations[2].QuizID)
	s.Equal("1", relations[2].GameID)
	s.Equal(int32(0), relations[2].Order)
	s.Equal(model.QuizType_MAIN, relations[2].Type)
}

func (s *triviaSuite) TestInsertTriviaWithUnsupprtedCurrency() {
	game := &model.TriviaGame{ID: 0, Currency: "NTD", Region: "JP", Type: model.TriviaType_TRIVIA, TriviaQuizIDs: []int64{1}}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrCurrencyNotSupported.Error())
}

func (s *triviaSuite) TestInsertTriviaWithNoPermission() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(nil, program.ErrNoHostedProgram)
	game := &model.TriviaGame{ID: 0, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA, TriviaQuizIDs: []int64{1}}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrNoOpenPermission.Error())
}

func (s *triviaSuite) TestInsertTriviaWithUnsupportedLeaderboard() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "HK").Return(false)
	game := &model.TriviaGame{ID: 0, Currency: "HKD", Region: "HK", Type: model.TriviaType_TRIVIA, TriviaQuizIDs: []int64{1}}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrRegionNotSupported.Error())
}

func (s *triviaSuite) TestInsertTriviaWithEmptyQuizzes() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	game := &model.TriviaGame{ID: 0, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrNoQuizzes.Error())
}

func (s *triviaSuite) TestInsertTriviaWithDuplicateQuizzes() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	game := &model.TriviaGame{ID: 0, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA, TriviaQuizIDs: []int64{1, 1}}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrInvalidQuizIDs.Error())
}

func (s *triviaSuite) TestInsertTriviaWithInvalidSubQuiz() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	game := &model.TriviaGame{ID: 0, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA, TriviaQuizIDs: []int64{1}, SubQuizzes: []*model.TriviaSubQuiz{
		&model.TriviaSubQuiz{
			MainQuizID: int64(2),
			SubQuizIDs: []int64{3, 4},
		},
	}}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrInvalidQuizIDs.Error())
}

func (s *triviaSuite) TestInsertTriviaWithMainQuizAndSubQuizAreSame() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	game := &model.TriviaGame{ID: 0, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA, TriviaQuizIDs: []int64{1, 2}, SubQuizzes: []*model.TriviaSubQuiz{
		&model.TriviaSubQuiz{
			MainQuizID: int64(2),
			SubQuizIDs: []int64{1, 4},
		},
	}}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrInvalidQuizIDs.Error())
}

func (s *triviaSuite) TestUpdateTriviaSuccess() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	s.NoError(s.kv.Set(mockCTX, keys.TabTriviaGames, int64(1), &model.TriviaGame{ID: int64(1)}))
	game := &model.TriviaGame{
		ID:            1,
		Currency:      "NTD",
		Region:        "TW",
		HostID:        mockUserID,
		Type:          model.TriviaType_TRIVIA,
		TotalReward:   float32(200.00),
		TriviaQuizIDs: []int64{1},
	}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)
}

func (s *triviaSuite) TestUpdateTriviaWhenGameWasStarted() {
	s.NoError(s.im.saveTriviaStatus(mockCTX, &model.GameStatus{
		ID:     int64(1),
		Status: model.Status_QUIZSTART,
	}))
	game := &model.TriviaGame{ID: 1, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA, TriviaQuizIDs: []int64{1}}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrGameWasStarted.Error())
}

func (s *triviaSuite) TestUpdateTriviaWithNoOpenPermission() {
	s.NoError(s.kv.Set(mockCTX, keys.TabTriviaGames, int64(1), &model.TriviaGame{ID: int64(1), HostID: mockUserID}))

	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID2).Return(nil, program.ErrNoHostedProgram)
	// try to update hostID
	game := &model.TriviaGame{ID: 1, Currency: "NTD", Region: "TW", HostID: mockUserID2}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrNoOpenPermission.Error())

	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(nil, program.ErrNoHostedProgram)
	// try to update type
	game = &model.TriviaGame{ID: 1, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrNoOpenPermission.Error())
}

func (s *triviaSuite) TestUpdateTriviaWithCurrencyNotSupported() {
	s.NoError(s.kv.Set(mockCTX, keys.TabTriviaGames, int64(1), &model.TriviaGame{ID: int64(1), HostID: mockUserID}))
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)

	// try to update currency
	game := &model.TriviaGame{ID: 1, Currency: "NTD"}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrCurrencyNotSupported.Error())

	// try to update region
	game = &model.TriviaGame{ID: 1, Region: "TW"}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrCurrencyNotSupported.Error())
}

func (s *triviaSuite) TestUpdateTriviaWithInvalidQuizIDs() {
	s.NoError(s.kv.Set(mockCTX, keys.TabTriviaGames, int64(1), &model.TriviaGame{ID: int64(1), HostID: mockUserID}))
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)

	// try to update currency
	game := &model.TriviaGame{ID: 1, Currency: "NTD", Region: "TW", TriviaQuizIDs: []int64{1, 1}}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrInvalidQuizIDs.Error())
}

func (s *triviaSuite) prepareDefaultGame(hostID string, tt model.TriviaType, NumOfQuiz, NumOfGame int) []*model.TriviaGame {
	s.mockProgram.On("GetProgramIndex", mockCTX, hostID).Return(&pgModel.Program{
		CreatorID:  mockUserID,
		RegionMode: regionModel.RegionMode_ALL,
	}, nil)
	quizIDs := []int64{}
	for i := 0; i < NumOfQuiz; i++ {
		res, err := s.im.UpsertQuiz(mockCTX, &model.TriviaQuiz{
			Description: fmt.Sprintf("Quiz %d", i+1),
			Level:       model.QuizLevel_EASY,
			Tag:         "Science",
			Answer:      int32(0),
			Options:     []string{"option 0", "option 1", "option 2"},
		})
		s.NoError(err)
		quizIDs = append(quizIDs, res.ID)
	}
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	games := []*model.TriviaGame{}
	for i := 0; i < NumOfGame; i++ {
		res, err := s.im.UpsertTrivia(mockCTX, &model.TriviaGame{
			Currency:      "NTD",
			HostID:        hostID,
			QuizCountDown: int32(10),
			StartTime:     int64(i*1000 + 1000),
			TotalReward:   float32(2700),
			TriviaQuizIDs: quizIDs,
			Region:        "TW",
			Type:          tt,
		})
		s.NoError(err)
		games = append(games, res)
	}
	return games
}

func (s *triviaSuite) setQuizReady(gameID int64, quizNo int32, ready bool) error {
	if ready {
		return s.redis.SetNXLegacy(mockCTX, getQuizKey(gameID, quizNo), []byte("ready"), time.Second*5)
	}

	if _, err := s.redis.Del(mockCTX, getQuizKey(gameID, quizNo)); err != nil {
		return err
	}
	return nil
}

func (s *triviaSuite) TestGetTriviaRoleWithDefaultMode() {
	gameID := int64(1)
	userID := "QA"
	// Test: Game hasn't started
	s.mockUser.On("GetTriviaPersonInfo", mockCTX, userID).Return(&userModel.TriviaInfo{}, nil)
	_, err := s.im.GetTriviaPlayerInfo(mockCTX, gameID, userID)
	s.Error(err)

	// Test: prepare
	game := &model.GameStatus{
		ID:          gameID,
		TotalReward: float32(100),
		TotalQuiz:   2,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 0,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
		Status:           model.Status_PREPARE,
		Type:             model.TriviaType_TRIVIA,
		IsMedalSupported: true,
	}

	err = s.im.saveTriviaStatus(mockCTX, game)
	s.NoError(err)

	s.mockUser.On("GetTriviaPersonInfo", mockCTX, userID).Return(&userModel.TriviaInfo{DeathExemptionMedal: 0}, nil)
	info, err := s.im.GetTriviaPlayerInfo(mockCTX, gameID, userID)
	s.NoError(err)
	s.Equal(model.TriviaRole_PLAYER, info.Role)

	// Test: is survivor
	game.Status = model.Status_QUIZSTART
	err = s.im.saveTriviaStatus(mockCTX, game)
	s.NoError(err)

	info, err = s.im.GetTriviaPlayerInfo(mockCTX, gameID, userID)
	s.NoError(err)
	s.Equal(model.TriviaRole_PLAYER, info.Role)

	// Test: not survivor
	game.Status = model.Status_QUIZRESULT
	err = s.im.saveTriviaStatus(mockCTX, game)
	s.NoError(err)

	info, err = s.im.GetTriviaPlayerInfo(mockCTX, gameID, userID)
	s.NoError(err)
	s.Equal(model.TriviaRole_VIEWER, info.Role)

	// Test: not survivor
	game.Status = model.Status_QUIZEND
	err = s.im.saveTriviaStatus(mockCTX, game)
	s.NoError(err)

	info, err = s.im.GetTriviaPlayerInfo(mockCTX, gameID, userID)
	s.NoError(err)
	s.Equal(model.TriviaRole_VIEWER, info.Role)

	// Test: not survivor
	game.CurrentQuiz.QuizNo = 1
	game.Status = model.Status_QUIZSTART
	err = s.im.saveTriviaStatus(mockCTX, game)
	s.NoError(err)
	info, err = s.im.GetTriviaPlayerInfo(mockCTX, gameID, userID)
	s.NoError(err)
	s.Equal(model.TriviaRole_VIEWER, info.Role)

	// not suervivor
	game.Status = model.Status_GAMERESULT
	err = s.im.saveTriviaStatus(mockCTX, game)
	s.NoError(err)
	info, err = s.im.GetTriviaPlayerInfo(mockCTX, gameID, userID)
	s.NoError(err)
	s.Equal(model.TriviaRole_VIEWER, info.Role)

	// is survivor
	s.NoError(s.redis.Set(mockCTX, getQuizSurvivorKey(int64(1), int32(1)), []byte("trivia:gameID:1:quizNo:1:quizOpt:0"), time.Second))
	s.NoError(s.redis.SAdd(mockCTX, "trivia:gameID:1:quizNo:1:quizOpt:0", userID))
	info, err = s.im.GetTriviaPlayerInfo(mockCTX, gameID, userID)
	s.NoError(err)
	s.Equal(model.TriviaRole_PLAYER, info.Role)

	game.Status = model.Status_GAMEEND
	err = s.im.saveTriviaStatus(mockCTX, game)
	s.NoError(err)
	info, err = s.im.GetTriviaPlayerInfo(mockCTX, gameID, userID)
	s.NoError(err)
	s.Equal(model.TriviaRole_PLAYER, info.Role)
}

func (s *triviaSuite) TestGetTriviaRoleWithCorrectMostMode() {
	gameID := int64(1)
	userID := "QA"
	game := &model.GameStatus{
		ID:          gameID,
		TotalReward: float32(100),
		TotalQuiz:   2,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 2,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
		Status:   model.Status_QUIZRESULT,
		Type:     model.TriviaType_TRIVIA_ON_TV,
		PlayMode: model.PlayMode_CorrectMost,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, game))

	triviaInfo := &userModel.TriviaInfo{
		DeathExemptionMedal: int32(3),
	}
	s.mockUser.On("GetTriviaPersonInfo", mock.AnythingOfType("ctx.CTX"), userID).Return(triviaInfo, nil).Once()
	// user is not the survivor, but should be still a player
	info, err := s.im.GetTriviaPlayerInfo(mockCTX, gameID, userID)
	s.NoError(err)
	s.Equal(model.TriviaRole_PLAYER, info.Role)
}

func (s *triviaSuite) TestAnswer() {
	s.mockFuncs.On("currentTime").Return(int64(1000))
	mockCTX = ctx.WithValues(mockCTX, map[string]interface{}{
		"deviceType": "WEB",
	})
	gameID := int64(1)
	quizNo := int32(0)
	// Test: Game hasn't started
	err := s.im.Answer(mockCTX, "QA", int64(1), int32(1), 0, "")
	s.Error(err)

	game := &model.GameStatus{
		ID:       gameID,
		PlayMode: model.PlayMode_Default,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, game))

	// Test: Answer succeeds
	err = s.redis.SetNXLegacy(mockCTX, getQuizKey(gameID, quizNo), []byte("ready"), time.Second*5)
	s.True(err == nil || err == redis.ErrNotFound)

	// with wrong deviceType, should fail
	ans := model.TriviaAnswerLog{
		UserID:    "QA",
		GameID:    gameID,
		QuizNo:    quizNo,
		Option:    0,
		Timestamp: int64(1000),
	}
	s.mockPublisher.On(
		"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
	).Return(nil).Once()
	err = s.im.Answer(mockCTX, "QA", gameID, quizNo, 0, "")
	s.NoError(err)

	mockCTX = ctx.WithValues(mockCTX, map[string]interface{}{
		"deviceType": "IOS",
		"version":    "2.07",
	})

	// SetTimer and answer again
	err = s.im.Answer(mockCTX, "QA", gameID, quizNo, 0, "")
	s.NoError(err)

	// Test: Answer same answer twice still succeeds
	err = s.im.Answer(mockCTX, "QA", gameID, quizNo, 0, "")
	s.NoError(err)

	// Test: Already answer another option will cause a failure
	err = s.im.Answer(mockCTX, "QA", gameID, quizNo, 1, "")
	s.Error(err)

	// Check the number of people who choose the option is correct
	cnt, err := s.redis.SCard(mockCTX, getQuizOptKey(gameID, quizNo, 0))
	s.NoError(err)
	users, err := s.im.OptionUserList(mockCTX, gameID, quizNo, 0)
	s.NoError(err)
	s.Equal(1, cnt)
	s.Equal(users, []string{"QA"})
	cnt, err = s.redis.SCard(mockCTX, getQuizOptKey(gameID, quizNo, 1))
	s.NoError(err)
	s.Equal(0, cnt)

	// Test: Answer failed if the user is not survivor
	quizNo = 1
	err = s.redis.SetNXLegacy(mockCTX, getQuizKey(gameID, quizNo), []byte("ready"), time.Second*5)
	s.True(err == nil || err == redis.ErrNotFound)
	err = s.im.Answer(mockCTX, "QA", gameID, quizNo, 0, "")
	s.Equal(ErrNotSurvivor, err)

	// Test: Answer succeeds if the user survived
	key := getQuizAnsKey(gameID, quizNo-1)
	err = s.redis.Set(mockCTX, key, []byte(getQuizOptKey(gameID, 0, 0)), time.Second*15)
	s.NoError(err)
	err = s.redis.SAdd(mockCTX, getQuizOptKey(gameID, 0, 0), "QA")
	s.NoError(err)
	err = s.im.setQuizSurvivor(mockCTX, gameID, 0, getQuizOptKey(gameID, 0, 0))
	s.NoError(err)

	// SetTimer and answer again
	ans = model.TriviaAnswerLog{
		UserID:    "QA",
		GameID:    gameID,
		QuizNo:    quizNo,
		Option:    0,
		Timestamp: int64(1000),
	}
	s.mockPublisher.On(
		"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
	).Return(nil).Once()
	err = s.im.Answer(mockCTX, "QA", gameID, quizNo, 0, "")
	s.NoError(err)
}

func (s *triviaSuite) TestAnswerWithSign() {
	testConf.CheckAnswerSign = true
	answerKeys := []string{"wZ3D", "u14=", "ISSG", "35xV", "d1g=", "or/V", "zsAg", "zzw=", "sc4+", "/q+B", "12U=", "1Ze0"}
	nonces := []string{"rpHUFMro", "PV6rqrE6", "T2iDMJaW", "LdVOFVnU", "hm8PiQXk", "xb+7GKst", "EuanKCdf", "k7MXy7L0", "3BYldJN6", "viSOVsJ8", "p0HQ7ifj", "I65ul/wW"}
	for i := 0; i < 12; i++ {
		h := sha256.New()
		h.Write([]byte(answerKeys[i] + ":" + nonces[i] + ":123:" + strconv.Itoa(i) + ":2"))
		sign := fmt.Sprintf("%x", h.Sum(nil))
		s.True(s.im.checkSign(mockCTX, mockUserID, 123, int32(i), 2, sign))
		s.False(s.im.checkSign(mockCTX, mockUserID, 123, int32(i), 1, sign))
	}

	testConf.CheckAnswerSign = false
}

func (s *triviaSuite) TestAnswerWithCorrectMostPlayMode() {
	gameID := int64(1)
	quizNo := int32(1)

	game := &model.GameStatus{
		ID:       int64(1),
		PlayMode: model.PlayMode_CorrectMost,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, game))

	s.NoError(s.redis.Set(mockCTX, getQuizKey(gameID, quizNo), []byte("ready"), time.Second*5))

	s.mockFuncs.On("currentTime").Return(int64(1000)).Once()
	ans := model.TriviaAnswerLog{
		UserID:    "QA",
		GameID:    gameID,
		QuizNo:    quizNo,
		Option:    0,
		Timestamp: int64(1000),
	}
	s.mockPublisher.On(
		"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
	).Return(nil).Once()

	// user isn't the survivor in quizNo 0, but still can answer
	s.NoError(s.im.Answer(mockCTX, "QA", gameID, quizNo, 0, ""))
}

func (s *triviaSuite) TestLogAnswerFromTaskQueueWithInsert() {
	status := &model.GameStatus{
		ID:           123456,
		HostID:       "host1",
		RoomID:       "room1",
		LiveStreamID: "12345678",
	}
	s.im.saveTriviaStatus(mockCTX, status)

	ans := &model.TriviaAnswerLog{
		UserID: "test-userID",
		GameID: 123456,
		QuizNo: 789,
		Option: 3,
	}
	data, err := json.Marshal(ans)
	s.Require().NoError(err)

	s.NoError(s.im.LogAnswerFromTaskQueue(mockCTX, data, queueModel.CallbackOption{PublishTime: time.Now()}))

	insertedAns := &model.TriviaAnswerLog{}
	err = s.query.FindOne(
		mockCTX, keys.TabTriviaAnswers,
		bson.M{
			"userID": ans.UserID,
			"gameID": ans.GameID,
			"quizNo": ans.QuizNo,
		},
		insertedAns,
	)
	s.NoError(err)
	s.Equal(&model.TriviaAnswerLog{
		UserID:       "test-userID",
		GameID:       123456,
		QuizNo:       789,
		Option:       3,
		HostID:       "host1",
		RoomID:       "room1",
		LiveStreamID: "12345678",
	}, insertedAns)
}

func (s *triviaSuite) TestLogAnswerFromTaskQueueWithInsertAndPatch() {
	status := &model.GameStatus{
		ID:           123456,
		HostID:       "host1",
		RoomID:       "room1",
		LiveStreamID: "12345678",
	}
	s.im.saveTriviaStatus(mockCTX, status)

	ans := &model.TriviaAnswerLog{
		UserID:       "test-userID",
		GameID:       123456,
		QuizNo:       789,
		Option:       3,
		HostID:       "host1",
		RoomID:       "room1",
		LiveStreamID: "12345678",
	}
	data, err := json.Marshal(ans)
	s.Require().NoError(err)

	s.NoError(s.im.LogAnswerFromTaskQueue(mockCTX, data, queueModel.CallbackOption{PublishTime: time.Now()}))

	insertedAns := &model.TriviaAnswerLog{}
	err = s.query.FindOne(
		mockCTX, keys.TabTriviaAnswers,
		bson.M{
			"userID": ans.UserID,
			"gameID": ans.GameID,
			"quizNo": ans.QuizNo,
		},
		insertedAns,
	)
	s.NoError(err)
	s.Equal(ans, insertedAns)

	ansWithMedal := &model.TriviaAnswerLog{
		UserID:    "test-userID",
		GameID:    123456,
		QuizNo:    789,
		MedalUsed: 1,
	}
	data, err = json.Marshal(ansWithMedal)
	s.Require().NoError(err)

	expectedAns := &model.TriviaAnswerLog{
		UserID:       "test-userID",
		GameID:       123456,
		QuizNo:       789,
		Option:       3,
		MedalUsed:    1,
		HostID:       "host1",
		RoomID:       "room1",
		LiveStreamID: "12345678",
	}

	s.NoError(s.im.LogAnswerFromTaskQueue(mockCTX, data, queueModel.CallbackOption{PublishTime: time.Now()}))
	patchedAns := &model.TriviaAnswerLog{}
	err = s.query.FindOne(
		mockCTX, keys.TabTriviaAnswers,
		bson.M{
			"userID": ans.UserID,
			"gameID": ans.GameID,
			"quizNo": ans.QuizNo,
		},
		patchedAns,
	)
	s.NoError(err)
	s.Equal(expectedAns, patchedAns)
}

func (s *triviaSuite) TestQuizResultNotStarted() {
	gameID := int64(1)
	// Test: Game hasn't started
	_, err := s.im.QuizResult(mockCTX, gameID)
	s.Error(err)
}

func (s *triviaSuite) TestQuizResultStillCanBeAnswered() {
	gameID := int64(1)
	game := &model.GameStatus{
		ID:          gameID,
		TotalReward: float32(100),
		TotalQuiz:   2,
		Status:      model.Status_QUIZSTART,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 0,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, game))

	// Test: quiz hasn't ended
	s.setQuizReady(gameID, 0, true)
	_, err := s.im.QuizResult(mockCTX, gameID)
	s.Error(err)
}

func (s *triviaSuite) TestQuizResultPlayModeDefault() {
	gameID := int64(1)
	game := &model.GameStatus{
		ID:          gameID,
		TotalReward: float32(100),
		TotalQuiz:   2,
		Status:      model.Status_QUIZSTART,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 0,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, game))

	s.NoError(s.redis.Set(mockCTX, getQuizAnsKey(gameID, int32(0)), []byte("0"), time.Second))
	s.NoError(s.redis.SAdd(mockCTX, getQuizOptKey(gameID, 0, 0), mockUserID))
	newStatus, err := s.im.QuizResult(mockCTX, gameID)
	s.NoError(err)
	s.True(newStatus.Result == nil)
	s.Equal(int32(1), newStatus.CurrentQuiz.Options[0].Count)
	s.Equal(int32(0), newStatus.CurrentQuiz.Options[1].Count)
	s.Equal(int32(0), newStatus.CurrentQuiz.Options[2].Count)
}

func (s *triviaSuite) TestQuizResultPlayModeCorrectMost() {
	gameID := int64(1)
	game := &model.GameStatus{
		ID:          gameID,
		TotalReward: float32(100),
		TotalQuiz:   2,
		Status:      model.Status_QUIZSTART,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 0,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
		Region:   "TW",
		PlayMode: model.PlayMode_CorrectMost,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, game))

	s.NoError(s.redis.Set(mockCTX, getQuizAnsKey(gameID, int32(0)), []byte("0"), time.Second))
	params := model.TriviaCorrectLeaderboardParams{
		GameID: gameID,
		QuizNo: int32(0),
		Region: "TW",
	}
	s.mockPublisher.On("Publish", mockCTX, params, mock.AnythingOfType("queue.OptionFunc"), mock.AnythingOfType("queue.OptionFunc")).Return(nil).Once()
	_, err := s.im.QuizResult(mockCTX, gameID)
	s.NoError(err)
}

func (s *triviaSuite) TestQuizResultInvalidAction() {
	tr := s.setupTrivia(&model.GameStatus{
		TotalReward: float32(100),
		TotalQuiz:   2,
		Status:      model.Status_QUIZEND,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 1,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
		Region: "TW",
	})

	_, err := s.im.QuizResult(mockCTX, tr.ID)
	s.Equal(ErrInvalidAction, err)
}

func (s *triviaSuite) TestQuizResultNoWinner() {
	tr := s.setupTrivia(&model.GameStatus{
		TotalReward: float32(100),
		TotalQuiz:   2,
		Status:      model.Status_QUIZSTART,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 1,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
	})

	s.setQuizReady(tr.ID, 1, false)
	err := s.im.setQuizAns(mockCTX, tr.ID, 1, "0")
	s.NoError(err)
	newStatus, err := s.im.QuizResult(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_QUIZRESULT, newStatus.Status)

	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), tr.ID, mock.Anything, mock.Anything).Return(nil).Once()
	s.mockFuncs.On("currentTime").Return(int64(1000))

	newStatus, err = s.im.GameResult(mockCTX, tr.ID)
	s.True(newStatus.Result != nil)
	s.Equal(0, len(newStatus.Result.Winners))
	s.Equal(int32(0), newStatus.Result.WinnersCount)
}

func (s *triviaSuite) TestQuizResultEarlyEnd() {
	quizNo := int32(0)
	tr := s.setupTrivia(&model.GameStatus{
		TotalReward: float32(100),
		TotalQuiz:   2,
		Status:      model.Status_QUIZSTART,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: quizNo,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
	})

	s.setQuizReady(tr.ID, quizNo, false)
	err := s.im.setQuizAns(mockCTX, tr.ID, quizNo, "0")
	s.NoError(err)
	newStatus, err := s.im.QuizResult(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_QUIZRESULT, newStatus.Status)

	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), tr.ID, mock.Anything, mock.Anything).Return(nil).Once()
	s.mockFuncs.On("currentTime").Return(int64(1000))

	newStatus, err = s.im.GameResult(mockCTX, tr.ID)
	s.NoError(err)
	s.True(newStatus.Result != nil)
	s.Equal(0, len(newStatus.Result.Winners))
}

func (s *triviaSuite) TestGameResultOneWinner() {
	tr := s.setupTrivia(&model.GameStatus{
		TotalReward: float32(100),
		TotalQuiz:   2,
		Status:      model.Status_QUIZSTART,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 1,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
	})

	s.mockUser.On("GetDecoratedUsers", mockCTX, mockUserID).Return([]models.User{
		models.User{
			UserID:      mockUserID,
			DisplayName: mockOpenID,
		},
	}, nil).Once()
	s.setQuizReady(tr.ID, 1, false)
	err := s.im.setQuizAns(mockCTX, tr.ID, 1, "0")
	s.NoError(err)
	err = s.redis.SAdd(mockCTX, getQuizOptKey(tr.ID, 1, 0), mockUserID)
	s.NoError(err)

	newStatus, err := s.im.QuizResult(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_QUIZRESULT, newStatus.Status)

	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), tr.ID, mock.Anything, mock.Anything).Return(nil).Once()
	s.mockFuncs.On("currentTime").Return(int64(1000))

	newStatus, err = s.im.GameResult(mockCTX, tr.ID)
	s.Equal(model.Status_GAMERESULT, newStatus.Status)
	s.True(newStatus.Result != nil)
	s.Equal(1, len(newStatus.Result.Winners))
	s.Equal(int32(1), newStatus.Result.WinnersCount)
	s.Equal(float32(100), newStatus.Result.Reward)
}

func (s *triviaSuite) TestGameResultTwoWinner() {
	tr := s.setupTrivia(&model.GameStatus{
		TotalReward: float32(99),
		TotalQuiz:   2,
		Status:      model.Status_QUIZSTART,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 1,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
		Currency: "NTD",
	})

	s.mockUser.On("GetDecoratedUsers", mockCTX, mock.Anything, mock.Anything).Return([]models.User{
		models.User{
			UserID:      mockUserID,
			DisplayName: mockOpenID,
		},
		models.User{
			UserID:      mockUserID2,
			DisplayName: mockOpenID2,
		},
	}, nil).Once()
	s.setQuizReady(tr.ID, 1, false)
	err := s.im.setQuizAns(mockCTX, tr.ID, 1, "0")
	s.NoError(err)
	err = s.redis.SAdd(mockCTX, getQuizOptKey(tr.ID, 1, 0), mockUserID)
	s.NoError(err)

	err = s.redis.SAdd(mockCTX, getQuizOptKey(tr.ID, 1, 0), mockUserID2)
	s.NoError(err)

	newStatus, err := s.im.QuizResult(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_QUIZRESULT, newStatus.Status)

	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), tr.ID, mock.Anything, mock.Anything).Return(nil).Once()
	s.mockFuncs.On("currentTime").Return(int64(1000))

	newStatus, err = s.im.GameResult(mockCTX, tr.ID)
	s.Equal(model.Status_GAMERESULT, newStatus.Status)
	s.True(newStatus.Result != nil)
	s.Equal(2, len(newStatus.Result.Winners))
	s.Equal(float32(50), newStatus.Result.Reward)
}

func (s *triviaSuite) TestGameResultCorrectMostModeInvalid() {
	gameID := int64(1)
	s.setupTrivia(&model.GameStatus{
		ID:     gameID,
		Status: model.Status_QUIZEND,
		Region: "TW",
		Type:   model.TriviaType_TRIVIA_ON_TV,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: int32(1),
		},
		PlayMode: model.PlayMode_CorrectMost,
	})
	_, err := s.im.GameResult(mockCTX, gameID)
	s.EqualError(err, ErrCorrecLeaderboardNotFinished.Error())
}

func (s *triviaSuite) TestGameResultCorrectMostMode() {
	gameID := int64(1)
	s.setupTrivia(&model.GameStatus{
		ID:     gameID,
		Status: model.Status_QUIZEND,
		Region: "TW",
		Type:   model.TriviaType_TRIVIA_ON_TV,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: int32(1),
		},
		PlayMode: model.PlayMode_CorrectMost,
	})

	s.NoError(s.redis.Set(mockCTX, getCorrectLeaderboardKey(gameID, int32(1)), []byte("ready"), time.Minute))

	boardName := getCorrectLeaderboardName(gameID)
	s.mockleaderboard.On("GetByRank", mockCTX,
		lmodel.Type_TRIVIA_TV_CORRECT, lmodel.Period_DAY,
		mock.AnythingOfType("time.Time"), boardName, regionSrv.GLOBAL, 0, 0).Return([]*lmodel.IDScoreRank{
		&lmodel.IDScoreRank{
			ID:    mockUserID,
			Score: int64(13),
			Rank:  int32(0),
		},
	}, nil).Once()
	s.mockleaderboard.On("GetByScore", mockCTX,
		lmodel.Type_TRIVIA_TV_CORRECT, lmodel.Period_DAY,
		mock.AnythingOfType("time.Time"), boardName, regionSrv.GLOBAL, "13", "13").Return([]*lmodel.IDScoreRank{
		&lmodel.IDScoreRank{
			ID:    mockUserID,
			Score: int64(13),
			Rank:  int32(0),
		},
		&lmodel.IDScoreRank{
			ID:    mockUserID2,
			Score: int64(13),
			Rank:  int32(0),
		},
	}, nil).Once()

	s.mockUser.On("GetDecoratedUsers", mockCTX, mockUserID, mockUserID2).Return([]models.User{
		models.User{
			UserID:      mockUserID,
			DisplayName: mockOpenID,
		},
		models.User{
			UserID:      mockUserID2,
			DisplayName: mockOpenID2,
		},
	}, nil).Once()

	s.mockFuncs.On("currentTime").Return(int64(1000)).Once()
	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), gameID, mock.Anything, mock.Anything).Return(nil).Once()

	status, err := s.im.GameResult(mockCTX, gameID)
	s.NoError(err)
	s.Equal([]string{mockUserID, mockUserID2}, status.Result.WinnerIDs)
	s.Equal(int32(2), status.Result.WinnersCount)
	s.Len(status.Result.Winners, 2)
	s.Equal(mockUserID, status.Result.Winners[0].UserID)
	s.Equal(mockUserID2, status.Result.Winners[1].UserID)
	s.Equal(int32(13), status.Result.CorrectCount)
}

func (s *triviaSuite) TestGetTriviaStatus() {
	// Test: Game doesn't exist
	_, err := s.im.GetTriviaStatus(mockCTX, int64(1), false)
	s.Error(err)

	key := getStatusKey(int64(1))
	status := model.GameStatus{
		ID:          int64(1),
		TotalReward: float32(100),
	}
	bytes, err := json.Marshal(&status)
	s.NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	// Test : Get game status succeed
	res, err := s.im.GetTriviaStatus(mockCTX, int64(1), false)
	s.NoError(err)
	s.Equal(float32(100), res.TotalReward)

	// Test: Unmarshal status failed
	s.redis.Set(mockCTX, key, []byte("abc"), time.Minute)
	res, err = s.im.GetTriviaStatus(mockCTX, int64(1), false)
	s.Error(err)
}

func (s *triviaSuite) setupTrivia(gs *model.GameStatus) *model.TriviaGame {
	qz1 := mockQuiz1
	qz2 := mockQuiz2
	rqz1, err := s.im.UpsertQuiz(mockCTX, &qz1)
	s.Require().NoError(err)
	rqz2, err := s.im.UpsertQuiz(mockCTX, &qz2)
	s.Require().NoError(err)

	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)

	tr := mockTrivia
	tr.TriviaQuizIDs = []int64{
		rqz1.ID, rqz2.ID,
	}

	rtr, err := s.im.UpsertTrivia(mockCTX, &tr)
	s.Require().NoError(err)

	if gs != nil {
		gs.ID = tr.ID
		err := s.im.saveTriviaStatus(mockCTX, gs)
		s.Require().NoError(err)
	}
	return rtr
}

func (s *triviaSuite) TestGameStarted() {
	tr := s.setupTrivia(&model.GameStatus{
		Status: model.Status_PREPARE,
	})
	_, err := s.im.GameStart(mockCTX, tr.ID)
	s.Equal(ErrGameWasStarted, err)
}

func (s *triviaSuite) TestGameStartGetHostFailed() {
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	tr := s.setupTrivia(nil)
	tr.TriviaQuizIDs = []int64{}
	_, err := s.im.UpsertTrivia(mockCTX, tr)
	s.Require().NoError(err)

	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return(nil, fmt.Errorf("some error"))
	_, err = s.im.GameStart(mockCTX, tr.ID)
	s.Error(err)
}

func (s *triviaSuite) TestGameStartGetHostNoRoomID() {
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	tr := s.setupTrivia(nil)
	tr.TriviaQuizIDs = []int64{}
	_, err := s.im.UpsertTrivia(mockCTX, tr)
	s.Require().NoError(err)

	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{models.User{}}, nil)
	_, err = s.im.GameStart(mockCTX, tr.ID)
	s.Error(err)
}

func (s *triviaSuite) TestFirstQuestion() {
	tr := s.setupTrivia(&model.GameStatus{
		Status:        model.Status_PREPARE,
		TotalQuiz:     1,
		QuizCountDown: 3,
	})

	s.mockLiveHelper.On("MappingRoomIDLegacy", mockCTX, "").Return("XDXDXD", nil)
	gs, err := s.im.QuizStart(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_QUIZSTART, gs.Status)
	s.Equal(int32(0), gs.CurrentQuiz.QuizNo)
}

func (s *triviaSuite) TestNextQuestion() {
	quizNo := int32(0)
	tr := s.setupTrivia(&model.GameStatus{
		Status: model.Status_QUIZEND,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: quizNo,
			Options: []*model.QuizOption{
				&model.QuizOption{},
			},
		},
		QuizCountDown: 3,
		TotalQuiz:     3,
	})

	// Set previous option result
	s.setSurvivors(tr.ID, quizNo, mockUserID)

	gs, err := s.im.QuizStart(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_QUIZSTART, gs.Status)
	s.Equal(int32(1), gs.CurrentQuiz.QuizNo)
}

func (s *triviaSuite) TestQuizStartFinish() {
	quizNo := int32(0)
	tr := s.setupTrivia(&model.GameStatus{
		Status: model.Status_QUIZEND,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: quizNo,
		},
		QuizCountDown: 3,
		IsFinish:      true,
		TotalQuiz:     3,
	})
	_, err := s.im.QuizStart(mockCTX, tr.ID)
	s.Equal(ErrGameEnded, err)
}

func (s *triviaSuite) TestQuizStartInvalidAction() {
	tr := s.setupTrivia(&model.GameStatus{
		Status: model.Status_QUIZRESULT,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 1,
		},
		QuizCountDown: 3,
		TotalQuiz:     3,
	})

	_, err := s.im.QuizStart(mockCTX, tr.ID)
	s.Equal(ErrInvalidAction, err)
}

func (s *triviaSuite) TestQuizEnd() {
	tr := s.setupTrivia(&model.GameStatus{
		Status: model.Status_QUIZRESULT,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 1,
		},
		QuizCountDown: 3,
		TotalQuiz:     3,
	})

	gs, err := s.im.QuizEnd(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_QUIZEND, gs.Status)
	s.Equal(int32(1), gs.CurrentQuiz.QuizNo)
}

func (s *triviaSuite) TestQuizEndInvalidAction() {
	tr := s.setupTrivia(&model.GameStatus{
		Status: model.Status_QUIZSTART,
	})

	_, err := s.im.QuizEnd(mockCTX, tr.ID)
	s.Equal(ErrInvalidAction, err)
}

func (s *triviaSuite) setSurvivors(gameID int64, quizNo int32, survivorIDs ...string) {
	quizKey := getQuizOptKey(gameID, quizNo, 1)
	err := s.im.setQuizSurvivor(mockCTX, gameID, quizNo, quizKey)
	s.Require().NoError(err)

	for _, i := range survivorIDs {
		err = s.redis.SAdd(mockCTX, quizKey, i)
		s.Require().NoError(err)
	}
}

func (s *triviaSuite) TestGameResult() {
	quizNo := int32(1)
	tr := s.setupTrivia(&model.GameStatus{
		Status: model.Status_QUIZEND,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: quizNo,
		},
		QuizCountDown: 3,
		TotalQuiz:     2,
		IsFinish:      true,
	})

	s.setSurvivors(tr.ID, quizNo)
	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), tr.ID, mock.Anything, mock.Anything).Return(nil).Once()
	s.mockFuncs.On("currentTime").Return(int64(1000))
	gs, err := s.im.GameResult(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_GAMERESULT, gs.Status)
}

func (s *triviaSuite) TestGameResultInvalidAction() {
	quizNo := int32(0)
	tr := s.setupTrivia(&model.GameStatus{
		Status: model.Status_QUIZEND,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 0,
		},
		QuizCountDown: 3,
		TotalQuiz:     2,
		Type:          model.TriviaType_TRIVIA,
	})

	s.setSurvivors(tr.ID, quizNo, mockUserID)
	_, err := s.im.GameResult(mockCTX, tr.ID)
	s.Equal(ErrInvalidAction, err)
}

func (s *triviaSuite) TestGameEnd() {
	tr := s.setupTrivia(&model.GameStatus{
		Status:   model.Status_GAMERESULT,
		IsFinish: true,
		Result: &model.TriviaResult{
			Winners: []*userModel.DisplayInfo{
				&userModel.DisplayInfo{
					UserID: mockUserID,
				},
			},
			WinnerIDs: []string{mockUserID},
			Reward:    100.0,
		},
		TotalQuiz: int32(1),
		Currency:  "NTD",
		Region:    "TW",
		Type:      model.TriviaType_TRIVIA,
	})
	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), tr.ID, mock.Anything, mock.Anything).Return(nil).Twice()

	gs, err := s.im.GameEnd(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_GAMEEND, gs.Status)

	// try to set GameResult after GAMEEND, should fail
	_, err = s.im.GameResult(mockCTX, tr.ID)
	s.Error(err)
}

func (s *triviaSuite) TestGameEndWithPatchFailed() {
	tr := s.setupTrivia(&model.GameStatus{
		Status:   model.Status_GAMERESULT,
		IsFinish: true,
		Result: &model.TriviaResult{
			Winners: []*userModel.DisplayInfo{
				&userModel.DisplayInfo{
					UserID: mockUserID,
				},
			},
			WinnerIDs: []string{mockUserID},
			Reward:    100.0,
		},
		Currency: "NTD",
		Region:   "TW",
		Type:     model.TriviaType_TRIVIA,
	})
	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), tr.ID, mock.Anything, mock.Anything).Return(nil).Twice()

	gs, err := s.im.GameEnd(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_GAMEEND, gs.Status)
}

func (s *triviaSuite) TestPayUSD() {
	tr := s.setupTrivia(&model.GameStatus{
		Status:    model.Status_GAMERESULT,
		IsFinish:  true,
		TotalQuiz: int32(1),
		Result: &model.TriviaResult{
			Winners: []*userModel.DisplayInfo{
				&userModel.DisplayInfo{
					UserID: mockUserID,
				},
			},
			WinnerIDs: []string{mockUserID},
			Reward:    100.0,
		},
		Currency: "USD",
		Region:   "TW",
	})
	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), tr.ID, mock.Anything, mock.Anything).Return(nil).Twice()

	gs, err := s.im.GameEnd(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_GAMEEND, gs.Status)
}

func (s *triviaSuite) TestGameEndInvalidAction() {
	tr := s.setupTrivia(&model.GameStatus{
		Status: model.Status_QUIZEND,
		Region: "TW",
	})

	_, err := s.im.GameEnd(mockCTX, tr.ID)
	s.Equal(ErrInvalidAction, err)
}

func (s *triviaSuite) TestGameClose() {
	tr := s.setupTrivia(&model.GameStatus{
		Status: model.Status_PREPARE,
		Region: "TW",
		Type:   model.TriviaType_TRIVIA,
	})

	gs, err := s.im.GameClose(mockCTX, tr.ID)
	s.NoError(err)
	s.Equal(model.Status_CLOSED, gs.Status)

	_, err = s.im.GameClose(mockCTX, tr.ID+int64(100))
	s.NoError(err)
}

func (s *triviaSuite) TestGetQuizByID() {
	qz1 := &model.TriviaQuiz{ID: 1, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 3, Tag: "Anime", Level: 1}
	err := s.im.kv.Set(mockCTX, keys.TabTriviaQuizzes, int64(1), qz1)
	s.NoError(err)
	qz2 := &model.TriviaQuiz{ID: 2, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 3, Tag: "Anime", Level: 1}
	err = s.im.kv.Set(mockCTX, keys.TabTriviaQuizzes, int64(2), qz2)
	s.NoError(err)
	qz3 := &model.TriviaQuiz{ID: 3, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 3, Tag: "Anime", Level: 1}
	err = s.im.kv.Set(mockCTX, keys.TabTriviaQuizzes, int64(3), qz3)
	s.NoError(err)

	quiz, err := s.im.GetQuizByID(mockCTX, int64(1))
	s.NoError(err)
	s.Equal(qz1, quiz)
	quizzes, err := s.im.GetQuizzesByIDs(mockCTX, []int64{2, 5, 1, 4, 3})
	s.NoError(err)
	s.Equal(qz2, quizzes[0])
	s.Equal(qz1, quizzes[1])
	s.Equal(qz3, quizzes[2])
}

func (s *triviaSuite) TestGetTriviaswithCount() {
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "HK").Return(true)
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)

	game := &model.TriviaGame{ID: 0, StartTime: 1509720000, HostID: mockUserID, Currency: "NTD", Region: "TW"}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.Error(err)

	// Create 3 TriviaGames
	game = &model.TriviaGame{ID: 0, StartTime: 1509720000, HostID: mockUserID, Currency: "NTD", Region: "TW", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	game = &model.TriviaGame{ID: 0, StartTime: 1509740000, HostID: mockUserID, Currency: "NTD", Region: "TW", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	game = &model.TriviaGame{ID: 0, StartTime: 1509770000, HostID: mockUserID, Currency: "HKD", Region: "HK", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	// test GetTrivias by only giving region
	region := "TW"
	data, err := s.im.GetTrivias(mockCTX, nil, nil, nil, &region, nil, nil, int32(1))
	s.NoError(err)
	count, err := s.im.GetTriviasCount(mockCTX, nil, nil, nil, &region, nil)
	s.NoError(err)
	s.Equal(int64(2), data[0].ID)
	s.Equal(int64(1509740000), data[0].StartTime)
	s.Equal(int32(2), count)

	// test GetTrivias by only giving limit
	_, err = s.im.GetTrivias(mockCTX, nil, nil, nil, nil, nil, nil, int32(0))
	s.Error(err)
	data, err = s.im.GetTrivias(mockCTX, nil, nil, nil, nil, nil, nil, int32(1))
	s.NoError(err)
	count, err = s.im.GetTriviasCount(mockCTX, nil, nil, nil, nil, nil)
	s.NoError(err)
	s.Equal(data[0].ID, int64(3))
	s.Equal(data[0].StartTime, int64(1509770000))
	s.Equal(count, int32(3))

	// test GetTrivias by giving limit and offset
	temp := int32(1)
	offset := &temp
	data, err = s.im.GetTrivias(mockCTX, nil, nil, nil, nil, nil, offset, int32(1))
	s.NoError(err)
	s.Equal(data[0].ID, int64(2))
	s.Equal(data[0].StartTime, int64(1509740000))

	// test GetTrivias by giving timeGreaterThan or timeLessThan
	temp = int32(0)
	left := int64(1509730000)
	timeG := &left
	data, err = s.im.GetTrivias(mockCTX, timeG, nil, nil, nil, nil, offset, int32(3))
	s.NoError(err)
	count, err = s.im.GetTriviasCount(mockCTX, timeG, nil, nil, nil, nil)
	s.NoError(err)
	s.Len(data, 2)
	s.Equal(int64(3), data[0].ID)
	s.Equal(int64(1509770000), data[0].StartTime)
	s.Equal(int64(2), data[1].ID)
	s.Equal(int64(1509740000), data[1].StartTime)
	s.Equal(int32(2), count)

	// test GetTrivias by only giving all arguments
	temp = int32(0)
	left = int64(1509730000)
	right := int64(1509760000)
	timeL := &right
	data, err = s.im.GetTrivias(mockCTX, timeG, timeL, nil, nil, nil, offset, int32(3))
	s.NoError(err)
	count, err = s.im.GetTriviasCount(mockCTX, timeG, timeL, nil, nil, nil)
	s.NoError(err)
	s.Equal(len(data), 1)
	s.Equal(data[0].ID, int64(2))
	s.Equal(data[0].StartTime, int64(1509740000))
	s.Equal(count, int32(1))
}

func (s *triviaSuite) TestUpsertQuiz() {
	qz := &model.TriviaQuiz{ID: 0}
	_, err := s.im.UpsertQuiz(mockCTX, qz)
	s.Equal(ErrBadQuizArgument, err)

	qz = &model.TriviaQuiz{ID: 0, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 3, Tag: "Anime", Level: 1}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	quiz, err := s.im.GetQuizByID(mockCTX, int64(1))
	s.NoError(err)
	s.Equal(qz, quiz)

	qz = &model.TriviaQuiz{ID: int64(1), Answer: 4}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.Equal(ErrBadQuizArgument, err)

	qz = &model.TriviaQuiz{ID: int64(1), Level: 3, Answer: 3}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	qz = &model.TriviaQuiz{ID: int64(1), Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 3, Tag: "Anime", Level: 3}
	quiz, err = s.im.GetQuizByID(mockCTX, int64(1))
	s.NoError(err)
	s.Equal(qz, quiz)

	qz = &model.TriviaQuiz{ID: int64(1), Description: "Which of the following is kemono friends?", Options: []string{"Sabaru", "Bus", "Boss", "All of above"}, Answer: 1, Tag: "Douga", Level: 2}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	quiz, err = s.im.GetQuizByID(mockCTX, int64(1))
	s.NoError(err)
	s.Equal(qz, quiz)

	qz = &model.TriviaQuiz{ID: int64(1), Description: "Which of the following is kemono friends?", Options: []string{"Sabaruchan", "Araisan", "Finiku", "All of above"}, Answer: 3, Tag: "Douga", Level: 1}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	quiz, err = s.im.GetQuizByID(mockCTX, int64(1))
	s.NoError(err)
	s.Equal(qz, quiz)

	qz = &model.TriviaQuiz{ID: int64(1), Description: "Which of the following is kemono friends?", Options: []string{"Sabaruchan", "Araisan", "Finiku", "Arupaca", "All of above"}, Answer: 4, Tag: "Douga", Level: 1, Region: "JP"}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	quiz, err = s.im.GetQuizByID(mockCTX, int64(1))
	s.NoError(err)
	s.Equal(qz, quiz)

	qz = &model.TriviaQuiz{ID: int64(1), Description: "Which of the following is kemono friends?", Options: []string{"Sabaruchan", "Kabanchan", "All of above"}, Answer: 4, Tag: "Douga", Level: 1}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.Equal(ErrBadQuizArgument, err)

	qz = &model.TriviaQuiz{ID: int64(1), Level: 12}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.Equal(ErrBadQuizArgument, err)

	qz = &model.TriviaQuiz{ID: 0, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 3, Tag: "Anime", Level: 1, DisplayName: "test"}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	quiz, err = s.im.GetQuizByID(mockCTX, int64(2))
	s.NoError(err)
	s.Equal("test", quiz.DisplayName)

	qz = &model.TriviaQuiz{ID: 2, DisplayName: "test2", Answer: int32(2)}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	quiz, err = s.im.GetQuizByID(mockCTX, int64(2))
	s.NoError(err)
	s.Equal("test2", quiz.DisplayName)
	s.Equal(int32(2), quiz.Answer)

	qz = &model.TriviaQuiz{ID: 2, DisplayName: "test3", Answer: IgnoreAnswer}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	quiz, err = s.im.GetQuizByID(mockCTX, int64(2))
	s.NoError(err)
	s.Equal("test3", quiz.DisplayName)
	s.Equal(int32(2), quiz.Answer)

	qz = &model.TriviaQuiz{ID: 2, Answer: int32(0)}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	quiz, err = s.im.GetQuizByID(mockCTX, int64(2))
	s.NoError(err)
	s.Equal("test3", quiz.DisplayName)
	s.Equal(int32(0), quiz.Answer)

	qz = &model.TriviaQuiz{ID: 0, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan"}, Answer: 0, Tag: "Anime", Level: 1, DisplayName: "test"}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	qz = &model.TriviaQuiz{ID: 0, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Kachan"}, Answer: 1, Tag: "Anime", Level: 1, DisplayName: "test"}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
}

func (s *triviaSuite) TestGetQuizzes() {
	// Create three Quizzes
	qz1 := &model.TriviaQuiz{ID: int64(0), Description: "Quiz1", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 1, Level: 1, Tag: "math"}
	_, err := s.im.UpsertQuiz(mockCTX, qz1)
	s.NoError(err)
	qz2 := &model.TriviaQuiz{ID: int64(0), Description: "quiz2", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 1, Level: 2, Tag: "science"}
	_, err = s.im.UpsertQuiz(mockCTX, qz2)
	s.NoError(err)
	qz3 := &model.TriviaQuiz{ID: int64(0), Description: "Quiz3", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 1, Level: 3, Tag: "math"}
	_, err = s.im.UpsertQuiz(mockCTX, qz3)
	s.NoError(err)

	// Error limit
	_, err = s.im.GetQuizzes(mockCTX, nil, nil, nil, nil, nil, nil, int32(0))
	s.Error(err)

	// Offset and limit
	temp := int32(1)
	offset := &temp
	data, err := s.im.GetQuizzes(mockCTX, nil, nil, nil, nil, nil, offset, int32(1))
	s.NoError(err)
	s.Equal(data[0], qz2)

	// one filter argument
	tag := "math"
	ta := &tag
	data, err = s.im.GetQuizzes(mockCTX, nil, nil, ta, nil, nil, offset, int32(1))
	s.NoError(err)
	s.Equal(data[0], qz1)

	// all filter arguments
	temp = int32(0)
	desp := "quiz"
	level := int32(1)
	des := &desp
	lev := &level
	data, err = s.im.GetQuizzes(mockCTX, des, lev, ta, nil, nil, offset, int32(1))
	s.NoError(err)
	s.Equal(data[0], qz1)
}

func (s *triviaSuite) TestGetLatestGames() {
	s.mockFuncs.On("currentTime").Return(int64(19000))

	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	// no games
	cur, next, err := s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Nil(cur)
	s.Nil(next)

	// in Z, not in cache, and a wrong format ID
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17171": 10000}))
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"1717_": 15000}))
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))
	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Nil(cur)
	s.Nil(next)

	// in Z, in kv
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17172": 20000}))
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))
	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Nil(cur)
	s.Nil(next)

	s.kv.Set(mockCTX, keys.TabTriviaGames, int64(17172), &model.TriviaGame{ID: int64(17172)})
	game := &model.TriviaGame{
		ID:            17172,
		StartTime:     20000,
		HostID:        mockUserID,
		Type:          model.TriviaType_TRIVIA,
		TriviaQuizIDs: []int64{1, 2, 3},
	}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.Require().NoError(err)

	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))
	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Nil(cur)
	s.Equal(int64(17172), next.ID)
	// add cache, change now time
	key := getStatusKey(int64(17172))

	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil)
	_, err = s.im.GameStart(mockCTX, int64(17172))
	s.Require().NoError(err)

	status := model.GameStatus{
		ID:          int64(17172),
		TotalReward: float32(100),
		Status:      model.Status_GAMERESULT,
		Result: &model.TriviaResult{
			Winners: []*userModel.DisplayInfo{},
			Reward:  0.0,
		},
		Type: model.TriviaType_TRIVIA,
	}
	bytes, err := json.Marshal(&status)
	s.Require().NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))
	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Equal(int64(17172), cur.ID)
	s.Nil(next)

	// in Z, in cache, not in kv
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17173": 30000}))
	key = getStatusKey(int64(17173))
	status = model.GameStatus{
		ID:          int64(17173),
		TotalReward: float32(100),
		Status:      model.Status_GAMERESULT,
		Result: &model.TriviaResult{
			Winners: []*userModel.DisplayInfo{},
			Reward:  0.0,
		},
	}
	bytes, err = json.Marshal(&status)
	s.Require().NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)
	s.mockFuncs.On("currentTime").Return(int64(35000))

	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))
	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Equal(int64(17172), cur.ID)
	s.Nil(next)

	// in Z, not in cache, not in kv
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17173": 30000}))
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))
	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Equal(int64(17172), cur.ID)
	s.Nil(next)

	// in Z, not in cache, in kv
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17175": 50000}))
	game = &model.TriviaGame{ID: 17175, StartTime: 50000, Type: model.TriviaType_TRIVIA}
	s.Require().NoError(s.kv.Set(mockCTX, keys.TabTriviaGames, game.ID, game))
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))
	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Equal(int64(17172), cur.ID)
	s.Equal(int64(17175), next.ID)

	// in Z, not in cache, in kv
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17176": 60000}))
	game = &model.TriviaGame{ID: 17176, StartTime: 60000, Type: model.TriviaType_TRIVIA}
	s.Require().NoError(s.kv.Set(mockCTX, keys.TabTriviaGames, game.ID, game))
	// modify start time
	s.kv.Set(mockCTX, keys.TabTriviaGames, int64(17175), &model.TriviaGame{ID: int64(17175)})
	game = &model.TriviaGame{ID: 17175, StartTime: 70000, HostID: mockUserID, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))

	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Equal(int64(17172), cur.ID)
	s.Equal(int64(17176), next.ID)

	// test end game
	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), int64(17172), mock.Anything, mock.Anything).Return(nil).Twice()

	_, err = s.im.GameEnd(mockCTX, int64(17172))
	s.NoError(err)
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))

	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Equal(int64(17172), cur.ID)
	s.Equal(int64(17176), next.ID)

	// test close game
	_, err = s.im.GameClose(mockCTX, int64(17172))
	s.NoError(err)
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))

	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Nil(cur)
	s.Equal(int64(17176), next.ID)
}

func (s *triviaSuite) TestGetLatestGamesByRegion() {
	s.mockFuncs.On("currentTime").Return(int64(19000))
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{
		CreatorID:  mockUserID,
		RegionMode: regionModel.RegionMode_INCLUDE,
		Regions:    []string{"TW"},
	}, nil)
	// no games
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_TRIVIA.String()))
	cur, next, err := s.im.GetLatestGamesByRegion(mockCTX, false, "TW", model.TriviaType_TRIVIA)
	s.NoError(err)
	s.Nil(cur)
	s.Nil(next)

	// prepare
	// in region
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17171": 20000}))
	s.kv.Set(mockCTX, keys.TabTriviaGames, int64(17171), &model.TriviaGame{ID: int64(17171)})
	game := &model.TriviaGame{ID: 17171, StartTime: 20000, HostID: mockUserID, Type: model.TriviaType_TRIVIA, TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.Require().NoError(err)

	s.Require().NoError(s.kv.Set(mockCTX, keys.TabTriviaGames, game.ID, game))
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_TRIVIA.String()))
	cur, next, err = s.im.GetLatestGamesByRegion(mockCTX, false, "TW", model.TriviaType_TRIVIA)
	s.NoError(err)
	s.Nil(cur)
	s.Equal(int64(17171), next.ID)

	// not in region
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_TRIVIA.String()))
	cur, next, err = s.im.GetLatestGamesByRegion(mockCTX, false, "JP", model.TriviaType_TRIVIA)
	s.NoError(err)
	s.Nil(cur)
	s.Nil(next)

	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil)
	_, err = s.im.GameStart(mockCTX, int64(17171))
	s.NoError(err)
	// running
	// in region
	key := getStatusKey(int64(17171))
	status := model.GameStatus{
		ID:          int64(17171),
		TotalReward: float32(100),
		Status:      model.Status_GAMERESULT,
		Result: &model.TriviaResult{
			Winners: []*userModel.DisplayInfo{},
			Reward:  0.0,
		},
	}
	bytes, err := json.Marshal(&status)
	s.Require().NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_TRIVIA.String()))
	cur, next, err = s.im.GetLatestGamesByRegion(mockCTX, false, "TW", model.TriviaType_TRIVIA)
	s.NoError(err)
	s.Nil(next)
	s.Equal(int64(17171), cur.ID)

	// not in region
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_TRIVIA.String()))
	cur, next, err = s.im.GetLatestGamesByRegion(mockCTX, false, "JP", model.TriviaType_TRIVIA)
	s.NoError(err)
	s.Nil(cur)
	s.Nil(next)
}

func (s *triviaSuite) TestGetLatestGamesByRegionFilterByType() {
	s.mockFuncs.On("currentTime").Return(int64(19000))
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{
		CreatorID:  mockUserID,
		RegionMode: regionModel.RegionMode_ALL,
	}, nil)

	// filter by type
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17172": 20000}))
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17173": 20000}))
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17174": 20000}))
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"17175": 20000}))
	s.kv.Set(mockCTX, keys.TabTriviaGames, int64(17172), &model.TriviaGame{ID: int64(17172)})
	s.kv.Set(mockCTX, keys.TabTriviaGames, int64(17173), &model.TriviaGame{ID: int64(17173)})
	s.kv.Set(mockCTX, keys.TabTriviaGames, int64(17174), &model.TriviaGame{ID: int64(17174)})
	s.kv.Set(mockCTX, keys.TabTriviaGames, int64(17175), &model.TriviaGame{ID: int64(17175)})
	game := &model.TriviaGame{ID: 17172, StartTime: 20000, HostID: mockUserID, Type: model.TriviaType_TRIVIA, TriviaQuizIDs: []int64{1}}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.Require().NoError(err)
	game = &model.TriviaGame{ID: 17173, StartTime: 20000, HostID: mockUserID, Type: model.TriviaType_TRIVIA_ON_TV, TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.Require().NoError(err)
	game = &model.TriviaGame{ID: 17174, StartTime: 20000, HostID: mockUserID, Type: model.TriviaType_TRIVIA}
	s.Require().NoError(s.kv.Set(mockCTX, keys.TabTriviaGames, game.ID, game))
	game = &model.TriviaGame{ID: 17175, StartTime: 20000, HostID: mockUserID, Type: model.TriviaType_TRIVIA_ON_TV}
	s.Require().NoError(s.kv.Set(mockCTX, keys.TabTriviaGames, game.ID, game))

	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil)
	_, err = s.im.GameStart(mockCTX, int64(17172))
	s.NoError(err)

	key := getStatusKey(int64(17172))
	status := model.GameStatus{
		ID:          int64(17172),
		TotalReward: float32(100),
		Status:      model.Status_GAMERESULT,
		Result: &model.TriviaResult{
			Winners: []*userModel.DisplayInfo{},
			Reward:  0.0,
		},
		Type: model.TriviaType_TRIVIA,
	}
	bytes, err := json.Marshal(&status)
	s.Require().NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil)
	_, err = s.im.GameStart(mockCTX, int64(17173))
	s.NoError(err)

	key = getStatusKey(int64(17173))
	status = model.GameStatus{
		ID:          int64(17173),
		TotalReward: float32(100),
		Status:      model.Status_GAMERESULT,
		Result: &model.TriviaResult{
			Winners: []*userModel.DisplayInfo{},
			Reward:  0.0,
		},
		Type: model.TriviaType_TRIVIA_ON_TV,
	}
	bytes, err = json.Marshal(&status)
	s.Require().NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_TRIVIA.String()))
	cur, next, err := s.im.GetLatestGamesByRegion(mockCTX, false, "TW", model.TriviaType_TRIVIA)
	s.NoError(err)
	s.Equal(int64(17172), cur.ID)
	s.Equal(int64(17174), next.ID)
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_TRIVIA_ON_TV.String()))
	cur, next, err = s.im.GetLatestGamesByRegion(mockCTX, false, "TW", model.TriviaType_TRIVIA_ON_TV)
	s.NoError(err)
	s.Equal(int64(17173), cur.ID)
	s.Equal(int64(17175), next.ID)
}

func (s *triviaSuite) TestGetTriviaByID() {
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	tr := &model.TriviaGame{TriviaQuizIDs: []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12}, HostID: mockUserID, Currency: "NTD", Region: "TW"}
	_, err := s.im.UpsertTrivia(mockCTX, tr)
	s.NoError(err)

	tr = &model.TriviaGame{}
	trE := &model.TriviaGame{ID: int64(1), QuizCountDown: 10, HostID: mockUserID, Currency: "NTD", Region: "TW", SubQuizzes: []*model.TriviaSubQuiz{}, NextStartTime: encoding.NilInt, Picture: encoding.NilString, Status: encoding.NilInt}
	err = s.query.FindOne(mockCTX, keys.TabTriviaGames, bson.M{"ID": int64(1)}, tr)
	s.NoError(err)
	s.Equal(trE, tr)

	game, err := s.im.GetTriviaByID(mockCTX, int64(1), true)
	s.NoError(err)
	tr = &model.TriviaGame{ID: int64(1), QuizCountDown: 10, TriviaQuizIDs: nil, HostID: mockUserID, Currency: "NTD", Region: "TW", NextStartTime: encoding.NilInt, Picture: encoding.NilString, Status: encoding.NilInt}
	s.Equal(tr, game)

	game, err = s.im.GetTriviaByID(mockCTX, int64(1), false)
	s.NoError(err)
	tr = &model.TriviaGame{ID: int64(1), QuizCountDown: 10, TriviaQuizIDs: []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12}, HostID: mockUserID, Currency: "NTD", Region: "TW", NextStartTime: encoding.NilInt, Picture: encoding.NilString, Status: encoding.NilInt}
	s.Equal(tr, game)
}

func (s *triviaSuite) TestGetQuizzesCount() {
	qz1 := &model.TriviaQuiz{Description: "a", Options: []string{"1"}, Answer: 0, Level: 1, Tag: "history", Region: "TW"}
	_, err := s.im.UpsertQuiz(mockCTX, qz1)
	s.NoError(err)
	qz1 = &model.TriviaQuiz{Description: "a", Options: []string{"1"}, Answer: 0, Level: 1, Tag: "math", Region: "JP"}
	_, err = s.im.UpsertQuiz(mockCTX, qz1)
	s.NoError(err)

	des := ""
	lev := int32(1)
	tag := "history"
	isUsed := false
	count, err := s.im.GetQuizzesCount(mockCTX, &des, &lev, &tag, &isUsed, nil)
	s.NoError(err)
	s.Equal(int32(1), count)

	var nildes *string
	var nillev *int32
	var niltag *string
	var nilisUsed *bool
	region := "TW"
	count, err = s.im.GetQuizzesCount(mockCTX, nildes, nillev, niltag, nilisUsed, &region)
	s.NoError(err)
	s.Equal(int32(1), count)

	count, err = s.im.GetQuizzesCount(mockCTX, nildes, nillev, niltag, nilisUsed, nil)
	s.NoError(err)
	s.Equal(int32(2), count)
}

func (s *triviaSuite) TestEndCurrentQuiz() {
	gameID := int64(1)
	game := &model.GameStatus{
		ID:          gameID,
		TotalReward: float32(100),
		TotalQuiz:   2,
		Status:      model.Status_QUIZSTART,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: 0,
			Options: []*model.QuizOption{
				&model.QuizOption{
					IsAnswer: true,
				},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
	}

	err := s.im.saveTriviaStatus(mockCTX, game)
	s.NoError(err)
	s.setQuizReady(gameID, 0, true)
	s.im.EndCurrentQuiz(mockCTX, gameID)
	_, err = s.im.QuizResult(mockCTX, gameID)
	s.Error(err)
}

func (s *triviaSuite) TestResetTriviaStatus() {
	quizNo := int32(0)
	tr := s.setupTrivia(nil)
	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil)
	s.mockFuncs.On("currentTime").Return(int64(1000))
	// prepare
	_, err := s.im.GameStart(mockCTX, tr.ID)
	s.NoError(err)

	s.mockLiveHelper.On("MappingRoomIDLegacy", mockCTX, "123").Return("XDXDXD", nil)
	_, err = s.im.QuizStart(mockCTX, tr.ID)
	s.NoError(err)
	ans := model.TriviaAnswerLog{
		UserID:    mockUserID,
		GameID:    tr.ID,
		QuizNo:    quizNo,
		Option:    0,
		Timestamp: int64(1000),
	}
	s.mockPublisher.On(
		"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
	).Return(nil).Once()
	err = s.im.Answer(mockCTX, mockUserID, tr.ID, quizNo, 0, "")
	s.NoError(err)

	// pre-check
	keys := []string{}
	keys = append(keys, getStatusKey(tr.ID))
	keys = append(keys, getUserKey(mockUserID, tr.ID, quizNo))
	keys = append(keys, getQuizOptKey(tr.ID, quizNo, 0))
	keys = append(keys, getQuizAnsKey(tr.ID, quizNo))
	keys = append(keys, getQuizKey(tr.ID, quizNo))
	for _, key := range keys {
		e, err := s.redis.Exists(mockCTX, key)
		s.NoError(err, key)
		s.True(e, key)
	}

	err = s.im.ResetTriviaStatus(mockCTX, tr.ID)
	s.NoError(err)

	for _, key := range keys {
		e, err := s.redis.Exists(mockCTX, key)
		s.NoError(err, key)
		s.False(e, key)
	}
}

func (s *triviaSuite) TestPictures() {
	mockTime := int64(1500000000)
	testConf.Pictures.Default = "default"
	testConf.Pictures.Prepare = "prepare"
	testConf.Pictures.OnLive = "onLive"
	testConf.PicturesOnTV.Default = "defalutOnTV"
	testConf.PicturesOnTV.Prepare = "prepareOnTV"
	testConf.PicturesOnTV.OnLive = "onLiveOnTV"

	testConf.Hosts = map[string]map[string]triviaConfig.HostInfo{
		"TRIVIA": map[string]triviaConfig.HostInfo{
			"andyTrivia": triviaConfig.HostInfo{
				Pictures: triviaConfig.Pictures{
					Default: "default",
					Prepare: "prepare",
					OnLive:  "onLive",
				},
			},
		},
		"TRIVIA_ON_TV": map[string]triviaConfig.HostInfo{
			"andytvTrivia": triviaConfig.HostInfo{
				Pictures: triviaConfig.Pictures{
					Default: "defalutOnTV",
					Prepare: "prepareOnTV",
					OnLive:  "onLiveOnTV",
				},
			},
		},
	}

	tempConf := getCfg("TW")
	tests := []struct {
		Desc        string
		Res         string
		ResErr      error
		InputOnLive bool
		InputGame   *model.TriviaGame
		Now         int64
	}{
		{
			Desc:        "default",
			Res:         tempConf.Pictures.Default,
			ResErr:      nil,
			InputOnLive: false,
			InputGame: &model.TriviaGame{
				StartTime: mockTime,
				Type:      model.TriviaType_TRIVIA,
				HostID:    "andyTrivia",
			},
		},
		{
			Desc:        "prepare",
			Res:         tempConf.Pictures.Prepare,
			ResErr:      nil,
			InputOnLive: true,
			InputGame: &model.TriviaGame{
				StartTime: mockTime,
				Type:      model.TriviaType_TRIVIA,
				HostID:    "andyTrivia",
			},
			Now: 1400000000,
		},
		{
			Desc:        "onLive",
			Res:         tempConf.Pictures.OnLive,
			ResErr:      nil,
			InputOnLive: true,
			InputGame: &model.TriviaGame{
				StartTime: mockTime,
				Type:      model.TriviaType_TRIVIA,
				HostID:    "andyTrivia",
			},
			Now: 1600000000,
		},
		{
			Desc:   "error",
			Res:    "",
			ResErr: fmt.Errorf("game is nil"),
		},
		{
			Desc:        "defaultOnTV",
			Res:         tempConf.PicturesOnTV.Default,
			ResErr:      nil,
			InputOnLive: false,
			InputGame: &model.TriviaGame{
				StartTime: mockTime,
				Type:      model.TriviaType_TRIVIA_ON_TV,
				HostID:    "andytvTrivia",
			},
		},
		{
			Desc:        "prepareOnTV",
			Res:         tempConf.PicturesOnTV.Prepare,
			ResErr:      nil,
			InputOnLive: true,
			InputGame: &model.TriviaGame{
				StartTime: mockTime,
				Type:      model.TriviaType_TRIVIA_ON_TV,
				HostID:    "andytvTrivia",
			},
			Now: 1400000000,
		},
		{
			Desc:        "onLiveOnTV",
			Res:         tempConf.PicturesOnTV.OnLive,
			ResErr:      nil,
			InputOnLive: true,
			InputGame: &model.TriviaGame{
				StartTime: mockTime,
				Type:      model.TriviaType_TRIVIA_ON_TV,
				HostID:    "andytvTrivia",
			},
			Now: 1600000000,
		},
	}

	for _, test := range tests {
		timeNow = func() time.Time {
			return time.Unix(test.Now, int64(0))
		}
		g := test.InputGame

		err := s.im.injectPicture(test.InputOnLive, g)
		s.Equal(test.ResErr, err, test.Desc)
		if err == nil {
			s.Equal(test.Res, g.Picture, test.Desc)
		}
	}
}

func (s *triviaSuite) TestSetReferralSuccessAndGainMedal() {
	// mockUserID is referral's userID
	// mockUserID2 is referee's userID
	s.mockUser.On("GetTriviaPersonInfoByReferral", mockCTX, mockReferralCode).Return(&userModel.TriviaInfo{
		ReferralCode: mockReferralCode,
		UserID:       mockUserID2,
		Status:       7,
	}, nil).Once()

	// in getUserInfo (referral)
	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{
		models.User{
			UserID:       mockUserID,
			RegisterTime: int64(12345677),
		},
	}, nil).Once()

	testConf.Referral.RegisterDay = 3
	s.mockFuncs.On("currentTime").Return(int64(12345678))

	s.mockUser.On("AtomicPatchTriviaPersonInfo", mockCTX, mockUserID, int32(0), int32(maskSetReferral)).Return(nil).Once()

	// in acquireDeathExemptionMedal
	s.mockUser.On("GetTriviaPersonInfo", mockCTX, mockUserID).Return(&userModel.TriviaInfo{
		UserID: mockUserID,
		Status: 3,
	}, nil).Once()

	// in incMedalAndNotify (referral)
	s.mockUser.On("AtomicPatchTriviaPersonInfo", mockCTX, mockUserID, int32(1), int32(maskNotApplicable)).Return(nil).Once()
	s.mockFuncs.On("translate", "tw", referralGainLife, mock.Anything).Return("拿到免死金牌", nil).Once()
	s.mockOfficial.On("Send", mockCTX, mock.AnythingOfType("string"), mockUserID, "拿到免死金牌", "", chat.Message_TEXT).Return(nil).Once()

	// in getUserInfo (referral)
	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{
		models.User{
			UserID:       mockUserID,
			RegisterTime: int64(12345677),
		},
	}, nil)

	s.mockUser.On("GetLanguage", mockCTX, mockUserID).Return("tw")
	s.mockUser.On("GetLanguage", mockCTX, mockUserID2).Return("tw")

	// in incMedalAndNotify (referee)
	s.mockUser.On("AtomicPatchTriviaPersonInfo", mockCTX, mockUserID2, int32(1), int32(0)).Return(nil).Once()
	s.mockFuncs.On("translate", "tw", refereeGainLife, mock.Anything, mock.Anything).Return("拿到免死金牌", nil).Once()
	s.mockOfficial.On("Send", mockCTX, mock.AnythingOfType("string"), mockUserID2, "拿到免死金牌", "", chat.Message_TEXT).Return(nil).Once()

	s.NoError(s.im.SetReferral(mockCTX, mockReferralCode, mockUserID, "TW"))
}

func (s *triviaSuite) TestSetReferralFailSelfSetting() {
	s.mockUser.On("GetTriviaPersonInfoByReferral", mockCTX, mockReferralCode).Return(&userModel.TriviaInfo{
		ReferralCode: mockReferralCode,
		UserID:       mockUserID,
		Status:       7,
	}, nil).Once()

	err := s.im.SetReferral(mockCTX, mockReferralCode, mockUserID, "TW")
	s.EqualError(err, fmt.Errorf("Referral and Referree are same person").Error())
}

func (s *triviaSuite) TestSetReferralButReferralExpired() {
	s.mockUser.On("GetTriviaPersonInfoByReferral", mockCTX, mockReferralCode).Return(&userModel.TriviaInfo{
		ReferralCode: mockReferralCode,
		UserID:       mockUserID2,
		Status:       7,
	}, nil).Once()

	// in getUserInfo (referral)
	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{
		models.User{
			UserID:       mockUserID,
			RegisterTime: int64(12345677),
		},
	}, nil).Once()

	testConf.Referral.RegisterDay = 3
	s.mockFuncs.On("currentTime").Return(int64(22345678))

	err := s.im.SetReferral(mockCTX, mockReferralCode, mockUserID, "TW")
	s.EqualError(err, ErrReferralExpired.Error())
}

func (s *triviaSuite) TestSetReferralButNotJoinGameYet() {
	s.mockUser.On("GetTriviaPersonInfoByReferral", mockCTX, mockReferralCode).Return(&userModel.TriviaInfo{
		ReferralCode: mockReferralCode,
		UserID:       mockUserID2,
		Status:       7,
	}, nil).Once()

	// in getUserInfo (referral)
	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{
		models.User{
			UserID:       mockUserID,
			RegisterTime: int64(12345677),
		},
	}, nil).Once()

	testConf.Referral.RegisterDay = 3
	s.mockFuncs.On("currentTime").Return(int64(12345678))

	s.mockUser.On("AtomicPatchTriviaPersonInfo", mockCTX, mockUserID, int32(0), int32(maskSetReferral)).Return(nil).Once()

	// in acquireDeathExemptionMedal
	s.mockUser.On("GetTriviaPersonInfo", mockCTX, mockUserID).Return(&userModel.TriviaInfo{
		UserID: mockUserID,
		Status: 2,
	}, nil).Once()

	s.NoError(s.im.SetReferral(mockCTX, mockReferralCode, mockUserID, "TW"))
}

func (s *triviaSuite) TestEnterTriviaStreamAndGetMedal() {
	s.mockUser.On("GetTriviaPersonInfo", mock.AnythingOfType("ctx.CTX"), mockUserID).Return(&userModel.TriviaInfo{
		UserID: mockUserID,
		Status: 2,
	}, nil).Once()

	s.mockUser.On("AtomicPatchTriviaPersonInfo", mock.AnythingOfType("ctx.CTX"), mockUserID, int32(0), int32(maskJoinGame)).Return(nil).Once()
	s.NoError(s.redis.Set(mockCTX, getReferralCodeKey(mockUserID), []byte(mockUserID2), time.Minute))

	// in acquireDeathExemptionMedal
	s.mockUser.On("GetTriviaPersonInfo", mock.AnythingOfType("ctx.CTX"), mockUserID).Return(&userModel.TriviaInfo{
		UserID: mockUserID,
		Status: 3,
	}, nil).Once()

	// in incMedalAndNotify (referral)
	s.mockUser.On("AtomicPatchTriviaPersonInfo", mock.AnythingOfType("ctx.CTX"), mockUserID, int32(1), int32(maskNotApplicable)).Return(nil).Once()
	s.mockFuncs.On("translate", "tw", referralGainLife, mock.Anything).Return("拿到免死金牌", nil).Once()
	s.mockOfficial.On("Send", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("string"), mockUserID, "拿到免死金牌", "", chat.Message_TEXT).Return(nil).Once()

	// in getUserInfo (referral)
	s.mockUser.On("GetPlainUsers", mock.AnythingOfType("ctx.CTX"), mockUserID).Return([]models.User{
		models.User{
			UserID:       mockUserID,
			RegisterTime: int64(12345677),
		},
	}, nil)

	s.mockUser.On("GetLanguage", mock.AnythingOfType("ctx.CTX"), mockUserID).Return("tw")
	s.mockUser.On("GetLanguage", mock.AnythingOfType("ctx.CTX"), mockUserID2).Return("tw")

	// in incMedalAndNotify (referee)
	s.mockUser.On("AtomicPatchTriviaPersonInfo", mock.AnythingOfType("ctx.CTX"), mockUserID2, int32(1), int32(0)).Return(nil).Once()
	s.mockFuncs.On("translate", "tw", refereeGainLife, mock.Anything, mock.Anything).Return("拿到免死金牌", nil).Once()
	s.mockOfficial.On("Send", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("string"), mockUserID2, "拿到免死金牌", "", chat.Message_TEXT).Return(nil).Once()

	s.NoError(s.im.EnterTriviaStream(mockCTX, mockUserID))
}

func (s *triviaSuite) TestGetTriviaPlayerInfo() {
	// Prepare game data
	testConf.Referral.QuizNo = 2

	status := &model.GameStatus{
		ID:     int64(1),
		Status: model.Status_QUIZSTART,
		Type:   model.TriviaType_TRIVIA,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: int32(0),
		},
		IsMedalSupported: true,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, status))

	s.mockUser.On("GetTriviaPersonInfo", mockCTX, mockUserID).Return(&userModel.TriviaInfo{
		UserID:              mockUserID,
		Status:              7,
		DeathExemptionMedal: int32(1),
	}, nil).Once()

	resp, err := s.im.GetTriviaPlayerInfo(mockCTX, int64(1), mockUserID)
	s.NoError(err)
	// quiz 0 and status is QuizStart, so user is a player
	s.Equal(model.TriviaRole_PLAYER, resp.Role)
	s.Equal(int32(1), resp.DeathExemptionMedal)
	s.True(resp.IsMedalUsable) // quiz 0 and status is QuizStart with 1 medal, so user can use
	s.True(resp.IsMedalSupported)
	s.Equal(int32(2), resp.MedalUsableBeforeNQuiz)
}

func (s *triviaSuite) TestGetTriviaPlayerInfoInGameEnd() {
	// Prepare game data
	testConf.Referral.QuizNo = 2

	status := &model.GameStatus{
		ID:     int64(1),
		Status: model.Status_GAMEEND,
		Type:   model.TriviaType_TRIVIA,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: int32(0),
		},
		IsMedalSupported: true,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, status))

	s.mockUser.On("GetTriviaPersonInfo", mockCTX, mockUserID).Return(&userModel.TriviaInfo{
		UserID:              mockUserID,
		DeathExemptionMedal: int32(1),
	}, nil).Once()

	resp, err := s.im.GetTriviaPlayerInfo(mockCTX, int64(1), mockUserID)
	s.NoError(err)
	s.Equal(model.TriviaRole_PLAYER, resp.Role)
}

func (s *triviaSuite) TestGetTriviaPlayerInfoInQuiz1AndUserHasMedals() {
	// Prepare game data
	testConf.Referral.QuizNo = 3

	status := &model.GameStatus{
		ID:     int64(1),
		Status: model.Status_QUIZRESULT,
		Type:   model.TriviaType_TRIVIA,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: int32(1),
		},
		IsMedalSupported: true,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, status))

	// let user be survivor
	s.NoError(s.redis.Set(mockCTX, getQuizSurvivorKey(int64(1), int32(1)), []byte("ans"), time.Minute))
	s.NoError(s.redis.SAdd(mockCTX, "ans", mockUserID))

	s.mockUser.On("GetTriviaPersonInfo", mockCTX, mockUserID).Return(&userModel.TriviaInfo{
		UserID:              mockUserID,
		DeathExemptionMedal: int32(2),
	}, nil).Once()

	resp, err := s.im.GetTriviaPlayerInfo(mockCTX, int64(1), mockUserID)
	s.NoError(err)
	s.Equal(model.TriviaRole_PLAYER, resp.Role)
	s.Equal(int32(2), resp.DeathExemptionMedal)
	s.True(resp.IsMedalUsable)
	s.True(resp.IsMedalSupported)
}

func (s *triviaSuite) TestRegionToCurrency() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "JP").Return(true)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "HK").Return(true)

	game := &model.TriviaGame{ID: 0, HostID: mockUserID, Currency: "NTD", Region: "UK", TriviaQuizIDs: []int64{1}}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrCurrencyNotSupported.Error())

	game = &model.TriviaGame{ID: 0, HostID: mockUserID, Currency: "17Coin", Region: "TW", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrCurrencyNotSupported.Error())

	game = &model.TriviaGame{ID: 0, HostID: mockUserID, Currency: "NTD", Region: "TW", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	game = &model.TriviaGame{ID: 1, HostID: mockUserID, Currency: "JPY", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrCurrencyNotSupported.Error())

	game = &model.TriviaGame{ID: 1, HostID: mockUserID, Region: "JP", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrCurrencyNotSupported.Error())

	game = &model.TriviaGame{ID: 1, HostID: mockUserID, Currency: "TWD", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	game = &model.TriviaGame{ID: 0, HostID: mockUserID, Currency: "TWD", Region: "TW", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	game = &model.TriviaGame{ID: 0, HostID: mockUserID, Currency: "USD", Region: "TW", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	game = &model.TriviaGame{ID: 0, HostID: mockUserID, Currency: "JPY", Region: "TW", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrCurrencyNotSupported.Error())

	game = &model.TriviaGame{ID: 0, HostID: mockUserID, Currency: "JPY", Region: "JP", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	game = &model.TriviaGame{ID: 0, HostID: mockUserID, Currency: "HKD", Region: "JP", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.EqualError(err, ErrCurrencyNotSupported.Error())

	game = &model.TriviaGame{ID: 0, HostID: mockUserID, Currency: "HKD", Region: "HK", TriviaQuizIDs: []int64{1}}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)
}

func (s *triviaSuite) TestSubQuizzes() {
	yamlString := `quiz:
bufferTime: 10
hostID: aaa-bbb-ccc
hostIDOnTV: ccc-bbb-aaa
`

	yaml.Unmarshal([]byte(yamlString), &testConf)

	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserIDOnTV).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	quizzes := []int64{}
	for i := 1; i <= 12; i++ {
		quizzes = append(quizzes, int64(i))
	}
	subQuizzes := []*model.TriviaSubQuiz{}
	for i := 1; i <= 12; i++ {
		sub := &model.TriviaSubQuiz{MainQuizID: int64(i), SubQuizIDs: []int64{int64(i*3 + 10), int64(i*3 + 11), int64(i*3 + 12)}}
		subQuizzes = append(subQuizzes, sub)
	}
	game := &model.TriviaGame{TriviaQuizIDs: quizzes, SubQuizzes: subQuizzes, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA_ON_TV}
	ret, err := s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)
	for idx := range subQuizzes {
		s.Equal(subQuizzes[idx].MainQuizID, ret.SubQuizzes[idx].MainQuizID)
		for _, subQuizID := range subQuizzes[idx].SubQuizIDs {
			s.True(validator.IsInInt64Slice(ret.SubQuizzes[idx].SubQuizIDs, subQuizID))
		}
	}
	exp := &model.TriviaGame{ID: int64(1), QuizCountDown: 10, HostID: mockUserIDOnTV, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA_ON_TV}
	ret = truncateTrivia(ret)
	s.Equal(exp, ret)

	ret, err = s.im.GetTriviaByID(mockCTX, int64(1), true)
	s.NoError(err)
	exp = &model.TriviaGame{ID: int64(1), QuizCountDown: 10, HostID: mockUserIDOnTV, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA_ON_TV, NextStartTime: encoding.NilInt, Picture: encoding.NilString, Status: encoding.NilInt}
	s.Equal(exp, ret)

	quizzes[0], quizzes[11] = quizzes[11], quizzes[0]
	subQuizzes[0], subQuizzes[11] = subQuizzes[11], subQuizzes[0]

	game = &model.TriviaGame{ID: int64(1), TriviaQuizIDs: quizzes, SubQuizzes: subQuizzes, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA_ON_TV}
	ret, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)
	for idx := range subQuizzes {
		s.Equal(subQuizzes[idx].MainQuizID, ret.SubQuizzes[idx].MainQuizID)
		for _, subQuizID := range subQuizzes[idx].SubQuizIDs {
			s.True(validator.IsInInt64Slice(ret.SubQuizzes[idx].SubQuizIDs, subQuizID))
		}
	}
	exp = &model.TriviaGame{ID: int64(1), QuizCountDown: 10, HostID: mockUserIDOnTV, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA_ON_TV}
	ret = truncateTrivia(ret)
	s.Equal(exp, ret)

	ret, err = s.im.GetTriviaByID(mockCTX, int64(1), true)
	s.NoError(err)
	exp = &model.TriviaGame{ID: int64(1), QuizCountDown: 10, HostID: mockUserIDOnTV, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA_ON_TV, NextStartTime: encoding.NilInt, Picture: encoding.NilString, Status: encoding.NilInt}
	s.Equal(exp, ret)

	subQuizzes = append(subQuizzes[:7], subQuizzes[8:]...)

	game = &model.TriviaGame{ID: int64(1), TriviaQuizIDs: quizzes, SubQuizzes: subQuizzes, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA_ON_TV}
	ret, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)
	for idx := range subQuizzes {
		s.Equal(subQuizzes[idx].MainQuizID, ret.SubQuizzes[idx].MainQuizID)
		for _, subQuizID := range subQuizzes[idx].SubQuizIDs {
			s.True(validator.IsInInt64Slice(ret.SubQuizzes[idx].SubQuizIDs, subQuizID))
		}
	}
	exp = &model.TriviaGame{ID: int64(1), QuizCountDown: 10, HostID: mockUserIDOnTV, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA_ON_TV}
	ret = truncateTrivia(ret)
	s.Equal(exp, ret)

	ret, err = s.im.GetTriviaByID(mockCTX, int64(1), true)
	s.NoError(err)
	exp = &model.TriviaGame{ID: int64(1), QuizCountDown: 10, HostID: mockUserIDOnTV, Currency: "NTD", Region: "TW", Type: model.TriviaType_TRIVIA_ON_TV, NextStartTime: encoding.NilInt, Picture: encoding.NilString, Status: encoding.NilInt}
	s.Equal(exp, ret)

	var nilInt64 *int64
	var nilString *string
	t1 := "DUMMYTYPE"
	hostID := mockUserIDOnTV
	c, err := s.im.GetTriviasCount(mockCTX, nilInt64, nilInt64, &hostID, nilString, &t1)
	s.NoError(err)
	s.Equal(int32(1), c)
	t1 = "TRIVIA"
	c, err = s.im.GetTriviasCount(mockCTX, nilInt64, nilInt64, &hostID, nilString, &t1)
	s.NoError(err)
	s.Equal(int32(0), c)
	t1 = "TRIVIA_ON_TV"
	c, err = s.im.GetTriviasCount(mockCTX, nilInt64, nilInt64, &hostID, nilString, &t1)
	s.NoError(err)
	s.Equal(int32(1), c)

	// test error subquiz's mainQuizID, we'll return error
	game = &model.TriviaGame{
		TriviaQuizIDs: []int64{int64(1)},
		SubQuizzes: []*model.TriviaSubQuiz{
			&model.TriviaSubQuiz{
				MainQuizID: int64(2),
				SubQuizIDs: []int64{int64(3)},
			},
		},
		Currency: "NTD",
		Region:   "TW", Type: model.TriviaType_TRIVIA_ON_TV,
	}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.Error(err)

	// test error subquiz's mainQuizID, we'll return error
	game = &model.TriviaGame{
		TriviaQuizIDs: []int64{int64(1), int64(2), int64(3)},
		SubQuizzes: []*model.TriviaSubQuiz{
			&model.TriviaSubQuiz{
				MainQuizID: int64(1),
				SubQuizIDs: []int64{int64(2)},
			},
		},
		Currency: "NTD",
		Region:   "TW", Type: model.TriviaType_TRIVIA_ON_TV,
	}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.Error(err)

	// subQuizID cannot be the same as existed tr.QuizIDs and SubQuizIDs
	game = &model.TriviaGame{
		TriviaQuizIDs: []int64{int64(1), int64(2), int64(3)},
		SubQuizzes: []*model.TriviaSubQuiz{
			&model.TriviaSubQuiz{
				MainQuizID: int64(1),
				SubQuizIDs: []int64{int64(4)},
			},
			&model.TriviaSubQuiz{
				MainQuizID: int64(2),
				SubQuizIDs: []int64{int64(4)},
			},
		},
		Currency: "NTD",
		Region:   "TW", Type: model.TriviaType_TRIVIA_ON_TV,
	}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.Error(err)
}

func (s *triviaSuite) TestReplaceCurrentQuizID() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockHostID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	game := &model.TriviaGame{
		ID:            int64(0),
		HostID:        mockHostID,
		Currency:      "NTD",
		Region:        "TW",
		TriviaQuizIDs: []int64{2, 3, 4, 5, 6},
		SubQuizzes: []*model.TriviaSubQuiz{
			&model.TriviaSubQuiz{
				MainQuizID: int64(3),
				SubQuizIDs: []int64{7, 8, 9},
			},
		},
		Type: model.TriviaType_TRIVIA_ON_TV,
	}

	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	// index out of bound
	_, err = s.im.ReplaceCurrentQuizID(mockCTX, int64(1), int32(5), int64(7))
	s.Error(err)

	// no such subQuiz
	_, err = s.im.ReplaceCurrentQuizID(mockCTX, int64(1), int32(1), int64(10))
	s.NoError(err)
	data, err := s.im.GetTriviaByID(mockCTX, int64(1), false)
	s.NoError(err)
	s.Equal(int64(3), data.TriviaQuizIDs[1])
	s.Equal(int64(3), data.SubQuizzes[0].MainQuizID)
	s.Equal(int64(7), data.SubQuizzes[0].SubQuizIDs[0])
	s.Equal(int64(8), data.SubQuizzes[0].SubQuizIDs[1])
	s.Equal(int64(9), data.SubQuizzes[0].SubQuizIDs[2])

	// succeed in replacing
	_, err = s.im.ReplaceCurrentQuizID(mockCTX, int64(1), int32(1), int64(7))
	s.NoError(err)
	data, err = s.im.GetTriviaByID(mockCTX, int64(1), false)
	s.NoError(err)
	s.Equal(int64(7), data.TriviaQuizIDs[1])
	s.Equal(int64(7), data.SubQuizzes[0].MainQuizID)
	s.Equal(int64(3), data.SubQuizzes[0].SubQuizIDs[0])
}

func (s *triviaSuite) TestDeleteTriviaGame() {
	// Prepare game data
	games := s.prepareDefaultGame(mockHostID, model.TriviaType_TRIVIA, 3, 3)

	// set now to 1000
	s.mockFuncs.On("currentTime").Return(int64(1000))
	s.mockUser.On("GetPlainUsers", mockCTX, "QA").Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil)
	status, err := s.im.GameStart(mockCTX, games[0].ID)
	s.NoError(err)
	s.Equal(model.Status_PREPARE, status.Status)

	// Start the first game
	s.mockLiveHelper.On("MappingRoomIDLegacy", mockCTX, "123").Return("XDXDXD", nil)
	status, err = s.im.QuizStart(mockCTX, games[0].ID)
	s.NoError(err)
	s.Equal(int32(0), status.CurrentQuiz.QuizNo)
	s.Equal(float32(2700), status.TotalReward)

	// Assume 10 users answer the first quiz in time
	for i := 0; i < 10; i++ {
		userID := fmt.Sprintf("%s %d", "USER", i)
		ans := model.TriviaAnswerLog{
			UserID:    userID,
			GameID:    status.ID,
			QuizNo:    status.CurrentQuiz.QuizNo,
			Option:    0,
			Timestamp: int64(1000),
		}
		s.mockPublisher.On(
			"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
		).Return(nil).Once()
		err := s.im.Answer(mockCTX, userID, status.ID, status.CurrentQuiz.QuizNo, 0, "")
		s.NoError(err)
	}

	var nili64 *int64
	var nilstring *string

	count, err := s.im.GetTriviasCount(mockCTX, nili64, nili64, nilstring, nilstring, nilstring)
	s.NoError(err)
	s.Equal(int32(3), count)

	err = s.im.DeleteTrivia(mockCTX, int64(1))
	s.NoError(err)

	count, err = s.im.GetTriviasCount(mockCTX, nili64, nili64, nilstring, nilstring, nilstring)
	s.NoError(err)
	s.Equal(int32(2), count)
}

func (s *triviaSuite) TestDeleteTriviaQuiz() {
	qz := &model.TriviaQuiz{ID: 0, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 3, Tag: "Anime", Level: 1}
	_, err := s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	quiz, err := s.im.GetQuizByID(mockCTX, int64(1))
	s.NoError(err)
	qz = &model.TriviaQuiz{ID: 1, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 3, Tag: "Anime", Level: 1}
	s.Equal(qz, quiz)

	qz = &model.TriviaQuiz{ID: 0, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 3, Tag: "Anime", Level: 1}
	_, err = s.im.UpsertQuiz(mockCTX, qz)
	s.NoError(err)
	quiz, err = s.im.GetQuizByID(mockCTX, int64(2))
	s.NoError(err)
	qz = &model.TriviaQuiz{ID: 2, Description: "Which of the following is friends?", Options: []string{"Sabaru", "Kabanchan", "Boss", "All of above"}, Answer: 3, Tag: "Anime", Level: 1}
	s.Equal(qz, quiz)

	var nili32 *int32
	var nilstring *string
	var nilbool *bool
	count, err := s.im.GetQuizzesCount(mockCTX, nilstring, nili32, nilstring, nilbool, nilstring)
	s.Equal(int32(2), count)

	err = s.im.DeleteQuiz(mockCTX, int64(1))
	s.NoError(err)

	count, err = s.im.GetQuizzesCount(mockCTX, nilstring, nili32, nilstring, nilbool, nilstring)
	s.Equal(int32(1), count)

	// Test delete quiz that is using by some game
	yamlString := `quiz:
bufferTime: 10
hostID: aaa-bbb-ccc
`
	yaml.Unmarshal([]byte(yamlString), &testConf)

	s.mockProgram.On("GetProgramIndex", mockCTX, mockUserID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)
	game := &model.TriviaGame{ID: 0, Currency: "NTD", Region: "TW", TriviaQuizIDs: []int64{2}, Type: model.TriviaType_TRIVIA}
	_, err = s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	err = s.im.DeleteQuiz(mockCTX, int64(2))
	s.Error(err)

	count, err = s.im.GetQuizzesCount(mockCTX, nilstring, nili32, nilstring, nilbool, nilstring)
	s.Equal(int32(1), count)
}

func (s *triviaSuite) TestExemptionMedalNotUsedByTriviaOnTV() {
	// Prepare game data
	testConf.Referral.QuizNo = 2
	s.mockProgram.On("GetProgramIndex", mockCTX, mockHostID).Return(&pgModel.Program{}, nil)
	quizIDs := []int64{}
	for i := 0; i < 3; i++ {
		res, err := s.im.UpsertQuiz(mockCTX, &model.TriviaQuiz{
			Description: fmt.Sprintf("Quiz %d", i+1),
			Level:       model.QuizLevel_EASY,
			Tag:         "Science",
			Answer:      int32(0),
			Options:     []string{"option 0", "option 1", "option 2"},
			Region:      "TW",
		})
		s.NoError(err)
		quizIDs = append(quizIDs, res.ID)
	}
	games := []*model.TriviaGame{}
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)

	for i := 0; i < 3; i++ {
		res, err := s.im.UpsertTrivia(mockCTX, &model.TriviaGame{
			Currency:      "NTD",
			HostID:        mockHostID,
			QuizCountDown: int32(10),
			StartTime:     int64(i*1000 + 1000),
			TotalReward:   float32(2700),
			TriviaQuizIDs: quizIDs,
			Region:        "TW",
			Type:          model.TriviaType_TRIVIA_ON_TV,
		})
		s.NoError(err)
		games = append(games, res)
	}

	// set now to 1000
	s.mockFuncs.On("currentTime").Return(int64(1000))
	s.mockUser.On("GetPlainUsers", mockCTX, "QA").Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil)
	status, err := s.im.GameStart(mockCTX, games[0].ID)
	s.NoError(err)
	s.Equal(model.Status_PREPARE, status.Status)

	// Start the first game
	s.mockLiveHelper.On("MappingRoomIDLegacy", mockCTX, "123").Return("XDXDXD", nil)
	status, err = s.im.QuizStart(mockCTX, games[0].ID)
	s.NoError(err)
	s.Equal(int32(0), status.CurrentQuiz.QuizNo)
	s.Equal(float32(2700), status.TotalReward)

	triviaInfo := &userModel.TriviaInfo{
		DeathExemptionMedal: int32(3),
	}
	s.mockUser.On("GetTriviaPersonInfo", mock.AnythingOfType("ctx.CTX"), "USER 9").Return(triviaInfo, nil).Once()
	// Assume USER 9 have medals
	info, err := s.im.GetTriviaPlayerInfo(mockCTX, status.ID, "USER 9")
	s.NoError(err)

	answerKeys := []string{}
	for i := 0; i < 10; i++ {
		answerKeys = append(answerKeys, []string{"vOKd", "5vg=", "bBad", "KewK", "/vo=", "aoga", "+7j3", "Bpc=", "O4n2", "onmX", "A4I=", "uwir"}...)
	}
	expInfo := &model.TriviaPlayerInfo{
		Role:                   model.TriviaRole_PLAYER,
		DeathExemptionMedal:    3,
		IsMedalUsable:          false,
		IsMedalSupported:       false,
		MedalUsableBeforeNQuiz: 2,
		AnswerKeys:             answerKeys,
	}
	s.Equal(expInfo, info)

	// Assume 10 users answer the first quiz in time
	for i := 0; i < 9; i++ {
		userID := fmt.Sprintf("%s %d", "USER", i)
		ans := model.TriviaAnswerLog{
			UserID:    userID,
			GameID:    status.ID,
			QuizNo:    status.CurrentQuiz.QuizNo,
			Option:    0,
			Timestamp: int64(1000),
		}
		s.mockPublisher.On(
			"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
		).Return(nil).Once()
		err := s.im.Answer(mockCTX, userID, status.ID, status.CurrentQuiz.QuizNo, 0, "")
		s.NoError(err)
	}
	// And USER 9 answers wrong
	userID := "USER9"
	ans := model.TriviaAnswerLog{
		UserID:    userID,
		GameID:    status.ID,
		QuizNo:    status.CurrentQuiz.QuizNo,
		Option:    1,
		Timestamp: int64(1000),
	}
	s.mockPublisher.On(
		"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
	).Return(nil).Once()
	err = s.im.Answer(mockCTX, userID, status.ID, status.CurrentQuiz.QuizNo, 1, "")
	s.NoError(err)

	// Assume 10 users give answer too late
	err = s.redis.Expire(mockCTX, getQuizKey(status.ID, status.CurrentQuiz.QuizNo), 0)
	s.NoError(err)
	for i := 10; i < 20; i++ {
		err := s.im.Answer(mockCTX, fmt.Sprintf("%s %d", "USER", i), status.ID, status.CurrentQuiz.QuizNo, 0, "")
		s.Error(err)
	}

	// the first quiz result
	status, err = s.im.QuizResult(mockCTX, status.ID)
	s.NoError(err)
	s.Equal(int32(9), status.AlivePlayerCount)
	// No one can use medal
	s.Equal(int32(0), status.CurrentQuiz.Options[1].MedalUsedCount)

	// the first quiz end
	status, err = s.im.QuizEnd(mockCTX, status.ID)
	s.NoError(err)
}

func (s *triviaSuite) TestForceStop() {
	// Prepare game data
	games := s.prepareDefaultGame(mockHostID, model.TriviaType_TRIVIA_ON_TV, 3, 2)
	games[0].TotalReward = float32(100.00)
	games[1].TotalReward = float32(100.00)
	_, err := s.im.UpsertTrivia(mockCTX, games[0])
	s.NoError(err)
	_, err = s.im.UpsertTrivia(mockCTX, games[1])
	s.NoError(err)

	// set now to 1000
	s.mockFuncs.On("currentTime").Return(int64(1000))
	s.mockUser.On("GetPlainUsers", mockCTX, "QA").Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil)
	status, err := s.im.GameStart(mockCTX, games[0].ID)
	s.NoError(err)
	s.Equal(model.Status_PREPARE, status.Status)

	// Start the first game
	s.mockLiveHelper.On("MappingRoomIDLegacy", mockCTX, "123").Return("XDXDXD", nil)
	status, err = s.im.QuizStart(mockCTX, games[0].ID)
	s.NoError(err)
	s.Equal(int32(0), status.CurrentQuiz.QuizNo)
	s.Equal(float32(100), status.TotalReward)

	// Assume 10 users answer the first quiz in time
	for i := 0; i < 10; i++ {
		userID := fmt.Sprintf("%s %d", "USER", i)
		ans := model.TriviaAnswerLog{
			UserID:    userID,
			GameID:    status.ID,
			QuizNo:    status.CurrentQuiz.QuizNo,
			Option:    0,
			Timestamp: int64(1000),
		}
		s.mockPublisher.On(
			"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
		).Return(nil).Once()
		err := s.im.Answer(mockCTX, userID, status.ID, status.CurrentQuiz.QuizNo, 0, "")
		s.NoError(err)
	}

	// Assume 10 users give answer too late
	err = s.redis.Expire(mockCTX, getQuizKey(status.ID, status.CurrentQuiz.QuizNo), 0)
	s.NoError(err)
	for i := 10; i < 20; i++ {
		err := s.im.Answer(mockCTX, fmt.Sprintf("%s %d", "USER", i), status.ID, status.CurrentQuiz.QuizNo, 0, "")
		s.Error(err)
	}

	// the first quiz result

	// Replace blog.Event by recording
	status, err = s.im.QuizResult(mockCTX, status.ID)
	s.NoError(err)
	s.Equal(int32(10), status.AlivePlayerCount)

	// the first quiz end
	status, err = s.im.QuizEnd(mockCTX, status.ID)
	s.NoError(err)

	// start the second quiz
	status, err = s.im.QuizStart(mockCTX, games[0].ID)
	s.NoError(err)
	s.Equal(int32(1), status.CurrentQuiz.QuizNo)

	// Assume first 3 users answer the correct option
	for i := 0; i < 3; i++ {
		userID := fmt.Sprintf("%s %d", "USER", i)
		ans := model.TriviaAnswerLog{
			UserID:    userID,
			GameID:    status.ID,
			QuizNo:    status.CurrentQuiz.QuizNo,
			Option:    0,
			Timestamp: int64(1000),
		}
		s.mockPublisher.On(
			"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
		).Return(nil).Once()
		err := s.im.Answer(mockCTX, userID, status.ID, status.CurrentQuiz.QuizNo, 0, "")
		s.NoError(err)
	}

	// the last 7 users answer the wrong option
	for i := 3; i < 10; i++ {
		userID := fmt.Sprintf("%s %d", "USER", i)
		ans := model.TriviaAnswerLog{
			UserID:    userID,
			GameID:    status.ID,
			QuizNo:    status.CurrentQuiz.QuizNo,
			Option:    1,
			Timestamp: int64(1000),
		}
		s.mockPublisher.On(
			"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
		).Return(nil).Once()
		err := s.im.Answer(mockCTX, userID, status.ID, status.CurrentQuiz.QuizNo, 1, "")
		s.NoError(err)
	}

	// the second quiz result
	err = s.redis.Expire(mockCTX, getQuizKey(status.ID, status.CurrentQuiz.QuizNo), 0)
	s.NoError(err)
	status, err = s.im.QuizResult(mockCTX, status.ID)
	s.NoError(err)
	s.Equal(int32(3), status.AlivePlayerCount)

	// the second quiz end
	status, err = s.im.QuizEnd(mockCTX, status.ID)
	s.NoError(err)

	// the last quiz result
	matchBy := func(userID string) bool {
		if "USER 0" == userID || "USER 1" == userID || "USER 2" == userID {
			return true
		}
		return false
	}

	s.mockUser.On("GetDecoratedUsers", mockCTX, mock.MatchedBy(matchBy), mock.MatchedBy(matchBy), mock.MatchedBy(matchBy)).Return([]models.User{
		models.User{
			UserID:      "USER 0",
			DisplayName: "USER 0",
		},
		models.User{
			UserID:      "USER 1",
			DisplayName: "USER 1",
		},
		models.User{
			UserID:      "USER 2",
			DisplayName: "USER 2",
		},
	}, nil).Once()
	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), status.ID, mock.Anything, mock.Anything).Return(nil).Once()
	status, err = s.im.GameResult(mockCTX, status.ID)
	s.NoError(err)
	s.True(status.Result != nil)
	s.Equal(float32(33.4), status.Result.Reward)
	s.Equal(int64(2000), status.Result.NextStartTime)

	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), status.ID, mock.Anything, mock.Anything).Return(nil).Twice()
	status, err = s.im.GameEnd(mockCTX, status.ID)
	s.NoError(err)
}

func (s *triviaSuite) TestGetTriviaVoteDisplayNames() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockHostID).Return(&pgModel.Program{}, nil)

	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)

	for i := 0; i < 13; i++ {
		_, err := s.im.UpsertQuiz(mockCTX, &model.TriviaQuiz{
			Description: fmt.Sprintf("Quiz %d", i+1),
			Level:       model.QuizLevel_EASY,
			Tag:         "Science",
			Answer:      int32(0),
			Options:     []string{"option 0", "option 1", "option 2"},
			DisplayName: fmt.Sprintf("Test %d", i+1),
		})
		s.NoError(err)
	}
	_, err := s.im.UpsertTrivia(mockCTX, &model.TriviaGame{
		ID:            int64(0),
		StartTime:     int64(123456789),
		TotalReward:   100.0,
		QuizCountDown: int32(10),
		HostID:        mockHostID,
		TriviaQuizIDs: []int64{1, 5},
		Currency:      "TWD",
		Region:        "TW",
		SubQuizzes:    nil,
		Type:          model.TriviaType_TRIVIA_ON_TV,
	})
	s.NoError(err)

	resp, err := s.im.GetTriviaVoteDisplayNames(mockCTX, int64(1))
	s.Error(err)
	s.NotNil(resp)
	s.Equal(0, len(resp))

	_, err = s.im.UpsertTrivia(mockCTX, &model.TriviaGame{
		ID:            int64(0),
		StartTime:     int64(123456789),
		TotalReward:   100.0,
		QuizCountDown: int32(10),
		HostID:        mockHostID,
		TriviaQuizIDs: []int64{1, 5},
		Currency:      "TWD",
		Region:        "TW",
		SubQuizzes: []*model.TriviaSubQuiz{
			&model.TriviaSubQuiz{
				MainQuizID: int64(1),
				SubQuizIDs: []int64{2, 3, 4},
			},
			&model.TriviaSubQuiz{
				MainQuizID: int64(5),
				SubQuizIDs: []int64{6, 7, 8},
			},
		},
		Type: model.TriviaType_TRIVIA_ON_TV,
	})
	s.NoError(err)

	resp, err = s.im.GetTriviaVoteDisplayNames(mockCTX, int64(2))
	s.NoError(err)
	s.NotNil(resp)
	s.Equal(2, len(resp))
	s.Equal(int32(0), resp[0].QuizNo)
	s.Equal([]string{"Test 1", "Test 2", "Test 3", "Test 4"}, resp[0].DisplayNames)
	s.Equal(int32(1), resp[1].QuizNo)
	s.Equal([]string{"Test 5", "Test 6", "Test 7", "Test 8"}, resp[1].DisplayNames)
}

func (s *triviaSuite) TestTriviaOnTVCloseMultipleGame() {
	// Prepare game data
	NumOfGame := 3
	gamesOnTV := s.prepareDefaultGame(mockHostID, model.TriviaType_TRIVIA_ON_TV, 3, NumOfGame)
	s.prepareDefaultGame(mockHostID2, model.TriviaType_TRIVIA, 10, NumOfGame)
	_, err := s.im.UpsertTrivia(mockCTX, &model.TriviaGame{ID: 1, StartTime: 1002, TotalReward: float32(2700)})
	s.NoError(err)
	_, err = s.im.UpsertTrivia(mockCTX, &model.TriviaGame{ID: 2, StartTime: 1003, TotalReward: float32(2700)})
	s.NoError(err)
	_, err = s.im.UpsertTrivia(mockCTX, &model.TriviaGame{ID: 3, StartTime: 1001, TotalReward: float32(2700)})
	s.NoError(err)
	gamesOnTV[0].StartTime = 1002
	gamesOnTV[1].StartTime = 1003
	gamesOnTV[2].StartTime = 1001

	_, err = s.im.UpsertTrivia(mockCTX, &model.TriviaGame{ID: 4, StartTime: 4000})
	s.NoError(err)
	_, err = s.im.UpsertTrivia(mockCTX, &model.TriviaGame{ID: 5, StartTime: 5000})
	s.NoError(err)
	_, err = s.im.UpsertTrivia(mockCTX, &model.TriviaGame{ID: 6, StartTime: 6000})
	s.NoError(err)

	expGame, err := s.im.GetTriviaByID(mockCTX, 3, true)
	s.NoError(err)

	s.mockFuncs.On("currentTime").Return(int64(1000)).Once()
	cur, next, err := s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Nil(cur)
	s.Equal(expGame.ID, next.ID)
	for idx, game := range gamesOnTV {
		s.mockFuncs.On("currentTime").Return(int64(1004) + int64(idx)*int64(1000)).Once()
		s.mockUser.On("GetPlainUsers", mockCTX, "QA").Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil).Once()
		status, err := s.im.GameStart(mockCTX, game.ID)
		s.NoError(err)
		s.Equal(model.Status_PREPARE, status.Status)

		s.mockFuncs.On("currentTime").Return(int64(1004) + int64(idx)*int64(1000)).Once()
		s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))
		cur, _, err := s.im.GetLatestGamesByUserID(mockCTX, false)
		s.NoError(err)
		status, err = s.im.GetTriviaStatus(mockCTX, cur.ID, false)
		s.Equal(model.Status_PREPARE, status.Status)

		// Start the first game
		s.mockLiveHelper.On("MappingRoomIDLegacy", mockCTX, "123").Return("XDXDXD", nil)
		status, err = s.im.QuizStart(mockCTX, game.ID)
		s.NoError(err)
		s.Equal(int32(0), status.CurrentQuiz.QuizNo)
		s.Equal(float32(2700), status.TotalReward)

		s.mockFuncs.On("currentTime").Return(int64(1004) + int64(idx)*int64(1000)).Times(10)
		// Assume 10 users answer the first quiz in time
		// Assume USER 0 answer wrong and use the medal in first quiz
		for i := 0; i < 10; i++ {
			userID := fmt.Sprintf("%s %d", "USER", i)
			ans := model.TriviaAnswerLog{
				UserID:    userID,
				GameID:    status.ID,
				QuizNo:    status.CurrentQuiz.QuizNo,
				Option:    0,
				Timestamp: int64(1004) + int64(idx)*int64(1000),
			}
			s.mockPublisher.On(
				"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
			).Return(nil).Once()
			err := s.im.Answer(mockCTX, userID, status.ID, status.CurrentQuiz.QuizNo, 0, "")
			s.NoError(err)
		}

		// Assume 10 users give answer too late
		err = s.redis.Expire(mockCTX, getQuizKey(status.ID, status.CurrentQuiz.QuizNo), 0)
		s.NoError(err)
		for i := 10; i < 20; i++ {
			err := s.im.Answer(mockCTX, fmt.Sprintf("%s %d", "USER", i), status.ID, status.CurrentQuiz.QuizNo, 0, "")
			s.Error(err)
		}

		// the first quiz result

		// Replace blog.Event by recording
		status, err = s.im.QuizResult(mockCTX, status.ID)
		s.NoError(err)
		s.Equal(int32(10), status.AlivePlayerCount)

		// the first quiz end
		status, err = s.im.QuizEnd(mockCTX, status.ID)
		s.NoError(err)

		// start the second quiz
		status, err = s.im.QuizStart(mockCTX, game.ID)
		s.NoError(err)
		s.Equal(int32(1), status.CurrentQuiz.QuizNo)

		s.mockFuncs.On("currentTime").Return(int64(1004) + int64(idx)*int64(1000)).Times(3)
		// Assume first 5 users answer the correct option
		for i := 0; i < 3; i++ {
			userID := fmt.Sprintf("%s %d", "USER", i)
			ans := model.TriviaAnswerLog{
				UserID:    userID,
				GameID:    status.ID,
				QuizNo:    status.CurrentQuiz.QuizNo,
				Option:    0,
				Timestamp: int64(1004) + int64(idx)*int64(1000),
			}
			s.mockPublisher.On(
				"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
			).Return(nil).Once()
			err := s.im.Answer(mockCTX, userID, status.ID, status.CurrentQuiz.QuizNo, 0, "")
			s.NoError(err)
		}

		s.mockFuncs.On("currentTime").Return(int64(1004) + int64(idx)*int64(1000)).Times(7)
		// the last 7 users answer the wrong option
		for i := 3; i < 10; i++ {
			userID := fmt.Sprintf("%s %d", "USER", i)
			ans := model.TriviaAnswerLog{
				UserID:    userID,
				GameID:    status.ID,
				QuizNo:    status.CurrentQuiz.QuizNo,
				Option:    1,
				Timestamp: int64(1004) + int64(idx)*int64(1000),
			}
			s.mockPublisher.On(
				"Publish", mock.AnythingOfType("ctx.CTX"), ans, mock.Anything, mock.Anything,
			).Return(nil).Once()
			err := s.im.Answer(mockCTX, userID, status.ID, status.CurrentQuiz.QuizNo, 1, "")
			s.NoError(err)
		}

		// the second quiz result
		err = s.redis.Expire(mockCTX, getQuizKey(status.ID, status.CurrentQuiz.QuizNo), 0)
		s.NoError(err)
		status, err = s.im.QuizResult(mockCTX, status.ID)
		s.NoError(err)
		s.Equal(int32(3), status.AlivePlayerCount)

		// the second quiz end
		status, err = s.im.QuizEnd(mockCTX, status.ID)
		s.NoError(err)

		// the last quiz result
		matchBy := func(userID string) bool {
			if "USER 0" == userID || "USER 1" == userID || "USER 2" == userID {
				return true
			}
			return false
		}

		s.mockUser.On("GetDecoratedUsers", mockCTX, mock.MatchedBy(matchBy), mock.MatchedBy(matchBy), mock.MatchedBy(matchBy)).Return([]models.User{
			models.User{
				UserID:      "USER 0",
				DisplayName: "USER 0",
			},
			models.User{
				UserID:      "USER 1",
				DisplayName: "USER 1",
			},
			models.User{
				UserID:      "USER 2",
				DisplayName: "USER 2",
			},
		}, nil).Once()

		s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), status.ID, mock.Anything, mock.Anything).Return(nil).Once()
		// get from cache after first time
		if idx == 0 {
			s.mockFuncs.On("currentTime").Return(int64(1004) + int64(idx)*int64(1000)).Once()
		}
		status, err = s.im.GameResult(mockCTX, status.ID)
		s.NoError(err)
		s.True(status.Result != nil)

		s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), status.ID, mock.Anything, mock.Anything).Return(nil).Twice()
		status, err = s.im.GameEnd(mockCTX, status.ID)
		s.NoError(err)

		s.mockFuncs.On("currentTime").Return(int64(1004) + int64(idx)*int64(1000)).Once()
		s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))
		cur, _, err = s.im.GetLatestGamesByUserID(mockCTX, false)
		s.NoError(err)
		status, err = s.im.GetTriviaStatus(mockCTX, cur.ID, false)
		s.Equal(model.Status_GAMEEND, status.Status)
	}

	now := int64(4000)
	s.mockFuncs.On("currentTime").Return(now).Twice()

	_, err = s.im.GameClose(mockCTX, gamesOnTV[NumOfGame-1].ID)
	s.NoError(err)
	for _, game := range gamesOnTV {
		ts, err := s.im.GetTriviaStatus(mockCTX, game.ID, false)
		s.NoError(err)
		s.Equal(model.Status_CLOSED, ts.Status)
	}
	s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_DONTCARE.String()))

	cur, next, err = s.im.GetLatestGamesByUserID(mockCTX, false)
	s.NoError(err)
	s.Nil(cur)
	s.Equal(int64(4), next.ID)

	fromTime := strconv.FormatInt(now-2*60*60, 10)
	toTime := strconv.FormatInt(now, 10)
	gs, err := s.im.persist.ZRangeByScoreWithScores(mockCTX, getGameListKey(), fromTime, toTime)
	for _, ts := range gs {
		gameID, err := strconv.ParseInt(ts.Value, 10, 64)
		s.NoError(err)
		_, err = s.im.GetTriviaStatus(mockCTX, gameID, false)
		s.Equal(fmt.Errorf("redigo: nil returned"), err)
	}
}

func (s *triviaSuite) TestTriviaOnTVGameCloseWithMutipleHosts() {
	// ID / startTime
	// 1 / 1000
	// 2 / 2000
	// 3 / 3000
	// 4 / 1000
	// 5 / 2000
	// 6 / 3000
	gamesOnTVHost1 := s.prepareDefaultGame(mockHostID, model.TriviaType_TRIVIA_ON_TV, 1, 3)
	gamesOnTVHost2 := s.prepareDefaultGame(mockHostID2, model.TriviaType_TRIVIA_ON_TV, 1, 3)

	for _, game := range gamesOnTVHost1 {
		s.mockUser.On("GetPlainUsers", mockCTX, game.HostID).Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil).Once()
		s.mockFuncs.On("currentTime").Return(int64(1000)).Once()
		_, err := s.im.GameStart(mockCTX, game.ID)
		s.NoError(err)

		// check status
		status, err := s.im.GetTriviaStatus(mockCTX, game.ID, false)
		s.NoError(err)
		s.Equal(model.Status_PREPARE, status.Status)
	}

	for _, game := range gamesOnTVHost2 {
		s.mockUser.On("GetPlainUsers", mockCTX, game.HostID).Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil).Once()
		s.mockFuncs.On("currentTime").Return(int64(1000)).Once()
		_, err := s.im.GameStart(mockCTX, game.ID)
		s.NoError(err)

		// check status
		status, err := s.im.GetTriviaStatus(mockCTX, game.ID, false)
		s.NoError(err)
		s.Equal(model.Status_PREPARE, status.Status)
	}

	s.mockFuncs.On("currentTime").Return(int64(3000))
	_, err := s.im.GameClose(mockCTX, gamesOnTVHost1[2].ID)
	s.NoError(err)

	// only Host1's games should be closed
	for _, game := range gamesOnTVHost1 {
		ts, err := s.im.GetTriviaStatus(mockCTX, game.ID, false)
		s.NoError(err)
		s.Equal(model.Status_CLOSED, ts.Status)
	}

	for _, game := range gamesOnTVHost2 {
		ts, err := s.im.GetTriviaStatus(mockCTX, game.ID, false)
		s.NoError(err)
		s.Equal(model.Status_PREPARE, ts.Status)
	}
}

func (s *triviaSuite) TestGetTriviaPersonalInfo() {
	triviaInfo := &userModel.TriviaInfo{
		DeathExemptionMedal: 3,
	}
	triviaPersonalInfo := &model.TriviaUserInfo{
		DeathExemptionMedal:    3,
		Currency:               "NTD",
		MedalUsableBeforeNQuiz: 11,
		ThisMonthCorrect:       100,
		ThisWeekCorrect:        20,
		LastWeekCorrect:        27,
		ThisMonthReward:        float32(123.4),

		LegacyDeathExemptionMedal:    3,
		LegacyCurrency:               "NTD",
		LegacyMedalUsableBeforeNQuiz: 11,
		LegacyThisMonthCorrect:       100,
		LegacyThisMonthReward:        123,
	}
	triviaEmptyRegionPersonalInfo := &model.TriviaUserInfo{
		DeathExemptionMedal:    3,
		Currency:               "USD",
		MedalUsableBeforeNQuiz: 11,

		LegacyDeathExemptionMedal:    3,
		LegacyCurrency:               "USD",
		LegacyMedalUsableBeforeNQuiz: 11,
	}
	s.mockleaderboard.On("GetScore", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_REWARD, lmodel.Period_MONTH, mock.AnythingOfType("time.Time"), mockUserID, leaderboard.TablePlatform, "TW", 0).Return(1234, nil).Once()
	s.mockleaderboard.On("GetScore", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_CORRECT, lmodel.Period_MONTH, mock.AnythingOfType("time.Time"), mockUserID, leaderboard.TablePlatform, "TW", 0).Return(100, nil).Once()
	s.mockleaderboard.On("GetScore", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_CORRECT, lmodel.Period_WEEK, mock.AnythingOfType("time.Time"), mockUserID, leaderboard.TablePlatform, "TW", 0).Return(20, nil).Once()
	s.mockleaderboard.On("GetScore", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_CORRECT, lmodel.Period_WEEK, mock.AnythingOfType("time.Time"), mockUserID, leaderboard.TablePlatform, "TW", -1).Return(27, nil).Once()
	s.mockUser.On("GetTriviaPersonInfo", mock.AnythingOfType("ctx.CTX"), mockUserID).Return(triviaInfo, nil)
	testConf.Referral.QuizNo = 11

	// empty region
	getRegionByHostID = func(region string) string {
		return ""
	}
	info, err := s.im.GetTriviaPersonalInfo(mockCTX, mockUserID, mockUserID2)
	s.NoError(err)
	s.Equal(triviaEmptyRegionPersonalInfo, info)

	// success
	getRegionByHostID = func(region string) string {
		return "TW"
	}
	info, err = s.im.GetTriviaPersonalInfo(mockCTX, mockUserID, mockUserID2)
	s.NoError(err)
	s.Equal(triviaPersonalInfo, info)
}

func (s *triviaSuite) TestGetCurrentStatus() {
	mockRoomID := "1234"
	mockQuizNo := int32(3)

	// prepare game
	games := s.prepareDefaultGame(mockUserID, model.TriviaType_TRIVIA_ON_TV, 5, 1)
	game := games[0]

	// set now to 1000
	s.mockFuncs.On("currentTime").Return(int64(1000)).Once()
	s.mockUser.On("GetPlainUsers", mockCTX, mockUserID).Return([]models.User{models.User{RoomID: 123, Region: "TW"}}, nil).Once()
	_, err := s.im.GameStart(mockCTX, game.ID)
	s.NoError(err)

	// prepare GameStatus
	status := model.GameStatus{
		Status: model.Status_QUIZSTART,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: mockQuizNo,
			Options: []*model.QuizOption{
				&model.QuizOption{},
				&model.QuizOption{},
				&model.QuizOption{},
				&model.QuizOption{},
			},
		},
		ID:          game.ID,
		TotalReward: game.TotalReward,
		RoomID:      mockRoomID,
	}

	// set count
	key := getQuizOptKey(game.ID, mockQuizNo, 0)
	err = s.redis.SAdd(mockCTX, key, "test1")
	s.Require().NoError(err)
	key = getQuizOptKey(game.ID, mockQuizNo, 3)
	err = s.redis.SAdd(mockCTX, key, "test2")
	s.Require().NoError(err)

	s.mockLiveHelper.On("GetCurrentViewerCount", mock.AnythingOfType("ctx.CTX"), mockRoomID).Return(int32(100), nil)
	s.mockFuncs.On("currentTime").Return(int64(1000))

	tests := []struct {
		Desc       string
		VoteStatus voteModel.ResVoteInfoExt
		GameStatus model.Status
		ResType    model.LookType
	}{
		{
			Desc: "vote, VOTESTART",
			VoteStatus: voteModel.ResVoteInfoExt{
				Status: voteModel.StatusExtVoteStart,
			},
			GameStatus: model.Status_QUIZSTART,
			ResType:    model.LookType_VOTEINFO,
		},
		{
			Desc: "vote, VOTEEND",
			VoteStatus: voteModel.ResVoteInfoExt{
				Status: voteModel.StatusExtVoteEnd,
			},
			GameStatus: model.Status_QUIZSTART,
			ResType:    model.LookType_VOTEINFO,
		},
		{
			Desc: "gamestatus, VOTECLOSE",
			VoteStatus: voteModel.ResVoteInfoExt{
				Status: voteModel.StatusExtVoteClose,
			},
			GameStatus: model.Status_QUIZSTART,
			ResType:    model.LookType_GAMESTATUS,
		},
	}
	for _, test := range tests {
		// set trivia status
		key = getStatusKey(game.ID)
		status.Status = test.GameStatus
		bytes, err := json.Marshal(&status)
		s.NoError(err)
		err = s.redis.Set(mockCTX, key, bytes, time.Minute)
		s.Require().NoError(err)

		// set vote status
		s.mockVoteAdapter.On("GetVoteCurrentResult", mock.AnythingOfType("ctx.CTX"), voteModel.VoteTypeExtTriviaOnTv, mockRoomID).Return(&test.VoteStatus, nil).Once()

		cs, err := s.im.GetCurrentStatus(mockCTX, mockUserID)
		s.NoError(err, test.Desc)
		s.Equal(test.ResType.String(), cs.Type.String(), test.Desc)
	}
}

func (s *triviaSuite) TestGameEndWithTriviaCorrect() {
	gameStatus := &model.GameStatus{
		ID:     int64(1),
		Status: model.Status_GAMERESULT,
		Result: &model.TriviaResult{
			Winners: []*userModel.DisplayInfo{
				&userModel.DisplayInfo{
					UserID: "test1",
				},
			},
			WinnerIDs: []string{"test1"},
			Reward:    100.00,
			Currency:  "TWD",
		},
		TotalQuiz:   int32(2),
		Region:      "TW",
		Currency:    "TWD",
		TotalReward: 100.00,
		Type:        model.TriviaType_TRIVIA,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, gameStatus))

	optKey := getQuizOptKey(int64(1), int32(0), 0)
	s.NoError(s.redis.SAdd(mockCTX, optKey, []string{"test1", "test2", "test3", "test4"}...))
	optKey = getQuizOptKey(int64(1), int32(0), 1)
	s.NoError(s.redis.SAdd(mockCTX, optKey, []string{"test5", "test6"}...))
	optKey = getQuizOptKey(int64(1), int32(0), 2)
	s.NoError(s.redis.SAdd(mockCTX, optKey, []string{"test7"}...))
	optKey = getQuizOptKey(int64(1), int32(0), 3)
	s.NoError(s.redis.SAdd(mockCTX, optKey, []string{"test8", "test9", "test10"}...))
	// set answer to index 0
	s.NoError(s.redis.Set(mockCTX, getQuizAnsKey(int64(1), int32(0)), []byte("0"), time.Minute))

	optKey = getQuizOptKey(int64(1), int32(1), 0)
	s.NoError(s.redis.SAdd(mockCTX, optKey, []string{"test3"}...))
	optKey = getQuizOptKey(int64(1), int32(1), 1)
	s.NoError(s.redis.SAdd(mockCTX, optKey, []string{"test1"}...))
	optKey = getQuizOptKey(int64(1), int32(1), 2)
	s.NoError(s.redis.SAdd(mockCTX, optKey, []string{"test2"}...))
	optKey = getQuizOptKey(int64(1), int32(1), 3)
	s.NoError(s.redis.SAdd(mockCTX, optKey, []string{"test4"}...))
	// ser answer to index 1
	s.NoError(s.redis.Set(mockCTX, getQuizAnsKey(int64(1), int32(1)), []byte("1"), time.Minute))

	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), int64(1), mock.Anything, mock.Anything).Return(nil).Twice()
	_, err := s.im.GameEnd(mockCTX, int64(1))
	s.NoError(err)
}

func (s *triviaSuite) TestGetLatestGamesOrder() {
	s.prepareDefaultGame(mockUserID, model.TriviaType_TRIVIA_ON_TV, 1, 5)
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"1": 1}))
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"2": 1}))
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"3": 1}))
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"4": 1}))
	s.Require().NoError(s.redis.ZAdd(mockCTX, getGameListKey(), map[string]int{"5": 1}))

	// Order get from z
	games := []int64{2, 3, 5, 1, 4}
	ast := []int64{1, 2, 3, 4, 5}
	for idx, gameID := range games {
		s.mockFuncs.On("currentTime").Return(int64(ast[idx])).Once()

		b := []byte(strconv.FormatInt(int64(ast[idx]), 10))
		s.Require().NoError(s.redis.SetNXLegacy(mockCTX, getActualStartTimeKey(gameID), b, time.Hour))

		game := &model.GameStatus{
			ID:          gameID,
			TotalReward: float32(100),
			TotalQuiz:   2,
			CurrentQuiz: &model.CurrentQuiz{
				QuizNo: 0,
				Options: []*model.QuizOption{
					&model.QuizOption{
						IsAnswer: true,
					},
					&model.QuizOption{},
					&model.QuizOption{},
				},
			},
			Status: model.Status_PREPARE,
			Type:   model.TriviaType_TRIVIA_ON_TV,
			Region: "TW",
		}

		err := s.im.saveTriviaStatus(mockCTX, game)
		s.NoError(err)
		s.Require().NoError(s.im.pager.Update(mockCTX, model.TriviaType_TRIVIA_ON_TV.String()))
		cur, _, err := s.im.GetLatestGamesByRegion(mockCTX, false, "TW", model.TriviaType_TRIVIA_ON_TV)
		s.NoError(err)
		s.Equal(gameID, cur.ID)
	}
}

func (s *triviaSuite) TestGameResultResponse() {
	s.mockProgram.On("GetProgramIndex", mockCTX, mockHostID).Return(&pgModel.Program{}, nil)
	s.mockleaderboard.On("IsSupportedRegion", mockCTX, "TW").Return(true)

	game := &model.TriviaGame{
		ID:            int64(0),
		TotalReward:   100.00,
		Currency:      "TWD",
		Region:        "TW",
		HostID:        mockHostID,
		TriviaQuizIDs: []int64{1},
	}

	game2 := &model.TriviaGame{
		ID:            int64(0),
		TotalReward:   100.00,
		Currency:      "TWD",
		Region:        "TW",
		HostID:        mockHostID,
		TriviaQuizIDs: []int64{1},
		Type:          model.TriviaType_TRIVIA_ON_TV,
	}
	_, err := s.im.UpsertTrivia(mockCTX, game)
	s.NoError(err)

	_, err = s.im.UpsertTrivia(mockCTX, game2)
	s.NoError(err)

	gameStatus := &model.GameStatus{
		ID:        int64(1),
		Status:    model.Status_QUIZEND,
		TotalQuiz: int32(1),
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: int32(0),
		},
		Region:      "TW",
		Currency:    "TWD",
		TotalReward: 100.00,
		Type:        model.TriviaType_TRIVIA,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, gameStatus))

	// set 400 survivors
	userIDs := []string{}
	userInfos := []models.User{}
	mocks := []interface{}{mock.AnythingOfType("ctx.CTX")}
	for i := 0; i < 400; i++ {
		userID := fmt.Sprintf("%s %d", "USER", i)
		userIDs = append(userIDs, userID)
		if i < 200 {
			userInfos = append(userInfos, models.User{
				UserID: userID,
			})
			mocks = append(mocks, interface{}(mock.AnythingOfType("string")))
		}
	}

	s.NoError(s.redis.Set(mockCTX, getQuizSurvivorKey(int64(1), int32(0)), []byte("1:0"), time.Second))
	s.NoError(s.redis.SAdd(mockCTX, "1:0", userIDs...))

	userCall := s.mockUser.On("GetDecoratedUsers", mocks...)
	userCall.Return(userInfos, nil).Once()

	s.mockPublisher.On("Publish", mockCTX, int64(1), mock.Anything, mock.Anything).Return(nil).Once()
	s.mockFuncs.On("currentTime").Return(int64(1000))

	resp, err := s.im.GameResult(mockCTX, int64(1))
	s.NoError(err)
	s.Equal(200, len(resp.Result.Winners))
	s.Equal(200, len(resp.Result.WinnerIDs))
	s.Equal(int32(400), resp.Result.WinnersCount)
	s.Equal(float32(1), resp.Result.Reward)

	// test redis status
	gs, err := s.im.GetTriviaStatus(mockCTX, int64(1), false)
	s.NoError(err)
	s.Len(gs.Result.WinnerIDs, 400)
	s.Len(gs.Result.Winners, 200)
	s.Equal(400, int(gs.Result.WinnersCount))

	gameStatus = &model.GameStatus{
		ID:        int64(2),
		Status:    model.Status_QUIZEND,
		TotalQuiz: int32(1),
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: int32(0),
		},
		Region:      "TW",
		Currency:    "TWD",
		TotalReward: 100.00,
		Type:        model.TriviaType_TRIVIA_ON_TV,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, gameStatus))

	s.NoError(s.redis.Set(mockCTX, getQuizSurvivorKey(int64(2), int32(0)), []byte("2:0"), time.Second))
	s.NoError(s.redis.SAdd(mockCTX, "2:0", userIDs...))

	userCall = s.mockUser.On("GetDecoratedUsers", mocks...)
	userCall.Return(userInfos, nil).Once()
	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), int64(2), mock.Anything, mock.Anything).Return(nil).Once()

	resp, err = s.im.GameResult(mockCTX, int64(2))
	s.NoError(err)
	s.Equal(200, len(resp.Result.Winners))
	s.Equal(200, len(resp.Result.WinnerIDs))
	s.Equal(int32(400), resp.Result.WinnersCount)
	s.Equal(float32(0.3), resp.Result.Reward)

	// test redis status
	gs, err = s.im.GetTriviaStatus(mockCTX, int64(2), false)
	s.NoError(err)
	s.Len(gs.Result.WinnerIDs, 400)
	s.Len(gs.Result.Winners, 200)
	s.Equal(400, int(gs.Result.WinnersCount))
}

func (s *triviaSuite) TestGameEndWithManyUsers() {
	mgs := model.GameStatus{
		Status:   model.Status_GAMERESULT,
		IsFinish: true,
		Result: &model.TriviaResult{
			Winners:   []*userModel.DisplayInfo{},
			WinnerIDs: []string{},
			Reward:    100.0,
		},
		Currency: "NTD",
		Region:   "TW",
		// workarround for ignore leaderboard set
		Type: model.TriviaType_TRIVIA_ON_TV,
	}
	for i := 0; i < 400; i++ {
		userID := fmt.Sprintf("%s %d", "USER", i)
		mgs.Result.WinnerIDs = append(mgs.Result.WinnerIDs, userID)
		if i < 200 {
			mgs.Result.Winners = append(mgs.Result.Winners, &userModel.DisplayInfo{
				UserID: userID,
			})
		}
	}
	tr := s.setupTrivia(&mgs)

	s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), tr.ID, mock.Anything, mock.Anything).Return(nil).Twice()
	resp, err := s.im.GameEnd(mockCTX, tr.ID)
	s.NoError(err)
	s.Len(resp.Result.Winners, 200)
	s.Len(resp.Result.WinnerIDs, 200)

	// test redis status
	gs, err := s.im.GetTriviaStatus(mockCTX, tr.ID, false)
	s.NoError(err)
	s.Len(gs.Result.WinnerIDs, 400)
	s.Len(gs.Result.Winners, 200)
}

func (s *triviaSuite) TestSendMedal() {
	// no status should failed
	err := s.im.SendMedal(mockCTX, int64(1), mockHostID)
	s.EqualError(err, ErrInvalidAction.Error())

	gameStatus := &model.GameStatus{
		ID:        int64(1),
		Status:    model.Status_QUIZSTART,
		Type:      model.TriviaType_TRIVIA,
		TotalQuiz: int32(10),
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, gameStatus))
	err = s.im.SendMedal(mockCTX, int64(1), mockHostID)
	s.EqualError(err, ErrInvalidAction.Error())

	testConf.MedalActivity.Countdown = int32(5)
	testConf.MedalActivity.SendRateLimit = int32(1)
	testConf.MedalActivity.MaxAcceptDelay = int32(1)

	gameStatus = &model.GameStatus{
		ID:        int64(1),
		Status:    model.Status_GAMEEND,
		RoomID:    "12345",
		Type:      model.TriviaType_TRIVIA,
		TotalQuiz: int32(10),
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, gameStatus))

	s.mockFuncs.On("currentTime").Return(int64(12345678)).Once()
	nonce := "6UB4VdMz"
	s.mockMessenger.On("Publish", mock.AnythingOfType("ctx.CTX"), "12345", &models.Message{
		Type: models.MsgType_TRIVIA_MEDAL,
		DeathExemptionMedalInfo: &model.DeathExemptionMedalInfo{
			Countdown: int32(5),
			MedalKey:  nonce,
			Timestamp: int64(12345678),
			GameID:    int64(1),
		},
	}).Return(float64(0), nil)
	s.NoError(s.im.SendMedal(mockCTX, int64(1), mockHostID))

	// cannot not send in one second
	err = s.im.SendMedal(mockCTX, int64(1), mockHostID)
	s.EqualError(err, ErrInvalidAction.Error())

	time.Sleep(time.Second)
	s.mockFuncs.On("currentTime").Return(int64(12345682)).Once()
	nonce = "x1CGN0sI"
	s.mockMessenger.On("Publish", mock.AnythingOfType("ctx.CTX"), "12345", &models.Message{
		Type: models.MsgType_TRIVIA_MEDAL,
		DeathExemptionMedalInfo: &model.DeathExemptionMedalInfo{
			Countdown: int32(5),
			MedalKey:  nonce,
			Timestamp: int64(12345682),
			GameID:    int64(1),
		},
	}).Return(float64(0), fmt.Errorf("error"))
	s.Error(s.im.SendMedal(mockCTX, int64(1), mockHostID))

	// because we had already deleted sendMedalLimitKey, this time would work without waiting 1 second
	s.mockFuncs.On("currentTime").Return(int64(12345683)).Once()
	nonce = "qB0ecoGM"
	s.mockMessenger.On("Publish", mock.AnythingOfType("ctx.CTX"), "12345", &models.Message{
		Type: models.MsgType_TRIVIA_MEDAL,
		DeathExemptionMedalInfo: &model.DeathExemptionMedalInfo{
			Countdown: int32(5),
			MedalKey:  nonce,
			Timestamp: int64(12345683),
			GameID:    int64(1),
		},
	}).Return(float64(0), nil)
	s.NoError(s.im.SendMedal(mockCTX, int64(1), mockHostID))
}

func (s *triviaSuite) TestAcceptMedalWithMedalPatch() {
	testConf.MedalActivity.MaxAcceptDelay = int32(1)
	s.mockFuncs.On("currentTime").Return(int64(12345675))

	// out of delay time
	err := s.im.AcceptMedal(mockCTX, int64(1), "test1", "testsign", int64(12345670))
	s.EqualError(err, ErrInvalidAction.Error())

	// invalid sign
	sign := "923b084f0307cc9d90c9224e4c9611602e7ac3a89147a8d9244e139220d266ef"
	err = s.im.AcceptMedal(mockCTX, int64(1), "test1", sign, int64(12345677))
	s.EqualError(err, ErrInvalidAction.Error())

	// can get for the first time
	sign = "923b084f0307cc9d90c9224e4c9611602e7ac3a89147a8d9244e139220d266ef"
	s.mockUser.On("AtomicPatchTriviaPersonInfo", mockCTX, "test1", int32(1), int32(0)).Return(nil).Once()
	s.NoError(s.im.AcceptMedal(mockCTX, int64(1), "test1", sign, int64(12345674)))

	// cannot get for the second time
	sign = "923b084f0307cc9d90c9224e4c9611602e7ac3a89147a8d9244e139220d266ef"
	err = s.im.AcceptMedal(mockCTX, int64(1), "test1", sign, int64(12345674))
	s.EqualError(err, ErrInvalidAction.Error())

	// another two people get model
	sign = "e1bb3c92f4d42356e528379cf571af14d66ecc6dbae373c06d1d507da2768856"
	s.mockUser.On("AtomicPatchTriviaPersonInfo", mockCTX, "test2", int32(1), int32(0)).Return(nil).Once()
	s.NoError(s.im.AcceptMedal(mockCTX, int64(1), "test2", sign, int64(12345674)))

	sign = "24486967b259847d337445dc6a8008e7b4aef3dcb8b15431674ae9f552518825"
	s.mockUser.On("AtomicPatchTriviaPersonInfo", mockCTX, "test3", int32(1), int32(0)).Return(nil).Once()
	s.NoError(s.im.AcceptMedal(mockCTX, int64(1), "test3", sign, int64(12345674)))
}

func (s *triviaSuite) TestMissionCorrectDayTime() {
	// Prepare data 1: trivia status
	gameID := int64(1)
	gs := &model.GameStatus{
		ID: gameID,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: int32(2),
		},
		StartTime: 1528282799,
		Region:    "TW",
		Type:      model.TriviaType_TRIVIA,
		HostID:    "andy",
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, gs))
	// Prepare data 2: answer users
	members := []string{"user1", "user2", "user3"}
	for quizNo := int32(0); quizNo <= gs.CurrentQuiz.QuizNo; quizNo++ {
		quizKey := getQuizOptKey(gameID, quizNo, 0)
		key1 := getQuizSurvivorKey(gameID, quizNo)
		err := s.redis.Set(mockCTX, key1, []byte(quizKey), time.Minute)
		s.NoError(err)
		for i := 0; i < 3-int(quizNo); i++ {
			s.NoError(s.redis.SAdd(mockCTX, quizKey, members[i]))
		}
		d := missionModel.MissionParams{
			UserID: members[int(quizNo)],
			Value:  3 - quizNo,
			Region: "TW",
			Type:   missionModel.Type_TriviaDaytimeCorrect,
			RefID:  "andy",
		}
		s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), &d, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
	}

	// OK
	err := s.im.MissionCorrect(mockCTX, []byte(strconv.FormatInt(gameID, 10)), queueModel.CallbackOption{PublishTime: time.Now()})
	s.NoError(err)
}

func (s *triviaSuite) TestMissionCorrectNight() {
	// Prepare data 1: trivia status
	gameID := int64(1)
	gs := &model.GameStatus{
		ID: gameID,
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: int32(2),
		},
		StartTime: 1528282800,
		Region:    "TW",
		Type:      model.TriviaType_TRIVIA,
		HostID:    "andy",
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, gs))
	// Prepare data 2: answer users
	members := []string{"user1", "user2", "user3"}
	for quizNo := int32(0); quizNo <= gs.CurrentQuiz.QuizNo; quizNo++ {
		quizKey := getQuizOptKey(gameID, quizNo, 0)
		key1 := getQuizSurvivorKey(gameID, quizNo)
		err := s.redis.Set(mockCTX, key1, []byte(quizKey), time.Minute)
		s.NoError(err)
		for i := 0; i < 3-int(quizNo); i++ {
			s.NoError(s.redis.SAdd(mockCTX, quizKey, members[i]))
		}
		d := missionModel.MissionParams{
			UserID: members[int(quizNo)],
			Value:  3 - quizNo,
			Region: "TW",
			Type:   missionModel.Type_TriviaNightCorrect,
			RefID:  "andy",
		}
		s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), &d, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
	}

	// OK
	err := s.im.MissionCorrect(mockCTX, []byte(strconv.FormatInt(gameID, 10)), queueModel.CallbackOption{PublishTime: time.Now()})
	s.NoError(err)
}

func (s *triviaSuite) TestMissionPlay() {
	// Prepare data 1: trivia status
	gameID := int64(1)
	gs := &model.GameStatus{
		ID:        gameID,
		StartTime: 1528282800,
		Region:    "TW",
		Type:      model.TriviaType_TRIVIA_ON_TV,
		HostID:    "andy",
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, gs))
	// Prepare data 2: answer users
	members := []string{"user1", "user2", "user3"}
	quizKey := getQuizOptKey(gameID, int32(0), 0)
	for i := 0; i < 3; i++ {
		s.NoError(s.redis.SAdd(mockCTX, quizKey, members[i]))
	}

	for _, member := range members {
		data := missionModel.MissionParams{
			UserID: member,
			Value:  int32(1),
			Region: "TW",
			Type:   missionModel.Type_TriviaOnTVPlay,
			RefID:  "andy",
		}
		s.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), &data, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
	}
	// OK
	err := s.im.MissionPlay(mockCTX, []byte(strconv.FormatInt(gameID, 10)), queueModel.CallbackOption{PublishTime: time.Now()})
	s.NoError(err)
}

func (s *triviaSuite) TestGetExtraInfo() {
	testConf.Hosts = map[string]map[string]triviaConfig.HostInfo{
		"TRIVIA_ON_TV": map[string]triviaConfig.HostInfo{
			"andyTrivia": triviaConfig.HostInfo{
				DisclaimerTitle: &i18nModel.Token{
					Key: "key1",
				},
				DisclaimerText: &i18nModel.Token{
					Key: "key2",
				},
				LeaveGameToastText: &i18nModel.Token{
					Key: "key3",
				},
				VoteTopic: &i18nModel.Token{
					Key: "key4",
				},
				ShareWording: &i18nModel.Token{
					Key: "key5",
				},
			},
		},
	}

	_, err := s.im.GetTriviaExtraInfo(mockCTX, int64(1), "andy")
	s.Error(err)

	gameStatus := &model.GameStatus{
		ID:     int64(1),
		RoomID: "2000",
		HostID: "andyTrivia",
		Type:   model.TriviaType_TRIVIA_ON_TV,
	}
	s.NoError(s.im.saveTriviaStatus(mockCTX, gameStatus))
	extraInfo, err := s.im.GetTriviaExtraInfo(mockCTX, int64(1), "andy")
	s.True(extraInfo.ShouldShowDisclaimer)

	s.NoError(s.im.RecordDisclaimer(mockCTX, "andy", "2000"))
	extraInfo, err = s.im.GetTriviaExtraInfo(mockCTX, int64(1), "andy")
	s.True(!extraInfo.ShouldShowDisclaimer)
	s.Equal("key1", extraInfo.DisclaimerTitle.Key)
	s.Equal("key2", extraInfo.DisclaimerText.Key)
	s.Equal("key3", extraInfo.LeaveGameToastText.Key)
	s.Equal("key4", extraInfo.VoteTopic.Key)
	s.Equal("key5", extraInfo.ShareWording.Key)
}

func (s *triviaSuite) TestGetTriviaHosts() {
	testConf.Hosts = map[string]map[string]triviaConfig.HostInfo{
		"TRIVIA_ON_TV": map[string]triviaConfig.HostInfo{
			"andyTrivia":  triviaConfig.HostInfo{},
			"andyTrivia2": triviaConfig.HostInfo{},
			"andyTrivia3": triviaConfig.HostInfo{},
		},
	}

	s.mockProgram.On("List", mockCTX, "andyTrivia", 1, 0, false).Return([]*pgModel.Program{
		&pgModel.Program{
			CreatorID: "andyTrivia",
			Name:      "答題搶獎金",
		},
	}, nil)

	s.mockProgram.On("List", mockCTX, "andyTrivia2", 1, 0, false).Return([]*pgModel.Program{
		&pgModel.Program{
			CreatorID: "andyTrivia2",
			Name:      "答題搶獎金2",
		},
	}, nil)

	s.mockProgram.On("List", mockCTX, "andyTrivia3", 1, 0, false).Return([]*pgModel.Program{}, fmt.Errorf("error"))

	triviaHosts := s.im.GetTriviaHosts(mockCTX, "TW", model.TriviaType_TRIVIA_ON_TV)
	s.Equal(2, len(triviaHosts))
	if triviaHosts[0].HostID == "andyTrivia" {
		s.Equal("答題搶獎金", triviaHosts[0].Name)
		s.Equal("答題搶獎金2", triviaHosts[1].Name)
	} else if triviaHosts[0].HostID == "andyTrivia2" {
		s.Equal("答題搶獎金2", triviaHosts[0].Name)
		s.Equal("答題搶獎金", triviaHosts[1].Name)
	}
}

func (s *triviaSuite) TestAfterTradeFunc() {
	afterTradeFunc := getAfterTradeFunc(
		mockCTX, int64(1), []string{mockUserID, mockUserID2}, int64(12345678), int64(1),
	)

	tx := s.db.MustBegin()
	err := afterTradeFunc(tx, "tradeID-aaa", []string{"dealID1", "dealID2"}, int64(1))
	s.NoError(err)
	err = tx.Commit()
	s.NoError(err)

	// // Check GameResult in MySQL
	gameResult := []model.DBGameResult{}
	err = s.db.Select(&gameResult, "SELECT * FROM TriviaGameResult")
	s.NoError(err)
	s.Equal(2, len(gameResult))
	s.Equal(mockUserID, gameResult[0].UserID)
	s.Equal(int64(12345678), gameResult[0].Reward)
	s.Equal("tradeID-aaa", gameResult[0].TradeID)
	s.Equal("dealID1", gameResult[0].DealingID)
	s.Equal(mockUserID2, gameResult[1].UserID)
	s.Equal(int64(12345678), gameResult[1].Reward)
	s.Equal("tradeID-aaa", gameResult[1].TradeID)
	s.Equal("dealID2", gameResult[1].DealingID)
}

func (s *triviaSuite) TestGetTriviaMedalInfo() {
	s.mockUser.On("GetTriviaPersonInfo", mockCTX, mockUserID).Return(&userModel.TriviaInfo{
		DeathExemptionMedal: int32(1717),
	}, nil).Once()

	info, err := s.im.GetTriviaMedalInfo(mockCTX, mockUserID)
	s.NoError(err)
	s.Equal(int32(1717), info.Count)
}

func (s *triviaSuite) TestPrepareUseMedal() {
	gameID := int64(1)
	quizNo := int32(0)

	ans1 := model.TriviaAnswerLog{
		UserID:    mockUserID,
		GameID:    gameID,
		QuizNo:    quizNo,
		MedalUsed: 1,
	}
	s.mockPublisher.On(
		"Publish", mock.AnythingOfType("ctx.CTX"), ans1, mock.Anything, mock.Anything,
	).Return(nil).Once()

	ans2 := model.TriviaAnswerLog{
		UserID:    mockUserID2,
		GameID:    gameID,
		QuizNo:    quizNo,
		MedalUsed: 1,
	}
	s.mockPublisher.On(
		"Publish", mock.AnythingOfType("ctx.CTX"), ans2, mock.Anything, mock.Anything,
	).Return(nil).Once()

	s.mockPublisher.On(
		"Publish", mock.AnythingOfType("ctx.CTX"), mockUserID, mock.Anything, mock.Anything,
	).Return(nil).Once()
	s.mockPublisher.On(
		"Publish", mock.AnythingOfType("ctx.CTX"), mockUserID2, mock.Anything, mock.Anything,
	).Return(nil).Once()

	prepareUseMedal := model.TriviaPrepareUseMedal{
		GameID:     gameID,
		QuizNo:     quizNo,
		MedalUsers: []string{mockUserID, mockUserID2},
	}
	bytes, _ := json.Marshal(prepareUseMedal)
	s.NoError(s.im.PrepareUseMedal(mockCTX, bytes, queueModel.CallbackOption{PublishTime: time.Now()}))
}

func (s *triviaSuite) TestUseMedal() {
	s.mockUser.On("AtomicPatchTriviaPersonInfo", mockCTX, mockUserID, int32(-1), int32(0)).Return(nil).Once()

	s.NoError(s.im.UseMedal(mockCTX, []byte(mockUserID), queueModel.CallbackOption{PublishTime: time.Now()}))
}

func (s *triviaSuite) TestSetTriviaLeaderboardWithDefaultMode() {
	mockUserID3 := "mockUserID3"
	mockUserID4 := "mockUserID4"

	status := &model.GameStatus{
		ID:        int64(1),
		Region:    "TW",
		Type:      model.TriviaType_TRIVIA_ON_TV,
		TotalQuiz: int32(2),
		Result: &model.TriviaResult{
			Reward:    float32(17.2),
			WinnerIDs: []string{mockUserID, mockUserID2},
		},
	}
	s.im.saveTriviaStatus(mockCTX, status)

	s.NoError(s.redis.Set(mockCTX, getQuizAnsKey(int64(1), int32(0)), []byte("0"), time.Minute))
	s.NoError(s.redis.Set(mockCTX, getQuizAnsKey(int64(1), int32(1)), []byte("2"), time.Minute))
	s.NoError(s.redis.SAdd(mockCTX, getQuizOptKey(int64(1), int32(0), 1), mockUserID3))
	s.NoError(s.redis.SAdd(mockCTX, getQuizOptKey(int64(1), int32(0), 0), []string{mockUserID, mockUserID2, mockUserID4}...))
	s.NoError(s.redis.SAdd(mockCTX, getQuizOptKey(int64(1), int32(1), 3), mockUserID4))
	s.NoError(s.redis.SAdd(mockCTX, getQuizOptKey(int64(1), int32(1), 2), []string{mockUserID, mockUserID2}...))
	s.mockleaderboard.On("SetForPeriod", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_CORRECT, mock.Anything, 2, mockUserID, leaderboard.TablePlatform, "TW", lmodel.Period_WEEK, lmodel.Period_MONTH).Return(nil).Once()
	s.mockleaderboard.On("SetForPeriod", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_CORRECT, mock.Anything, 2, mockUserID2, leaderboard.TablePlatform, "TW", lmodel.Period_WEEK, lmodel.Period_MONTH).Return(nil).Once()
	s.mockleaderboard.On("SetForPeriod", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_CORRECT, mock.Anything, 1, mockUserID4, leaderboard.TablePlatform, "TW", lmodel.Period_WEEK, lmodel.Period_MONTH).Return(nil).Once()

	s.mockleaderboard.On("SetForPeriod", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_REWARD, mock.Anything, 172, mockUserID, leaderboard.TablePlatform, "TW", lmodel.Period_WEEK, lmodel.Period_MONTH).Return(nil).Once()
	s.mockleaderboard.On("SetForPeriod", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_REWARD, mock.Anything, 172, mockUserID2, leaderboard.TablePlatform, "TW", lmodel.Period_WEEK, lmodel.Period_MONTH).Return(nil).Once()

	bytes, _ := json.Marshal(int64(1))
	s.NoError(s.im.SetTriviaLeaderboard(mockCTX, bytes, queueModel.CallbackOption{PublishTime: time.Now()}))
}

func (s *triviaSuite) TestSetTriviaLeaderboardWithCorrectMostMode() {
	status := &model.GameStatus{
		ID:        int64(1),
		Region:    "TW",
		Type:      model.TriviaType_TRIVIA_ON_TV,
		TotalQuiz: int32(2),
		Result: &model.TriviaResult{
			Reward:    float32(17.2),
			WinnerIDs: []string{mockUserID, mockUserID2},
		},
		PlayMode: model.PlayMode_CorrectMost,
	}
	s.im.saveTriviaStatus(mockCTX, status)

	s.mockleaderboard.On("Get", mock.AnythingOfType("ctx.CTX"),
		lmodel.Type_TRIVIA_TV_CORRECT, lmodel.Period_DAY,
		mock.AnythingOfType("time.Time"), getCorrectLeaderboardName(int64(1)),
		regionSrv.GLOBAL, "", correctMostModeBatchCnt, 0,
	).Return([]*lmodel.IDScoreRank{
		&lmodel.IDScoreRank{
			ID:    mockUserID,
			Score: int64(13),
			Rank:  int32(0),
		},
		&lmodel.IDScoreRank{
			ID:    mockUserID2,
			Score: int64(12),
			Rank:  int32(1),
		},
	}, nil, "", nil).Once()

	s.mockleaderboard.On("SetForPeriod", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_CORRECT, mock.Anything, 13, mockUserID, leaderboard.TablePlatform, "TW", lmodel.Period_WEEK, lmodel.Period_MONTH).Return(nil).Once()
	s.mockleaderboard.On("SetForPeriod", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_CORRECT, mock.Anything, 12, mockUserID2, leaderboard.TablePlatform, "TW", lmodel.Period_WEEK, lmodel.Period_MONTH).Return(nil).Once()

	s.mockleaderboard.On("SetForPeriod", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_REWARD, mock.Anything, 172, mockUserID, leaderboard.TablePlatform, "TW", lmodel.Period_WEEK, lmodel.Period_MONTH).Return(nil).Once()
	s.mockleaderboard.On("SetForPeriod", mock.AnythingOfType("ctx.CTX"), lmodel.Type_TRIVIA_TV_REWARD, mock.Anything, 172, mockUserID2, leaderboard.TablePlatform, "TW", lmodel.Period_WEEK, lmodel.Period_MONTH).Return(nil).Once()

	bytes, _ := json.Marshal(int64(1))
	s.NoError(s.im.SetTriviaLeaderboard(mockCTX, bytes, queueModel.CallbackOption{PublishTime: time.Now()}))
}

func (s *triviaSuite) TestSaveAndPay() {
	status := &model.GameStatus{
		ID:        int64(1),
		Region:    "TW",
		Type:      model.TriviaType_TRIVIA_ON_TV,
		Currency:  "NTD",
		TotalQuiz: int32(2),
		Result: &model.TriviaResult{
			Reward:    float32(17.2),
			WinnerIDs: []string{mockUserID, mockUserID2},
		},
	}
	s.im.saveTriviaStatus(mockCTX, status)

	exchangeRate := float64(30)
	s.mockExchange.On("GetExchangeRate", mock.AnythingOfType("ctx.CTX"), exModel.ExCurrency_TWD, exModel.ExCurrency_USD).Return(exchangeRate, nil).Once()

	nanoReward := int64(float64(status.Result.Reward) / exchangeRate * math.Pow10(9))
	incrReward := float32(float64(nanoReward) / math.Pow10(9))
	s.mockUser.On("IncrementTotalRewardEarned", mock.AnythingOfType("ctx.CTX"), mockUserID, incrReward).Return(nil).Once()
	s.mockUser.On("IncrementTotalRewardEarned", mock.AnythingOfType("ctx.CTX"), mockUserID2, incrReward).Return(nil).Once()

	dealings := []*moneyModel.Dealing{}
	for _, winnerID := range status.Result.WinnerIDs {
		dealings = append(dealings, &moneyModel.Dealing{
			Category:     moneyModel.Category_TRIVIA,
			FromUserID:   env.OfficialPseudoUserID,
			ToUserID:     winnerID,
			FromCurrency: moneyModel.Currency_REWARD_NANO_USD,
			ToCurrency:   moneyModel.Currency_REWARD_NANO_USD,
			Amount:       nanoReward,
		})
	}
	s.mockBank.On("Trade", mock.AnythingOfType("ctx.CTX"), dealings, mock.AnythingOfType("money.Option")).Return("", []string{}, int64(0), nil).Once()

	bytes, _ := json.Marshal(int64(1))
	s.NoError(s.im.SaveAndPay(mockCTX, bytes, queueModel.CallbackOption{PublishTime: time.Now()}))
}

func (s *triviaSuite) TestRecordCorrectLeaderboard() {
	gameID := int64(1)
	quizNo := int32(0)

	s.NoError(s.redis.Set(mockCTX, getQuizSurvivorKey(gameID, quizNo), []byte("answer"), time.Minute))
	s.NoError(s.redis.SAdd(mockCTX, "answer", mockUserID, mockUserID2))

	boardName := getCorrectLeaderboardName(gameID)
	s.mockleaderboard.On("SetForPeriod", mockCTX, lmodel.Type_TRIVIA_TV_CORRECT, mock.AnythingOfType("time.Time"), 1, mockUserID, boardName, "TW", lmodel.Period_DAY).Return(nil).Once()
	s.mockleaderboard.On("SetForPeriod", mockCTX, lmodel.Type_TRIVIA_TV_CORRECT, mock.AnythingOfType("time.Time"), 1, mockUserID2, boardName, "TW", lmodel.Period_DAY).Return(nil).Once()

	params := model.TriviaCorrectLeaderboardParams{
		GameID: gameID,
		QuizNo: quizNo,
		Region: "TW",
	}
	bytes, _ := json.Marshal(params)
	s.NoError(s.im.RecordCorrectLeaderboard(mockCTX, bytes, queueModel.CallbackOption{PublishTime: time.Now()}))
}

func (s *triviaSuite) TestgenAnswerKeys() {
	keys := s.im.genAnswerKeys(mockCTX, mockUserID)
	s.Len(keys, maxQuizCntInGame)
}

func (s *triviaSuite) TestGetCorrectCount() {
	gameID := int64(1)
	status := &model.GameStatus{
		ID:       int64(1),
		Region:   "TW",
		PlayMode: model.PlayMode_CorrectMost,
	}
	s.im.saveTriviaStatus(mockCTX, status)

	boardName := getCorrectLeaderboardName(gameID)
	s.mockleaderboard.On("GetScore", mockCTX,
		lmodel.Type_TRIVIA_TV_CORRECT, lmodel.Period_DAY,
		mock.AnythingOfType("time.Time"), mockUserID, boardName, regionSrv.GLOBAL, 0,
	).Return(17, nil).Once()

	count, err := s.im.GetCorrectCount(mockCTX, gameID, mockUserID)
	s.NoError(err)
	s.Equal(int32(17), count)
}

func (s *triviaSuite) TestSetCorrectLeaderboardDone() {
	gameID := int64(1)
	quizNo := int32(1)
	s.NoError(s.im.SetCorrectLeaderboardDone(mockCTX, gameID, quizNo))

	// check redis
	bytes, err := s.redis.Get(mockCTX, getCorrectLeaderboardKey(gameID, quizNo))
	s.NoError(err)
	s.Equal("ready", string(bytes))
}

func (s *triviaSuite) TestGetQuizEditStatus() {
	// game not exist
	ret, err := s.im.GetQuizEditStatus(mockCTX, 1, 0)
	s.Require().NoError(err)
	s.Equal(model.EditStatus_ALL, ret)

	// prepare
	key := getStatusKey(int64(1))
	status := model.GameStatus{
		ID:          int64(1),
		Status:      model.Status_PREPARE,
		TotalReward: float32(100),
	}
	bytes, err := json.Marshal(&status)
	s.NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	ret, err = s.im.GetQuizEditStatus(mockCTX, 1, 0)
	s.Require().NoError(err)
	s.Equal(model.EditStatus_ALL, ret)

	status.CurrentQuiz = &model.CurrentQuiz{QuizNo: -1}
	bytes, err = json.Marshal(&status)
	s.NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	ret, err = s.im.GetQuizEditStatus(mockCTX, 1, 0)
	s.Require().NoError(err)
	s.Equal(model.EditStatus_ALL, ret)

	// quiz start
	status.Status = model.Status_QUIZSTART
	status.CurrentQuiz.QuizNo = 0
	bytes, err = json.Marshal(&status)
	s.NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	ret, err = s.im.GetQuizEditStatus(mockCTX, 1, 0)
	s.Require().NoError(err)
	s.Equal(model.EditStatus_ANSWERONLY, ret)

	// answer published
	status.Status = model.Status_QUIZRESULT
	bytes, err = json.Marshal(&status)
	s.NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	ret, err = s.im.GetQuizEditStatus(mockCTX, 1, 0)
	s.Require().NoError(err)
	s.Equal(model.EditStatus_READONLY, ret)

	// quiz end
	status.Status = model.Status_QUIZEND
	bytes, err = json.Marshal(&status)
	s.NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	ret, err = s.im.GetQuizEditStatus(mockCTX, 1, 0)
	s.Require().NoError(err)
	s.Equal(model.EditStatus_READONLY, ret)

	// further quiz
	status.Status = model.Status_QUIZRESULT
	status.CurrentQuiz.QuizNo = 26
	bytes, err = json.Marshal(&status)
	s.NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	ret, err = s.im.GetQuizEditStatus(mockCTX, 1, 56)
	s.Require().NoError(err)
	s.Equal(model.EditStatus_ALL, ret)

	// outdated quiz
	status.Status = model.Status_QUIZRESULT
	status.CurrentQuiz.QuizNo = 26
	bytes, err = json.Marshal(&status)
	s.NoError(err)
	s.redis.Set(mockCTX, key, bytes, time.Minute)

	ret, err = s.im.GetQuizEditStatus(mockCTX, 1, 13)
	s.Require().NoError(err)
	s.Equal(model.EditStatus_READONLY, ret)
}

func (s *triviaSuite) TestGetDrawResult() {
	// prepare
	mockGameID := int64(1)
	mockQuizNo := int32(2)

	key := getStatusKey(mockGameID)
	status := model.GameStatus{
		HostID:      mockHostID,
		ID:          mockGameID,
		Status:      model.Status_PREPARE,
		TotalReward: float32(100),
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: -1,
		},
	}
	bytes, err := json.Marshal(&status)
	s.Require().NoError(err)
	err = s.redis.Set(mockCTX, key, bytes, time.Minute)
	s.Require().NoError(err)

	err = s.query.Insert(
		mockCTX, keys.TabTriviaAnswers,
		model.TriviaAnswerLog{
			GameID: mockGameID,
			UserID: mockUserID,
			QuizNo: mockQuizNo,
			Option: 1,
		},
	)
	s.Require().NoError(err)
	err = s.query.Insert(
		mockCTX, keys.TabTriviaAnswers,
		model.TriviaAnswerLog{
			GameID: mockGameID,
			UserID: mockUserID2,
			QuizNo: mockQuizNo,
			Option: 3,
		},
	)
	s.Require().NoError(err)

	drawResult := model.DrawResult{
		GameID:  mockGameID,
		QuizNo:  mockQuizNo,
		Options: []int{1, 3},
		UserOptions: []*model.UserOption{
			&model.UserOption{
				UserID: mockUserID,
				Option: 1,
			},
			&model.UserOption{
				UserID: mockUserID2,
				Option: 3,
			},
		},
	}
	// Set result users to redis
	drawKey := s.im.getDrawKey(status.HostID)
	err = s.redis.SetStruct(mockCTX, drawKey, drawResult, ttlForDraw)
	s.Require().NoError(err)
	s.mockUser.On("GetDecoratedUsers", mockCTX, mockUserID, mockUserID2).Return([]models.User{
		models.User{
			UserID:      mockUserID,
			DisplayName: mockOpenID,
		},
		models.User{
			UserID:      mockUserID2,
			DisplayName: mockOpenID2,
		},
	}, nil).Once()
	s.mockPublisher.On(
		"Publish", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*trivia.DrawResult"), mock.Anything, mock.Anything,
	).Return(nil).Once()

	result, err := s.im.GetDrawResult(mockCTX, mockHostID)
	s.Require().NoError(err)
	s.Len(result.Result.WinnerIDs, len(drawResult.UserOptions))
}

func (s *triviaSuite) TestDraw() {
	// prepare
	mockGameID := int64(1)
	mockQuizNo := int32(2)

	key := getStatusKey(mockGameID)
	status := model.GameStatus{
		HostID:      mockHostID,
		ID:          mockGameID,
		Status:      model.Status_PREPARE,
		TotalReward: float32(100),
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: -1,
		},
	}
	bytes, err := json.Marshal(&status)
	s.Require().NoError(err)
	err = s.redis.Set(mockCTX, key, bytes, time.Minute)
	s.Require().NoError(err)

	key = getQuizOptKey(mockGameID, mockQuizNo, 1)
	s.redis.SAdd(mockCTX, key, mockUserID)

	// add for dedup test
	key = getQuizOptKey(mockGameID, mockQuizNo, 2)
	s.redis.SAdd(mockCTX, key, mockUserID)

	s.redis.SAdd(mockCTX, key, mockUserID2)

	s.mockUser.On("GetDecoratedUsers", mockCTX, mockUserID, mockUserID2).Return([]models.User{
		models.User{
			UserID:      mockUserID,
			DisplayName: mockOpenID,
		},
		models.User{
			UserID:      mockUserID2,
			DisplayName: mockOpenID,
		},
	}, nil).Once()

	result, err := s.im.Draw(mockCTX, mockGameID, mockQuizNo, []int{1, 2}, 2)
	s.Require().NoError(err)
	s.Require().Equal(2, len(result.Result.WinnerIDs))
	s.Equal(mockUserID, result.Result.WinnerIDs[0])
	s.Equal(mockUserID2, result.Result.WinnerIDs[1])
}

func (s *triviaSuite) TestRandomInDraw() {
	// prepare
	mockGameID := int64(1)
	mockQuizNo := int32(2)

	key := getStatusKey(mockGameID)
	status := model.GameStatus{
		HostID:      mockHostID,
		ID:          mockGameID,
		Status:      model.Status_PREPARE,
		TotalReward: float32(100),
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: -1,
		},
	}
	bytes, err := json.Marshal(&status)
	s.Require().NoError(err)
	err = s.redis.Set(mockCTX, key, bytes, time.Minute)
	s.Require().NoError(err)

	userPoolSize := 10
	drawNum := userPoolSize / 2
	mockUserIDs := []string{}
	for i := 0; i < userPoolSize; i++ {
		userID := "mock-user-id-" + strconv.Itoa(i)
		mockUserIDs = append(mockUserIDs, userID)
	}

	key = getQuizOptKey(mockGameID, mockQuizNo, 1)
	for _, userID := range mockUserIDs {
		s.redis.SAdd(mockCTX, key, userID)
	}

	randSeq := []int32{
		5, 6, 7, 4, 3,
	}

	for i := 0; i < userPoolSize-drawNum; i++ {
		s.mockFuncs.On("randInt31n", int32(userPoolSize-i)).Return(randSeq[i]).Once()
	}

	getDecoratedUsersCall := s.mockUser.On("GetDecoratedUsers",
		mockCTX,
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.Anything,
	)
	getDecoratedUsersCall.Run(func(args mock.Arguments) {
		resp := []models.User{}
		for i := 0; i < 5; i++ {
			userID := args.Get(i + 1).(string)
			resp = append(resp, models.User{
				UserID: userID,
				OpenID: mockOpenID,
			})
		}
		getDecoratedUsersCall.Return(resp, nil)
	}).Once()

	result, err := s.im.Draw(mockCTX, mockGameID, mockQuizNo, []int{1}, drawNum)
	s.Require().NoError(err)
	s.Require().Equal(5, len(result.Result.WinnerIDs))
}

func (s *triviaSuite) TestPatchDrawResult() {
	drawResult := &model.DrawResult{
		GameID:  1,
		QuizNo:  2,
		Options: []int{3, 4},
	}
	b, err := json.Marshal(drawResult)
	s.Require().NoError(err)
	err = s.im.PatchDrawResult(mockCTX, b, queueModel.CallbackOption{PublishTime: time.Now()})
	s.Require().NoError(err)
}

func (s *triviaSuite) TestDrawFailAfterPatch() {
	drawResult := &model.DrawResult{
		GameID:  1,
		QuizNo:  2,
		Options: []int{3, 4},
	}
	b, err := json.Marshal(drawResult)
	s.Require().NoError(err)
	err = s.im.PatchDrawResult(mockCTX, b, queueModel.CallbackOption{PublishTime: time.Now()})
	s.Require().NoError(err)

	// prepare
	mockGameID := int64(1)
	mockQuizNo := int32(2)

	key := getStatusKey(mockGameID)
	status := model.GameStatus{
		HostID:      mockHostID,
		ID:          mockGameID,
		Status:      model.Status_PREPARE,
		TotalReward: float32(100),
		CurrentQuiz: &model.CurrentQuiz{
			QuizNo: -1,
		},
	}
	bytes, err := json.Marshal(&status)
	s.Require().NoError(err)
	err = s.redis.Set(mockCTX, key, bytes, time.Minute)
	s.Require().NoError(err)

	key = getQuizOptKey(mockGameID, mockQuizNo, 1)
	s.redis.SAdd(mockCTX, key, mockUserID)

	_, err = s.im.Draw(mockCTX, mockGameID, mockQuizNo, []int{3}, 2)
	s.Require().Error(err)
}
