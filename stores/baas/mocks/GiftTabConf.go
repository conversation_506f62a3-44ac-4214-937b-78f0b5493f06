// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import baasconfig "github.com/17media/api/stores/baas/config"
import config "github.com/17media/api/models/config"
import ctx "github.com/17media/api/base/ctx"
import event "github.com/17media/api/models/event"
import gift "github.com/17media/api/models/gift"
import mock "github.com/stretchr/testify/mock"
import models "github.com/17media/api/models"

// GiftTabConf is an autogenerated mock type for the GiftTabConf type
type GiftTabConf struct {
	mock.Mock
}

// ComposeGiftTabs provides a mock function with given fields: context, user, streamer, language, deviceType, version, userPoint, giftInfos, _a8, filterForABTestNewbieFocus, streamerDeviceInfo
func (_m *GiftTabConf) ComposeGiftTabs(context ctx.CTX, user *models.User, streamer *models.User, language string, deviceType string, version string, userPoint int32, giftInfos []*gift.InfoInternal, _a8 *event.EventForGift, filterForABTestNewbieFocus bool, streamerDeviceInfo config.DeviceInfo) []*gift.Tab {
	ret := _m.Called(context, user, streamer, language, deviceType, version, userPoint, giftInfos, _a8, filterForABTestNewbieFocus, streamerDeviceInfo)

	var r0 []*gift.Tab
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, *models.User, string, string, string, int32, []*gift.InfoInternal, *event.EventForGift, bool, config.DeviceInfo) []*gift.Tab); ok {
		r0 = rf(context, user, streamer, language, deviceType, version, userPoint, giftInfos, _a8, filterForABTestNewbieFocus, streamerDeviceInfo)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*gift.Tab)
		}
	}

	return r0
}

// GetGiftTabIDs provides a mock function with given fields: _a0
func (_m *GiftTabConf) GetGiftTabIDs(_a0 *gift.InfoInternal) []string {
	ret := _m.Called(_a0)

	var r0 []string
	if rf, ok := ret.Get(0).(func(*gift.InfoInternal) []string); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	return r0
}

// GetSupportedGiftTypes provides a mock function with given fields:
func (_m *GiftTabConf) GetSupportedGiftTypes() baasconfig.GiftTypeSet {
	ret := _m.Called()

	var r0 baasconfig.GiftTypeSet
	if rf, ok := ret.Get(0).(func() baasconfig.GiftTypeSet); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(baasconfig.GiftTypeSet)
		}
	}

	return r0
}

// IsBasicGift provides a mock function with given fields: _a0
func (_m *GiftTabConf) IsBasicGift(_a0 *gift.InfoInternal) bool {
	ret := _m.Called(_a0)

	var r0 bool
	if rf, ok := ret.Get(0).(func(*gift.InfoInternal) bool); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}
