package pay

import (
	"database/sql"
	"fmt"

	"github.com/17media/dig"
	subTopicModel "github.com/17media/gomodel/models/subTopic"

	payModel "github.com/17media/api/models/pay"
	"github.com/17media/api/service/cron"
	ecpaySrv "github.com/17media/api/service/ecpay"
	kmsSrv "github.com/17media/api/service/kms"
	linepaySrv "github.com/17media/api/service/linepay"
	paypalSrv "github.com/17media/api/service/paypal"
	"github.com/17media/api/service/queue"
	"github.com/17media/api/service/redis"
	slackService "github.com/17media/api/service/slack"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/stores/army"
	"github.com/17media/api/stores/money"
	"github.com/17media/api/stores/pay/productmanager"
	"github.com/17media/api/stores/pay/verifier"
	"github.com/17media/api/stores/pay/verifier/appstore"
	"github.com/17media/api/stores/pay/verifier/armybabycoin"
	"github.com/17media/api/stores/pay/verifier/ecpay"
	"github.com/17media/api/stores/pay/verifier/gmo"
	"github.com/17media/api/stores/pay/verifier/internalsys"
	"github.com/17media/api/stores/pay/verifier/linepay"
	"github.com/17media/api/stores/pay/verifier/manual"
	"github.com/17media/api/stores/pay/verifier/paypal"
	"github.com/17media/api/stores/pay/verifier/playstore"
	"github.com/17media/api/stores/pay/verifier/spg"
	"github.com/17media/api/stores/pay/verifier/yzf"
	"github.com/17media/api/stores/user"

	// trigger init
	_ "github.com/17media/api/service/redis/redispersistent"
	_ "github.com/17media/api/setup/mysql"
	_ "github.com/17media/api/setup/mysql/mysqlgiftmaster"
	_ "github.com/17media/api/setup/mysql/mysqlgiftreader"
)

func init() {
	Register(dimanager.DefaultManager)
}

// CreateVerifiers create the pay/verifier objects
func CreateVerifiers(p Params) map[payModel.Platform]verifier.Verifier {
	ret := map[payModel.Platform]verifier.Verifier{}

	for platform, v := range appstore.New() {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range appstore.NewBaaS() {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range appstore.NewMakeUp(p.GiftMaster) {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range gmo.New(p.GiftMaster, p.Kms) {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range internalsys.New() {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range manual.New() {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range paypal.New(p.GiftMaster, p.Paypal) {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range playstore.New(p.EnableVoidedPurchases, p.RedisPersistent) {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range playstore.NewBaaS(p.EnableVoidedPurchases) {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range playstore.NewMakeUp(p.GiftMaster) {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range spg.New(p.SandboxOutgoingProxy, p.ProdOutgoingProxy, p.MerchantID, p.HashKey, p.HashIV) {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range yzf.New() {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range linepay.New(p.Linepay) {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range armybabycoin.New(p.Bank) {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	for platform, v := range ecpay.New(p.Ecpay) {
		if _, ok := ret[platform]; ok {
			panic(fmt.Sprintf("Verifier %v exists", platform))
		}
		ret[platform] = v
	}

	return ret
}

// Params provides parameters for New()
type Params struct {
	dig.In
	GiftMaster      *sql.DB                                    `name:"mysqlGiftMaster"`
	GiftReader      *sql.DB                                    `name:"mysqlGiftReader"`
	RedisPersistent redis.Service                              `name:"redisPersistent"`
	Cron            cron.Cron                                  `name:"cron"`
	Bank            money.Bank                                 `name:"bank"`
	User            user.Store                                 `name:"user"`
	Army            army.Store                                 `name:"army"`
	Slack           slackService.Slack                         `name:"slack"`
	Paypal          paypalSrv.Service                          `name:"paypal"`
	Linepay         linepaySrv.Service                         `name:"linepay"`
	Ecpay           ecpaySrv.Service                           `name:"ecpay"`
	ProductManager  productmanager.InternalPointProductManager `name:"payInternalPointProductManager"`
	Kms             kmsSrv.Service                             `name:"kms"`

	// flag for pay
	SlackPaymentChannels      *string `name:"slack_payment_channels"`
	SlackPaymentAlertChannels *string `name:"slackPaymentAlertChannels"`
	SlackPaymentTestChannels  *string `name:"slackPaymentTestChannels"`
	EnableSubscriptionCron    *bool   `name:"enable_subscription_cron"`

	// flag for pay/verifier/playstore
	EnableVoidedPurchases *bool `name:"playstore_voided_purchases"`

	// flag for pay/verifier/spg
	SandboxOutgoingProxy *string `name:"spg_sandbox_outgoing_proxy"`
	ProdOutgoingProxy    *string `name:"spg_prod_outgoing_proxy"`
	MerchantID           *string `name:"spg_merchant_id"`
	HashKey              *string `name:"spg_hash_key"`
	HashIV               *string `name:"spg_hash_iv"`
}

// Register registers the constructor of pay object to the manager
func Register(m *dimanager.Manager) {
	// used by stores/pay
	m.RegisterString("slack_payment_channels", "jt-logs", "available channels, comma separated string")
	m.RegisterString("slackPaymentAlertChannels", "CGFJ1PLSY", "available channels, comma separated string")
	m.RegisterString("slackPaymentTestChannels", "bot-dev", "available channels, comma separated string")
	m.RegisterBool("enable_subscription_cron", true, "enable subscription cron job or not")

	// used by stores/pay/verifier/playstore
	m.RegisterBool("playstore_voided_purchases", false, "enable to use Voided Purchases API")

	// used by stores/pay/verifier/spg
	m.RegisterString("spg_sandbox_outgoing_proxy", "", "outgoing proxy for SPG sandbox payment")
	m.RegisterString("spg_prod_outgoing_proxy", "", "outgoing proxy for SPG production payment")
	m.RegisterString("spg_merchant_id", "", "SPG merchant ID")
	m.RegisterString("spg_hash_key", "", "SPG hash key")
	m.RegisterString("spg_hash_iv", "", "SPG hash iv")

	fn := func(p Params) Store {
		verifiers := CreateVerifiers(p)
		return New(
			p,
			verifiers,
			queue.NewPubsubPublisher(subTopicModel.SubTopics[subTopicModel.KeyPaySlackPurchaseMsg].Topic),
			queue.NewPubsubPublisher(subTopicModel.SubTopics[subTopicModel.KeyMissionReroute].Topic),
			queue.NewPubsubPublisher(subTopicModel.SubTopics[subTopicModel.KeyMissionIdentity].Topic),
			queue.NewPubsubPublisher(subTopicModel.SubTopics[subTopicModel.KeyPayCheckVendorRefund].Topic),
		)
	}
	m.ProvideConstructor(fn, `pay`)
}

// GetPay returns the pay object
func GetPay(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"pay"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
