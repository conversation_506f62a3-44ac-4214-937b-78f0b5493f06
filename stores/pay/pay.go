// Package pay is a tool to validate iOS in-app purchase and Android in-app billing
// And to interact with 17 products
package pay

import (
	"errors"

	"github.com/jmoiron/sqlx"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models"
	payModel "github.com/17media/api/models/pay"
	queueModel "github.com/17media/api/models/queue"
)

// Payment error definitions
const (
	// PaymentErrNoneCode is the code for a successful payment.
	PaymentErrNoneCode = int32(0)
	// PaymentErrGeneralCode is the error code for a failed payment, and the client will retry.
	PaymentErrGeneralCode          = int32(1)
	PaymentErrReceiptProcessedCode = int32(16)
	PaymentErrInsufficentBalance   = int32(32)
	// PaymentErrQualificationForGiftPackageCode is the error code for when a user is not qualified to purchase a gift package.
	// In this case, the client will display an error message and will not retry.
	PaymentErrQualificationForGiftPackageCode = int32(64)
	// PaymentErrNotRetryCode is the error code for an order that the system is unable to process.
	// In this case, the client will display an error message and will not retry.
	PaymentErrNotRetryCode = int32(65)
)

// Error definitions
var (
	ErrDuplicate                      = errors.New("this receipt/token is for one time used and already exists")
	ErrOrderNotExist                  = errors.New("Error order not exist")
	ErrSubscriptionNotExist           = errors.New("Error subscription not exist")
	ErrBlockedReceipt                 = errors.New("this receipt is blocked for some reason. ex: use sandbox receipt on prod")
	ErrRateExist                      = errors.New("Exchange rate exists already")
	ErrRateNotExist                   = errors.New("Exchange Rate doesn't exist")
	ErrRenewNotSupported              = errors.New("Renew the subscription is not supported by the vendor")
	ErrVendorTokenNotFound            = errors.New("The required vendor token is missing")
	ErrAlreadyRefund                  = errors.New("this purchase has been refunded")
	ErrVerifierNotExist               = errors.New("The verifier doesn't exist")
	ErrGetProductFailure              = errors.New("GetProduct failed")
	ErrProductHandlerNotExist         = errors.New("The product handler doesn't exist")
	ErrProductExchangeFailure         = errors.New("Product exchange failed")
	ErrEmptyPayload                   = errors.New("The payload is empty")
	ErrNextSubscriptionAlreadyCharged = errors.New("next subscription has been charged")
	ErrNotFound                       = errors.New("not found")
)

// SettleAction represents action (accept or reject) for reserved refund
type SettleAction struct {
	Vendor        payModel.Platform
	TransactionID string
	Accept        bool
}

// Store serves all functions related to in-app purchase
type Store interface {
	// product returns all product of given product type in interface.
	// the content of the lists are defined inside of every product.
	// what parameters used in `filter` struct are defined inside of every product.
	GetProducts(context ctx.CTX, productType payModel.ProductType, filter payModel.ProductFilter) (products *payModel.ResProducts, err error)

	// Prepare creates a order and generates request body and url for caller to send request to vendor
	// This is no use for AppStore and PlayStore
	Prepare(context ctx.CTX, vendor payModel.Platform, user *models.User, payProduct payModel.PayProduct, returnURL, clientBackURL string, vendorAttrs map[string]string, subscriptionAttrs *payModel.SubscriptionAttrs) (orderRequest *payModel.ResPrepareOrder, err error)

	Pay(context ctx.CTX, platform string, vendor payModel.Platform, user *models.User, payload, region string, opts ...Option) (payments *payModel.Payments, err error)

	// PayByOrderID executes unfinished order by orderID.
	// To support VIP team process, an order can be unfinished.
	// After user paid, VIP team will give product to user manually.
	// In such case, call this function to pay product.
	PayByOrderID(context ctx.CTX, orderID, platform, adminSlackID, adminEmail string) (res *payModel.ResIntraExchangeProductByOrder, err error)

	// GetOrderStatus returns the order's status by orderID
	GetOrderStatus(context ctx.CTX, orderID string) (orderStatus *payModel.ResOrderStatus, err error)

	// GetOrderUserID returns userID by orderID
	GetOrderUserID(context ctx.CTX, orderID string) (userID string, err error)

	// GetOrderUserIDByPaymentID returns userID by paymentID
	GetOrderUserIDByPaymentID(context ctx.CTX, paymentID string) (userID string, err error)

	// Unsubscribe let you unsubscribe a product
	Unsubscribe(context ctx.CTX, subscriptionID int64) error
	UnsubscribeAll(context ctx.CTX, userID string) error
	UnsubscribeByArmySubscriptionID(context ctx.CTX, id int64) error
	GetSubscriptionLogs(context ctx.CTX, cursor, userID string, limit int) (*payModel.ResSubscriptionLog, error)

	// GetPointIncreasingLogs returns user's increasing point logs for specific product type
	GetPointIncreasingLogs(context ctx.CTX, cursor, userID string, productType payModel.ProductType, limit int) (*payModel.ResPointIncreasingLogs, error)
	// SendSlackPurchaseMsgFromTaskQueue is a worker function to send slack purchase msg
	SendSlackPurchaseMsgFromTaskQueue(context ctx.CTX, data []byte, option queueModel.CallbackOption) error

	// Refund executes the refund of given transactionID and vendor.
	// Available options:
	// - WithRefundCallback(f func(tx *sqlx.Tx, timeMs int64) error)
	//   Caller can assign a callback that will be executed after refunding successfully
	//   `tx` will be put into the same transaction with refund
	Refund(context ctx.CTX, vendor payModel.Platform, transactionID, execUserID string, reason payModel.RefundReason, options ...RefundOption) error

	// ReservedRefund marks the refund of given transactionID and vendor for later used,
	// Caller should use `SettleRefund` to settle down the refund.
	ReservedRefund(context ctx.CTX, vendor payModel.Platform, transactionID, reqUserID string, options ...RefundOption) error

	// SettleRefund settles down reserved refund
	SettleRefund(
		context ctx.CTX,
		vendor payModel.Platform, transactionID string, accept bool, callback func(tx *sqlx.Tx, timeMs int64) error,
		execUserID string, reason payModel.RefundReason,
	) error

	// SyncRefund refunds purchase if it has been refunded by vendor
	SyncRefund(context ctx.CTX, refundTaskBytes []byte, option queueModel.CallbackOption) error
	// RefundOnDemand refunds purchase if necessary
	RefundOnDemand(context ctx.CTX, vendor payModel.Platform, transactionID string) error

	// GenPayload generates payload for specific verifier type
	GenPayload(context ctx.CTX, vendor payModel.Platform, input payModel.GenPayloadInput) (payload, orderID string, err error)

	// CreatePayment creates a order and send request to vendor to create a payment then get required infos back for client
	CreatePayment(context ctx.CTX, vendor payModel.Platform, user *models.User, payProduct payModel.PayProduct, returnURL, clientBackURL string, vendorAttrs map[string]string, subscriptionAttrs *payModel.SubscriptionAttrs) (paymentInfo *payModel.PaymentInfo, err error)

	// ExecutePayment confirm a order and send request to vendor to execute payment
	ExecutePayment(context ctx.CTX, vendor payModel.Platform, paymentID, payerID string) error

	// CancelPrevSubscription cancels previous subscription for upgrade
	CancelPrevSubscription(context ctx.CTX, userID string, targetType payModel.TargetType, targetID, productID string, expiryTimeMs int64) error
}
