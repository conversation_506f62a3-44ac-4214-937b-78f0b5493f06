package purchasereport

import (
	"bytes"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"github.com/17media/logrus"
	"github.com/gocarina/gocsv"
	"github.com/jmoiron/sqlx"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/db"
	"github.com/17media/api/base/env"
	"github.com/17media/api/models"
	"github.com/17media/api/models/intra"
	payModel "github.com/17media/api/models/pay"
	purchasePayModel "github.com/17media/api/models/purchase/pay"
	"github.com/17media/api/service/region"
	"github.com/17media/api/service/storage"
	"github.com/17media/api/stores/intra/file"
	"github.com/17media/api/stores/pay/product"
	"github.com/17media/api/stores/user"
)

type reporter struct {
	dbReaderx *sqlx.DB
	rs        region.Service
	us        user.Store
	intraFile file.Store
}

type csvFormat struct {
	OrderID        string `csv:"OrderID"`
	GMORecurringID string `csv:"GMORecurringID"`
	UserID         string `csv:"UserID"`
	OpenID         string `csv:"OpenID"`
	StreamerUserID string `csv:"streamerUserID"`
	StreamerOpenID string `csv:"streamerOpenID"`
	ProductID      string `csv:"ProductID"`
	Price          string `csv:"Price"`
	Points         string `csv:"Points"`
	UpdateDate     string `csv:"Update Date"`
}

type csvPointFormat struct {
	LocalTime     string `csv:"Date Time"`
	UserID        string `csv:"UserID"`
	OpenID        string `csv:"OpenID"`
	TransactionID string `csv:"TransactionID"`
	ProductID     string `csv:"ProductID"`
	Sandbox       string `csv:"Sandbox"`
	Price         string `csv:"PriceUSD"`
	Platform      string `csv:"Platform"`
	Currency      string `csv:"Currency"`
	CurrencyPrice string `csv:"CurrencyPrice"`
}

var (
	envSandbox = env.PayIsSandbox

	regionChannels = []RegionInfo{
		RegionInfo{
			Region: "TW",
			Channel: []PurchaseChannel{
				PurchaseChannel_SPG,
				PurchaseChannel_GASH,
				PurchaseChannel_PayPal,
				PurchaseChannel_MyCard,
			},
		},
		RegionInfo{
			Region: "JP",
			Channel: []PurchaseChannel{
				PurchaseChannel_GMO,
				PurchaseChannel_PayPal,
				PurchaseChannel_JP_LINEPAY,
			},
		},
	}

	PointProducts = map[string]struct {
		Products map[PurchaseChannel][]string
	}{
		"TW": {
			Products: map[PurchaseChannel][]string{
				PurchaseChannel_SPG: []string{
					"web_points_spgateway_1",
					"web_points_spgateway_2",
					"web_points_spgateway_3",
					"web_points_spgateway_4",
					"web_points_spgateway_5",
					"web_points_spgateway_6",
					"web_points_spgateway_7",
					"web_points_spgateway_8",
					"web_points_spgateway_9",
					"web_points_spgateway_10",
					"202001_web_points_spgateway_1",
					"202001_web_points_spgateway_2",
					"202001_web_points_spgateway_3",
					"202001_web_points_spgateway_4",
					"202001_web_points_spgateway_5",
					"202001_web_points_spgateway_6",
					"202001_web_points_spgateway_7",
					"202001_web_points_spgateway_8",
					"202001_web_points_spgateway_9",
					"202001_web_points_spgateway_10",
				},
				PurchaseChannel_PayPal: []string{
					"web_points_24500",
					"web_points_1350000",
					"web_points_105000",
					"web_points_300000",
					"web_points_3500000",
					"web_points_52099",
					"web_points_650000",
					"web_points_7500000",
				},
				PurchaseChannel_MyCard: []string{
					"web_points_mycard_1",
					"web_points_mycard_2",
					"web_points_mycard_3",
					"web_points_mycard_4",
					"web_points_mycard_5",
					"web_points_mycard_6",
					"web_points_mycard_7",
					"web_points_mycard_8",
					"web_points_mycard_9",
					"web_points_mycard_10",
					"202001_web_points_mycard_1",
					"202001_web_points_mycard_2",
					"202001_web_points_mycard_3",
					"202001_web_points_mycard_4",
					"202001_web_points_mycard_5",
					"202001_web_points_mycard_6",
					"202001_web_points_mycard_7",
					"202001_web_points_mycard_8",
					"202001_web_points_mycard_9",
					"202001_web_points_mycard_10",
				},
				PurchaseChannel_GASH: []string{
					"web_points_gash_1",
					"web_points_gash_2",
					"web_points_gash_3",
					"web_points_gash_4",
					"web_points_gash_5",
					"web_points_gash_6",
					"web_points_gash_7",
					"web_points_gash_8",
					"web_points_gash_9",
					"web_points_gash_10",
					"202001_wweb_points_gash_1",
					"202001_wweb_points_gash_2",
					"202001_wweb_points_gash_3",
					"202001_wweb_points_gash_4",
					"202001_wweb_points_gash_5",
					"202001_wweb_points_gash_6",
					"202001_wweb_points_gash_7",
					"202001_wweb_points_gash_8",
					"202001_wweb_points_gash_9",
					"202001_wweb_points_gash_10",
				},
			},
		},
		"JP": {
			Products: map[PurchaseChannel][]string{
				PurchaseChannel_GMO: []string{
					strconv.FormatInt(int64(payModel.PayProduct_GMO_17LIVE_POINTS_120), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_17LIVE_POINTS_600), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_17LIVE_POINTS_1300), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_17LIVE_POINTS_2600), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_17LIVE_POINTS_7000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_17LIVE_POINTS_15000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_17LIVE_POINTS_51000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_17LIVE_POINTS_102000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_17LIVE_POINTS_265000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_17LIVE_POINTS_550000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_17LIVE_POINTS_600), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_17LIVE_POINTS_1300), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_17LIVE_POINTS_2600), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_17LIVE_POINTS_7000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_17LIVE_POINTS_15000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_17LIVE_POINTS_51000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_17LIVE_POINTS_102000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_17LIVE_POINTS_265000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_17LIVE_POINTS_550000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_MEDIA17_BABY_POINTS_120), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_MEDIA17_BABY_POINTS_600), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_MEDIA17_BABY_POINTS_1300), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_MEDIA17_BABY_POINTS_2600), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_MEDIA17_BABY_POINTS_7000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_MEDIA17_BABY_POINTS_15000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_MEDIA17_BABY_POINTS_51000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_MEDIA17_BABY_POINTS_102000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_MEDIA17_BABY_POINTS_265000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_GMO_MEDIA17_BABY_POINTS_550000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_MEDIA17_BABY_POINTS_600), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_MEDIA17_BABY_POINTS_1300), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_MEDIA17_BABY_POINTS_2600), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_MEDIA17_BABY_POINTS_7000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_MEDIA17_BABY_POINTS_15000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_MEDIA17_BABY_POINTS_51000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_MEDIA17_BABY_POINTS_102000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_MEDIA17_BABY_POINTS_265000), 10),
					strconv.FormatInt(int64(payModel.PayProduct_201910_GMO_MEDIA17_BABY_POINTS_550000), 10),
				},
				PurchaseChannel_PayPal: []string{
					"jp2_web_points_105000",
					"jp2_web_points_11000",
					"jp2_web_points_1350000",
					"jp2_web_points_24500",
					"jp2_web_points_300000",
					"jp2_web_points_3500000",
					"jp2_web_points_52099",
					"jp2_web_points_5400",
					"jp2_web_points_650000",
					"jp2_web_points_7500000",
					"jp_web_points_105000",
					"jp_web_points_11000",
					"jp_web_points_1350000",
					"jp_web_points_24500",
					"jp_web_points_300000",
					"jp_web_points_3500000",
					"jp_web_points_52099",
					"jp_web_points_5400",
					"jp_web_points_650000",
					"jp_web_points_7500000",
				},
				PurchaseChannel_JP_LINEPAY: []string{
					"jp_linepay_points_105000",
					"jp_linepay_points_11000",
					"jp_linepay_points_1350000",
					"jp_linepay_points_24500",
					"jp_linepay_points_300000",
					"jp_linepay_points_3500000",
					"jp_linepay_points_52099",
					"jp_linepay_points_5400",
					"jp_linepay_points_650000",
					"jp_linepay_points_7500000",
				},
			},
		},
	}
	armyProducts = map[string][]payModel.PayProduct{
		"JP": []payModel.PayProduct{
			payModel.PayProduct_ARMY_JP_RANK_1_ONE_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_1_THREE_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_1_SIX_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_2_ONE_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_2_THREE_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_2_SIX_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_3_ONE_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_3_THREE_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_3_SIX_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_4_ONE_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_4_THREE_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_4_SIX_MONTH,
			payModel.PayProduct_ARMY_JP_RANK_CORPORAL_ONE_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_1_ONE_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_1_THREE_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_1_SIX_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_2_ONE_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_2_THREE_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_2_SIX_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_3_ONE_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_3_THREE_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_3_SIX_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_4_ONE_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_4_THREE_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_4_SIX_MONTH,
			payModel.PayProduct_201910_ARMY_JP_RANK_CORPORAL_ONE_MONTH,
		},
		"TW": []payModel.PayProduct{
			payModel.PayProduct_ARMY_RANK_1_ONE_MONTH,
			payModel.PayProduct_ARMY_RANK_1_THREE_MONTH,
			payModel.PayProduct_ARMY_RANK_1_SIX_MONTH,
			payModel.PayProduct_ARMY_RANK_2_ONE_MONTH,
			payModel.PayProduct_ARMY_RANK_2_THREE_MONTH,
			payModel.PayProduct_ARMY_RANK_2_SIX_MONTH,
			payModel.PayProduct_ARMY_RANK_3_ONE_MONTH,
			payModel.PayProduct_ARMY_RANK_3_THREE_MONTH,
			payModel.PayProduct_ARMY_RANK_3_SIX_MONTH,
			payModel.PayProduct_ARMY_RANK_4_ONE_MONTH,
			payModel.PayProduct_ARMY_RANK_4_THREE_MONTH,
			payModel.PayProduct_ARMY_RANK_4_SIX_MONTH,
			payModel.PayProduct_ARMY_TW_RANK_CORPORAL_ONE_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_1_ONE_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_1_THREE_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_1_SIX_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_2_ONE_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_2_THREE_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_2_SIX_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_3_ONE_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_3_THREE_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_3_SIX_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_4_ONE_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_4_THREE_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_4_SIX_MONTH,
			payModel.PayProduct_ARMY_HK_RANK_CORPORAL_ONE_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_1_ONE_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_1_THREE_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_1_SIX_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_2_ONE_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_2_THREE_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_2_SIX_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_3_ONE_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_3_THREE_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_3_SIX_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_4_ONE_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_4_THREE_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_4_SIX_MONTH,
			payModel.PayProduct_ARMY_MY_RANK_CORPORAL_ONE_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_1_ONE_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_1_THREE_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_1_SIX_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_2_ONE_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_2_THREE_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_2_SIX_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_3_ONE_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_3_THREE_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_3_SIX_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_4_ONE_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_4_THREE_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_4_SIX_MONTH,
			payModel.PayProduct_ARMY_GLOBAL_RANK_CORPORAL_ONE_MONTH,
			payModel.PayProduct_ARMY_IAP_RANK_CORPORAL_ONE_MONTH,
			payModel.PayProduct_ARMY_IAP_RANK_1_ONE_MONTH,
			payModel.PayProduct_ARMY_IAP_RANK_2_ONE_MONTH,
			payModel.PayProduct_ARMY_IAP_RANK_3_ONE_MONTH,
			payModel.PayProduct_ARMY_IAP_RANK_4_ONE_MONTH,
		},
	}
)

// New create an instance for purchase report
func New(readerDb *sql.DB, userStore user.Store, fileStore file.Store, rs region.Service) Report {
	dbReaderx := sqlx.NewDb(readerDb, db.SQLDriver)
	return &reporter{
		dbReaderx: dbReaderx,
		rs:        rs,
		us:        userStore,
		intraFile: fileStore,
	}
}

// GetRegion returns available region to generate report
func (r *reporter) GetRegion(context ctx.CTX) (regionInfo []RegionInfo) {
	return regionChannels
}

// GetPointPurchaseReport returns point purchase from third party and provide download file path, logs are got from startTimeMs to endTimeMs
func (r *reporter) GetPointPurchaseReport(context ctx.CTX, region string, channel PurchaseChannel, startTime, endTime time.Time) (filekey string, err error) {
	st := startTime.Unix()
	et := endTime.Unix()
	ex := false
	for _, rc := range regionChannels {
		if rc.Region == region {
			for _, i := range rc.Channel {
				if i == channel {
					ex = true
					break
				}
			}
			break
		}
	}
	if ex == false && channel != PurchaseChannel_ALL {
		return "", fmt.Errorf("PurchaseChannel not exist in this region")
	}

	type report struct {
		TransactionID sql.NullString `db:"transactionID"`
		UserID        string         `db:"userID"`
		ProductID     string         `db:"productID"`
		Sandbox       int32          `db:"sandbox"`
		Price         float64        `db:"price"`
		Platform      string         `db:"platform"`
		Timestamp     int64          `db:"timestamp"`
		Currency      string         `db:"currency"`
		CurrencyPrice float64        `db:"currencyPrice"`
	}
	var products []string

	if channel != PurchaseChannel_ALL {
		if _, ok := PointProducts[region]; ok {
			products = PointProducts[region].Products[channel]
		}
	} else {
		for _, ps := range PointProducts[region].Products {
			products = append(products, ps...)
		}
	}
	if len(products) == 0 {
		return "", ErrNoData
	}

	reports := []*report{}
	query := `SELECT ppl.transactionID, ppl.userID, ppl.productID, ppl.sandbox, ppl.price, ppl.platform, ppl.timestamp, IFNULL(p.currency, '') AS currency, IFNULL(p.currencyPrice, 0) AS currencyPrice 
		FROM ProductPurchaseLog ppl 
		LEFT JOIN PointProduct p ON ppl.productID=p.productID AND ppl.platform=p.platform
		WHERE ppl.timestamp >= ? AND ppl.timestamp < ? AND ppl.productID IN (?) 
		ORDER BY timestamp`
	q, args, err := sqlx.In(query, st, et, products)
	if err != nil {
		context.WithField("err", err).Error("sqlx.In failed")
		return
	}
	if err = r.dbReaderx.Select(&reports, q, args...); err != nil {
		context.WithFields(logrus.Fields{
			"err":  err,
			"q":    q,
			"args": args,
		}).Error("dbx.Select failed")
		return
	}
	if len(reports) == 0 {
		return "", ErrNoData
	}

	// generate csv file
	csvData := []csvPointFormat{}

	loc, err := r.rs.GetTimeLocation(context, region)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err":    err,
			"region": region,
		}).Error("get location error")
		return
	}
	var userIDs []string
	for _, report := range reports {
		userIDs = append(userIDs, report.UserID)
	}

	users, err := r.us.GetPlainUsers(context, userIDs...)
	if err != nil {
		context.WithField("err", err).Error("Get user info error")
		return
	}

	for i, report := range reports {
		t := time.Unix(report.Timestamp, 0).In(loc)

		if report.Currency == "" && report.CurrencyPrice == 0 {
			context.WithFields(logrus.Fields{
				"report":   report,
				"products": products,
			}).Warn("can not find product in PointProduct")
		}

		data := csvPointFormat{
			LocalTime:     t.String(),
			UserID:        report.UserID,
			OpenID:        users[i].OpenID,
			TransactionID: report.TransactionID.String,
			ProductID:     report.ProductID,
			Sandbox:       strconv.FormatInt(int64(report.Sandbox), 10),
			Price:         strconv.FormatFloat(report.Price, 'f', 2, 64),
			Platform:      report.Platform,
			Currency:      report.Currency,
			CurrencyPrice: strconv.FormatFloat(report.CurrencyPrice, 'f', 2, 64),
		}

		csvData = append(csvData, data)
	}

	buffer := bytes.NewBuffer([]byte("\xEF\xBB\xBF"))
	if err = gocsv.Marshal(csvData, buffer); err != nil {
		context.WithFields(logrus.Fields{
			"err":       err,
			"channel":   channel,
			"startTime": startTime,
			"endTime":   endTime,
		}).Error("gocsv.MarshalWithoutHeader write log error")
		return
	}

	// upload file
	filename := fmt.Sprintf("Point_purchase_report_%v_%v_%v_%v.csv", region, channel.String(), startTime.String(), endTime.String())

	psf := &file.File{
		Category:    intra.FileCategory_FINANCIAL_REPORT,
		Name:        filename,
		ContentType: storage.CSV,
		Reader:      bytes.NewReader(buffer.Bytes()),
	}

	filekeys, err := r.intraFile.Upload(context, []*file.File{psf})
	if err != nil {
		context.WithField("err", err).Error("Intra file upload error")
		return
	}

	return filekeys[0], nil
}

// GetArmySubscribeReport returns army subscribe logs by region
func (r *reporter) GetArmySubscribeReport(context ctx.CTX, region string, startTime, endTime time.Time) (filekey string, err error) {
	st := startTime.Unix() * 1000
	et := endTime.Unix() * 1000

	var ledgacyPlatforms []payModel.Platform
	var payMethods []purchasePayModel.Method
	switch region {
	case "JP":
		ledgacyPlatforms = []payModel.Platform{
			payModel.Platform_GMO,
		}
		payMethods = []purchasePayModel.Method{
			purchasePayModel.MethodGMO,
			purchasePayModel.MethodGMOPayPay,
		}
	default:
		ledgacyPlatforms = []payModel.Platform{
			payModel.Platform_SPG,
			payModel.Platform_ECPAY,
		}
		payMethods = []purchasePayModel.Method{
			purchasePayModel.MethodECPay,
		}

		region = "TW"
	}

	sandboxInt := 0
	if envSandbox() {
		sandboxInt = 1
	}

	type report struct {
		OrderID        string `db:"orderID"`
		GMORecurringID string `db:"gmoRecurringID"`
		UserID         string `db:"userID"`
		StreamerUserID string `db:"streamerUserID"`
		ProductID      string `db:"productID"`
		Timestamp      int64  `db:"updateTimeMillis"`
	}

	// Select army orders
	// migration = 0 for v1 orders
	// migration = 1 for v2 orders
	reports := []*report{}
	query := `
	SELECT o.orderID, o.userID, o.productID, o.updateTimeMillis, 
		IF(sub.migration = 1, "", COALESCE(prevSub.userVendorToken, "")) AS gmoRecurringID,
		COALESCE(sub.targetID, "") AS streamerUserID
	FROM PaymentOrder o
	LEFT JOIN SubscriptionLog sub ON o.orderID = sub.orderID
	LEFT JOIN PaymentOrder prev ON o.sourceOrder = prev.orderID AND o.sourceOrder != ''
	LEFT JOIN SubscriptionLog prevSub ON prev.orderID = prevSub.orderID
	WHERE o.updateTimeMillis >= ? AND o.updateTimeMillis < ? AND o.productID IN (?) AND
		o.sandbox=? AND ((o.vendor IN (?) AND sub.migration = 0) OR (o.vendor IN (?) AND sub.migration = 1)) AND o.status=1
	ORDER BY o.updateTimeMillis`
	q, args, err := sqlx.In(query, st, et, armyProducts[region], sandboxInt, ledgacyPlatforms, payMethods)
	if err != nil {
		context.WithField("err", err).Error("sqlx.In failed")
		return
	}
	if err = r.dbReaderx.Select(&reports, q, args...); err != nil {
		context.WithField("err", err).Error("dbx.Select failed")
		return
	}

	if len(reports) == 0 {
		return "", ErrNoData
	}

	// generate csv file
	csvData := []csvFormat{
		{
			"OrderID",
			"GMO RecurringID",
			"UserID",
			"OpenID",
			"Streamer UserID",
			"Streamer OpenID",
			"ProductID",
			"Price",
			"Points",
			"Update Date",
		},
	}

	loc, err := r.rs.GetTimeLocation(context, region)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err":    err,
			"region": region,
		}).Error("get location error")
		return
	}
	var userIDs []string
	for _, report := range reports {
		userIDs = append(userIDs, report.UserID)
		if report.StreamerUserID != "" {
			userIDs = append(userIDs, report.StreamerUserID)
		}
	}

	users, err := r.us.GetPlainUsers(context, userIDs...)
	if err != nil {
		context.WithField("err", err).Error("Get user info error")
		return
	}

	userMap := map[string]models.User{}
	for _, u := range users {
		userMap[u.UserID] = u
	}

	for i := range reports {
		t := time.Unix((reports[i].Timestamp / 1000), 0).In(loc)
		productID := reports[i].ProductID
		handler := product.ProductHDLs[productID]
		p := handler.GetProduct()
		if !p.IsValid() || p.ArmyAddon == nil {
			continue
		}
		point := p.ArmyAddon.Point
		price := p.ArmyAddon.Price
		data := csvFormat{
			OrderID:        reports[i].OrderID,
			GMORecurringID: reports[i].GMORecurringID,
			UserID:         reports[i].UserID,
			OpenID:         userMap[reports[i].UserID].OpenID,
			StreamerUserID: reports[i].StreamerUserID,
			StreamerOpenID: userMap[reports[i].StreamerUserID].OpenID,
			ProductID:      reports[i].ProductID,
			Price:          strconv.FormatInt(int64(price), 10),
			Points:         strconv.FormatInt(point, 10),
			UpdateDate:     t.String(),
		}
		csvData = append(csvData, data)
	}

	buffer := bytes.NewBuffer([]byte("\xEF\xBB\xBF"))
	if err = gocsv.MarshalWithoutHeaders(csvData, buffer); err != nil {
		context.WithFields(logrus.Fields{
			"err": err,
		}).Error("gocsv.MarshalWithoutHeader write log error")
		return
	}

	// upload file
	filename := fmt.Sprintf("ARMY_subscribe_report_%v_%v_%v.csv", region, startTime.String(), endTime.String())

	psf := &file.File{
		Category:    intra.FileCategory_FINANCIAL_REPORT,
		Name:        filename,
		ContentType: storage.CSV,
		Reader:      bytes.NewReader(buffer.Bytes()),
	}

	filekeys, err := r.intraFile.Upload(context, []*file.File{psf})
	if err != nil {
		context.WithField("err", err).Error("Intra file upload error")
		return
	}

	return filekeys[0], nil
}
