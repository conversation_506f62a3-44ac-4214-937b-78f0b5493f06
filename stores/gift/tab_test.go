package gift

import (
	"fmt"
	"testing"
	"time"

	mapset "github.com/deckarep/golang-set/v2"
	"github.com/stretchr/testify/suite"

	"github.com/17media/api/models"
	confModel "github.com/17media/api/models/config"
	eventModels "github.com/17media/api/models/event"
	giftModel "github.com/17media/api/models/gift"
	giftConfig "github.com/17media/api/stores/gift/config"
)

type tabSuite struct {
	suite.Suite
}

func (s *tabSuite) TestComposeGiftTabs() {
	gifts := []*giftModel.InfoInternal{
		{
			Info: giftModel.Info{
				GiftID:    "Gift_none_label",
				Point:     600,
				LabelType: "",
			},
			Sequence:   1,
			IsOnline:   1,
			Tab:        "normal,group_by_label",
			RegionList: nil,
		},
		{
			Info: giftModel.Info{
				GiftID:    "Gift_new",
				Point:     700,
				LabelType: "new",
			},
			Sequence:   2,
			IsOnline:   1,
			Tab:        "normal,group_by_label",
			RegionList: nil,
		},
		{
			Info: giftModel.Info{
				GiftID:    "Gift_event",
				Point:     800,
				LabelType: "event",
			},
			Sequence:   3,
			IsOnline:   1,
			Tab:        "normal,group_by_label",
			RegionList: nil,
		},
		{
			Info: giftModel.Info{
				GiftID:    "boxgacha_event",
				Point:     800,
				LabelType: "boxgacha",
			},
			Sequence:   3,
			IsOnline:   1,
			Tab:        "normal,group_by_label",
			RegionList: nil,
		},
		{
			Info: giftModel.Info{
				GiftID:    "Gift_limited",
				Point:     700,
				LabelType: "",
				Type:      giftModel.Info_Limited,
			},
			Sequence:   0,
			IsOnline:   1,
			Tab:        "",
			RegionList: nil,
		},
	}
	conf := tabConf{
		Tabs: []tab{
			{
				ID:                       "normal",
				Type:                     giftModel.Tab_NORMAL,
				InRecent:                 false,
				Name:                     nil,
				MinPoint:                 0,
				MaxPoint:                 9999,
				MinLevel:                 0,
				MaxLevel:                 0,
				Layout:                   "",
				UseWhitelistedGift:       false,
				SupportedVersion:         nil,
				SupportedStreamerVersion: nil,
				GroupByLabel:             false,
			},
			{
				ID:                       "group_by_label",
				Type:                     giftModel.Tab_NORMAL,
				InRecent:                 false,
				Name:                     nil,
				MinPoint:                 0,
				MaxPoint:                 0,
				MinLevel:                 0,
				MaxLevel:                 0,
				Layout:                   "",
				UseWhitelistedGift:       false,
				SupportedVersion:         nil,
				SupportedStreamerVersion: nil,
				GroupByLabel:             true,
			},
			{
				ID:                       "recommendation",
				Type:                     giftModel.Tab_RECOMMENDATION,
				InRecent:                 false,
				Name:                     nil,
				MinPoint:                 0,
				MaxPoint:                 0,
				MinLevel:                 0,
				MaxLevel:                 0,
				Layout:                   "",
				UseWhitelistedGift:       false,
				SupportedVersion:         nil,
				SupportedStreamerVersion: nil,
				GroupByLabel:             false,
			},
			{
				ID:                       "customEvent",
				Type:                     giftModel.Tab_CUSTOM_EVENT,
				InRecent:                 false,
				Name:                     nil,
				MinPoint:                 0,
				MaxPoint:                 0,
				MinLevel:                 0,
				MaxLevel:                 0,
				Layout:                   "",
				UseWhitelistedGift:       false,
				SupportedVersion:         nil,
				SupportedStreamerVersion: nil,
				GroupByLabel:             false,
			},
		},
		LabelGroupingOrder: []string{"new", "none", "event"},
		HiddenGiftLabelSet: map[string]struct{}{"boxgacha": {}},
		HiddenGiftLabels:   []string{"boxgacha"},
		typeID: map[giftModel.Tab_Type]string{
			giftModel.Tab_RECOMMENDATION: "recommendation",
			giftModel.Tab_CUSTOM_EVENT:   "customEvent",
		},
		supportedVersions:         nil,
		supportedStreamerVersions: nil,
	}

	type args struct {
		user                       *models.User
		streamer                   *models.User
		language                   string
		deviceType                 string
		version                    string
		userPoint                  int32
		giftInfos                  []*giftModel.InfoInternal
		event                      *eventModels.EventForGift
		filterForABTestNewbieFocus bool
		streamerDeviceInfo         confModel.DeviceInfo
		smartGiftBox               giftConfig.SmartGiftBoxMetadata
	}

	tests := []struct {
		desc   string
		args   args
		assert func(tabs []*giftModel.Tab)
	}{
		{
			desc: "success, smart gift box feature on",
			args: args{
				user:                       &mockUser,
				streamer:                   &mockUser,
				language:                   "TW",
				deviceType:                 "IOS",
				version:                    "9.9.9",
				userPoint:                  10,
				giftInfos:                  gifts,
				event:                      nil,
				filterForABTestNewbieFocus: false,
				streamerDeviceInfo:         confModel.DeviceInfo{},
				smartGiftBox: giftConfig.SmartGiftBoxMetadata{
					Enabled: true,
					CustomEventGiftIDs: mapset.NewSet(
						"Gift_new",
						"Gift_event",
					),
				},
			},
			assert: func(tabs []*giftModel.Tab) {
				s.Require().Len(tabs, 4)
				s.Len(tabs[0].Gifts, 4)
				s.Equal("normal", tabs[0].TabID)
				s.Equal("group_by_label", tabs[1].TabID)
				s.Equal("", tabs[0].Gifts[0].LabelType)
				s.Equal("Gift_none_label", tabs[0].Gifts[0].GiftID)
				s.Equal("new", tabs[0].Gifts[1].LabelType)
				s.Equal("Gift_new", tabs[0].Gifts[1].GiftID)
				s.Equal("event", tabs[0].Gifts[2].LabelType)
				s.Equal("Gift_event", tabs[0].Gifts[2].GiftID)
				s.Equal("", tabs[0].Gifts[3].LabelType)
				s.Equal("Gift_limited", tabs[0].Gifts[3].GiftID)

				s.Len(tabs[1].Gifts, 3)
				s.Equal("new", tabs[1].Gifts[0].LabelType)
				s.Equal("Gift_new", tabs[1].Gifts[0].GiftID)
				s.Equal("", tabs[1].Gifts[1].LabelType)
				s.Equal("Gift_none_label", tabs[1].Gifts[1].GiftID)
				s.Equal("event", tabs[1].Gifts[2].LabelType)
				s.Equal("Gift_event", tabs[1].Gifts[2].GiftID)

				s.Len(tabs[2].Gifts, 0)
				s.Equal("recommendation", tabs[2].TabID)
				s.Equal(giftModel.Tab_RECOMMENDATION, tabs[2].Type)

				s.Len(tabs[3].Gifts, 2)
				s.Equal("customEvent", tabs[3].TabID)
				s.Equal(giftModel.Tab_CUSTOM_EVENT, tabs[3].Type)
				s.Equal("Gift_new", tabs[3].Gifts[0].GiftID)
				s.Equal("Gift_event", tabs[3].Gifts[1].GiftID)
			},
		},
		{
			desc: "success, smart gift box feature off",
			args: args{
				user:                       &mockUser,
				streamer:                   &mockUser,
				language:                   "TW",
				deviceType:                 "IOS",
				version:                    "9.9.9",
				userPoint:                  10,
				giftInfos:                  gifts,
				event:                      nil,
				filterForABTestNewbieFocus: false,
				streamerDeviceInfo:         confModel.DeviceInfo{},
				smartGiftBox: giftConfig.SmartGiftBoxMetadata{
					Enabled: false,
				},
			},
			assert: func(tabs []*giftModel.Tab) {
				s.Require().Len(tabs, 2)
				s.Equal("normal", tabs[0].TabID)
				s.Equal("group_by_label", tabs[1].TabID)
			},
		},
		{
			desc: "success, smart gift box on but no custom event gifts ID",
			args: args{
				user:                       &mockUser,
				streamer:                   &mockUser,
				language:                   "TW",
				deviceType:                 "IOS",
				version:                    "9.9.9",
				userPoint:                  10,
				giftInfos:                  gifts,
				event:                      nil,
				filterForABTestNewbieFocus: false,
				streamerDeviceInfo:         confModel.DeviceInfo{},
				smartGiftBox: giftConfig.SmartGiftBoxMetadata{
					Enabled: true,
				},
			},
			assert: func(tabs []*giftModel.Tab) {
				s.Require().Len(tabs, 4)
				s.Equal("normal", tabs[0].TabID)
				s.Equal("group_by_label", tabs[1].TabID)
				s.Equal("recommendation", tabs[2].TabID)
				s.Equal("customEvent", tabs[3].TabID)
				s.Len(tabs[3].Gifts, 0)
			},
		},
	}

	for _, test := range tests {
		s.Run(test.desc, func() {
			tabs := conf.ComposeGiftTabs(mockCTX,
				test.args.user, test.args.streamer,
				test.args.language, test.args.deviceType, test.args.version,
				test.args.userPoint,
				test.args.giftInfos,
				test.args.event,
				test.args.filterForABTestNewbieFocus,
				test.args.streamerDeviceInfo,
				test.args.smartGiftBox,
			)
			test.assert(tabs)
		})
	}
}

func (s *tabSuite) TestTabConfig() {
	tests := []struct {
		Desc           string
		Input          string
		ExpRes         tabConf
		ExpNewbiePoint int32
		ExpNewbieLevel int32
		ExpWarning     []string
		ExpErr         error
	}{
		{
			Desc: "Correct format",
			Input: `
labelGroupingOrder:
  - new
  - none
  - event
hiddenGiftLabels:
  - boxgacha
tabs:
  - id: "Newbie"
    groupByLabel: false
    type: 6
    inRecent: true
    name:
      EN: "Newbie"
      TW: "新人專區"
    maxPoint: 200
    maxLevel: 60
    supportedStreamerVersion:
      android: 1.1.1
      ios: 2.2.2
  - id: "luckyBag"
    groupByLabel: false
    type: 2
    inRecent: true
    name:
      EN: "Lucky Bag"
      TW: "隨機禮"
  - id: "TreasureChest"
    groupByLabel: false
    type: 3
    inRecent: false
    name:
      EN: "Lucky Draw"
      TW: "好運禮"
      CN: "好运礼"
      ID: "Peti Kebahagiaan"
      JP: "宝のツボ"
  - id: "slot2"
    groupByLabel: false
    type: 3
    inRecent: false
    name:
      EN: "Slot2"
      TW: "擲筊禮2"
  - id: "Heavenly"
    groupByLabel: false
    type: 0
    inRecent: true
    name:
      EN: "Heavenly"
      TW: "奢華"
    maxPoint: 2147483647
    minPoint: 10000
`,
			ExpRes: tabConf{
				LabelGroupingOrder: []string{
					"new",
					"none",
					"event",
				},
				HiddenGiftLabels: []string{
					"boxgacha",
				},
				HiddenGiftLabelSet: map[string]struct{}{
					"boxgacha": struct{}{},
				},
				Tabs: []tab{
					tab{
						ID:       "Newbie",
						Type:     giftModel.Tab_NEWBIE,
						InRecent: true,
						Name: map[string]string{
							"EN": "Newbie",
							"TW": "新人專區",
						},
						MaxPoint: int32(200),
						MaxLevel: int32(60),
						SupportedStreamerVersion: map[string]string{
							"android": "1.1.1",
							"ios":     "2.2.2",
						},
					},
					tab{
						ID:       "luckyBag",
						Type:     giftModel.Tab_LUCKY_BAG,
						InRecent: true,
						Name: map[string]string{
							"EN": "Lucky Bag",
							"TW": "隨機禮",
						},
					},
					tab{
						ID:       "TreasureChest",
						Type:     giftModel.Tab_SLOT,
						InRecent: false,
						Name: map[string]string{
							"EN": "Lucky Draw",
							"TW": "好運禮",
							"CN": "好运礼",
							"ID": "Peti Kebahagiaan",
							"JP": "宝のツボ",
						},
					},
					tab{
						ID:       "slot2",
						Type:     giftModel.Tab_SLOT,
						InRecent: false,
						Name: map[string]string{
							"EN": "Slot2",
							"TW": "擲筊禮2",
						},
					},
					tab{
						ID:       "Heavenly",
						Type:     giftModel.Tab_NORMAL,
						InRecent: true,
						Name: map[string]string{
							"EN": "Heavenly",
							"TW": "奢華",
						},
						MaxPoint: int32(2147483647),
						MinPoint: int32(10000),
					},
				},
				typeID: map[giftModel.Tab_Type]string{
					giftModel.Tab_NEWBIE:    "Newbie",
					giftModel.Tab_LUCKY_BAG: "luckyBag",
					giftModel.Tab_SLOT:      "slot2",
					giftModel.Tab_NORMAL:    "Heavenly",
				},
				supportedVersions: map[giftModel.Tab_Type]map[string]string{
					giftModel.Tab_NEWBIE:    nil,
					giftModel.Tab_LUCKY_BAG: nil,
					giftModel.Tab_SLOT:      nil,
					giftModel.Tab_NORMAL:    nil,
				},
				supportedStreamerVersions: map[giftModel.Tab_Type]map[string]string{
					giftModel.Tab_NEWBIE: map[string]string{
						"android": "1.1.1",
						"ios":     "2.2.2",
					},
					giftModel.Tab_LUCKY_BAG: nil,
					giftModel.Tab_SLOT:      nil,
					giftModel.Tab_NORMAL:    nil,
				},
				newbiePoint: int32(200),
				newbieLevel: int32(60),
			},
			ExpWarning: nil,
			ExpErr:     nil,
		},
		{
			Desc: "case: too many slots",
			Input: `
tabs:
  - id: "Newbie"
    type: 6
    name:
      EN: "Newbie"
      TW: "新人專區"
    maxPoint: 200
    maxLevel: 60
  - id: "default"
    type: 1
    name:
      EN: "Featured"
      TW: "推薦"
  - id: "luckyBag"
    type: 2
    name:
      EN: "Lucky Bag"
      TW: "隨機禮"
  - id: "slot"
    type: 3
    name:
      EN: "Slot"
      TW: "擲筊禮"
  - id: "slot2"
    type: 3
    name:
      EN: "Slot2"
      TW: "擲筊禮2"
  - id: "slot3"
    type: 3
    name:
      EN: "Slot3"
      TW: "擲筊禮3"
`,
			ExpErr: fmt.Errorf("number of slot tab must be 0, 1, or 2"),
		},
		{
			Desc: "case: too many lucky bags",
			Input: `
tabs:
  - id: "Newbie"
    type: 6
    name:
      EN: "Newbie"
      TW: "新人專區"
    maxPoint: 200
    maxLevel: 60
  - id: "default"
    type: 1
    name:
      EN: "Featured"
      TW: "推薦"
  - id: "luckyBag"
    type: 2
    name:
      EN: "Lucky Bag"
      TW: "隨機禮"
  - id: "luckyBag2"
    type: 2
    name:
      EN: "Lucky Bag2"
      TW: "隨機禮2"
  - id: "slot"
    type: 3
    name:
      EN: "Slot"
      TW: "擲筊禮"
`,
			ExpErr: fmt.Errorf("only one lucky bag is allowed"),
		},
	}

	for _, test := range tests {
		cfg := tabConf{}
		timeNow = func() time.Time { return time.Unix(0, 0) }
		data, warn, err := cfg.Check([]byte(test.Input))
		s.Equal(test.ExpWarning, warn, test.Desc)
		s.Equal(test.ExpErr, err, test.Desc)
		if err == nil {
			cfg.Apply(data)
		}
		if test.ExpErr == nil {
			s.Require().Equal(test.ExpRes, cfg, test.Desc)
		}
	}
}

func (s *tabSuite) TestGetMarqueesWithSingleKeySuccess() {
	tests := []struct {
		Desc       string
		Input      string
		ExpRes     TabMarqueesCfg
		ExpWarning []string
		ExpErr     error
	}{
		{
			Desc: "get marquee with single i18nKey from configs success",
			Input: `
army:
  - giftbox_exclusive_define
`,
			ExpErr:     nil,
			ExpWarning: []string{},
			ExpRes: TabMarqueesCfg{
				"army": []string{
					"giftbox_exclusive_define",
				},
			},
		},
	}

	for _, test := range tests {
		cfg := TabMarqueesCfg{}
		data, _, err := cfg.Check([]byte(test.Input))
		s.Require().NoError(err)
		cfg.Apply(data)

		s.NotNil(cfg)
		s.Equal(1, len(cfg["army"]))
		s.Equal(test.ExpRes["army"][0], cfg["army"][0])
	}
}

func (s *tabSuite) TestGetMarqueesWithMultiKeysSuccess() {
	tests := []struct {
		Desc       string
		Input      string
		ExpRes     TabMarqueesCfg
		ExpWarning []string
		ExpErr     error
	}{
		{
			Desc: "get marquee with multiple i18nKeys from configs success",
			Input: `
army:
  - giftbox_exclusive_define
TreasureChest:
  - giftbox_luckydraw_define
  - giftbox_baggage_define
Voucher:
  - giftbox_baggage_define
`,
			ExpErr:     nil,
			ExpWarning: []string{},
			ExpRes: TabMarqueesCfg{
				"army": []string{
					"giftbox_exclusive_define",
				},
				"TreasureChest": []string{
					"giftbox_luckydraw_define",
					"giftbox_baggage_define",
				},
				"Voucher": []string{
					"giftbox_baggage_define",
				},
			},
		},
	}

	for _, test := range tests {
		cfg := TabMarqueesCfg{}
		data, _, err := cfg.Check([]byte(test.Input))
		s.Require().NoError(err)
		cfg.Apply(data)

		s.NotNil(cfg)
		s.Equal(1, len(cfg["army"]))
		s.Equal(test.ExpRes["army"][0], cfg["army"][0])
		s.Equal(2, len(cfg["TreasureChest"]))
		s.Equal(test.ExpRes["TreasureChest"][0], cfg["TreasureChest"][0])
		s.Equal(test.ExpRes["TreasureChest"][1], cfg["TreasureChest"][1])
		s.Equal(1, len(cfg["Voucher"]))
		s.Equal(test.ExpRes["Voucher"][0], cfg["Voucher"][0])
	}
}

func (s *tabSuite) TestGetBannersWithSingleTabSuccess() {
	tests := []struct {
		Desc       string
		Input      string
		ExpRes     TabBannersCfg
		ExpWarning []string
		ExpErr     error
	}{
		{
			Desc: "get banners with single tab from configs success",
			Input: `
army:
  - actionType: 2
    giftBanner:
      EN: "https://assets-17app.akamaized.net/7e8fd3ac-ea26-4d81-9513-d7ed236a1e32.png"
      TW: "https://assets-17app.akamaized.net/717ddd12-eff1-4304-a08e-10a3068d2096.png"
      JP: "https://assets-17app.akamaized.net/b7aa9fd9-f839-4cb4-84c5-cf07e32cd2da.png"
  - actionType: 4
    giftBanner:
      EN: "https://assets-17app.akamaized.net/0dac8595-2070-4cc9-8545-72effea14ccb.png"
      TW: "https://assets-17app.akamaized.net/0b866145-0128-4d40-ac5e-58b33c321ed9.png"
      JP: "https://assets-17app.akamaized.net/ea6c8131-6b61-42e0-9a1c-1814d8311ccd.png"
`,
			ExpErr:     nil,
			ExpWarning: []string{},
			ExpRes: TabBannersCfg{
				"army": []*banner{
					&banner{
						ActionType: giftModel.Banner_ARMY,
						GiftBanner: map[string]string{
							"EN": "https://assets-17app.akamaized.net/7e8fd3ac-ea26-4d81-9513-d7ed236a1e32.png",
							"TW": "https://assets-17app.akamaized.net/717ddd12-eff1-4304-a08e-10a3068d2096.png",
							"JP": "https://assets-17app.akamaized.net/b7aa9fd9-f839-4cb4-84c5-cf07e32cd2da.png",
						},
					},
					&banner{
						ActionType: giftModel.Banner_ELITE,
						GiftBanner: map[string]string{
							"EN": "https://assets-17app.akamaized.net/0dac8595-2070-4cc9-8545-72effea14ccb.png",
							"TW": "https://assets-17app.akamaized.net/0b866145-0128-4d40-ac5e-58b33c321ed9.png",
							"JP": "https://assets-17app.akamaized.net/ea6c8131-6b61-42e0-9a1c-1814d8311ccd.png",
						},
					},
				},
			},
		},
	}

	for _, test := range tests {
		cfg := TabBannersCfg{}
		data, _, err := cfg.Check([]byte(test.Input))
		s.Require().NoError(err)
		cfg.Apply(data)

		s.NotNil(cfg)
		s.Equal(2, len(cfg["army"]))
		s.Equal(test.ExpRes["army"][0].ActionType, cfg["army"][0].ActionType)
		s.Equal(test.ExpRes["army"][0].GiftBanner["EN"], cfg["army"][0].GiftBanner["EN"])
		s.Equal(test.ExpRes["army"][1].ActionType, cfg["army"][1].ActionType)
		s.Equal(test.ExpRes["army"][1].GiftBanner["JP"], cfg["army"][1].GiftBanner["JP"])
	}
}

func (s *tabSuite) TestGetBannersWithMultipleTabsSuccess() {
	tests := []struct {
		Desc       string
		Input      string
		ExpRes     TabBannersCfg
		ExpWarning []string
		ExpErr     error
	}{
		{
			Desc: "get banners with multiple tabs from configs success",
			Input: `
army:
  - actionType: 2
    giftBanner:
      EN: "https://assets-17app.akamaized.net/7e8fd3ac-ea26-4d81-9513-d7ed236a1e32.png"
      TW: "https://assets-17app.akamaized.net/717ddd12-eff1-4304-a08e-10a3068d2096.png"
      JP: "https://assets-17app.akamaized.net/b7aa9fd9-f839-4cb4-84c5-cf07e32cd2da.png"
event:
  - actionType: 4
    giftBanner:
      EN: "https://assets-17app.akamaized.net/0dac8595-2070-4cc9-8545-72effea14ccb.png"
      TW: "https://assets-17app.akamaized.net/0b866145-0128-4d40-ac5e-58b33c321ed9.png"
      JP: "https://assets-17app.akamaized.net/ea6c8131-6b61-42e0-9a1c-1814d8311ccd.png"
`,
			ExpErr:     nil,
			ExpWarning: []string{},
			ExpRes: TabBannersCfg{
				"army": []*banner{
					&banner{
						ActionType: giftModel.Banner_ARMY,
						GiftBanner: map[string]string{
							"EN": "https://assets-17app.akamaized.net/7e8fd3ac-ea26-4d81-9513-d7ed236a1e32.png",
							"TW": "https://assets-17app.akamaized.net/717ddd12-eff1-4304-a08e-10a3068d2096.png",
							"JP": "https://assets-17app.akamaized.net/b7aa9fd9-f839-4cb4-84c5-cf07e32cd2da.png",
						},
					},
				},
				"event": []*banner{
					&banner{
						ActionType: giftModel.Banner_ELITE,
						GiftBanner: map[string]string{
							"EN": "https://assets-17app.akamaized.net/0dac8595-2070-4cc9-8545-72effea14ccb.png",
							"TW": "https://assets-17app.akamaized.net/0b866145-0128-4d40-ac5e-58b33c321ed9.png",
							"JP": "https://assets-17app.akamaized.net/ea6c8131-6b61-42e0-9a1c-1814d8311ccd.png",
						},
					},
				},
			},
		},
	}

	for _, test := range tests {
		cfg := TabBannersCfg{}
		data, _, err := cfg.Check([]byte(test.Input))
		s.Require().NoError(err)
		cfg.Apply(data)

		s.NotNil(cfg)
		s.Equal(1, len(cfg["army"]))
		s.Equal(test.ExpRes["army"][0].ActionType, cfg["army"][0].ActionType)
		s.Equal(test.ExpRes["army"][0].GiftBanner["EN"], cfg["army"][0].GiftBanner["EN"])
		s.Equal(1, len(cfg["event"]))
		s.Equal(test.ExpRes["event"][0].ActionType, cfg["event"][0].ActionType)
		s.Equal(test.ExpRes["event"][0].GiftBanner["JP"], cfg["event"][0].GiftBanner["JP"])
	}
}

func TestTabSuite(t *testing.T) {
	suite.Run(t, new(tabSuite))
}
