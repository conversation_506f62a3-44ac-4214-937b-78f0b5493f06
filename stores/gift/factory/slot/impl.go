package slot

import (
	"bytes"
	"database/sql"
	"encoding/gob"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	btime "github.com/17media/api/base/time"

	"github.com/fatih/structs"
	"github.com/jmoiron/sqlx"
	yaml "gopkg.in/yaml.v2"

	"github.com/17media/logrus"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/env"
	blog "github.com/17media/api/base/log"
	"github.com/17media/api/base/metrics"
	"github.com/17media/api/models"
	giftModel "github.com/17media/api/models/gift"
	i18nModel "github.com/17media/api/models/i18n"
	"github.com/17media/api/models/keys"
	marModel "github.com/17media/api/models/marquee"
	messengerModel "github.com/17media/api/models/messenger"
	moneyModel "github.com/17media/api/models/money"
	regModel "github.com/17media/api/models/region"
	userModel "github.com/17media/api/models/user"
	"github.com/17media/api/service/i18n"
	"github.com/17media/api/service/redis"
	util "github.com/17media/api/stores"
	giftConfig "github.com/17media/api/stores/gift/config"
	giftFactory "github.com/17media/api/stores/gift/factory"
	giftHelper "github.com/17media/api/stores/gift/helper"
)

const (
	// SlotGiftConfigPath contains the etcd path for the config
	SlotGiftConfigPath = "17app/gift/factory/slot.yaml"

	pfxSlot               = "slot"
	pfxJackpot            = "jackpot"
	pfxJackpotAmount      = "jackpotAmount"
	pfxCloseSlot          = "closeSlot"
	pfxCloseSlotCountDown = "countDownClose"

	marqueeAnimationID = "marquee_gift_slot"

	// ttlForKackpotLock is designed to be ttl of a criticle section lock
	// jackpot with contribution type equals to byUser will use it to
	// protect winner checking in isSlotHit
	//
	ttlForJackpotLock = 3 * time.Minute
)

var (
	met = metrics.New("gift.slot")

	mustHitSlotReasonableAmount = int32(100000)

	// For test use
	randFloat64 = rand.Float64

	getJackpotBankUserID = giftConfig.GetBankUserID
	getJackpotPoolUserID = giftConfig.GetPoolUserID
)

type slot struct {
	HitGiftIDs         []string          `yaml:"hitGiftIDs"`
	MissGiftIDs        []string          `yaml:"missGiftIDs"`
	InitialAmount      int32             `yaml:"initialAmount"`
	IncreasePercentage int32             `yaml:"increasePercentage"`
	DrawExpectation    int32             `yaml:"drawExpectation"`
	MinHitAmount       int32             `yaml:"minHitAmount"`
	MinHitDuration     int32             `yaml:"minHitDuration"`
	MustHitAmount      int32             `yaml:"mustHitAmount"`
	I18nTokenKey       string            `yaml:"i18nTokenKey"` // key for i18n.Tokenize
	SlotTab            giftModel.SlotTab `yaml:"slotTab"`      // type: slot or treasure chest
	LockEnabled        bool              `yaml:"lock"`         // Indicate if this item is locked or not
	// LockCountDownAmount indicates remaining times to hit slot before lock
	LockCountDownAmount int32 `yaml:"lockCountDownAmount"`
	// ContributionType means who contributes the reward of this lottery pool.
	// 0 (dummy), it's forbidden, invalidate. to avoid field missing.
	// 1 (bySystem) means normal treasure gifts. 17 contributes same amount of gift points
	//   into lottery pool to be reward for winner;
	// 2 (byUser) means other treasure gifts which are sent by users but gift points will
	//   be splited partially to contribute to lottery pool to be reward for winner.
	//   for example, a 100 points slot gift with contributionType 1,
	//   and poolRatio is 30 (30%), then (100 * 0.3) points will be put into
	//   lottery pool, and streamer can only get (100 * (1 - 0.3)) points from received gift.
	ContributionType int32 `yaml:"contributionType"`
	// PoolRatio means what ratio is configured to split gift points for lotto
	PoolRatio float32 `yaml:"poolRatio"`
	// PrizeRatio means how much winner can get from the lottery pool
	PrizeRatio float32 `yaml:"prizeRatio"`
}

// contributionType means who contributes the reward of this lottery pool.
// 0 (dummy), it's forbidden, invalidate. to avoid field missing.
// 1 (bySystem) means normal treasure gifts. 17 contributes same amount of gift points
//
//	into lottery pool to be reward for winner;
//
// 2 (byUser) means other treasure gifts which are sent by users but gift points will
//
//	be splited partially to contribute to lottery pool to be reward for
//	winner.
//
// | contributionType | Value |
// | --- | --- |
// | dummy | 0 |
// | bySystem | 1 |
// | byUser | 2 |
type contributionType int32

const (
	dummy    contributionType = 0
	bySystem contributionType = 1
	byUser   contributionType = 2
)

// Config contains config from etcd
type Config struct {
	config map[string]slot // config is map of giftID to slot

	GiftDb *sql.DB
}

// Check provides interface for etcd config
func (o *Config) Check(data []byte) (interface{}, []string, error) {
	context := ctx.Background()
	tmp := map[string]slot{}

	if err := yaml.Unmarshal(data, tmp); err != nil {
		return nil, nil, err
	}

	var warnings []string
	for slotGiftID, slotGift := range tmp {
		// Check if must hit amount is reasonable
		if slotGift.MustHitAmount < mustHitSlotReasonableAmount {
			context.WithField("slotGiftID", slotGiftID).Error("MustHitAmount must be reasonable, so this gift-setting will be skipped")
			delete(tmp, slotGiftID)
			warnings = append(warnings, fmt.Sprintf("MustHitAmount must be reasonable %v %v, so this gift-setting will be skipped", slotGiftID, slotGift.MustHitAmount))
			continue
		}

		slotGiftPoint, err := giftFactory.CheckGiftValidInDB(o.GiftDb, slotGiftID, giftModel.Info_Slot)
		if err != nil {
			delete(tmp, slotGiftID)
			warnings = append(warnings, fmt.Sprintf("no such gift in db %v, so this gift-setting will be skipped", slotGiftID))
			continue
		}

		// Check if draw expectation is smaller than gift price
		if slotGift.DrawExpectation > slotGiftPoint {
			context.WithFields(
				logrus.Fields{"slotGiftID": slotGiftID, "slotGiftPoint": slotGiftPoint, "DrawExpectation": slotGift.DrawExpectation},
			).Error("DrawExpectation must be smaller than slotGiftPoint, so this gift-setting will be skipped")
			delete(tmp, slotGiftID)
			warnings = append(warnings, fmt.Sprintf("DrawExpectation must be smaller than slotGiftPoint %v %v %v, so this gift-setting will be skipped", slotGiftID, slotGiftPoint, slotGift.DrawExpectation))
			continue
		}

		// Check if hitGiftIDs and missGiftIDs exist and must be dummy type gift
		for _, hitGiftID := range slotGift.HitGiftIDs {
			if _, err := giftFactory.CheckGiftValidInDB(o.GiftDb, hitGiftID, giftModel.Info_Dummy); err != nil {
				delete(tmp, slotGiftID)
				warnings = append(warnings, fmt.Sprintf("no such gift in db %v, so this gift-setting will be skipped", hitGiftID))
				continue
			}
		}
		for _, missGiftID := range slotGift.MissGiftIDs {
			if _, err := giftFactory.CheckGiftValidInDB(o.GiftDb, missGiftID, giftModel.Info_Dummy); err != nil {
				delete(tmp, slotGiftID)
				warnings = append(warnings, fmt.Sprintf("no such gift in db %v, so this gift-setting will be skipped", missGiftID))
				continue
			}
		}

		// Check if increasePercentage is smaller than 100%
		if slotGift.IncreasePercentage >= 100 {
			context.WithField("slotGiftID", slotGiftID).Error("increasePercentage must be smaller than 100%")
			delete(tmp, slotGiftID)
			warnings = append(warnings, fmt.Sprintf("IncreasePercentage must be smaller than 100 %v %v, so this gift-setting will be skipped", slotGiftID, slotGift.IncreasePercentage))
			continue
		}
		// Set default countdown amount when lock is enabled without LockCountDownAmount
		if slotGift.LockEnabled && slotGift.LockCountDownAmount <= 0 {
			warnings = append(warnings, fmt.Sprintf(
				"lockCountDownAmount must be larger than 0 when lock is true. original is %v, reset to 1", slotGift.LockCountDownAmount))
			slotGift.LockCountDownAmount = 1
			// try to refator as "tmp[slotGiftID].LockCountDownAmount = 1"
			tmp[slotGiftID] = slotGift
		}

		// check contributionType, the value shall be bySystem or byUser
		if slotGift.ContributionType != int32(bySystem) && slotGift.ContributionType != int32(byUser) {
			context.WithField("slotGiftID", slotGiftID).Error(
				"ContributionType must be bySystem (1) or byUser (2)")
			delete(tmp, slotGiftID)
			warnings = append(warnings, fmt.Sprintf(
				"invalid slot gift %v with contributionType %v", slotGiftID, slotGift.ContributionType))
		}

		// check poolRatio, the value shall be between 0 and 100
		if slotGift.PoolRatio < float32(0) || slotGift.PoolRatio > float32(100) {
			context.WithField("slotGiftID", slotGiftID).Error("PoolRatio must be between 0 and 100")
			delete(tmp, slotGiftID)
			warnings = append(warnings, fmt.Sprintf("invalid slot gift %v with pool ratio %v", slotGiftID, slotGift.PoolRatio))
		}

		// check prizeRatio, the value shall be between 0 and 100
		if slotGift.PrizeRatio < float32(0) || slotGift.PrizeRatio > float32(100) {
			context.WithField("slotGiftID", slotGiftID).Error("PrizeRatio must be between 0 and 100")
			delete(tmp, slotGiftID)
			warnings = append(warnings, fmt.Sprintf("invalid slot gift %v with prize ratio %v", slotGiftID, slotGift.PrizeRatio))
		}

	}
	return tmp, warnings, nil
}

// Apply provides interface for etcd config
func (o *Config) Apply(v interface{}) {
	o.config = v.(map[string]slot)
}

type impl struct {
	persist       redis.Service
	helper        giftHelper.Worker
	slotGiftsConf *Config

	prepared     bool
	originalGift *giftModel.InfoInternal

	t             time.Time
	slot          slot
	hit           bool
	jackpotAmount int32
	increase      int32
	fromIncPts    int64
	resultGiftIDs []string
	regionMode    regModel.RegionMode

	// Lua script handler for slot hits
	luaScript *redis.ScriptHdl
}

// Factory is the "factory" pattern for corresponding object
type Factory struct {
	SlotGiftsConf *Config
}

// GenerateProduct creates slot object
func (sf *Factory) GenerateProduct(r giftFactory.Resources) giftFactory.Product {
	return &impl{
		persist:       r.Persist,
		helper:        r.Helper,
		slotGiftsConf: sf.SlotGiftsConf,
		luaScript:     redis.NewScript(2, LuaSlotHit),
	}
}

func getJackpotKey(giftID string) string {
	return keys.RedisKey(keys.PfxGift, pfxSlot, pfxJackpot, giftID)
}

func getJackpotAmountKey(giftID string) string {
	return keys.RedisKey(keys.PfxGift, pfxSlot, pfxJackpotAmount, giftID)
}

func getJackpotLockKey(giftID string) string {
	return keys.RedisKey(keys.PfxGift, pfxSlot, pfxJackpot, "hitJackPotLock", giftID)
}

func getCloseSlotKey(giftID string) string {
	return keys.RedisKey(keys.PfxGift, pfxSlot, pfxCloseSlot, giftID)
}

func getCloseSlotCountDownKey(giftID string) string {
	return keys.RedisKey(keys.PfxGift, pfxSlot, pfxCloseSlotCountDown, giftID)
}

// determine our expected value is negative.
func isDeficit(slot slot, point int32, jackpotAmount int) bool {
	probHit := float64(slot.DrawExpectation) / float64(jackpotAmount)

	if probHit >= 1.0 {
		return true
	}

	return float64(slot.InitialAmount)*probHit >= (float64(point)*float64(100-slot.IncreasePercentage)/100)*(1.0-probHit)
}

func (im *impl) slotIsClosed(context ctx.CTX, giftID string) bool {
	// Try to get the slot lock key
	if _, err := im.persist.Get(context, getCloseSlotKey(giftID)); err != nil {
		if err != redis.ErrNotFound {
			context.WithField("err", err).Warn("Error on getting CloseSlotKey")
		}
		return false
	}
	return true
}

// isContributedByUser judges contribution type of a slot gift is contributed by
// system or user.
func (im *impl) isContributedByUser(context ctx.CTX, giftID string) bool {
	if im.slotGiftsConf.config[giftID].ContributionType == int32(byUser) {
		return true
	}

	return false
}

func (im *impl) Prepare(
	context ctx.CTX,
	fromUser, toUser *models.User,
	srcType models.Source, srcID string,
	giftInfo *giftModel.InfoInternal,
	sendOption giftModel.SendOption,
) (*giftFactory.TransactInfo, error) {
	im.originalGift = giftInfo
	im.t = btime.TimeNow()

	slotConfItem, exist := im.slotGiftsConf.config[giftInfo.GiftID]
	if !exist {
		return nil, fmt.Errorf("No this slot gift related info in config")
	}
	im.slot = slotConfItem

	// Check if this slot is locked
	if im.slot.LockEnabled && im.slotIsClosed(context, giftInfo.GiftID) {
		return nil, ErrTreasBoxClosed
	}

	// get current jackpotAmount from redis (contributionType == bySystem) or
	// virtual user (contributionType == byUser)
	jackpotAmount, err := im.getJackpotAmount(context, giftInfo.GiftID)
	if err != nil {
		return nil, err
	}

	dealings := []*moneyModel.Dealing{}
	queryGetters := []func(tradeID string, dealingIDs []string, timeMillis int64) moneyModel.Query{}

	// check if need to split giftInfo.Point
	if im.isContributedByUser(context, giftInfo.GiftID) {
		bankUserID, ok := getJackpotBankUserID(giftInfo.GiftID)
		if !ok {
			return nil, fmt.Errorf("get bankUserID fail")
		}

		poolUserID, ok := getJackpotPoolUserID(giftInfo.GiftID)
		if !ok {
			return nil, fmt.Errorf("get poolUserID fail")
		}

		// initial jackpot by first draw
		if int(jackpotAmount) == 0 && im.canJackpotInit(context, giftInfo.GiftID) {
			initDealing := jackpotBankTeller(bankUserID, poolUserID, int64(im.slot.InitialAmount), moneyModel.BankActionWithdraw)

			if initDealing != nil {
				initDealing.AfterFunc = func(tx *sqlx.Tx, tradeID string, dealingID string, timeMillis int64) error {
					// financial purpose
					if _, err := tx.Exec(`INSERT INTO LotteryPoolLogs SET bankID=?, poolID=?, category=?, points=?, timestamp=?, tradeID=?, dealingID=?, action=?`,
						bankUserID, poolUserID, moneyModel.Category_GIFT,
						initDealing.Amount, timeMillis/1000, tradeID, dealingID,
						giftModel.LotteryPoolActionInitial,
					); err != nil {
						return err
					}
					return nil
				}
				dealings = append(dealings, initDealing)
			}

			jackpotAmount = int(im.slot.InitialAmount)
		}

		// split gift point
		contributedPoints := int64(float32(giftInfo.Point) * float32(im.slot.PoolRatio) / float32(100))
		if contributedPoints > 0 {
			// if contributedPoints is larger than 0, draw it into lottery pool
			// CT to pool
			dealings = append(dealings, &moneyModel.Dealing{
				Category:     moneyModel.Category_GIFT_LUCKYDRAW,
				FromUserID:   fromUser.UserID,
				ToUserID:     poolUserID,
				FromCurrency: moneyModel.Currency_POINT,
				ToCurrency:   moneyModel.Currency_POINT,
				Amount:       int64(contributedPoints),
				AfterFunc: func(tx *sqlx.Tx, tradeID string, dealingID string, timeMillis int64) error {
					// PUL to pool
					// TODO: Remove this two legacy log when PointUsageLog is deprecated
					res, err := tx.Exec("INSERT INTO PointUsageLog SET userID=?, receiverUserID=?, type=?, giftID=?, point=?, timestamp=?, migration=?, tradeID=?",
						fromUser.UserID, poolUserID, giftConfig.AxeTreasurePUGLType, giftInfo.GiftID, contributedPoints,
						timeMillis/1000, 3, tradeID)
					if err != nil {
						return err
					}
					// PUL event
					pulID, _ := res.LastInsertId()
					deviceInfo := util.ExtractDeviceInfo(context)
					pulEvent := moneyModel.PULDataEvent{
						ID:             pulID,
						Timestamp:      timeMillis / 1000,
						UserID:         fromUser.UserID,
						ReceiverUserID: poolUserID,
						Type:           giftConfig.AxeTreasurePUGLType,
						GiftID:         giftInfo.GiftID,
						Point:          contributedPoints,
						DeviceType:     deviceInfo.Type,
						App:            deviceInfo.App,
					}
					blog.Event(context, "userPointUsage", structs.Map(pulEvent))

					return nil
				},
			})

			// minus contributedPoints from giftPoint
			// revenue user gets less points than gift who's contributionType is bySystem
			giftInfo.Point = giftInfo.Point - int32(contributedPoints)
			im.increase = int32(contributedPoints)
		}
	}

	context = ctx.WithValues(context, map[string]interface{}{"jackPotAmount": jackpotAmount})
	im.jackpotAmount = int32(jackpotAmount)

	// check if this user wins this slot lottery
	probHit := float64(im.slot.DrawExpectation) / float64(jackpotAmount)
	drawRes := randFloat64()
	im.hit = im.isSlotHit(
		context, drawRes, probHit,
		im.originalGift.GiftID, im.originalGift.Point, jackpotAmount)

	if im.hit {
		// if this user wins ...
		slotHitDealings := im.slotHitTransactions(context, fromUser.UserID, giftInfo.GiftID)
		dealings = append(dealings, slotHitDealings...)

		// im.slot.HitGiftIDs is for "winning" animation
		im.resultGiftIDs = im.slot.HitGiftIDs
		im.increase = 0
		im.fromIncPts = int64(im.jackpotAmount)

		// check if this slot gift is locked
		if im.slot.LockEnabled {
			// increase counter of hits by one.
			retVal, err := im.persist.Incr(context, getCloseSlotCountDownKey(giftInfo.GiftID))
			if err != nil {
				context.WithField("err", err).Warn("persust.Incr() failed")
				return nil, err
			}

			// locked it if count of hits equals to ore larger than LockCountDownAmount ...
			if int32(retVal) >= im.slot.LockCountDownAmount {
				// set a key with ttl 6 months for lock. if this slot wanna
				// be re-opened in the future, remember to delete this key.
				if err := im.persist.SetNXLegacy(context, getCloseSlotKey(giftInfo.GiftID),
					[]byte("1"), 180*24*time.Hour); err != nil {
					context.WithField("err", err).Warn("Failed to get key to close slot.")
					return nil, err
				}

				// delete its count down key. no need to count down any more.
				if _, err := im.persist.Del(context, getCloseSlotCountDownKey(giftInfo.GiftID)); err != nil {
					context.WithField("err", err).Warn("redis.Del() failed")
				}
			}
		}

	} else {
		// if miss ...
		// im.slot.MissGiftIDs is for "missing" animation
		im.resultGiftIDs = im.slot.MissGiftIDs
		im.fromIncPts = 0
		if !im.isContributedByUser(context, im.originalGift.GiftID) {
			im.increase = int32(float32(giftInfo.Point) * (float32(im.slot.IncreasePercentage) / float32(100)))
		}
	}

	regionMode, err := im.helper.GetRegionModeMapping(context, im.originalGift.RegionMode)
	if err != nil {
		return nil, err
	}
	im.regionMode = regionMode

	// prepare is done and compose send info
	im.prepared = true
	// FIXME:
	// queryGetters seems no any appending, but to ensure i merge same code structure
	// with pr 1194, keep it first and refactor it later.
	return &giftFactory.TransactInfo{
		ResultGift:   giftInfo,
		FromIncPts:   im.fromIncPts,
		ToIncPts:     int64(giftInfo.Point),
		Dealings:     dealings,
		QueryGetters: queryGetters,
		FactoryName:  "slot",
	}, nil
}

func (im *impl) OnSuccess(
	context ctx.CTX,
	fromUser, toUser *models.User,
	srcType models.Source, srcID string,
	notifID string,
	giftToken int64,
	displayUser *models.DisplayUserMsg,
	tradeID string,
) (*giftFactory.SendInfo, error) {
	if !im.prepared {
		return nil, fmt.Errorf("please call prepare first")
	}

	slotLocked := im.slot.LockEnabled && im.slotIsClosed(context, im.originalGift.GiftID)

	if im.hit {
		rewardPoints := im.jackpotAmount
		if im.isContributedByUser(context, im.originalGift.GiftID) {
			rewardPoints = int32(float32(rewardPoints) * float32(im.slot.PrizeRatio) / float32(100))

			// reset pool is done in "Prepare"
			// when contributionType is byUser

		} else {
			// reset pool amount
			if err := im.persist.Set(
				context,
				getJackpotAmountKey(im.originalGift.GiftID),
				[]byte(strconv.Itoa(int(im.slot.InitialAmount))),
				redis.Forever,
			); err != nil {
				context.WithField("err", err).Error("Reset slot jackpot amount fail")
			}
		}

		if _, err := im.persist.Del(context, getJackpotLockKey(im.originalGift.GiftID)); err != nil {
			context.WithField("err", err).Warn("clear gift lock fail")
		}

		// record winner information
		var buf bytes.Buffer
		if err := gob.NewEncoder(&buf).Encode(&giftModel.ResSlot_Jackpot{
			TimeMs: im.t.UnixNano() / int64(time.Millisecond),
			Amount: int32(rewardPoints),
			Winner: &userModel.DisplayInfo{
				UserID:      fromUser.UserID,
				DisplayName: fromUser.DisplayName,
				Picture:     fromUser.Picture,
				Level:       fromUser.Level,
			},
		}); err != nil {
			context.WithField("err", err).Warn("slot gift gob encode failed")
		} else {
			// TODO: deprecate these two `Set` by using the `ScriptDo` below
			if err := im.persist.Set(
				context,
				getJackpotKey(im.originalGift.GiftID),
				buf.Bytes(),
				redis.Forever,
			); err != nil {
				context.WithField("err", err).Error("Set slot jackpot fail")
			}

			if !im.isContributedByUser(context, im.originalGift.GiftID) {
				args, errLuaKey := keys.RedisLuaMultiKey(
					// KEYS[1]
					getJackpotKey(im.originalGift.GiftID),
					// KEYS[2]
					getJackpotAmountKey(im.originalGift.GiftID),
				)
				if errLuaKey != nil {
					context.WithField("err", err).Warn("RedisLuaMultiKey failed")
				}
				args = append(
					args,
					// ARGV[1]
					buf.Bytes(),
					// ARGV[2]
					[]byte(strconv.Itoa(int(im.slot.InitialAmount))),
				)
				// This lua script will replace the previous two `im.persis.Set`
				// after it is deployed to production for a while
				if _, err := im.persist.ScriptDo(context, im.luaScript, args...); err != nil {
					context.WithField("err", err).Warn("slot gift lua ScriptDo failed")
				}
			}
		}

		// bumpsum metrics
		met.BumpSum("hit", 1, "giftID", im.originalGift.GiftID)
	} else if !im.isContributedByUser(context, im.originalGift.GiftID) {
		// if miss ...
		// Add jackpot amount.
		if !slotLocked {
			if _, err := im.persist.Incrby(context, getJackpotAmountKey(im.originalGift.GiftID), int(im.increase)); err != nil {
				context.WithField("err", err).Warn("Incrby slot jackpot fail")
			}
			met.BumpSum("hit", 0, "giftID", im.originalGift.GiftID)
		}
	}

	// Add slot meta info to response
	metas := []*giftModel.Meta{}
	for _, id := range im.resultGiftIDs {
		metas = append(metas, &giftModel.Meta{
			TargetGiftID: id,
			Slot: &giftModel.SlotMeta{
				TriggerGiftID: im.originalGift.GiftID,
				Hit:           im.hit,
				Jackpot:       im.jackpotAmount,
				Increase:      im.increase,
				SlotTab:       im.slot.SlotTab,
			},
		})
	}

	newGiftMsg, msgs := im.genPubMsg(context, fromUser, toUser, notifID, giftToken, displayUser, metas)
	return &giftFactory.SendInfo{
		DisplayGiftIDs: getDisplayGiftIDs(im.originalGift, im.resultGiftIDs...),
		Metas:          metas,
		Msgs:           msgs,
		NewGiftMsg:     newGiftMsg,
	}, nil
}

func (im *impl) OnFail(context ctx.CTX, fromUser *models.User) error {
	if !im.prepared {
		return fmt.Errorf("please call prepare first")
	}
	if _, err := im.persist.Del(context, getJackpotLockKey(im.originalGift.GiftID)); err != nil {
		return err
	}
	return nil
}

func getDisplayGiftIDs(oriGift *giftModel.InfoInternal, resultIDs ...string) []string {
	displayGiftIDs := []string{oriGift.GiftID}
	displayGiftIDs = append(displayGiftIDs, resultIDs...)
	return displayGiftIDs
}

func (im *impl) genPubMsg(
	context ctx.CTX,
	fromUser, toUser *models.User,
	notifID string,
	giftToken int64,
	displayUser *models.DisplayUserMsg,
	metas []*giftModel.Meta,
) (*models.Message, map[string][]*models.Message) {
	msgMap := map[string][]*models.Message{
		notifID:                          []*models.Message{},
		messengerModel.GlobalLiveChannel: []*models.Message{},
	}

	paramOpenID := i18nModel.Param{
		Value:     fromUser.OpenID,
		MapOption: i18nModel.MapOption_NONE,
	}
	paramJackpotAmount := i18nModel.Param{
		Value:     strconv.Itoa(int(float64(im.jackpotAmount) * float64(im.slot.PrizeRatio) / 100)),
		MapOption: i18nModel.MapOption_NONE,
	}
	token := i18n.Tokenize(im.slot.I18nTokenKey, &paramOpenID, &paramJackpotAmount)

	if im.hit {
		msgMap[messengerModel.GlobalLiveChannel] = append(msgMap[messengerModel.GlobalLiveChannel],
			&models.Message{
				Type: models.MsgType_MARQUEE,
				MarqueeMsg: &models.MarqueeMsg{
					FeatureType: marModel.FeatureType_GIFT_SLOT,
					Token:       &token,
					RoomID:      notifID,
					AnimationID: marqueeAnimationID,
					Region: &regModel.RegionSetting{
						RegionMode: im.regionMode,
						Regions:    im.originalGift.Regions,
					},
				},
			},
			&models.Message{
				Type: models.MsgType_GLOBAL_INFO_UPDATE,
				GlobalInfoUpdateMsg: &models.GlobalInfoUpdateMsg{
					Type: models.GlobalInfoUpdateType_SLOT_GIFT,
				},
			},
		)
	}

	msg := &models.Message{
		Type: models.MsgType_NEW_GIFT,
		GiftMsg: &models.GiftMsg{
			DisplayUser: displayUser,
			GiftID:      im.originalGift.GiftID,
			GiftToken:   giftToken,
			GiftIDs:     getDisplayGiftIDs(im.originalGift, im.resultGiftIDs...),
			GiftMetas:   metas,
		},
	}

	// Deprecate this when move to only NewGift msg
	msgMap[notifID] = append(msgMap[notifID], msg)
	return msg, msgMap
}

// getSlotJackpotAmount is only for gifts who's contributionType equals to bySystem.
func (im *impl) getSlotJackpotAmount(context ctx.CTX, slotGiftID string) (int, error) {
	slotConfItem, exist := im.slotGiftsConf.config[slotGiftID]
	if !exist {
		return 0, fmt.Errorf("no slot gift %s in config", slotGiftID)
	}

	// Get current jackpot amount
	jackpotAmount := int(slotConfItem.InitialAmount)
	b, err := im.persist.Get(context, getJackpotAmountKey(slotGiftID))
	switch err {
	case redis.ErrNotFound:
		// Set jackpot initial amount
		if err := im.persist.Set(context, getJackpotAmountKey(slotGiftID), []byte(strconv.Itoa(jackpotAmount)), redis.Forever); err != nil {
			context.WithFields(logrus.Fields{"err": err, "slotGiftID": slotGiftID}).Warn("Set slot jackpot initial amount fail")
		}
	case nil:
		if jackpotAmount, err = strconv.Atoi(string(b)); err != nil {
			return 0, err
		}
	default:
		return 0, err
	}
	return jackpotAmount, nil
}

func (im *impl) getLastJackpot(context ctx.CTX, slotGiftID string) (giftModel.ResSlot_Jackpot, error) {
	// Get last jackpot info
	jackpot := giftModel.ResSlot_Jackpot{}
	b, err := im.persist.Get(context, getJackpotKey(slotGiftID))
	if err == nil {
		err = gob.NewDecoder(bytes.NewBuffer(b)).Decode(&jackpot)
	} else if err == redis.ErrNotFound {
		err = nil
	}
	return jackpot, err
}

// FIXME: tritonho: why the (luckybag, slot) impl is different from others
// it seems the impl object has multiple unrelated function.
// Suggestion: break the impl into two seperate object
func (im *impl) Get(context ctx.CTX, language string) (*giftModel.ResSlot, error) {
	res := giftModel.ResSlot{}
	for slotGiftID, slotGiftConf := range im.slotGiftsConf.config {
		// get current jackpotAmount from redis (contributionType == bySystem) or
		// virtual user (contributionType == byUser)
		jackpotAmount, err := im.getJackpotAmount(context, slotGiftID)
		if err != nil {
			context.WithField("err", err).Error("getJackpotAmount failed")
			return nil, err
		}
		// Get last jackpot info
		jackpot, err := im.getLastJackpot(context, slotGiftID)
		if err != nil {
			context.WithField("err", err).Error("getLastJackpot failed")
			return nil, err
		}

		remainingCount := slotGiftConf.LockCountDownAmount
		if slotGiftConf.LockCountDownAmount > 0 {
			b, err := im.persist.Get(context, getCloseSlotCountDownKey(slotGiftID))
			if err != nil && err != redis.ErrNotFound {
				context.WithField("err", err).Warn("redis.Get() failed")
				return nil, err
			} else if err == nil {
				// Get countdown number
				if val, err := strconv.Atoi(string(b)); err != nil {
					context.WithField("err", err).Warn("Atoi() failed")
				} else {
					remainingCount = slotGiftConf.LockCountDownAmount - int32(val)
					if remainingCount < 0 {
						context.WithField("value", val).Warn("CountDownAmount < 0")
						remainingCount = 0
					}
				}
			}
			// When lock is disabled, set remaining count as zero
			if !slotGiftConf.LockEnabled {
				remainingCount = int32(0)
			}
		}

		slotLock := slotGiftConf.LockEnabled && im.slotIsClosed(context, slotGiftID)
		if slotLock {
			remainingCount = int32(0)
		}

		// Compose response
		res.Slots = append(res.Slots, &giftModel.ResSlot_Slot{
			GiftID:          slotGiftID,
			Amount:          int32(jackpotAmount),
			Jackpot:         &jackpot,
			Lock:            slotLock,
			CountDownAmount: remainingCount, // This field makes sense only when Lock equals to false
		})
	}
	return &res, nil
}

// isSlotHit detect if there is an winner in this draw.
// there are conditions (in isDeficit):
//  1. if float64(slot.DrawExpectation) / float64(jackpotAmount) equals to or
//     larger than 1, then miss;
//  2. if float64(slot.InitialAmount)*probHit equals to or larger than
//     (float64(point)*float64(100-slot.IncreasePercentage)/100)*(1.0-probHit),
//     then miss;
//  3. otherwise, could hit.
func (im *impl) isSlotHit(
	context ctx.CTX,
	drawRes, probHit float64,
	giftID string, giftPoint int32, jackpotAmount int,
) bool {
	if isDeficit(im.slot, giftPoint, jackpotAmount) {
		context.WithFields(logrus.Fields{
			"jackpotAmount": jackpotAmount,
			"slotGiftID":    giftID,
		}).Warn("jackpot easy to win")

		// force miss
		return false
	}
	if int32(jackpotAmount) < im.slot.MustHitAmount {
		if int32(jackpotAmount) < im.slot.MinHitAmount {
			return false
		}
		jackpot, err := im.getLastJackpot(context, giftID)
		if err != nil {
			context.WithFields(logrus.Fields{
				"err":    err,
				"giftID": giftID,
			}).Error("getLastJackpot failed")
			return false
		}
		timeMs := im.t.UnixNano() / int64(time.Millisecond)
		if timeMs-jackpot.TimeMs < int64(im.slot.MinHitDuration)*int64(time.Hour)/int64(time.Millisecond) {
			return false
		}
	}

	if drawRes < probHit || int32(jackpotAmount) >= im.slot.MustHitAmount {
		// perhaps hit the jackpot ...
		context.WithField("timeNano", im.t.UnixNano()).Info("Someone trys to get the jackpot")
		if err := im.persist.SetNXLegacy(
			// acquire lock fail
			context, getJackpotLockKey(giftID), []byte("1"), 12*time.Hour); err != nil {
			return false
		}

		// hit!
		context.WithField("timeNano", im.t.UnixNano()).Info("Someone get the jackpot")
		return true
	}

	return false
}

// lockForJackpotInit holds lock to protect jackpot initial process
// this only for contribution type equals to byUser.
func (im *impl) lockForJackpotInit(context ctx.CTX, giftID string) error {
	defer met.BumpTime("lock.JackpotByUser.init").End()
	key := keys.RedisKey(keys.PfxGift, pfxJackpot, giftID, "init")
	if err := im.persist.SetNXLegacy(context, key, []byte("locked"), ttlForJackpotLock); err != nil {
		// redis.ErrNotFound means key already exists
		if err != redis.ErrNotFound {
			met.BumpSum("lock.jackpotbyuserinit.err", 1, "reason", "setNX failed")
			context.WithFields(logrus.Fields{
				"err": err,
				"key": key,
			}).Error("jackpotInit lock setNX failed")
		}
		return err
	}
	return nil
}

// canJackpotInit is designed to protect initial pool process
// to avoid duplicate initialization.
// use the same ttl setting as hitting, because of QPS calculation is
// the same.
func (im *impl) canJackpotInit(context ctx.CTX, giftID string) bool {
	// lock this criticle section
	if err := im.lockForJackpotInit(context, giftID); err != nil {
		return false
	}

	return true
}

// getJackpotAmount returns jackpotAmount from related lottery pool
func (im *impl) getJackpotAmount(
	context ctx.CTX, giftID string) (
	int, error,
) {

	typ := contributionType(im.slotGiftsConf.config[giftID].ContributionType)
	switch typ {
	case bySystem:
		jackpotAmount, err := im.getSlotJackpotAmount(context, giftID)
		if err != nil {
			return 0, fmt.Errorf("get jackpotAmount fail: giftID: %s", giftID)
		}
		return int(jackpotAmount), nil

	case byUser:
		poolUserID, ok := getJackpotPoolUserID(giftID)
		if !ok {
			return 0, fmt.Errorf("get jackpot poolUserID fail: giftID: %s", giftID)
		}
		jackpotAmount, err := im.helper.GetUserPoint(context, poolUserID)
		if err != nil {
			return 0, fmt.Errorf("get jackpotAmount fail: giftID: %s", giftID)
		}
		return int(jackpotAmount), nil

	case dummy:
	default:
		context.WithField("giftID", giftID).Error("illegal getJackpotAmount")
	}

	return 0, fmt.Errorf("invalid giftID: %s", giftID)
}

func (im *impl) slotHitTransactions(
	context ctx.CTX,
	fromUserID, giftID string,
) []*moneyModel.Dealing {

	if !im.hit {
		return []*moneyModel.Dealing{}
	}
	dealings := []*moneyModel.Dealing{}
	rewardPoints := im.jackpotAmount
	// record CT & PGL information of winner
	if im.isContributedByUser(context, giftID) {
		bankUserID, ok := getJackpotBankUserID(giftID)
		if !ok {
			return []*moneyModel.Dealing{}
		}

		poolUserID, ok := getJackpotPoolUserID(giftID)
		if !ok {
			return []*moneyModel.Dealing{}
		}

		rewardPoints = int32(float32(rewardPoints) * float32(im.slot.PrizeRatio) / float32(100))
		// CT from pool to user
		// FIXME:
		// winningDealingID is a workaround, remove it when pr 1264 is merged
		dealings = append(dealings, &moneyModel.Dealing{
			Category:     moneyModel.Category_GIFT,
			FromUserID:   poolUserID,
			ToUserID:     fromUserID,
			FromCurrency: moneyModel.Currency_POINT,
			ToCurrency:   moneyModel.Currency_POINT,
			Amount:       int64(rewardPoints),
			AfterFunc: func(tx *sqlx.Tx, tradeID string, dealingID string, timeMillis int64) error {
				// PGL of user
				if _, err := tx.Exec("INSERT INTO PointGainLog SET userID=?, type=?, productID=?, point=?, timestamp=?, platform=?, migration=?, tradeID=?",
					fromUserID, giftConfig.AxeTreasurePUGLType, giftID, rewardPoints, timeMillis/1000,
					giftConfig.AxeTreasurePUGLType, 3, tradeID); err != nil {
					return err
				}

				// financial purpose
				// extra record to LotteryPoolLogs for winner information
				if _, err := tx.Exec("INSERT INTO LotteryPoolLogs SET bankID=?, poolID=?, category=?, points=?, winnerID=?, timestamp=?, tradeID=?, dealingID=?, action=?",
					bankUserID, poolUserID, moneyModel.Category_GIFT,
					rewardPoints, fromUserID, timeMillis/1000, tradeID, dealingID,
					giftModel.LotteryPoolActionWinning,
				); err != nil {
					return err
				}

				return nil
			},
		})

		// CT from bank to pool, because reset the amount of prize in pool
		//
		// after pay-out to winner with PrizeRatio, remaining amount in Pool
		// is finalPoolPoints - rewardPoints.
		// so if we want to reset amount of Pool to InitPoolPoints,
		// Bank can just back fill backFillPoints =
		// InitPoolPoints - (im.jackpotAmount - rewardPoints)
		backFillPoints := int64(im.slot.InitialAmount - (im.jackpotAmount - int32(rewardPoints)))
		resetDealing := jackpotBankTeller(bankUserID, poolUserID, int64(backFillPoints), moneyModel.BankActionWithdraw)

		if resetDealing != nil {
			resetDealing.AfterFunc = func(tx *sqlx.Tx, tradeID string, dealingID string, timeMillis int64) error {
				// financial purpose
				// extra record to LotteryPoolLogs for lottery pool reset
				if _, err := tx.Exec("INSERT INTO LotteryPoolLogs SET bankID=?, poolID=?, category=?, points=?, timestamp=?, tradeID=?, dealingID=?, action=?",
					bankUserID, poolUserID, moneyModel.Category_GIFT,
					backFillPoints, timeMillis/1000, tradeID, dealingID,
					giftModel.LotteryPoolActionInitial,
				); err != nil {
					return err
				}
				return nil
			}
			dealings = append(dealings, resetDealing)
		}

	} else {
		// CT from pool to user
		// different from new kind of slot/treasure gifts, it uses:
		// 1. type = "slotJackpot"
		// 2. productID = "slot_jackpot"
		// 3. platform = "17"
		//
		// items of new kinds of slot/treasure gifts are:
		// 1. type = "axeTreasure"
		// 1. productID equals to giftID
		// 2. platform equals to giftConfig.AxeTreasurePUGLType (axeTreasure)
		// "productID equals to giftID" can separate different lottery pools for sure
		// "platform equals to giftConfig.AxeTreasurePUGLType" can identify new kinds of gifts
		//
		// TODO:
		// we have only "axe treasure gifts" for now, so we hard code naming with "axe".
		// once if there's any other similar gifts need to be integrated, naming shall be
		// refactor.
		dealings = append(dealings, &moneyModel.Dealing{
			Category:     moneyModel.Category_GIFT,
			FromUserID:   env.OfficialPseudoUserID,
			ToUserID:     fromUserID,
			FromCurrency: moneyModel.Currency_POINT,
			ToCurrency:   moneyModel.Currency_POINT,
			Amount:       int64(rewardPoints),
			AfterFunc: func(tx *sqlx.Tx, tradeID string, dealingID string, timeMillis int64) error {
				// PGL of user
				if _, err := tx.Exec("INSERT INTO PointGainLog SET userID=?, type=?, productID=?, point=?, timestamp=?, platform=?, migration=?, tradeID=?",
					fromUserID, "slotJackpot", "slot_jackpot", rewardPoints, timeMillis/1000,
					"17", 3, tradeID); err != nil {
					return err
				}
				return nil
			},
		})

	}

	return dealings
}

// jackpotBankTeller is designed to wrap bank dealings.
func jackpotBankTeller(
	bankUserID, customerID string,
	amount int64, action moneyModel.BankAction,
) *moneyModel.Dealing {
	if amount <= int64(0) || len(customerID) <= 0 ||
		len(bankUserID) <= 0 {
		return nil
	}

	if action == moneyModel.BankActionWithdraw {
		return &moneyModel.Dealing{
			Category:     moneyModel.Category_GIFT,
			FromUserID:   bankUserID,
			ToUserID:     customerID,
			FromCurrency: moneyModel.Currency_POINT,
			ToCurrency:   moneyModel.Currency_POINT,
			Amount:       amount,
		}
	}
	return &moneyModel.Dealing{
		Category:     moneyModel.Category_GIFT,
		FromUserID:   customerID,
		ToUserID:     bankUserID,
		FromCurrency: moneyModel.Currency_POINT,
		ToCurrency:   moneyModel.Currency_POINT,
		Amount:       amount,
	}
}
