package luckybag

import (
	"encoding/gob"
	"sort"
	"time"

	"github.com/elliotchance/pie/v2"

	"github.com/17media/api/base/ctx"
	giftModel "github.com/17media/api/models/gift"
	"github.com/17media/api/models/keys"
	"github.com/17media/api/service/localcache"
	"github.com/17media/api/service/pagingv2"
	"github.com/17media/api/service/redis"
	giftHelper "github.com/17media/api/stores/gift/helper"
)

const (
	configDefaultLanguage = "EN"
	maxLuckybagItem       = 2000 // Just a made-up number. There’s only a slim chance that number of luckbag is larger than 2000.
	defaultBatchSize      = 500
)

var (
	luckybagStatListKey = keys.RedisKey(keys.PfxGift, "luckybag_stat_list")
)

func init() {
	// fix for type not registered for interface []*luckybagStat
	gob.Register([]*luckybagStat{})
}

type store struct {
	persist           redis.Service
	helper            giftHelper.Worker
	luckybagListCache pagingv2.Service
}

type luckybagStat struct {
	*giftModel.LuckyBag
	DescMap map[string]string
}

// New creates luckybag store object
func New(
	localcache localcache.Service,
	pagingFactory pagingv2.Factory,
	persist redis.Service,
	helper giftHelper.Worker,
) Store {
	s := &store{
		persist: persist,
		helper:  helper,
	}
	s.luckybagListCache = pagingFactory.NewPaging(pagingv2.Params{
		KeyPfx:           luckybagStatListKey,
		Getter:           s.luckybagStatsGetter,
		RenewDuration:    3 * time.Second,
		CacheDuration:    2 * time.Minute,
		MaxCacheDuration: 20 * time.Minute,
		ShardSize:        100,
		Localcache:       localcache,
	})

	return s
}

func (s *store) luckybagStatsGetter(context ctx.CTX, language string) (wholeList interface{}, err error) {
	return s.getLuckybagStats(context)
}

func (s *store) Update(context ctx.CTX) error {
	return s.luckybagListCache.Update(context, luckybagStatListKey)
}

func (s *store) Get(context ctx.CTX, language string) (*giftModel.ResLuckyBag, error) {
	luckbagStat := []*luckybagStat{}
	_, _, err := s.luckybagListCache.Get(context, luckybagStatListKey, "", maxLuckybagItem, &luckbagStat)
	if err != nil {
		return nil, err
	}

	bags := []*giftModel.LuckyBag{}
	for _, j := range luckbagStat {
		j.LuckyBag.Description = j.DescMap[language]
		if j.Description == "" {
			j.LuckyBag.Description = j.DescMap[configDefaultLanguage]
		}
		bags = append(bags, j.LuckyBag)
	}
	return &giftModel.ResLuckyBag{LuckyBags: bags}, nil
}

func (s *store) getLuckybagStats(context ctx.CTX) ([]*luckybagStat, error) {
	infos, err := s.helper.GetAllLuckybags(context)
	if err != nil {
		return nil, err
	}
	count := len(infos)
	luckyBags := make([]*luckybagStat, 0, count)

	for _, chunkInfos := range pie.Chunk(infos, defaultBatchSize) {
		filterInfos := pie.Filter(chunkInfos, func(info *giftModel.LuckybagInfo) bool {
			return len(info.Items) > 0
		})
		luckyBagInfos, err := s.composeLuckyBagInfos(context, filterInfos)
		if err != nil {
			context.WithField("err", err).Error("s.composeLuckyBagInfos failed")
			return nil, err
		}
		luckyBags = append(luckyBags, luckyBagInfos...)
	}

	return luckyBags, nil
}

func (s *store) composeLuckyBagInfos(context ctx.CTX, infos []*giftModel.LuckybagInfo) ([]*luckybagStat, error) {
	count := len(infos)
	giftIDs := make([]string, 0, count)
	for _, luckybag := range infos {
		giftIDs = append(giftIDs, luckybag.GiftID)
	}

	drawCounts, err := s.helper.GetLuckyBagDrawCounts(context, giftIDs)
	if err != nil {
		context.WithField("err", err).Error("helper.GetLuckyBagDrawCounts failed")
		return nil, err
	}

	jackpots, err := s.helper.GetLuckyBagJackpots(context, giftIDs)
	if err != nil {
		context.WithField("err", err).Error("helper.GetLuckyBagJackpots failed")
		return nil, err
	}

	luckyBags := make([]*luckybagStat, 0, count)
	for i := 0; i < len(drawCounts); i++ {
		skunkCount := drawCounts[i]
		jackpot := jackpots[i]

		// Compose response
		luckybag := infos[i]
		ceilingEnable := luckybag.ShouldShowCeilingCount()

		remainingCount := int32(0)
		if ceilingEnable {
			remainingCount = luckybag.CeilingCount - int32(skunkCount)
		}

		luckyBags = append(luckyBags, &luckybagStat{
			LuckyBag: &giftModel.LuckyBag{
				GiftID:     luckybag.GiftID,
				SkunkCount: int32(skunkCount),
				Jackpot:    &jackpot,
				Ceiling: &giftModel.Ceiling{
					Enable:         ceilingEnable,
					RemainingCount: remainingCount,
				},
			},
			DescMap: map[string]string{
				"TW": luckybag.TwDesc,
				"CN": luckybag.CnDesc,
				"JP": luckybag.JpDesc,
				"EN": luckybag.EnDesc,
				"AR": luckybag.ArDesc,
			},
		})
	}

	return luckyBags, nil
}

func (s *store) GetLuckyBagsByIDs(context ctx.CTX, giftIDs []string, language string) (*giftModel.ResLuckyBag, error) {
	infos, err := s.helper.GetLuckyBagInfos(context, giftIDs)
	if err != nil {
		return nil, err
	}

	count := len(infos)
	luckyBags := make([]*luckybagStat, 0, count)

	for _, chunkInfos := range pie.Chunk(infos, defaultBatchSize) {
		luckyBagInfos, err := s.composeLuckyBagInfos(context, chunkInfos)
		if err != nil {
			context.WithField("err", err).Error("s.composeLuckyBagInfos failed")
			return nil, err
		}
		luckyBags = append(luckyBags, luckyBagInfos...)
	}

	bags := make([]*giftModel.LuckyBag, 0, len(luckyBags))
	for _, luckyBag := range luckyBags {
		luckyBag.LuckyBag.Description = luckyBag.DescMap[language]
		if luckyBag.Description == "" {
			luckyBag.LuckyBag.Description = luckyBag.DescMap[configDefaultLanguage]
		}
		bags = append(bags, luckyBag.LuckyBag)
	}
	return &giftModel.ResLuckyBag{LuckyBags: bags}, nil
}

func (s *store) GetLuckyBagsDetail(context ctx.CTX, giftID, language string) (*giftModel.ResLuckyBagDetail, error) {
	info, err := s.helper.GetLuckyBagDetails(context, giftID)
	if err != nil {
		return nil, err
	}
	if info == nil || len(info.LuckBagItem) == 0 {
		return nil, nil
	}

	res := giftModel.ResLuckyBagDetail{}

	for _, item := range info.LuckBagItem {
		giftName := s.helper.GetLuckyBagGiftName(item, language)

		detail := giftModel.LuckBagDetail{
			GiftID:      item.GiftID,
			Description: giftName,
			Picture:     item.Icon,
			Point:       item.Point,
			Probability: float64(item.BasisPoint) / 100,
		}
		if item.IsFirstPrize {
			res.Jackpot = append(res.Jackpot, detail)
		} else {
			res.RegularGift = append(res.RegularGift, detail)
		}
	}

	// Add ceiling gift detail
	if info.CeilingGiftID != "" && info.CeilingCount > 0 {
		gift, err := s.helper.GetGiftInfo(context, info.CeilingGiftID)
		if err != nil {
			return nil, err
		}
		res.Ceiling = &giftModel.CeilingDetail{
			GiftID:        gift.GiftID,
			Description:   s.helper.GetGiftName(gift, language),
			Picture:       gift.Icon,
			Point:         gift.Point,
			CeilingCounts: info.CeilingCount,
		}
	}

	sort.Slice(res.Jackpot, func(i, j int) bool {
		return res.Jackpot[i].Probability > res.Jackpot[j].Probability
	})
	sort.Slice(res.RegularGift, func(i, j int) bool {
		return res.RegularGift[i].Probability > res.RegularGift[j].Probability
	})
	return &res, nil
}
