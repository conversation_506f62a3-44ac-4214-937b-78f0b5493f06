package game

import (
	"github.com/17media/dig"

	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/stores/gift/factory"
	giftHelper "github.com/17media/api/stores/gift/helper"
)

func init() {
	Register(dimanager.DefaultManager)
	RegisterStore(dimanager.DefaultManager)
}

// Register registers the constructor of gift factory game object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
	}

	fn := func(p params) factory.Factory {
		return &Factory{}
	}

	m.ProvideConstructor(fn, `giftFactoryGame`)
}

func RegisterStore(m *dimanager.Manager) {
	type params struct {
		dig.In

		GiftHelper giftHelper.Worker `name:"giftHelper"`
	}

	fn := func(p params) Store {
		return New(
			p.GiftHelper,
		)
	}

	m.ProvideConstructor(fn, `giftFactoryGameStore`)
}

// GetGiftFactoryGame returns the gift factory game object
func GetGiftFactoryGame(m *dimanager.Manager) factory.Factory {
	var output factory.Factory
	type params struct {
		dig.In
		Output factory.Factory `name:"giftFactoryGame"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}

// GetGiftFactoryGameStore returns the gift factory game store object
func GetGiftFactoryGameStore(m *dimanager.Manager) factory.Factory {
	var output factory.Factory
	type params struct {
		dig.In
		Output factory.Factory `name:"giftFactoryGameStore"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
