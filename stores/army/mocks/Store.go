// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	army "github.com/17media/api/stores/army"

	helper "github.com/17media/api/stores/army/helper"

	i18n "github.com/17media/api/models/i18n"

	merchandise "github.com/17media/api/models/purchase/merchandise"

	mock "github.com/stretchr/testify/mock"

	models "github.com/17media/api/models"

	modelsarmy "github.com/17media/api/models/army"

	money "github.com/17media/api/stores/money"

	order "github.com/17media/api/models/purchase/order"

	pay "github.com/17media/api/models/pay"

	purchase "github.com/17media/api/models/purchase"

	purchasepay "github.com/17media/api/models/purchase/pay"

	queue "github.com/17media/api/models/queue"

	sqlx "github.com/jmoiron/sqlx"

	time "time"

	user "github.com/17media/api/models/user"
)

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// CancelAndroidSubscriptions provides a mock function with given fields: context, preReceiptIDs
func (_m *Store) CancelAndroidSubscriptions(context ctx.CTX, preReceiptIDs []string) error {
	ret := _m.Called(context, preReceiptIDs)

	if len(ret) == 0 {
		panic("no return value specified for CancelAndroidSubscriptions")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []string) error); ok {
		r0 = rf(context, preReceiptIDs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CheckForPrepareOrder provides a mock function with given fields: context, _a1, payProduct, targetUserID
func (_m *Store) CheckForPrepareOrder(context ctx.CTX, _a1 *models.User, payProduct pay.PayProduct, targetUserID string) error {
	ret := _m.Called(context, _a1, payProduct, targetUserID)

	if len(ret) == 0 {
		panic("no return value specified for CheckForPrepareOrder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, pay.PayProduct, string) error); ok {
		r0 = rf(context, _a1, payProduct, targetUserID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CompareArmyRank provides a mock function with given fields: fromMerchandiseID, toMerchandiseID
func (_m *Store) CompareArmyRank(fromMerchandiseID string, toMerchandiseID string) (army.ArmyRankCompareResult, error) {
	ret := _m.Called(fromMerchandiseID, toMerchandiseID)

	if len(ret) == 0 {
		panic("no return value specified for CompareArmyRank")
	}

	var r0 army.ArmyRankCompareResult
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (army.ArmyRankCompareResult, error)); ok {
		return rf(fromMerchandiseID, toMerchandiseID)
	}
	if rf, ok := ret.Get(0).(func(string, string) army.ArmyRankCompareResult); ok {
		r0 = rf(fromMerchandiseID, toMerchandiseID)
	} else {
		r0 = ret.Get(0).(army.ArmyRankCompareResult)
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(fromMerchandiseID, toMerchandiseID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExecuteRevenueRecognitionTask provides a mock function with given fields: context, data, option
func (_m *Store) ExecuteRevenueRecognitionTask(context ctx.CTX, data []byte, option queue.CallbackOption) error {
	ret := _m.Called(context, data, option)

	if len(ret) == 0 {
		panic("no return value specified for ExecuteRevenueRecognitionTask")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []byte, queue.CallbackOption) error); ok {
		r0 = rf(context, data, option)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ExecuteRevenueRecognitions provides a mock function with given fields: context, recognitionIDs
func (_m *Store) ExecuteRevenueRecognitions(context ctx.CTX, recognitionIDs []int64) error {
	ret := _m.Called(context, recognitionIDs)

	if len(ret) == 0 {
		panic("no return value specified for ExecuteRevenueRecognitions")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []int64) error); ok {
		r0 = rf(context, recognitionIDs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetArmyLevelBonus provides a mock function with given fields: context, userID
func (_m *Store) GetArmyLevelBonus(context ctx.CTX, userID string) (int, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetArmyLevelBonus")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (int, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) int); ok {
		r0 = rf(context, userID)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArmyOnlyBroadcastLevel provides a mock function with given fields: context, streamerUserID
func (_m *Store) GetArmyOnlyBroadcastLevel(context ctx.CTX, streamerUserID string) int {
	ret := _m.Called(context, streamerUserID)

	if len(ret) == 0 {
		panic("no return value specified for GetArmyOnlyBroadcastLevel")
	}

	var r0 int
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) int); ok {
		r0 = rf(context, streamerUserID)
	} else {
		r0 = ret.Get(0).(int)
	}

	return r0
}

// GetArmyRank provides a mock function with given fields: context, memberID, masterID
func (_m *Store) GetArmyRank(context ctx.CTX, memberID string, masterID string) (modelsarmy.ArmyRank, *modelsarmy.UserDisplayInfo, error) {
	ret := _m.Called(context, memberID, masterID)

	if len(ret) == 0 {
		panic("no return value specified for GetArmyRank")
	}

	var r0 modelsarmy.ArmyRank
	var r1 *modelsarmy.UserDisplayInfo
	var r2 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) (modelsarmy.ArmyRank, *modelsarmy.UserDisplayInfo, error)); ok {
		return rf(context, memberID, masterID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) modelsarmy.ArmyRank); ok {
		r0 = rf(context, memberID, masterID)
	} else {
		r0 = ret.Get(0).(modelsarmy.ArmyRank)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string) *modelsarmy.UserDisplayInfo); ok {
		r1 = rf(context, memberID, masterID)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*modelsarmy.UserDisplayInfo)
		}
	}

	if rf, ok := ret.Get(2).(func(ctx.CTX, string, string) error); ok {
		r2 = rf(context, memberID, masterID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GetArmyRankByMerchandiseID provides a mock function with given fields: merchandiseID
func (_m *Store) GetArmyRankByMerchandiseID(merchandiseID string) (modelsarmy.ArmyRank, error) {
	ret := _m.Called(merchandiseID)

	if len(ret) == 0 {
		panic("no return value specified for GetArmyRankByMerchandiseID")
	}

	var r0 modelsarmy.ArmyRank
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (modelsarmy.ArmyRank, error)); ok {
		return rf(merchandiseID)
	}
	if rf, ok := ret.Get(0).(func(string) modelsarmy.ArmyRank); ok {
		r0 = rf(merchandiseID)
	} else {
		r0 = ret.Get(0).(modelsarmy.ArmyRank)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(merchandiseID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArmySubscribedMap provides a mock function with given fields: context, userID
func (_m *Store) GetArmySubscribedMap(context ctx.CTX, userID string) (modelsarmy.SubscribedMap, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetArmySubscribedMap")
	}

	var r0 modelsarmy.SubscribedMap
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (modelsarmy.SubscribedMap, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) modelsarmy.SubscribedMap); ok {
		r0 = rf(context, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(modelsarmy.SubscribedMap)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArmySubscriptionByID provides a mock function with given fields: context, id
func (_m *Store) GetArmySubscriptionByID(context ctx.CTX, id int64) (*modelsarmy.ArmySubscription, error) {
	ret := _m.Called(context, id)

	if len(ret) == 0 {
		panic("no return value specified for GetArmySubscriptionByID")
	}

	var r0 *modelsarmy.ArmySubscription
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, int64) (*modelsarmy.ArmySubscription, error)); ok {
		return rf(context, id)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, int64) *modelsarmy.ArmySubscription); ok {
		r0 = rf(context, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsarmy.ArmySubscription)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, int64) error); ok {
		r1 = rf(context, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArmySubscriptionByOrderID provides a mock function with given fields: context, orderID
func (_m *Store) GetArmySubscriptionByOrderID(context ctx.CTX, orderID string) (*modelsarmy.ArmySubscription, error) {
	ret := _m.Called(context, orderID)

	if len(ret) == 0 {
		panic("no return value specified for GetArmySubscriptionByOrderID")
	}

	var r0 *modelsarmy.ArmySubscription
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (*modelsarmy.ArmySubscription, error)); ok {
		return rf(context, orderID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) *modelsarmy.ArmySubscription); ok {
		r0 = rf(context, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsarmy.ArmySubscription)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArmySubscriptionByRecurrenceID provides a mock function with given fields: context, tx, recurrenceID
func (_m *Store) GetArmySubscriptionByRecurrenceID(context ctx.CTX, tx *sqlx.Tx, recurrenceID string) (*modelsarmy.ArmySubscription, error) {
	ret := _m.Called(context, tx, recurrenceID)

	if len(ret) == 0 {
		panic("no return value specified for GetArmySubscriptionByRecurrenceID")
	}

	var r0 *modelsarmy.ArmySubscription
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string) (*modelsarmy.ArmySubscription, error)); ok {
		return rf(context, tx, recurrenceID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string) *modelsarmy.ArmySubscription); ok {
		r0 = rf(context, tx, recurrenceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsarmy.ArmySubscription)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, string) error); ok {
		r1 = rf(context, tx, recurrenceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArmySubscriptionHistory provides a mock function with given fields: context, orderID
func (_m *Store) GetArmySubscriptionHistory(context ctx.CTX, orderID string) ([]*modelsarmy.ArmySubscriptionHistory, error) {
	ret := _m.Called(context, orderID)

	if len(ret) == 0 {
		panic("no return value specified for GetArmySubscriptionHistory")
	}

	var r0 []*modelsarmy.ArmySubscriptionHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) ([]*modelsarmy.ArmySubscriptionHistory, error)); ok {
		return rf(context, orderID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) []*modelsarmy.ArmySubscriptionHistory); ok {
		r0 = rf(context, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsarmy.ArmySubscriptionHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArmySubscriptions provides a mock function with given fields: context, userID, optFuncs
func (_m *Store) GetArmySubscriptions(context ctx.CTX, userID string, optFuncs ...army.GetSubscriptionsOptFunc) ([]modelsarmy.ArmySubscriptionInfo, error) {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, userID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetArmySubscriptions")
	}

	var r0 []modelsarmy.ArmySubscriptionInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, ...army.GetSubscriptionsOptFunc) ([]modelsarmy.ArmySubscriptionInfo, error)); ok {
		return rf(context, userID, optFuncs...)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, ...army.GetSubscriptionsOptFunc) []modelsarmy.ArmySubscriptionInfo); ok {
		r0 = rf(context, userID, optFuncs...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modelsarmy.ArmySubscriptionInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, ...army.GetSubscriptionsOptFunc) error); ok {
		r1 = rf(context, userID, optFuncs...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArmySubscriptionsByProductID provides a mock function with given fields: context, productIDs, states
func (_m *Store) GetArmySubscriptionsByProductID(context ctx.CTX, productIDs []pay.PayProduct, states []modelsarmy.SubscriptionState) ([]*modelsarmy.ArmySubscription, error) {
	ret := _m.Called(context, productIDs, states)

	if len(ret) == 0 {
		panic("no return value specified for GetArmySubscriptionsByProductID")
	}

	var r0 []*modelsarmy.ArmySubscription
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []pay.PayProduct, []modelsarmy.SubscriptionState) ([]*modelsarmy.ArmySubscription, error)); ok {
		return rf(context, productIDs, states)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, []pay.PayProduct, []modelsarmy.SubscriptionState) []*modelsarmy.ArmySubscription); ok {
		r0 = rf(context, productIDs, states)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsarmy.ArmySubscription)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, []pay.PayProduct, []modelsarmy.SubscriptionState) error); ok {
		r1 = rf(context, productIDs, states)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArmySubscriptionsByStreamer provides a mock function with given fields: context, streamerID
func (_m *Store) GetArmySubscriptionsByStreamer(context ctx.CTX, streamerID string) ([]*modelsarmy.ArmySubscription, error) {
	ret := _m.Called(context, streamerID)

	if len(ret) == 0 {
		panic("no return value specified for GetArmySubscriptionsByStreamer")
	}

	var r0 []*modelsarmy.ArmySubscription
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) ([]*modelsarmy.ArmySubscription, error)); ok {
		return rf(context, streamerID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) []*modelsarmy.ArmySubscription); ok {
		r0 = rf(context, streamerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsarmy.ArmySubscription)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, streamerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCurrentPayVendor provides a mock function with given fields: context, userID, streamerID
func (_m *Store) GetCurrentPayVendor(context ctx.CTX, userID string, streamerID string) (pay.Platform, error) {
	ret := _m.Called(context, userID, streamerID)

	if len(ret) == 0 {
		panic("no return value specified for GetCurrentPayVendor")
	}

	var r0 pay.Platform
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) (pay.Platform, error)); ok {
		return rf(context, userID, streamerID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) pay.Platform); ok {
		r0 = rf(context, userID, streamerID)
	} else {
		r0 = ret.Get(0).(pay.Platform)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string) error); ok {
		r1 = rf(context, userID, streamerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCurrentSubscription provides a mock function with given fields: context, tx, userID, streamerID
func (_m *Store) GetCurrentSubscription(context ctx.CTX, tx *sqlx.Tx, userID string, streamerID string) (*modelsarmy.ArmySubscription, error) {
	ret := _m.Called(context, tx, userID, streamerID)

	if len(ret) == 0 {
		panic("no return value specified for GetCurrentSubscription")
	}

	var r0 *modelsarmy.ArmySubscription
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string, string) (*modelsarmy.ArmySubscription, error)); ok {
		return rf(context, tx, userID, streamerID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string, string) *modelsarmy.ArmySubscription); ok {
		r0 = rf(context, tx, userID, streamerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsarmy.ArmySubscription)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, string, string) error); ok {
		r1 = rf(context, tx, userID, streamerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCurrentSubscriptionInfo provides a mock function with given fields: context, userID, streamerID
func (_m *Store) GetCurrentSubscriptionInfo(context ctx.CTX, userID string, streamerID string) (*modelsarmy.ArmySubscriptionInfo, error) {
	ret := _m.Called(context, userID, streamerID)

	if len(ret) == 0 {
		panic("no return value specified for GetCurrentSubscriptionInfo")
	}

	var r0 *modelsarmy.ArmySubscriptionInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) (*modelsarmy.ArmySubscriptionInfo, error)); ok {
		return rf(context, userID, streamerID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) *modelsarmy.ArmySubscriptionInfo); ok {
		r0 = rf(context, userID, streamerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsarmy.ArmySubscriptionInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string) error); ok {
		r1 = rf(context, userID, streamerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetFirstRankAndArmyLevel provides a mock function with given fields: context, userID
func (_m *Store) GetFirstRankAndArmyLevel(context ctx.CTX, userID string) (*modelsarmy.ArmyInfo, int, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetFirstRankAndArmyLevel")
	}

	var r0 *modelsarmy.ArmyInfo
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (*modelsarmy.ArmyInfo, int, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) *modelsarmy.ArmyInfo); ok {
		r0 = rf(context, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsarmy.ArmyInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) int); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(ctx.CTX, string) error); ok {
		r2 = rf(context, userID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GetLastArmySubscriptionByRecurrenceID provides a mock function with given fields: context, tx, recurrenceID
func (_m *Store) GetLastArmySubscriptionByRecurrenceID(context ctx.CTX, tx *sqlx.Tx, recurrenceID string) (*modelsarmy.ArmySubscription, error) {
	ret := _m.Called(context, tx, recurrenceID)

	if len(ret) == 0 {
		panic("no return value specified for GetLastArmySubscriptionByRecurrenceID")
	}

	var r0 *modelsarmy.ArmySubscription
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string) (*modelsarmy.ArmySubscription, error)); ok {
		return rf(context, tx, recurrenceID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string) *modelsarmy.ArmySubscription); ok {
		r0 = rf(context, tx, recurrenceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsarmy.ArmySubscription)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, string) error); ok {
		r1 = rf(context, tx, recurrenceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetMyArmyOverView provides a mock function with given fields: context, userID
func (_m *Store) GetMyArmyOverView(context ctx.CTX, userID string) (*modelsarmy.MyArmyOverview, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetMyArmyOverView")
	}

	var r0 *modelsarmy.MyArmyOverview
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (*modelsarmy.MyArmyOverview, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) *modelsarmy.MyArmyOverview); ok {
		r0 = rf(context, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsarmy.MyArmyOverview)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetNextSubscriptionPeriodEstimate provides a mock function with given fields: context, subscriberUserID, streamerUserID, payMethod, sellingChannel, _a5, isSandbox, optFuncs
func (_m *Store) GetNextSubscriptionPeriodEstimate(context ctx.CTX, subscriberUserID string, streamerUserID string, payMethod purchasepay.Method, sellingChannel order.SellingChannel, _a5 *merchandise.MerchandiseWithPrice, isSandbox bool, optFuncs ...army.GetPeriodOptFunc) (*modelsarmy.SubscriptionPeriod, error) {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, subscriberUserID, streamerUserID, payMethod, sellingChannel, _a5, isSandbox)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetNextSubscriptionPeriodEstimate")
	}

	var r0 *modelsarmy.SubscriptionPeriod
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, purchasepay.Method, order.SellingChannel, *merchandise.MerchandiseWithPrice, bool, ...army.GetPeriodOptFunc) (*modelsarmy.SubscriptionPeriod, error)); ok {
		return rf(context, subscriberUserID, streamerUserID, payMethod, sellingChannel, _a5, isSandbox, optFuncs...)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, purchasepay.Method, order.SellingChannel, *merchandise.MerchandiseWithPrice, bool, ...army.GetPeriodOptFunc) *modelsarmy.SubscriptionPeriod); ok {
		r0 = rf(context, subscriberUserID, streamerUserID, payMethod, sellingChannel, _a5, isSandbox, optFuncs...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsarmy.SubscriptionPeriod)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, purchasepay.Method, order.SellingChannel, *merchandise.MerchandiseWithPrice, bool, ...army.GetPeriodOptFunc) error); ok {
		r1 = rf(context, subscriberUserID, streamerUserID, payMethod, sellingChannel, _a5, isSandbox, optFuncs...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetNumOfArmySubscribers provides a mock function with given fields: context, userID, startTime, endTime
func (_m *Store) GetNumOfArmySubscribers(context ctx.CTX, userID string, startTime time.Time, endTime time.Time) (int, error) {
	ret := _m.Called(context, userID, startTime, endTime)

	if len(ret) == 0 {
		panic("no return value specified for GetNumOfArmySubscribers")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, time.Time, time.Time) (int, error)); ok {
		return rf(context, userID, startTime, endTime)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, time.Time, time.Time) int); ok {
		r0 = rf(context, userID, startTime, endTime)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, time.Time, time.Time) error); ok {
		r1 = rf(context, userID, startTime, endTime)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRankToken provides a mock function with given fields: context, rank
func (_m *Store) GetRankToken(context ctx.CTX, rank modelsarmy.ArmyRank) *i18n.Token {
	ret := _m.Called(context, rank)

	if len(ret) == 0 {
		panic("no return value specified for GetRankToken")
	}

	var r0 *i18n.Token
	if rf, ok := ret.Get(0).(func(ctx.CTX, modelsarmy.ArmyRank) *i18n.Token); ok {
		r0 = rf(context, rank)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*i18n.Token)
		}
	}

	return r0
}

// GetRenewableSubscriptionsByPayMethod provides a mock function with given fields: context, payMethod
func (_m *Store) GetRenewableSubscriptionsByPayMethod(context ctx.CTX, payMethod purchasepay.Method) ([]*modelsarmy.PlainSubscriptionInfo, error) {
	ret := _m.Called(context, payMethod)

	if len(ret) == 0 {
		panic("no return value specified for GetRenewableSubscriptionsByPayMethod")
	}

	var r0 []*modelsarmy.PlainSubscriptionInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, purchasepay.Method) ([]*modelsarmy.PlainSubscriptionInfo, error)); ok {
		return rf(context, payMethod)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, purchasepay.Method) []*modelsarmy.PlainSubscriptionInfo); ok {
		r0 = rf(context, payMethod)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsarmy.PlainSubscriptionInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, purchasepay.Method) error); ok {
		r1 = rf(context, payMethod)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSubscribers provides a mock function with given fields: context, masterUserID
func (_m *Store) GetSubscribers(context ctx.CTX, masterUserID string) ([]*modelsarmy.ArmyInfo, error) {
	ret := _m.Called(context, masterUserID)

	if len(ret) == 0 {
		panic("no return value specified for GetSubscribers")
	}

	var r0 []*modelsarmy.ArmyInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) ([]*modelsarmy.ArmyInfo, error)); ok {
		return rf(context, masterUserID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) []*modelsarmy.ArmyInfo); ok {
		r0 = rf(context, masterUserID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsarmy.ArmyInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, masterUserID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSubscribersLite provides a mock function with given fields: context, masterUserID
func (_m *Store) GetSubscribersLite(context ctx.CTX, masterUserID string) ([]modelsarmy.SubscriberLite, error) {
	ret := _m.Called(context, masterUserID)

	if len(ret) == 0 {
		panic("no return value specified for GetSubscribersLite")
	}

	var r0 []modelsarmy.SubscriberLite
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) ([]modelsarmy.SubscriberLite, error)); ok {
		return rf(context, masterUserID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) []modelsarmy.SubscriberLite); ok {
		r0 = rf(context, masterUserID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modelsarmy.SubscriberLite)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, masterUserID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSubscriptionPeriod provides a mock function with given fields: context, subscriberUserID, streamerUserID, currentSubLog, shippingInfo, purchaseInfo, isRenew, optFuncs
func (_m *Store) GetSubscriptionPeriod(context ctx.CTX, subscriberUserID string, streamerUserID string, currentSubLog *pay.SubscriptionLog, shippingInfo army.ShippingInfo, purchaseInfo army.PurchaseInfo, isRenew bool, optFuncs ...army.GetPeriodOptFunc) (*modelsarmy.SubscriptionPeriod, error) {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, subscriberUserID, streamerUserID, currentSubLog, shippingInfo, purchaseInfo, isRenew)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetSubscriptionPeriod")
	}

	var r0 *modelsarmy.SubscriptionPeriod
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, *pay.SubscriptionLog, army.ShippingInfo, army.PurchaseInfo, bool, ...army.GetPeriodOptFunc) (*modelsarmy.SubscriptionPeriod, error)); ok {
		return rf(context, subscriberUserID, streamerUserID, currentSubLog, shippingInfo, purchaseInfo, isRenew, optFuncs...)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, *pay.SubscriptionLog, army.ShippingInfo, army.PurchaseInfo, bool, ...army.GetPeriodOptFunc) *modelsarmy.SubscriptionPeriod); ok {
		r0 = rf(context, subscriberUserID, streamerUserID, currentSubLog, shippingInfo, purchaseInfo, isRenew, optFuncs...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsarmy.SubscriptionPeriod)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, *pay.SubscriptionLog, army.ShippingInfo, army.PurchaseInfo, bool, ...army.GetPeriodOptFunc) error); ok {
		r1 = rf(context, subscriberUserID, streamerUserID, currentSubLog, shippingInfo, purchaseInfo, isRenew, optFuncs...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUnreadSubscriptionInfo provides a mock function with given fields: context, userID
func (_m *Store) GetUnreadSubscriptionInfo(context ctx.CTX, userID string) (int64, int32, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUnreadSubscriptionInfo")
	}

	var r0 int64
	var r1 int32
	var r2 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (int64, int32, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) int64); ok {
		r0 = rf(context, userID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) int32); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Get(1).(int32)
	}

	if rf, ok := ret.Get(2).(func(ctx.CTX, string) error); ok {
		r2 = rf(context, userID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// HandleArmySubscriptionCancellationTask provides a mock function with given fields: context, data, option
func (_m *Store) HandleArmySubscriptionCancellationTask(context ctx.CTX, data []byte, option queue.CallbackOption) error {
	ret := _m.Called(context, data, option)

	if len(ret) == 0 {
		panic("no return value specified for HandleArmySubscriptionCancellationTask")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []byte, queue.CallbackOption) error); ok {
		r0 = rf(context, data, option)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// InjectMyArmyOverView provides a mock function with given fields: context, _a1
func (_m *Store) InjectMyArmyOverView(context ctx.CTX, _a1 *models.User) error {
	ret := _m.Called(context, _a1)

	if len(ret) == 0 {
		panic("no return value specified for InjectMyArmyOverView")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User) error); ok {
		r0 = rf(context, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IsEnableNewSubGroup1 provides a mock function with given fields: context, streamerUserID, optFuncs
func (_m *Store) IsEnableNewSubGroup1(context ctx.CTX, streamerUserID string, optFuncs ...helper.GetNewSubConfigOptFunc) bool {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, streamerUserID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for IsEnableNewSubGroup1")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, ...helper.GetNewSubConfigOptFunc) bool); ok {
		r0 = rf(context, streamerUserID, optFuncs...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsEnableNewSubGroup2 provides a mock function with given fields: context, streamerUserID, optFuncs
func (_m *Store) IsEnableNewSubGroup2(context ctx.CTX, streamerUserID string, optFuncs ...helper.GetNewSubConfigOptFunc) bool {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, streamerUserID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for IsEnableNewSubGroup2")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, ...helper.GetNewSubConfigOptFunc) bool); ok {
		r0 = rf(context, streamerUserID, optFuncs...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsEnableNewSubGroup3 provides a mock function with given fields: context, streamerUserID, optFuncs
func (_m *Store) IsEnableNewSubGroup3(context ctx.CTX, streamerUserID string, optFuncs ...helper.GetNewSubConfigOptFunc) bool {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, streamerUserID)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for IsEnableNewSubGroup3")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, ...helper.GetNewSubConfigOptFunc) bool); ok {
		r0 = rf(context, streamerUserID, optFuncs...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsInAndroidPlanChangedPeriod provides a mock function with given fields: context, tx, subscriberUserID, streamerUserID, rank
func (_m *Store) IsInAndroidPlanChangedPeriod(context ctx.CTX, tx *sqlx.Tx, subscriberUserID string, streamerUserID string, rank modelsarmy.ArmyRank) (bool, error) {
	ret := _m.Called(context, tx, subscriberUserID, streamerUserID, rank)

	if len(ret) == 0 {
		panic("no return value specified for IsInAndroidPlanChangedPeriod")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string, string, modelsarmy.ArmyRank) (bool, error)); ok {
		return rf(context, tx, subscriberUserID, streamerUserID, rank)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string, string, modelsarmy.ArmyRank) bool); ok {
		r0 = rf(context, tx, subscriberUserID, streamerUserID, rank)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, string, string, modelsarmy.ArmyRank) error); ok {
		r1 = rf(context, tx, subscriberUserID, streamerUserID, rank)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IsInvalidUser provides a mock function with given fields: _a0
func (_m *Store) IsInvalidUser(_a0 *models.User) bool {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for IsInvalidUser")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(*models.User) bool); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsLegalOrder provides a mock function with given fields: context, purchaserID, subscriberID, streamerID, payProduct, isRenew
func (_m *Store) IsLegalOrder(context ctx.CTX, purchaserID string, subscriberID string, streamerID string, payProduct pay.PayProduct, isRenew bool) (bool, error) {
	ret := _m.Called(context, purchaserID, subscriberID, streamerID, payProduct, isRenew)

	if len(ret) == 0 {
		panic("no return value specified for IsLegalOrder")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, pay.PayProduct, bool) (bool, error)); ok {
		return rf(context, purchaserID, subscriberID, streamerID, payProduct, isRenew)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, pay.PayProduct, bool) bool); ok {
		r0 = rf(context, purchaserID, subscriberID, streamerID, payProduct, isRenew)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, string, pay.PayProduct, bool) error); ok {
		r1 = rf(context, purchaserID, subscriberID, streamerID, payProduct, isRenew)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IsSubscribable provides a mock function with given fields: _a0
func (_m *Store) IsSubscribable(_a0 *models.User) bool {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for IsSubscribable")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(*models.User) bool); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// LockPrePurchaseSubscription provides a mock function with given fields: context, subscriberID, streamerID
func (_m *Store) LockPrePurchaseSubscription(context ctx.CTX, subscriberID string, streamerID string) (func(), error) {
	ret := _m.Called(context, subscriberID, streamerID)

	if len(ret) == 0 {
		panic("no return value specified for LockPrePurchaseSubscription")
	}

	var r0 func()
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) (func(), error)); ok {
		return rf(context, subscriberID, streamerID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) func()); ok {
		r0 = rf(context, subscriberID, streamerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(func())
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string) error); ok {
		r1 = rf(context, subscriberID, streamerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LockSubscription provides a mock function with given fields: context, subscriberID, streamerID
func (_m *Store) LockSubscription(context ctx.CTX, subscriberID string, streamerID string) (func(), error) {
	ret := _m.Called(context, subscriberID, streamerID)

	if len(ret) == 0 {
		panic("no return value specified for LockSubscription")
	}

	var r0 func()
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) (func(), error)); ok {
		return rf(context, subscriberID, streamerID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) func()); ok {
		r0 = rf(context, subscriberID, streamerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(func())
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string) error); ok {
		r1 = rf(context, subscriberID, streamerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MarkAndroidPlanChangedPeriod provides a mock function with given fields: context, subscriberUserID, streamerUserID, rank, endTimeMs
func (_m *Store) MarkAndroidPlanChangedPeriod(context ctx.CTX, subscriberUserID string, streamerUserID string, rank modelsarmy.ArmyRank, endTimeMs int64) error {
	ret := _m.Called(context, subscriberUserID, streamerUserID, rank, endTimeMs)

	if len(ret) == 0 {
		panic("no return value specified for MarkAndroidPlanChangedPeriod")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, modelsarmy.ArmyRank, int64) error); ok {
		r0 = rf(context, subscriberUserID, streamerUserID, rank, endTimeMs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PublishArmyGiftSubscription provides a mock function with given fields: context, roomID, streamID, purchaser, subscriber, streamer, newArmyRank, newOverview, purchaserDisplayInfo, subscriberDisplayInfo
func (_m *Store) PublishArmyGiftSubscription(context ctx.CTX, roomID string, streamID string, purchaser *models.User, subscriber *models.User, streamer *models.User, newArmyRank modelsarmy.ArmyRank, newOverview *modelsarmy.MyArmyOverview, purchaserDisplayInfo *user.DisplayInfo, subscriberDisplayInfo *user.DisplayInfo) {
	_m.Called(context, roomID, streamID, purchaser, subscriber, streamer, newArmyRank, newOverview, purchaserDisplayInfo, subscriberDisplayInfo)
}

// PublishArmyOverview provides a mock function with given fields: context, streamerID, roomID
func (_m *Store) PublishArmyOverview(context ctx.CTX, streamerID string, roomID string) {
	_m.Called(context, streamerID, roomID)
}

// PublishArmySubscription provides a mock function with given fields: context, alive, streamer, subscriber, originalArmyRank, newArmyRank, subStartTime
func (_m *Store) PublishArmySubscription(context ctx.CTX, alive bool, streamer *models.User, subscriber *models.User, originalArmyRank modelsarmy.ArmyRank, newArmyRank modelsarmy.ArmyRank, subStartTime time.Time) {
	_m.Called(context, alive, streamer, subscriber, originalArmyRank, newArmyRank, subStartTime)
}

// Refund provides a mock function with given fields: context, tx, orderID
func (_m *Store) Refund(context ctx.CTX, tx money.Tx, orderID string) error {
	ret := _m.Called(context, tx, orderID)

	if len(ret) == 0 {
		panic("no return value specified for Refund")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, money.Tx, string) error); ok {
		r0 = rf(context, tx, orderID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RegisterRevenueListener provides a mock function with given fields: context, listener
func (_m *Store) RegisterRevenueListener(context ctx.CTX, listener func(string, string, string, string, string, int64)) (string, error) {
	ret := _m.Called(context, listener)

	if len(ret) == 0 {
		panic("no return value specified for RegisterRevenueListener")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, func(string, string, string, string, string, int64)) (string, error)); ok {
		return rf(context, listener)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, func(string, string, string, string, string, int64)) string); ok {
		r0 = rf(context, listener)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, func(string, string, string, string, string, int64)) error); ok {
		r1 = rf(context, listener)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RegisterStateListener provides a mock function with given fields: context, listener
func (_m *Store) RegisterStateListener(context ctx.CTX, listener func(*modelsarmy.ArmySubscriptionEvent)) (string, error) {
	ret := _m.Called(context, listener)

	if len(ret) == 0 {
		panic("no return value specified for RegisterStateListener")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, func(*modelsarmy.ArmySubscriptionEvent)) (string, error)); ok {
		return rf(context, listener)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, func(*modelsarmy.ArmySubscriptionEvent)) string); ok {
		r0 = rf(context, listener)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, func(*modelsarmy.ArmySubscriptionEvent)) error); ok {
		r1 = rf(context, listener)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ReleasePrePurchaseSubscription provides a mock function with given fields: context, subscriberID, streamerID
func (_m *Store) ReleasePrePurchaseSubscription(context ctx.CTX, subscriberID string, streamerID string) error {
	ret := _m.Called(context, subscriberID, streamerID)

	if len(ret) == 0 {
		panic("no return value specified for ReleasePrePurchaseSubscription")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) error); ok {
		r0 = rf(context, subscriberID, streamerID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ReleaseSubscription provides a mock function with given fields: context, subscriberID, streamerID
func (_m *Store) ReleaseSubscription(context ctx.CTX, subscriberID string, streamerID string) error {
	ret := _m.Called(context, subscriberID, streamerID)

	if len(ret) == 0 {
		panic("no return value specified for ReleaseSubscription")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) error); ok {
		r0 = rf(context, subscriberID, streamerID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RemindBabyCoinInsufficientBalanceMsg provides a mock function with given fields: context, point, orderID
func (_m *Store) RemindBabyCoinInsufficientBalanceMsg(context ctx.CTX, point int64, orderID string) {
	_m.Called(context, point, orderID)
}

// RemindContactCardOrgMsgByRecurrenceID provides a mock function with given fields: context, recurrenceID
func (_m *Store) RemindContactCardOrgMsgByRecurrenceID(context ctx.CTX, recurrenceID string) {
	_m.Called(context, recurrenceID)
}

// RemindCreditCardExpireMsg provides a mock function with given fields: context, orderID
func (_m *Store) RemindCreditCardExpireMsg(context ctx.CTX, orderID string) {
	_m.Called(context, orderID)
}

// RemindCreditCardInsufficientBalanceMsg provides a mock function with given fields: context, price, orderID
func (_m *Store) RemindCreditCardInsufficientBalanceMsg(context ctx.CTX, price purchase.Value, orderID string) {
	_m.Called(context, price, orderID)
}

// RemindCreditCardInsufficientBalanceMsgByRecurrenceID provides a mock function with given fields: context, price, recurrenceID
func (_m *Store) RemindCreditCardInsufficientBalanceMsgByRecurrenceID(context ctx.CTX, price purchase.Value, recurrenceID string) {
	_m.Called(context, price, recurrenceID)
}

// RemindCreditCardNumberExceededMsgByRecurrenceID provides a mock function with given fields: context, recurrenceID
func (_m *Store) RemindCreditCardNumberExceededMsgByRecurrenceID(context ctx.CTX, recurrenceID string) {
	_m.Called(context, recurrenceID)
}

// RemindGeneralRenewFailMsg provides a mock function with given fields: context, orderID
func (_m *Store) RemindGeneralRenewFailMsg(context ctx.CTX, orderID string) {
	_m.Called(context, orderID)
}

// RemindGeneralRenewFailMsgByRecurrenceID provides a mock function with given fields: context, recurrenceID
func (_m *Store) RemindGeneralRenewFailMsgByRecurrenceID(context ctx.CTX, recurrenceID string) {
	_m.Called(context, recurrenceID)
}

// RemindIAPRefundMsgByOriginalTransactionID provides a mock function with given fields: context, originalTransactionID
func (_m *Store) RemindIAPRefundMsgByOriginalTransactionID(context ctx.CTX, originalTransactionID string) {
	_m.Called(context, originalTransactionID)
}

// RemindSubscription provides a mock function with given fields: context, orderID, dayBeforeRenew
func (_m *Store) RemindSubscription(context ctx.CTX, orderID string, dayBeforeRenew int) {
	_m.Called(context, orderID, dayBeforeRenew)
}

// RemindSubscriptionTerminated provides a mock function with given fields: context, sub
func (_m *Store) RemindSubscriptionTerminated(context ctx.CTX, sub *modelsarmy.PlainSubscriptionInfo) error {
	ret := _m.Called(context, sub)

	if len(ret) == 0 {
		panic("no return value specified for RemindSubscriptionTerminated")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *modelsarmy.PlainSubscriptionInfo) error); ok {
		r0 = rf(context, sub)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RemoveRevenueListener provides a mock function with given fields: context, listenerID
func (_m *Store) RemoveRevenueListener(context ctx.CTX, listenerID string) {
	_m.Called(context, listenerID)
}

// RemoveStateListener provides a mock function with given fields: context, listenerID
func (_m *Store) RemoveStateListener(context ctx.CTX, listenerID string) {
	_m.Called(context, listenerID)
}

// SearchGiftingUsers provides a mock function with given fields: context, streamID, keyword, cursor, count
func (_m *Store) SearchGiftingUsers(context ctx.CTX, streamID string, keyword string, cursor string, count int) ([]*modelsarmy.UserDisplayInfo, string, error) {
	ret := _m.Called(context, streamID, keyword, cursor, count)

	if len(ret) == 0 {
		panic("no return value specified for SearchGiftingUsers")
	}

	var r0 []*modelsarmy.UserDisplayInfo
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, int) ([]*modelsarmy.UserDisplayInfo, string, error)); ok {
		return rf(context, streamID, keyword, cursor, count)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, int) []*modelsarmy.UserDisplayInfo); ok {
		r0 = rf(context, streamID, keyword, cursor, count)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsarmy.UserDisplayInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, string, int) string); ok {
		r1 = rf(context, streamID, keyword, cursor, count)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(ctx.CTX, string, string, string, int) error); ok {
		r2 = rf(context, streamID, keyword, cursor, count)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// SendInvitation provides a mock function with given fields: context, userIDs, masterID
func (_m *Store) SendInvitation(context ctx.CTX, userIDs []string, masterID string) error {
	ret := _m.Called(context, userIDs, masterID)

	if len(ret) == 0 {
		panic("no return value specified for SendInvitation")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []string, string) error); ok {
		r0 = rf(context, userIDs, masterID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Subscribe provides a mock function with given fields: context, info
func (_m *Store) Subscribe(context ctx.CTX, info *army.SubscriptionData) error {
	ret := _m.Called(context, info)

	if len(ret) == 0 {
		panic("no return value specified for Subscribe")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *army.SubscriptionData) error); ok {
		r0 = rf(context, info)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SubscribeByRecurrenceID provides a mock function with given fields: context, tx, recurrenceID, purchaserID, subscriberID, streamerID, shippingInfo, purchaseInfo
func (_m *Store) SubscribeByRecurrenceID(context ctx.CTX, tx money.Tx, recurrenceID string, purchaserID string, subscriberID string, streamerID string, shippingInfo army.ShippingInfo, purchaseInfo army.PurchaseInfo) (func(), error) {
	ret := _m.Called(context, tx, recurrenceID, purchaserID, subscriberID, streamerID, shippingInfo, purchaseInfo)

	if len(ret) == 0 {
		panic("no return value specified for SubscribeByRecurrenceID")
	}

	var r0 func()
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, money.Tx, string, string, string, string, army.ShippingInfo, army.PurchaseInfo) (func(), error)); ok {
		return rf(context, tx, recurrenceID, purchaserID, subscriberID, streamerID, shippingInfo, purchaseInfo)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, money.Tx, string, string, string, string, army.ShippingInfo, army.PurchaseInfo) func()); ok {
		r0 = rf(context, tx, recurrenceID, purchaserID, subscriberID, streamerID, shippingInfo, purchaseInfo)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(func())
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, money.Tx, string, string, string, string, army.ShippingInfo, army.PurchaseInfo) error); ok {
		r1 = rf(context, tx, recurrenceID, purchaserID, subscriberID, streamerID, shippingInfo, purchaseInfo)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Terminate provides a mock function with given fields: context, subscriptionLog, terminateInfo
func (_m *Store) Terminate(context ctx.CTX, subscriptionLog *pay.SubscriptionLog, terminateInfo *pay.TerminateInfo) error {
	ret := _m.Called(context, subscriptionLog, terminateInfo)

	if len(ret) == 0 {
		panic("no return value specified for Terminate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *pay.SubscriptionLog, *pay.TerminateInfo) error); ok {
		r0 = rf(context, subscriptionLog, terminateInfo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TerminateByRecurrenceID provides a mock function with given fields: context, tx, recurrenceID, terminateInfo
func (_m *Store) TerminateByRecurrenceID(context ctx.CTX, tx money.Tx, recurrenceID string, terminateInfo *pay.TerminateInfo) (func(), error) {
	ret := _m.Called(context, tx, recurrenceID, terminateInfo)

	if len(ret) == 0 {
		panic("no return value specified for TerminateByRecurrenceID")
	}

	var r0 func()
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, money.Tx, string, *pay.TerminateInfo) (func(), error)); ok {
		return rf(context, tx, recurrenceID, terminateInfo)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, money.Tx, string, *pay.TerminateInfo) func()); ok {
		r0 = rf(context, tx, recurrenceID, terminateInfo)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(func())
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, money.Tx, string, *pay.TerminateInfo) error); ok {
		r1 = rf(context, tx, recurrenceID, terminateInfo)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateOverviewCache provides a mock function with given fields: context, userID
func (_m *Store) UpdateOverviewCache(context ctx.CTX, userID string) {
	_m.Called(context, userID)
}

// UpdateUnreadSubscriptionInfo provides a mock function with given fields: context, userID, isClear
func (_m *Store) UpdateUnreadSubscriptionInfo(context ctx.CTX, userID string, isClear bool) error {
	ret := _m.Called(context, userID, isClear)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUnreadSubscriptionInfo")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, bool) error); ok {
		r0 = rf(context, userID, isClear)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewStore creates a new instance of Store. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *Store {
	mock := &Store{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
