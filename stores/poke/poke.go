/*Package poke defines poke interface*/
package poke

import (
	"fmt"

	"github.com/17media/api/base/ctx"
	pokeModel "github.com/17media/api/models/poke"
	royaltyModel "github.com/17media/api/models/royalty"
)

const (
	// PfxLiveStream is the subprefix for poke in live
	PfxLiveStream = "liveStream"
	// PfxChallenge is the subprefix for challenge
	PfxChallenge = "challenge"

	// PokeChallengeMsg is i18n msg of challenge description
	PokeChallengeMsg = "POKE_CHALLENGE_MSG"
)

var (
	// ErrStreamNotAlive means the stream is not alive
	ErrStreamNotAlive = fmt.Errorf("The stream is not alive")
	// ErrStreamNotFound means we cant find any stream from the candidate pool
	ErrStreamNotFound = fmt.Errorf("Can't find any stream")
	// ErrStreamerNotInChallenge means the streamer is not in a challenge
	ErrStreamerNotInChallenge = fmt.Errorf("Streamer not in the challenge")
	// ErrPokeAllNotEnabled means PokeAll is not enabled to the user
	ErrPokeAllNotEnabled = fmt.Errorf("PokeAll is not enabled")
	// ErrPokeAllCoolingDown means PokeAll is still cooling Down and can't be used right now
	ErrPokeAllCoolingDown = fmt.Errorf("PokeAll is cooling Down")
	// ErrPokeStreamerInGroupCall means the user can't poke other streamers in the same group call
	ErrPokeStreamerInGroupCall = fmt.Errorf("Can't poke other streamers in group call")
	// ErrPokeBackNotInGroupCall means the user poke back a streamer not in the same group call
	ErrPokeBackNotInGroupCall = fmt.Errorf("Can't poke back a streamer not in the group call")
)

// Store defines poke related functions.
type Store interface {
	// Poke let streamer poke the viewers
	Poke(context ctx.CTX, fromUserID, toUserID, srcID, notifID string, pokeType pokeModel.PokeType) error

	// PokeAll let streamer poke the viewers of the receivingGroup
	PokeAll(context ctx.CTX, fromUserID, srcID, notifID string, hasContract bool, receiverGroup pokeModel.ReceiverGroup, topFanUserIDs []string) (pokeModel.PokeAllResp, error)

	// PokeBack let the poked viewer poke the streamer back without points
	// "Poke with points" is using sendGift API instead
	PokeBack(context ctx.CTX, fromUserID, toUserID, srcID, notifID, groupCallID string) error

	// SendLoyaltyPokeBack trigger Loyalty program to record how many times poking back
	SendLoyaltyPokeBack(context ctx.CTX, userID string, event royaltyModel.EventType) error

	// GetPokeAnimationID return animationID that poke required to use
	GetPokeAnimationID() string
}
