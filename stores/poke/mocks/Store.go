// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	mock "github.com/stretchr/testify/mock"

	modelspoke "github.com/17media/api/models/poke"

	royalty "github.com/17media/api/models/royalty"
)

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// GetPokeAnimationID provides a mock function with given fields:
func (_m *Store) GetPokeAnimationID() string {
	ret := _m.Called()

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// Poke provides a mock function with given fields: context, fromUserID, toUserID, srcID, notifID, pokeType
func (_m *Store) Poke(context ctx.CTX, fromUserID string, toUserID string, srcID string, notifID string, pokeType modelspoke.PokeType) error {
	ret := _m.Called(context, fromUserID, toUserID, srcID, notifID, pokeType)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, string, modelspoke.PokeType) error); ok {
		r0 = rf(context, fromUserID, toUserID, srcID, notifID, pokeType)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PokeAll provides a mock function with given fields: context, fromUserID, srcID, notifID, hasContract, receiverGroup, topFanUserIDs
func (_m *Store) PokeAll(context ctx.CTX, fromUserID string, srcID string, notifID string, hasContract bool, receiverGroup modelspoke.ReceiverGroup, topFanUserIDs []string) (modelspoke.PokeAllResp, error) {
	ret := _m.Called(context, fromUserID, srcID, notifID, hasContract, receiverGroup, topFanUserIDs)

	var r0 modelspoke.PokeAllResp
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, bool, modelspoke.ReceiverGroup, []string) modelspoke.PokeAllResp); ok {
		r0 = rf(context, fromUserID, srcID, notifID, hasContract, receiverGroup, topFanUserIDs)
	} else {
		r0 = ret.Get(0).(modelspoke.PokeAllResp)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, string, bool, modelspoke.ReceiverGroup, []string) error); ok {
		r1 = rf(context, fromUserID, srcID, notifID, hasContract, receiverGroup, topFanUserIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PokeBack provides a mock function with given fields: context, fromUserID, toUserID, srcID, notifID, groupCallID
func (_m *Store) PokeBack(context ctx.CTX, fromUserID string, toUserID string, srcID string, notifID string, groupCallID string) error {
	ret := _m.Called(context, fromUserID, toUserID, srcID, notifID, groupCallID)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, string, string) error); ok {
		r0 = rf(context, fromUserID, toUserID, srcID, notifID, groupCallID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SendLoyaltyPokeBack provides a mock function with given fields: context, userID, event
func (_m *Store) SendLoyaltyPokeBack(context ctx.CTX, userID string, event royalty.EventType) error {
	ret := _m.Called(context, userID, event)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, royalty.EventType) error); ok {
		r0 = rf(context, userID, event)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}
