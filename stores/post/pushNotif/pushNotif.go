package pushnotif

import (
	"time"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models"
	queueModel "github.com/17media/api/models/queue"
)

// NotifJob is the wrap packing info to task queue
type NotifJob struct {
	StreamerUserID string               `json:"streamerID"`     // StreamerID is the userID in `POST` table
	StreamerOpenID string               `json:"streamerOpenID"` // StreamerOpenID is the streamer's openID
	Follows        []*models.UserFollow `json:"follows"`        // Follows includes all followers following Streamer
	TargetRegions  map[string]time.Time `json:"targetRegions"`  // TargetRegions includes triggered regions with specified time
	DefineRegions  []string             `json:"defineRegions"`  // DefineRegions includes all regions in the config
	Caption        string               `json:"caption"`
	Picture        string               `json:"picture"`
	CronID         string               `json:"cronID"`
	PostID         string               `json:"postID"`
	Timestamp      int64                `json:"timestamp"`
}

// Store is the interface handling jobs processed in the task queue
type Store interface {
	// SendNotifJob enqueues NotifJob to task queue
	SendNotifJob(
		context ctx.CTX,
		post models.Post,
		targetRegionGroups map[string]time.Time,
		follows []*models.UserFollow,
		cronID string,
	) error
	// ProcessNotifJob dequeues NotifJob in task queue, and send push notification to target user
	ProcessNotifJob(
		context ctx.CTX,
		data []byte,
		option queueModel.CallbackOption,
	) error
}
