package streamRoomBox

import (
	"errors"
	"strconv"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/env"
	"github.com/17media/api/base/testutil"
	btime "github.com/17media/api/base/time"
	"github.com/17media/api/models"

	baggageModel "github.com/17media/api/models/baggage"
	confModel "github.com/17media/api/models/config"
	moneyModel "github.com/17media/api/models/money"
	redisModel "github.com/17media/api/models/redis"
	regionModel "github.com/17media/api/models/region"
	model "github.com/17media/api/models/streamRoomBox"

	mkv "github.com/17media/api/service/kv/mocks"
	mredis "github.com/17media/api/service/redis/mocks"
	mregion "github.com/17media/api/service/region/mocks"
	"github.com/17media/api/setup/dimanager"
	mBaggage "github.com/17media/api/stores/baggage/mocks"
	mLevel "github.com/17media/api/stores/level/mocks"
	mHelper "github.com/17media/api/stores/live/helper/mocks"
	mMoney "github.com/17media/api/stores/money/mocks"
	streamRoomBoxConfig "github.com/17media/api/stores/streamRoomBox/config"
	mBoxHelper "github.com/17media/api/stores/streamRoomBox/helper/mocks"
	mUser "github.com/17media/api/stores/user/mocks"
)

var (
	mockCTX  = ctx.Background()
	anyCTX   = mock.AnythingOfType("ctx.CTX")
	mockNow  = time.Date(2024, 4, 10, 0, 0, 0, 0, time.UTC)
	mockUser = &models.User{
		UserID: testutil.MockUserID,
	}
	taipeiLoc, _ = btime.LocationTaipei()
)

type closeFunc func()

type mockFuncs struct {
	testutil.MockFuncs
}

func (m *mockFuncs) GetEventEndTimeAt(region string, targetUser model.TargetUserType, targetTime time.Time) (int64, error) {
	ret := m.Called(region, targetUser, targetTime)
	return ret.Get(0).(int64), ret.Error(1)
}

func (m *mockFuncs) IsBlocked(userID, deviceID, ip string) bool {
	ret := m.Called(userID, deviceID, ip)
	return ret.Get(0).(bool)
}

func (m *mockFuncs) GetUpgradeSettingsAt(region string, targetUser model.TargetUserType, targetTime time.Time) ([]*model.UpgradeSetting, error) {
	ret := m.Called(region, targetUser, targetTime)
	return ret.Get(0).([]*model.UpgradeSetting), ret.Error(1)
}

func (m *mockFuncs) GetBoxSettingsAt(region string, targetUser model.TargetUserType, targetTime time.Time) ([]*model.BoxSetting, error) {
	ret := m.Called(region, targetUser, targetTime)
	return ret.Get(0).([]*model.BoxSetting), ret.Error(1)
}

func (m *mockFuncs) GetSystemStyle(region string) model.SystemStyle {
	ret := m.Called(region)
	return ret.Get(0).(model.SystemStyle)
}

func (m *mockFuncs) GetBoxStyles(region string) map[string]model.BoxStyle {
	ret := m.Called(region)
	return ret.Get(0).(map[string]model.BoxStyle)
}

func (m *mockFuncs) GetEventAt(region string, targetUser model.TargetUserType, targetTime time.Time) (*streamRoomBoxConfig.StreamRoomBoxEvent, error) {
	ret := m.Called(region, targetUser, targetTime)
	return ret.Get(0).(*streamRoomBoxConfig.StreamRoomBoxEvent), ret.Error(1)
}

func (m *mockFuncs) GetNewbieDay() int {
	ret := m.Called()
	return ret.Int(0)
}

type streamRoomBoxTestSuite struct {
	suite.Suite

	mockBank       *mMoney.Bank
	mockUser       *mUser.Store
	mockLiveHelper *mHelper.Helper
	mockBoxHelper  *mBoxHelper.Helper
	mockFuncs      *mockFuncs
	mockKV         *mkv.KV
	mockPersist    *mredis.Service
	mockRegion     *mregion.Service
	mockLevel      *mLevel.Level
	mockBaggage    *mBaggage.Store

	im      *impl
	manager *dimanager.Manager
}

func (s *streamRoomBoxTestSuite) SetupSuite() {
	s.manager = dimanager.DefaultManager
}

func (s *streamRoomBoxTestSuite) TearDownSuite() {
}

func TestStreamRoomBoxTestSuite(t *testing.T) {
	suite.Run(t, new(streamRoomBoxTestSuite))
}

func (s *streamRoomBoxTestSuite) SetupSubTest() {
	s.manager.ClearMock()

	s.mockBank = mMoney.RegisterBankMock(s.manager)
	s.mockRegion = mregion.RegisterMock(s.manager)
	s.mockLiveHelper = mHelper.RegisterMock(s.manager)
	s.mockUser = mUser.RegisterMock(s.manager)
	s.mockKV = mkv.RegisterMock(s.manager)
	s.mockPersist = mredis.RegisterPersistentMock(s.manager)
	s.mockLevel = mLevel.RegisterMock(s.manager)
	s.mockBaggage = mBaggage.RegisterMock(s.manager)
	s.manager.Compile()

	s.mockFuncs = &mockFuncs{}
	s.mockBoxHelper = &mBoxHelper.Helper{}
	timeNow = s.mockFuncs.TimeNow
	getUpgradeSettingsAt = s.mockFuncs.GetUpgradeSettingsAt
	getEventEndTimeAt = s.mockFuncs.GetEventEndTimeAt
	getBoxSettingsAt = s.mockFuncs.GetBoxSettingsAt
	isBlockedUser = s.mockFuncs.IsBlocked
	getSystemStyle = s.mockFuncs.GetSystemStyle
	getBoxStyles = s.mockFuncs.GetBoxStyles
	getEventAt = s.mockFuncs.GetEventAt
	getNewbieDay = s.mockFuncs.GetNewbieDay

	s.im = GetStreamRoomBox(s.manager).(*impl)
	s.im.boxHelper = s.mockBoxHelper

	getDailyBoxLimitPerUser = func() int { return 50 }
	getDailyBoxLimitPerRoom = func() int { return 2 }
}

func (s *streamRoomBoxTestSuite) TearDownSubTest() {
	s.manager.ClearMock()
	s.mockBank.AssertExpectations(s.T())
	s.mockUser.AssertExpectations(s.T())
	s.mockKV.AssertExpectations(s.T())
	s.mockPersist.AssertExpectations(s.T())
	s.mockFuncs.AssertExpectations(s.T())
	s.mockRegion.AssertExpectations(s.T())
	s.mockLiveHelper.AssertExpectations(s.T())
	s.mockBoxHelper.AssertExpectations(s.T())
	s.mockLevel.AssertExpectations(s.T())
	s.mockBaggage.AssertExpectations(s.T())
}

func (s *streamRoomBoxTestSuite) TestIncrClaimedCounts() {
	mockClaimTime := time.Date(2024, 4, 10, 12, 12, 0, 0, time.UTC)
	mockEndOfDay := time.Date(2024, 4, 11, 0, 0, 0, 0, time.UTC)
	endOfDayTs := mockEndOfDay.Unix()
	mockEndOfDayttl := time.Unix(endOfDayTs, 0).Sub(mockNow)
	mockUserID := "mockUserID"
	mockStreamerID := "mockStreamerID"
	mockStreamerViewerID := mockUserID + ":" + mockStreamerID
	mockDeviceID := "mockDeviceID"
	mockClaimRegion := "TW"

	type testCase struct {
		desc   string
		box    *model.StreamRoomBox
		setup  func(t testCase)
		expErr error
	}
	tests := []testCase{
		{
			desc: "success",
			box: &model.StreamRoomBox{
				StreamerViewerID: mockStreamerViewerID,
				UserID:           mockUserID,
				StreamerID:       mockStreamerID,
				Current: &model.BoxInfo{
					Level:     1,
					Count:     0,
					ExpiredAt: 0,
				},
				ClaimRegion: mockClaimRegion,
				ClaimInfo: &model.ClaimInfo{
					UserType: model.TargetUserType_Newbie,

					ClaimTime: mockClaimTime,
					DeviceID:  mockDeviceID,
				},
			},
			setup: func(t testCase) {
				s.mockBoxHelper.On("GetNextDayStart", anyCTX, t.box.ClaimInfo.ClaimTime.Unix(), t.box.ClaimRegion).Return(endOfDayTs).Once()
				s.mockFuncs.On("TimeNow").Return(mockNow).Once()
				s.mockPersist.On("Incr", anyCTX, keyTotalByUserID(mockUserID)).Return(int64(0), nil).Once()
				s.mockPersist.On("Expire", anyCTX, keyTotalByUserID(mockUserID), mockEndOfDayttl).Return(nil).Once()
				s.mockPersist.On("Incr", anyCTX, keyTotalByDeviceID(mockDeviceID)).Return(int64(0), nil).Once()
				s.mockPersist.On("Expire", anyCTX, keyTotalByDeviceID(mockDeviceID), mockEndOfDayttl).Return(nil).Once()
				s.mockPersist.On("Incr", anyCTX, keyTotalByRoom(mockUserID, mockStreamerID)).Return(int64(0), nil).Once()
				s.mockPersist.On("Expire", anyCTX, keyTotalByRoom(mockUserID, mockStreamerID), mockEndOfDayttl).Return(nil).Once()
				s.mockPersist.On("HIncrby", anyCTX, keyUpgradeStatus(mockUserID), strconv.Itoa(int(model.BoxLevel_Normal)), 1).Return(int64(0), nil).Once()
				s.mockPersist.On("Expire", anyCTX, keyUpgradeStatus(mockUserID), mockEndOfDayttl).Return(nil).Once()
			},
			expErr: nil,
		},
	}

	for _, t := range tests {
		s.Run(t.desc, func() {
			if t.setup != nil {
				t.setup(t)
			}
			err := s.im.incrClaimedCounts(mockCTX, t.box)
			s.Equal(t.expErr, err, t.desc)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestGetUpgradeSettings() {
	mockStreamRegion := "TW"
	mockUpgradeSettings := []*model.UpgradeSetting{
		{
			Level:       1,
			Limit:       -1,
			Probability: 50,
		},
		{
			Level:       2,
			Limit:       10,
			Probability: 30,
		},
		{
			Level:       3,
			Limit:       10,
			Probability: 20,
		},
	}
	mockClaimTime := time.Date(2024, 4, 10, 12, 12, 0, 0, time.UTC)
	mockUserID := "mockUserID"
	mockStreamerID := "mockStreamerID"
	mockStreamerViewerID := mockUserID + ":" + mockStreamerID
	mockDeviceID := "mockDeviceID"
	mockClaimRegion := "TW"
	Level1Field := "1"
	Level2Field := "2"
	Level3Field := "3"

	type testCase struct {
		desc   string
		userID string
		box    *model.StreamRoomBox
		setup  func(t testCase)
		expRes []*model.UpgradeSetting
		expErr error
	}
	tests := []testCase{
		{
			desc:   "success",
			userID: mockUserID,
			box: &model.StreamRoomBox{
				StreamerViewerID: mockStreamerViewerID,
				UserID:           mockUserID,
				StreamerID:       mockStreamerID,
				Current: &model.BoxInfo{
					Level:     1,
					Count:     0,
					ExpiredAt: 0,
				},
				ClaimRegion: mockClaimRegion,
				ClaimInfo: &model.ClaimInfo{
					UserType:  model.TargetUserType_Newbie,
					ClaimTime: mockClaimTime,
					DeviceID:  mockDeviceID,
				},
			},
			setup: func(t testCase) {
				s.mockFuncs.On("GetUpgradeSettingsAt", mockStreamRegion, model.TargetUserType_Newbie, mockClaimTime).Return(mockUpgradeSettings, nil).Once()
				s.mockPersist.On("HMGet", anyCTX, keyUpgradeStatus(t.userID), Level1Field, Level2Field, Level3Field).Return([]redisModel.MVal{
					{Valid: true, Value: []byte("40")},
					{Valid: true, Value: []byte("8")},
					{Valid: true, Value: []byte("2")},
				}, nil).Once()
			},
			expRes: []*model.UpgradeSetting{
				{
					Level:       1,
					Limit:       -1,
					Probability: 50,
					Current:     40,
				},
				{
					Level:       2,
					Limit:       10,
					Probability: 30,
					Current:     8,
				},
				{
					Level:       3,
					Limit:       10,
					Probability: 20,
					Current:     2,
				},
			},
			expErr: nil,
		},
		{
			desc:   "error: get user's upgrade settings from redis failed",
			userID: mockUserID,
			box: &model.StreamRoomBox{
				StreamerViewerID: mockStreamerViewerID,
				UserID:           mockUserID,
				StreamerID:       mockStreamerID,
				Current: &model.BoxInfo{
					Level:     1,
					Count:     0,
					ExpiredAt: 0,
				},
				ClaimRegion: mockClaimRegion,
				ClaimInfo: &model.ClaimInfo{
					UserType:  model.TargetUserType_Newbie,
					ClaimTime: mockClaimTime,
					DeviceID:  mockDeviceID,
				},
			},
			setup: func(t testCase) {
				s.mockFuncs.On("GetUpgradeSettingsAt", mockStreamRegion, model.TargetUserType_Newbie, mockClaimTime).Return(mockUpgradeSettings, nil).Once()
				s.mockPersist.On("HMGet", anyCTX, keyUpgradeStatus(t.userID), Level1Field, Level2Field, Level3Field).Return(nil, errors.New("error")).Once()
			},
			expRes: nil,
			expErr: errors.New("error"),
		},
	}

	for _, t := range tests {
		s.Run(t.desc, func() {
			if t.setup != nil {
				t.setup(t)
			}
			res, err := s.im.getUpgradeSettings(mockCTX, t.box)
			s.Equal(t.expRes, res, t.desc)
			s.Equal(t.expErr, err, t.desc)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestGetTodayUpgradedBox() {
	mockUserID := "mockUserID"

	tests := []struct {
		Desc             string
		Setup            func()
		InputUpgradeInfo []*model.UpgradeInfo
		ExpUpgradeInfo   []*model.UpgradeInfo
		ExpErr           error
	}{
		{
			Desc: "success",
			Setup: func() {
				s.mockPersist.On("HMGet", anyCTX, keyUpgradeStatus(mockUserID), "1", "2", "3").Return(
					[]redisModel.MVal{
						{
							Valid: true,
							Value: []byte("17"),
						},
						{
							Valid: true,
							Value: []byte("17"),
						},
						{
							Valid: false,
						},
					},
					nil).Once()
			},
			InputUpgradeInfo: []*model.UpgradeInfo{
				{
					Level: model.BoxLevel_Normal,
				},
				{
					Level: model.BoxLevel_Rare,
				},
				{
					Level: model.BoxLevel_SuperRare,
				},
			},
			ExpUpgradeInfo: []*model.UpgradeInfo{
				{
					Level: model.BoxLevel_Normal,
					Count: 17,
				},
				{
					Level: model.BoxLevel_Rare,
					Count: 17,
				},
				{
					Level: model.BoxLevel_SuperRare,
					Count: 0,
				},
			},
		},
	}

	for _, t := range tests {
		s.Run(t.Desc, func() {
			if t.Setup != nil {
				t.Setup()
			}
			err := s.im.getTodayUpgradedBox(mockCTX, mockUserID, t.InputUpgradeInfo)
			s.Equal(t.ExpErr, err)
			s.Equal(t.ExpUpgradeInfo, t.InputUpgradeInfo)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestGetStreamRoomBoxLiveInfo() {
	mockUserID := "mockUserID"
	mockViewer := &models.User{
		UserID:                   mockUserID,
		InternationalPhoneNumber: "777",
		RegisterRegion:           "JP",
		RegisterTime:             1712534400, // 2024年4月8日星期一 08:00:00 GMT+08:00
	}
	mockViewerIP := "111.222.333.444"
	mockStreamerID := "mockStreamerID"
	mockStreamerRegion := "JP"
	mockStreamer := &models.User{UserID: mockStreamerID, Region: mockStreamerRegion}
	mockDeviceInfo := &confModel.DeviceInfo{DeviceID: "mockDeviceID", PublicIP: mockViewerIP}
	mockRegionInfo := &regionModel.RegionInfo{
		RegionByIP: "JP",
		Region:     "TW",
	}
	mockSystemStyle := model.SystemStyle{
		CompleteIconURL: "complete-icon.png",
	}
	mockEventEndTime := int64(1714435200) // 2024年4月30日星期二 08:00:00 GMT+08:00
	mockNewbieEvent := &streamRoomBoxConfig.StreamRoomBoxEvent{
		TargetUser: model.TargetUserType_Newbie,
		EndTime:    mockEventEndTime,
	}
	mockAllEvent := &streamRoomBoxConfig.StreamRoomBoxEvent{
		TargetUser: model.TargetUserType_All,
		EndTime:    mockEventEndTime,
	}
	mockBoxSettings := []*model.BoxSetting{
		{
			Level:   model.BoxLevel_Normal,
			StyleID: "box_style_normal",
			RewardSettings: []*model.RewardSetting{
				{
					RewardType:  model.RewardType_BBC,
					Probability: 50,
					BBCSetting: model.NumericRewardSetting{
						Min: 1,
						Max: 3,
					},
				},
			},
		},
		{
			Level:   model.BoxLevel_Rare,
			StyleID: "box_style_rare",
			RewardSettings: []*model.RewardSetting{
				{
					RewardType:  model.RewardType_BBC,
					Probability: 30,
					BBCSetting: model.NumericRewardSetting{
						Min: 4,
						Max: 9,
					},
				},
			},
		},
		{
			Level:   model.BoxLevel_SuperRare,
			StyleID: "box_style_super_rare",
			RewardSettings: []*model.RewardSetting{
				{
					RewardType:  model.RewardType_Exp,
					Probability: 20,
					ExpSetting: model.NumericRewardSetting{
						Min: 10,
						Max: 25,
					},
				},
			},
		},
	}

	type args struct {
		viewer     *models.User
		streamerID string
		regionInfo *regionModel.RegionInfo
		deviceInfo *confModel.DeviceInfo
	}

	type testCase struct {
		desc      string
		args      args
		setup     func(t testCase)
		expResult *model.StreamRoomBoxLiveInfo
		expErr    error
	}
	tests := []testCase{
		{
			desc: "success: get stream room box live info",
			args: args{
				viewer:     mockViewer,
				streamerID: mockStreamerID,
				regionInfo: mockRegionInfo,
				deviceInfo: mockDeviceInfo,
			},
			setup: func(t testCase) {
				s.mockFuncs.On("TimeNow").Return(mockNow).Once()
				s.mockFuncs.On("IsBlocked", t.args.viewer.UserID, t.args.deviceInfo.DeviceID, t.args.deviceInfo.PublicIP).Return(false).Once()
				s.mockUser.On("GetRegionByUserID", anyCTX, t.args.streamerID).Return(mockStreamerRegion).Once()
				s.mockFuncs.On("GetEventEndTimeAt", mockStreamerRegion, model.TargetUserType_Newbie, mockNow).Return(mockEventEndTime, nil).Once()
				s.mockFuncs.On("GetBoxSettingsAt", mockStreamerRegion, model.TargetUserType_Newbie, mockNow).Return(mockBoxSettings, nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(30)
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, t.args.viewer.RegisterRegion).Return(int64(28800), nil)
				s.mockFuncs.On("GetEventAt", mockStreamerRegion, model.TargetUserType_Newbie, mockNow).Return(mockNewbieEvent, nil)
				s.mockFuncs.On("GetEventAt", mockStreamerRegion, model.TargetUserType_All, mockNow).Return(mockAllEvent, nil)
				s.mockUser.On("GetUsers", anyCTX, mockStreamerID).Return(
					[]models.User{*mockStreamer}, nil).Once()
				s.mockBoxHelper.On("GetBoxStatus", anyCTX, mockUserID, mockStreamerID, mockStreamerRegion).Return(&model.StreamRoomBox{
					Current: &model.BoxInfo{
						Level:     model.BoxLevel_Normal,
						Count:     0,
						ExpiredAt: 0,
					},
					Next: &model.BoxInfo{
						Level:     model.BoxLevel_SuperRare,
						Count:     0,
						ExpiredAt: 1713514392,
					},
					ClaimRegion: mockStreamerRegion,
					ClaimInfo:   nil, // ClaimInfo only injected in ClaimStreamRoomBox
				}, nil).Once()
				s.mockPersist.On("Get", anyCTX, keyTotalByRoom(mockUserID, mockStreamerID)).Return([]byte("0"), nil).Twice()
				s.mockPersist.On("Get", anyCTX, keyTotalByUserID(t.args.viewer.UserID)).Return([]byte("1"), nil).Once()
				s.mockPersist.On("Get", anyCTX, keyTotalByDeviceID(t.args.deviceInfo.DeviceID)).Return([]byte("1"), nil).Once()
				s.mockFuncs.On("GetBoxStyles", mockStreamerRegion).Return(map[string]model.BoxStyle{
					"box_style_normal":     {UpgradeAnimationID: "normal-animation"},
					"box_style_rare":       {UpgradeAnimationID: "rare-animation"},
					"box_style_super_rare": {UpgradeAnimationID: "super-rare-animation"},
				}).Once()
				s.mockFuncs.On("GetSystemStyle", mockStreamerRegion).Return(mockSystemStyle).Once()
			},
			expResult: &model.StreamRoomBoxLiveInfo{
				BoxLevel:                 model.BoxLevel_Normal,
				NextBoxLevel:             model.BoxLevel_SuperRare,
				StreamRoomClaimingCount:  0,
				IsPhoneVerified:          true,
				IsStreamRoomLimitReached: false,
				IsDailyUserLimitReached:  false,
				IsClaimingLastDay:        false,
				ClaimingEndTime:          1713514392,
				Timestamp:                mockNow.Unix(),
				SystemStyle: model.SystemStyle{
					CompleteIconURL: "complete-icon.png",
				},
				BoxRewardSettings: []model.BoxRewardSetting{
					{
						Level: model.BoxLevel_Normal,
						BBCSetting: model.NumericRewardSetting{
							Min: 1,
							Max: 3,
						},
						Style: model.BoxStyle{
							UpgradeAnimationID: "normal-animation",
						},
					},
					{
						Level: model.BoxLevel_Rare,
						BBCSetting: model.NumericRewardSetting{
							Min: 4,
							Max: 9,
						},
						Style: model.BoxStyle{
							UpgradeAnimationID: "rare-animation",
						},
					},
					{
						Level: model.BoxLevel_SuperRare,
						BBCSetting: model.NumericRewardSetting{
							Min: 0,
							Max: 0,
						},
						Style: model.BoxStyle{
							UpgradeAnimationID: "super-rare-animation",
						},
					},
				},
			},
			expErr: nil,
		},
		{
			desc: "success: get stream room box live info with nil next box",
			args: args{
				viewer:     mockViewer,
				streamerID: mockStreamerID,
				regionInfo: mockRegionInfo,
				deviceInfo: mockDeviceInfo,
			},
			setup: func(t testCase) {
				s.mockFuncs.On("TimeNow").Return(mockNow).Once()
				s.mockFuncs.On("IsBlocked", t.args.viewer.UserID, t.args.deviceInfo.DeviceID, t.args.deviceInfo.PublicIP).Return(false).Once()
				s.mockUser.On("GetRegionByUserID", anyCTX, t.args.streamerID).Return(mockStreamerRegion).Once()
				s.mockFuncs.On("GetEventEndTimeAt", mockStreamerRegion, model.TargetUserType_Newbie, mockNow).Return(mockEventEndTime, nil).Once()
				s.mockFuncs.On("GetBoxSettingsAt", mockStreamerRegion, model.TargetUserType_Newbie, mockNow).Return(mockBoxSettings, nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(30)
				s.mockFuncs.On("GetEventAt", mockStreamerRegion, model.TargetUserType_Newbie, mockNow).Return(mockNewbieEvent, nil)
				s.mockFuncs.On("GetEventAt", mockStreamerRegion, model.TargetUserType_All, mockNow).Return(mockAllEvent, nil)
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, t.args.viewer.RegisterRegion).Return(int64(28800), nil)
				s.mockUser.On("GetUsers", anyCTX, mockStreamerID).Return(
					[]models.User{*mockStreamer}, nil).Once()
				s.mockBoxHelper.On("GetBoxStatus", anyCTX, mockUserID, mockStreamerID, mockStreamerRegion).Return(&model.StreamRoomBox{
					ClaimRegion: mockStreamerRegion,
					Current: &model.BoxInfo{
						Level:     model.BoxLevel_Normal,
						Count:     0,
						ExpiredAt: 0,
					},
				}, nil).Once()
				s.mockPersist.On("Get", anyCTX, keyTotalByRoom(mockUserID, mockStreamerID)).Return([]byte("0"), nil).Twice()
				s.mockPersist.On("Get", anyCTX, keyTotalByUserID(t.args.viewer.UserID)).Return([]byte("1"), nil).Once()
				s.mockPersist.On("Get", anyCTX, keyTotalByDeviceID(t.args.deviceInfo.DeviceID)).Return([]byte("1"), nil).Once()
				s.mockFuncs.On("GetBoxStyles", mockStreamerRegion).Return(map[string]model.BoxStyle{
					"box_style_normal": {UpgradeAnimationID: "normal-animation"},
					"box_style_rare":   {UpgradeAnimationID: "rare-animation"},
				}).Once()
				s.mockFuncs.On("GetSystemStyle", mockStreamerRegion).Return(mockSystemStyle).Once()
			},
			expResult: &model.StreamRoomBoxLiveInfo{
				BoxLevel:                 model.BoxLevel_Normal,
				NextBoxLevel:             model.BoxLevel_None,
				StreamRoomClaimingCount:  0,
				IsPhoneVerified:          true,
				IsStreamRoomLimitReached: false,
				IsDailyUserLimitReached:  false,
				IsClaimingLastDay:        false,
				ClaimingEndTime:          int64(0),
				Timestamp:                mockNow.Unix(),
				SystemStyle: model.SystemStyle{
					CompleteIconURL: "complete-icon.png",
				},
				BoxRewardSettings: []model.BoxRewardSetting{
					{
						Level: model.BoxLevel_Normal,
						BBCSetting: model.NumericRewardSetting{
							Min: 1,
							Max: 3,
						},
						Style: model.BoxStyle{
							UpgradeAnimationID: "normal-animation",
						},
					},
					{
						Level: model.BoxLevel_Rare,
						BBCSetting: model.NumericRewardSetting{
							Min: 4,
							Max: 9,
						},
						Style: model.BoxStyle{
							UpgradeAnimationID: "rare-animation",
						},
					},
					{
						Level: model.BoxLevel_SuperRare,
						BBCSetting: model.NumericRewardSetting{
							Min: 0,
							Max: 0,
						},
						Style: model.BoxStyle{}, // box style not found in style map
					},
				},
			},
			expErr: nil,
		},
	}

	for _, t := range tests {
		s.Run(t.desc, func() {
			if t.setup != nil {
				t.setup(t)
			}
			res, err := s.im.GetStreamRoomBoxLiveInfo(mockCTX, t.args.viewer, t.args.streamerID, t.args.regionInfo, t.args.deviceInfo)
			s.Equal(t.expResult, res)
			s.Equal(t.expErr, err)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestClaimStreamRoomBox() {
	var (
		mockViewerID = mockUser.UserID

		mockStreamerID     = "mockStreamerID"
		mockStreamerRegion = "TW"
		mockStreamer       = &models.User{UserID: mockStreamerID, Region: mockStreamerRegion}

		mockRegionInfo = &regionModel.RegionInfo{}
		mockDeviceInfo = &confModel.DeviceInfo{}

		mockRewardInfo     = &model.RewardInfo{}
		mockBox_NotUpgrade = &model.StreamRoomBox{
			ClaimRegion: mockStreamerRegion,
			Current: &model.BoxInfo{
				Count: 0,
			},
		}
		mockBox_Upgrade = &model.StreamRoomBox{
			ClaimRegion: mockStreamerRegion,
			Current: &model.BoxInfo{
				Count: getDailyBoxLimitPerRoom(),
			},
			Next: &model.BoxInfo{
				Level: model.BoxLevel_SuperRare,
			},
		}
	)

	tests := []struct {
		Desc   string
		Setup  func() closeFunc
		ExpRes *model.ClaimResult
		ExpErr error
	}{
		{
			Desc: "fail because user is not in live",
			Setup: func() closeFunc {
				patches := gomonkey.ApplyPrivateMethod(s.im, "isUserInLive", func(context ctx.CTX, userID string, streamerID string) bool {
					return false
				})
				return func() { patches.Reset() }
			},
			ExpRes: nil,
			ExpErr: ErrNotEligible,
		},
		{
			Desc: "fail because user is not eligible",
			Setup: func() closeFunc {
				patches := gomonkey.ApplyPrivateMethod(s.im, "isUserInLive", func(context ctx.CTX, userID string, streamerID string) bool {
					return true
				})
				s.mockFuncs.On("TimeNow").Return(mockNow).Once()
				patches.ApplyPrivateMethod(s.im, "isUserEligible", func(context ctx.CTX, viewer *models.User, streamerID string, regionInfo *regionModel.RegionInfo, deviceInfo *confModel.DeviceInfo, targetTime time.Time) bool {
					return false // not eligible
				})
				return func() { patches.Reset() }
			},
			ExpRes: nil,
			ExpErr: ErrNotEligible,
		},
		{
			Desc: "fail because another claim is in progress",
			Setup: func() closeFunc {
				patches := gomonkey.ApplyPrivateMethod(s.im, "isUserInLive", func(context ctx.CTX, userID string, streamerID string) bool {
					return true
				})
				s.mockFuncs.On("TimeNow").Return(mockNow).Once()
				patches.ApplyPrivateMethod(s.im, "isUserEligible", func(context ctx.CTX, viewer *models.User, streamerID string, regionInfo *regionModel.RegionInfo, deviceInfo *confModel.DeviceInfo, targetTime time.Time) bool {
					return true
				})
				patches.ApplyPrivateMethod(s.im, "acquireClaimLock", func(context ctx.CTX, userID string) error {
					return ErrUserLocking // fail to acquire lock
				})
				return func() { patches.Reset() }
			},
			ExpRes: nil,
			ExpErr: ErrUserLocking,
		},
		{
			Desc: "fail becasue reach stream room limit",
			Setup: func() closeFunc {
				patches := gomonkey.ApplyPrivateMethod(s.im, "isUserInLive", func(context ctx.CTX, userID string, streamerID string) bool {
					return true
				})
				s.mockFuncs.On("TimeNow").Return(mockNow).Once()
				patches.ApplyPrivateMethod(s.im, "isUserEligible", func(context ctx.CTX, viewer *models.User, streamerID string, regionInfo *regionModel.RegionInfo, deviceInfo *confModel.DeviceInfo, targetTime time.Time) bool {
					return true
				})
				patches.ApplyPrivateMethod(s.im, "acquireClaimLock", func(context ctx.CTX, userID string) error {
					return nil
				})
				patches.ApplyPrivateMethod(s.im, "releaseClaimLock", func(context ctx.CTX, userID string) error {
					return nil
				})
				patches.ApplyPrivateMethod(s.im, "checkLimit", func(context ctx.CTX, userID, streamerID, deviceID string) error {
					return ErrReachStreamRoomLimit // reach limit
				})
				return func() { patches.Reset() }
			},
			ExpRes: nil,
			ExpErr: ErrReachStreamRoomLimit,
		},
		{
			Desc: "success but not upgrade",
			Setup: func() closeFunc {
				patches := gomonkey.ApplyPrivateMethod(s.im, "isUserInLive", func(context ctx.CTX, userID string, streamerID string) bool {
					return true
				})
				s.mockFuncs.On("TimeNow").Return(mockNow).Once()
				patches.ApplyPrivateMethod(s.im, "isUserEligible", func(context ctx.CTX, viewer *models.User, streamerID string, regionInfo *regionModel.RegionInfo, deviceInfo *confModel.DeviceInfo, targetTime time.Time) bool {
					return true
				})
				// lock
				patches.ApplyPrivateMethod(s.im, "acquireClaimLock", func(context ctx.CTX, userID string) error {
					return nil
				})
				patches.ApplyPrivateMethod(s.im, "releaseClaimLock", func(context ctx.CTX, userID string) error {
					return nil
				})
				patches.ApplyPrivateMethod(s.im, "getEligibleEvents", func(context ctx.CTX, user *models.User, region string, targetTime time.Time) []*streamRoomBoxConfig.StreamRoomBoxEvent {
					return []*streamRoomBoxConfig.StreamRoomBoxEvent{
						{TargetUser: model.TargetUserType_Newbie},
						{TargetUser: model.TargetUserType_All},
					}
				})
				// check limit
				patches.ApplyPrivateMethod(s.im, "checkLimit", func(context ctx.CTX, userID, streamerID, deviceID string) error {
					return nil
				})
				// prepare data
				s.mockUser.On("GetUsers", anyCTX, mockStreamerID).Return(
					[]models.User{*mockStreamer}, nil).Once()
				// get box info
				s.mockBoxHelper.On("GetBoxStatus", anyCTX, mockViewerID, mockStreamerID, mockStreamerRegion).Return(mockBox_NotUpgrade, nil).Once()

				patches.ApplyPrivateMethod(s.im, "getUserType", func(context ctx.CTX, user *models.User, targetTime time.Time) model.TargetUserType {
					return model.TargetUserType_Newbie
				})
				// open
				patches.ApplyPrivateMethod(s.im, "open", func(context ctx.CTX, box *model.StreamRoomBox, targetTime time.Time) (*model.RewardInfo, error) {
					return mockRewardInfo, nil
				})
				// send reward
				patches.ApplyPrivateMethod(s.im, "sendReward", func(context ctx.CTX, receiver *models.User, reward *model.RewardInfo) error {
					return nil
				})
				// incr count
				patches.ApplyPrivateMethod(s.im, "incrClaimedCounts", func(context ctx.CTX, box *model.StreamRoomBox, targetTime time.Time) error {
					return nil
				})
				s.mockBoxHelper.On("IncrCurrentAndSet", anyCTX, mockBox_NotUpgrade).Return(nil).Once()

				return func() { patches.Reset() }
			},
			ExpRes: &model.ClaimResult{
				NextBoxLevel: model.BoxLevel_None,
				RewardType:   mockRewardInfo.RewardType,
				BBC:          mockRewardInfo.BBC,
				Exp:          mockRewardInfo.Exp,
				GiftID:       mockRewardInfo.Gift.GiftID,
			},
			ExpErr: nil,
		},
		{
			Desc: "success and also upgrade",
			Setup: func() closeFunc {
				patches := gomonkey.ApplyPrivateMethod(s.im, "isUserInLive", func(context ctx.CTX, userID string, streamerID string) bool {
					return true
				})
				s.mockFuncs.On("TimeNow").Return(mockNow).Once()
				patches.ApplyPrivateMethod(s.im, "isUserEligible", func(context ctx.CTX, viewer *models.User, streamerID string, regionInfo *regionModel.RegionInfo, deviceInfo *confModel.DeviceInfo, targetTime time.Time) bool {
					return true
				})
				// lock
				patches.ApplyPrivateMethod(s.im, "acquireClaimLock", func(context ctx.CTX, userID string) error {
					return nil
				})
				patches.ApplyPrivateMethod(s.im, "releaseClaimLock", func(context ctx.CTX, userID string) error {
					return nil
				})
				// prepare data
				s.mockUser.On("GetUsers", anyCTX, mockStreamerID).Return(
					[]models.User{*mockStreamer}, nil).Once()

				patches.ApplyPrivateMethod(s.im, "getEligibleEvents", func(context ctx.CTX, user *models.User, region string, targetTime time.Time) []*streamRoomBoxConfig.StreamRoomBoxEvent {
					return []*streamRoomBoxConfig.StreamRoomBoxEvent{
						{TargetUser: model.TargetUserType_Newbie},
					}
				})
				patches.ApplyPrivateMethod(s.im, "getUserType", func(context ctx.CTX, user *models.User, targetTime time.Time) model.TargetUserType {
					return model.TargetUserType_Newbie
				})
				// check limit
				patches.ApplyPrivateMethod(s.im, "checkLimit", func(context ctx.CTX, userID, streamerID, deviceID string) error {
					return nil
				})
				// get box info
				s.mockBoxHelper.On("GetBoxStatus", anyCTX, mockViewerID, mockStreamerID, mockStreamerRegion).Return(mockBox_Upgrade, nil).Once()
				// open
				patches.ApplyPrivateMethod(s.im, "open", func(context ctx.CTX, box *model.StreamRoomBox, targetTime time.Time) (*model.RewardInfo, error) {
					return mockRewardInfo, nil
				})
				// send reward
				patches.ApplyPrivateMethod(s.im, "sendReward", func(context ctx.CTX, receiver *models.User, reward *model.RewardInfo) error {
					return nil
				})
				// incr count
				patches.ApplyPrivateMethod(s.im, "incrClaimedCounts", func(context ctx.CTX, box *model.StreamRoomBox, targetTime time.Time) error {
					return nil
				})
				s.mockBoxHelper.On("IncrCurrentAndSet", anyCTX, mockBox_Upgrade).Return(nil).Once()
				// upgrade
				patches.ApplyPrivateMethod(s.im, "isClaimingLastDay", func(context ctx.CTX, user *models.User, eventEndTime int64, targetTime time.Time) bool {
					return false
				})
				patches.ApplyPrivateMethod(s.im, "upgrade", func(context ctx.CTX, box *model.StreamRoomBox, targetTime time.Time) error {
					return nil
				})
				return func() { patches.Reset() }
			},
			ExpRes: &model.ClaimResult{
				NextBoxLevel: model.BoxLevel_SuperRare,
				RewardType:   mockRewardInfo.RewardType,
				BBC:          mockRewardInfo.BBC,
				Exp:          mockRewardInfo.Exp,
				GiftID:       mockRewardInfo.Gift.GiftID,
			},
			ExpErr: nil,
		},
	}

	for _, t := range tests {
		s.Run(t.Desc, func() {
			if t.Setup != nil {
				defer t.Setup()()
			}
			res, err := s.im.ClaimStreamRoomBox(mockCTX, mockUser, mockStreamerID, mockRegionInfo, mockDeviceInfo)
			s.Equal(t.ExpErr, err, t.Desc)
			s.Equal(t.ExpRes, res, t.Desc)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestUpgrade() {
	now := time.Date(2024, time.April, 17, 17, 0, 0, 0, taipeiLoc)
	endOfToday := time.Date(2024, time.April, 17, 23, 59, 59, 0, taipeiLoc).Unix()

	mockUpgradeSettings := []*model.UpgradeSetting{
		{
			Level: model.BoxLevel_Normal,
		},
		{
			Level: model.BoxLevel_Rare,
		},
		{
			Level: model.BoxLevel_SuperRare,
		},
	}

	mockBox := &model.StreamRoomBox{
		ClaimRegion: "TW",
		ClaimInfo: &model.ClaimInfo{
			ClaimTime: now,
		},
	}

	nextBox := &model.BoxInfo{
		Level:     model.BoxLevel_SuperRare,
		ExpiredAt: endOfToday,
	}

	tests := []struct {
		Desc     string
		Setup    func() closeFunc
		InputBox *model.StreamRoomBox
		ExpBox   *model.StreamRoomBox
		ExpErr   error
	}{
		{
			Desc: "success",
			Setup: func() closeFunc {
				patches := gomonkey.ApplyPrivateMethod(s.im, "getUpgradeSettings",
					func(context ctx.CTX, box *model.StreamRoomBox, targetTime time.Time) ([]*model.UpgradeSetting, error) {
						return mockUpgradeSettings, nil
					})
				patches.ApplyPrivateMethod(s.im, "drawUpgradeResult",
					func(context ctx.CTX, box *model.StreamRoomBox) (int, error) {
						return 2, nil // draw super rare
					})

				s.mockBoxHelper.On("GetEndOfToday", anyCTX, mockBox.ClaimInfo.ClaimTime.Unix(), mockBox.ClaimRegion).Return(endOfToday).Once()
				s.mockBoxHelper.On("ReplaceNextAndSet", anyCTX, mockBox, nextBox).Return(nil).Once()

				return func() {
					patches.Reset()
				}
			},
			InputBox: mockBox,
			ExpBox: &model.StreamRoomBox{
				ClaimRegion: "TW",
				ClaimInfo: &model.ClaimInfo{
					ClaimTime: now,
				},
				Next: nil, // since boxHelper.ReplaceNextAndSet is mocked, the next box won't be set
			},
			ExpErr: nil,
		},
	}

	for _, t := range tests {
		s.Run(t.Desc, func() {
			if t.Setup != nil {
				defer t.Setup()()
			}
			err := s.im.upgrade(mockCTX, t.InputBox)
			s.Equal(t.ExpErr, err, t.Desc)
			s.Equal(t.ExpBox, t.InputBox, t.Desc)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestDraw() {
	const (
		iterations = 10000
		lowerBound = 3333 - 500
		upperBound = 3333 + 500
		// We don't test its statistical accuracy, so we gave a relatively loose value to avoid testing failure.
	)

	settings := []*model.RewardSetting{
		{Probability: 10},
		{Probability: 10},
		{Probability: 10},
	}

	mockItems := make([]model.ProbabilityItem, len(settings))
	for i, setting := range settings {
		mockItems[i] = setting
	}

	tests := []struct {
		desc    string
		numIter int
		items   []model.ProbabilityItem
		assert  func(countMap map[int]int, errs []error)
	}{
		{
			desc:    "normal case",
			numIter: iterations,
			items:   mockItems,
			assert: func(countMap map[int]int, errs []error) {
				for _, err := range errs {
					s.NoError(err)
				}
				s.True(lowerBound <= countMap[0] && countMap[0] <= upperBound)
				s.True(lowerBound <= countMap[1] && countMap[1] <= upperBound)
				s.True(lowerBound <= countMap[2] && countMap[2] <= upperBound)
			},
		},
		{
			desc:    "empty bucket",
			numIter: 1,
			items:   []model.ProbabilityItem{},
			assert: func(countMap map[int]int, errs []error) {
				s.Len(errs, 1)
				s.EqualError(errs[0], errInvalidProbSetting.Error())
			},
		},
	}

	for _, test := range tests {
		s.Run(test.desc, func() {

			countMap := map[int]int{}
			errs := make([]error, test.numIter)
			for i := 0; i < test.numIter; i++ {
				res, err := s.im.draw(test.items)
				errs[i] = err
				countMap[res]++
			}
			test.assert(countMap, errs)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestDrawUpgradeResult() {
	const iterations = 10

	settings := []*model.UpgradeSetting{
		{Probability: 50, Limit: -1, Current: 0},  // -1 = no limit
		{Probability: 50, Limit: 10, Current: 10}, // limit reached
	}

	s.Run("drawUpgradeResult normal case", func() {
		countMap := make(map[int]int)
		for i := 0; i < iterations; i++ {
			res, err := s.im.drawUpgradeResult(settings)
			s.NoError(err)
			countMap[res]++
		}

		s.Equal(iterations, countMap[0], "The count should equal the number of iterations")
		s.Equal(0, countMap[1], "The limit is reached, so it should not be drawn")
	})
}

// test for drawFinalResult
func (s *streamRoomBoxTestSuite) TestDrawNumericRewardSetting() {
	const iterations = 10

	tests := []struct {
		Desc  string
		Input model.NumericRewardSetting
	}{
		{
			Desc: "result should be between min and max",
			Input: model.NumericRewardSetting{
				Min: 10,
				Max: 20,
			},
		},
		{
			Desc: "result should be equal to 10",
			Input: model.NumericRewardSetting{
				Min: 10,
				Max: 10,
			},
		},
	}

	for _, test := range tests {
		s.Run(test.Desc, func() {
			for i := 0; i < iterations; i++ {
				res := test.Input.Draw()
				s.True(test.Input.Min <= res && res <= test.Input.Max,
					"Result %d not within expected range [%d, %d]", res, test.Input.Min, test.Input.Max)
			}
		})
	}
}

// test for drawFinalResult
func (s *streamRoomBoxTestSuite) TestDrawGiftRewardSetting() {
	const iterations = 10

	tests := []struct {
		Desc       string
		Input      model.GiftRewardSetting
		ExpResPool map[string]bool
	}{
		{
			Desc: "3 giftIDs",
			Input: model.GiftRewardSetting{
				GiftIDs:   []string{"giftID1", "giftID2", "giftID3"},
				ExpireSec: 86400,
			},
			ExpResPool: map[string]bool{
				"giftID1": true,
				"giftID2": true,
				"giftID3": true,
			},
		},
		{
			Desc: "1 giftID",
			Input: model.GiftRewardSetting{
				GiftIDs:   []string{"giftID1"},
				ExpireSec: 86400,
			},
			ExpResPool: map[string]bool{
				"giftID1": true,
			},
		},
	}

	for _, test := range tests {
		s.Run(test.Desc, func() {
			for i := 0; i < iterations; i++ {
				res := test.Input.Draw()
				_, exists := test.ExpResPool[res]
				s.True(exists, "Result not found in expected pool")
			}
		})
	}
}

func (s *streamRoomBoxTestSuite) TestDrawFinalResult() {
	tests := []struct {
		desc          string
		rewardSetting *model.RewardSetting
		assert        func(reward *model.RewardInfo, err error)
	}{
		{
			desc: "draw BBC",
			rewardSetting: &model.RewardSetting{
				RewardType: model.RewardType_BBC,
				BBCSetting: model.NumericRewardSetting{
					Min: 10,
					Max: 100,
				},
			},
			assert: func(reward *model.RewardInfo, err error) {
				s.NoError(err)
				s.Equal(model.RewardType_BBC, reward.RewardType)
				s.True(reward.BBC >= 10 && reward.BBC <= 100)
			},
		},
		{
			desc: "draw exp",
			rewardSetting: &model.RewardSetting{
				RewardType: model.RewardType_Exp,
				ExpSetting: model.NumericRewardSetting{
					Min: 10,
					Max: 100,
				},
			},
			assert: func(reward *model.RewardInfo, err error) {
				s.NoError(err)
				s.Equal(model.RewardType_Exp, reward.RewardType)
				s.True(reward.Exp >= 10 && reward.Exp <= 100)
			},
		},
		{
			desc: "draw gift",
			rewardSetting: &model.RewardSetting{
				RewardType: model.RewardType_Gift,
				GiftSetting: model.GiftRewardSetting{
					GiftIDs:   []string{"giftID1", "giftID2"},
					ExpireSec: 86400,
				},
			},
			assert: func(reward *model.RewardInfo, err error) {
				s.NoError(err)
				s.Equal(model.RewardType_Gift, reward.RewardType)
				s.Contains([]string{"giftID1", "giftID2"}, reward.Gift.GiftID)
			},
		},
		{
			desc: "invalid type",
			rewardSetting: &model.RewardSetting{
				RewardType: 999,
			},
			assert: func(reward *model.RewardInfo, err error) {
				s.EqualError(err, ErrInvalidRewardType.Error())
			},
		},
	}

	for _, t := range tests {
		s.Run(t.desc, func() {
			reward, err := s.im.drawFinalResult(t.rewardSetting)
			t.assert(reward, err)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestIsStreamRoomLimitReached() {
	mockUserID := "mockUserID"
	mockStreamerID := "mockStreamerID"

	type testCase struct {
		desc         string
		userID       string
		streamerID   string
		curRoomCount []byte
		setup        func(t testCase)
		expRes       bool
		expErr       error
	}
	tests := []testCase{
		{
			desc:         "success: user's stream room limit not reached",
			userID:       mockUserID,
			streamerID:   mockStreamerID,
			curRoomCount: []byte("1"),
			setup: func(t testCase) {
				s.mockPersist.On("Get", anyCTX, keyTotalByRoom(t.userID, t.streamerID)).Return(t.curRoomCount, nil).Once()
			},
			expRes: false,
			expErr: nil,
		},
		{
			desc:         "success: user's stream room limit reached",
			userID:       mockUserID,
			streamerID:   mockStreamerID,
			curRoomCount: []byte("2"),
			setup: func(t testCase) {
				s.mockPersist.On("Get", anyCTX, keyTotalByRoom(t.userID, t.streamerID)).Return(t.curRoomCount, nil).Once()
			},
			expRes: true,
			expErr: nil,
		},
		{
			desc:         "error: get user's stream room limit redis failed",
			userID:       mockUserID,
			streamerID:   mockStreamerID,
			curRoomCount: []byte("2"),
			setup: func(t testCase) {
				s.mockPersist.On("Get", anyCTX, keyTotalByRoom(t.userID, t.streamerID)).Return(nil, errors.New("error")).Once()
			},
			expRes: true,
			expErr: errors.New("error"),
		},
	}

	for _, t := range tests {
		s.Run(t.desc, func() {
			if t.setup != nil {
				t.setup(t)
			}
			res, err := s.im.isStreamRoomLimitReached(mockCTX, t.userID, t.streamerID)
			s.Equal(t.expRes, res, t.desc)
			s.Equal(t.expErr, err, t.desc)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestIsDailyUserLimitReached() {
	mockUserID := "mockUserID"
	mockDeviceID := "mockDeviceID"

	type testCase struct {
		desc           string
		userID         string
		deviceID       string
		curUserCount   []byte
		curDeviceCount []byte
		setup          func(t testCase)
		expRes         bool
		expErr         error
	}
	tests := []testCase{
		{
			desc:           "success: user limit not reached",
			userID:         mockUserID,
			deviceID:       mockDeviceID,
			curUserCount:   []byte("40"),
			curDeviceCount: []byte("0"),
			setup: func(t testCase) {
				s.mockPersist.On("Get", anyCTX, keyTotalByUserID(t.userID)).Return(t.curUserCount, nil).Once()
				s.mockPersist.On("Get", anyCTX, keyTotalByDeviceID(t.deviceID)).Return(t.curDeviceCount, nil).Once()
			},
			expRes: false,
			expErr: nil,
		},
		{
			desc:           "success: user limit reached",
			userID:         mockUserID,
			deviceID:       mockDeviceID,
			curUserCount:   []byte("2"),
			curDeviceCount: []byte("50"),
			setup: func(t testCase) {
				s.mockPersist.On("Get", anyCTX, keyTotalByUserID(t.userID)).Return(t.curUserCount, nil).Once()
				s.mockPersist.On("Get", anyCTX, keyTotalByDeviceID(t.deviceID)).Return(t.curDeviceCount, nil).Once()
			},
			expRes: true,
			expErr: nil,
		},
		{
			desc:           "error: get user's limit redis failed",
			userID:         mockUserID,
			deviceID:       mockDeviceID,
			curUserCount:   []byte("2"),
			curDeviceCount: []byte("50"),
			setup: func(t testCase) {
				s.mockPersist.On("Get", anyCTX, keyTotalByUserID(t.userID)).Return(nil, errors.New("error")).Once()
			},
			expRes: true,
			expErr: errors.New("error"),
		},
	}

	for _, t := range tests {
		s.Run(t.desc, func() {
			if t.setup != nil {
				t.setup(t)
			}
			res, err := s.im.isDailyUserLimitReached(mockCTX, t.deviceID, t.userID)
			s.Equal(t.expRes, res, t.desc)
			s.Equal(t.expErr, err, t.desc)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestSendRewards() {
	mockDealing := []*moneyModel.Dealing{
		{
			Category:     moneyModel.Category_STREAM_ROOM_BOX_POINT,
			FromUserID:   env.OfficialPseudoUserID,
			ToUserID:     mockUser.UserID,
			FromCurrency: moneyModel.Currency_POINT,
			ToCurrency:   moneyModel.Currency_POINT,
			Amount:       100,
		},
	}

	tests := []struct {
		desc   string
		args   model.RewardInfo
		setup  func()
		assert func(err error)
	}{
		{
			desc: "send BBC point",
			args: model.RewardInfo{
				RewardType: model.RewardType_BBC,
				BBC:        100,
			},
			setup: func() {
				s.mockBank.On("Trade", anyCTX, mockDealing, mock.AnythingOfType("money.Option")).Return("", []string{}, int64(0), nil).Once()
			},
			assert: func(err error) {
				s.NoError(err)
			},
		},
		{
			desc: "BBC point should be positive",
			args: model.RewardInfo{
				RewardType: model.RewardType_BBC,
				BBC:        -1,
			},
			assert: func(err error) {
				s.Equal(ErrInvalidRewardBBC, err)
			},
		},
		{
			desc: "send exp",
			args: model.RewardInfo{
				RewardType: model.RewardType_Exp,
				Exp:        100,
			},
			setup: func() {
				s.mockLevel.On("IncrPlainExp", anyCTX, mockUser.UserID, int64(100)).Return(nil).Once()
			},
			assert: func(err error) {
				s.NoError(err)
			},
		},
		{
			desc: "send exp failed",
			args: model.RewardInfo{
				RewardType: model.RewardType_Exp,
				Exp:        100,
			},
			setup: func() {
				s.mockLevel.On("IncrPlainExp", anyCTX, mockUser.UserID, int64(100)).Return(errors.New("IncrPlainExp failed")).Once()
			},
			assert: func(err error) {
				s.EqualError(err, "IncrPlainExp failed")
			},
		},
		{
			desc: "send baggage",
			args: model.RewardInfo{
				RewardType: model.RewardType_Gift,
				Gift: model.GiftReward{
					GiftID:    "giftID1",
					ExpireSec: 86400,
				},
			},
			setup: func() {
				s.mockFuncs.On("TimeNow").Return(mockNow).Once()
				s.mockBaggage.On("AddItems",
					anyCTX, mockUser.UserID, "giftID1", baggageModel.Type_ITEM_GIFT, int32(1), mockNow.Unix(), mockNow.Unix()+86400, false,
				).Return(nil, nil).Once()
			},
			assert: func(err error) {
				s.NoError(err)
			},
		},
		{
			desc: "send baggage failed",
			args: model.RewardInfo{
				RewardType: model.RewardType_Gift,
				Gift: model.GiftReward{
					GiftID:    "giftID1",
					ExpireSec: 86400,
				},
			},
			setup: func() {
				s.mockFuncs.On("TimeNow").Return(mockNow).Once()
				s.mockBaggage.On("AddItems",
					anyCTX, mockUser.UserID, "giftID1", baggageModel.Type_ITEM_GIFT, int32(1), mockNow.Unix(), mockNow.Unix()+86400, false,
				).Return(nil, errors.New("AddItems failed")).Once()
			},
			assert: func(err error) {
				s.EqualError(err, "AddItems failed")
			},
		},
		{
			desc: "invalid reward type",
			args: model.RewardInfo{
				RewardType: 99999,
			},
			assert: func(err error) {
				s.Equal(ErrInvalidRewardType, err)
			},
		},
	}

	for _, test := range tests {
		s.Run(test.desc, func() {
			if test.setup != nil {
				test.setup()
			}

			err := s.im.sendReward(mockCTX, mockUser, &test.args)
			test.assert(err)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestCheckUserEligibility() {
	mockViewerIP := "111.222.333.444"
	mockStreamerID := "streamer-1"
	mockStreamRegion := "JP"
	mockValidNewbieUser := &models.User{
		UserID:                   "aaa-bbb-ccc",
		RegisterRegion:           "JP",
		RegisterTime:             1712534400, // 2024年4月8日星期一 08:00:00 GMT+08:00
		InternationalPhoneNumber: "777",
	}
	mockInvalidNewbieUser := &models.User{
		UserID:                   "aaa-bbb-ccc",
		RegisterRegion:           "JP",
		RegisterTime:             1707350400, // 2024年2月8日星期一 08:00:00 GMT+08:00
		InternationalPhoneNumber: "777",
	}
	mockValidRegionInfo := &regionModel.RegionInfo{
		RegionByIP: mockStreamRegion,
	}
	mockInvalidRegionInfo := &regionModel.RegionInfo{
		RegionByIP: "unknown",
	}
	mockDeviceInfo := &confModel.DeviceInfo{
		DeviceID: "deviceID",
		PublicIP: mockViewerIP,
	}
	mockEventEndTime := int64(1714435200) // 2024年4月30日星期二 08:00:00 GMT+08:00
	mockNewbieEvent := &streamRoomBoxConfig.StreamRoomBoxEvent{
		TargetUser: model.TargetUserType_Newbie,
		EndTime:    mockEventEndTime,
	}
	mockAllEvent := &streamRoomBoxConfig.StreamRoomBoxEvent{
		TargetUser: model.TargetUserType_All,
		EndTime:    mockEventEndTime,
	}

	type args struct {
		viewer     *models.User
		streamerID string
		regionInfo *regionModel.RegionInfo
		deviceInfo *confModel.DeviceInfo
		targetTime time.Time
	}

	tests := []struct {
		desc   string
		args   args
		setup  func(args args)
		assert func(res bool)
	}{
		{
			desc: "successful case for newbie user",
			args: args{
				viewer:     mockValidNewbieUser,
				streamerID: mockStreamerID,
				regionInfo: mockValidRegionInfo,
				deviceInfo: mockDeviceInfo,
				targetTime: mockNow,
			},
			setup: func(args args) {
				s.mockFuncs.On("IsBlocked", args.viewer.UserID, args.deviceInfo.DeviceID, args.deviceInfo.PublicIP).Return(false).Once()
				s.mockUser.On("GetRegionByUserID", anyCTX, args.streamerID).Return(mockStreamRegion).Once()
				// mock im.isValidNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.viewer.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(30).Once()
				s.mockFuncs.On("GetEventAt", mockStreamRegion, model.TargetUserType_Newbie, mockNow).Return(mockNewbieEvent, nil).Once()
				s.mockFuncs.On("GetEventAt", mockStreamRegion, model.TargetUserType_All, mockNow).Return(mockAllEvent, nil).Once()
				// mock getEventEndTimeAt
				s.mockFuncs.On("GetEventEndTimeAt", mockStreamRegion, model.TargetUserType_Newbie, mockNow).Return(mockEventEndTime, nil).Once()

			},
			assert: func(res bool) {
				s.True(res)
			},
		},
		{
			desc: "successful case for normal user",
			args: args{
				viewer:     mockInvalidNewbieUser,
				streamerID: mockStreamerID,
				regionInfo: mockValidRegionInfo,
				deviceInfo: mockDeviceInfo,
				targetTime: mockNow,
			},
			setup: func(args args) {
				s.mockFuncs.On("IsBlocked", args.viewer.UserID, args.deviceInfo.DeviceID, args.deviceInfo.PublicIP).Return(false).Once()
				s.mockUser.On("GetRegionByUserID", anyCTX, args.streamerID).Return(mockStreamRegion).Once()
				// mock im.isValidNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.viewer.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(30).Once()
				s.mockFuncs.On("GetEventAt", mockStreamRegion, model.TargetUserType_All, mockNow).Return(mockAllEvent, nil).Once()
				// mock getEventEndTimeAt
				s.mockFuncs.On("GetEventEndTimeAt", mockStreamRegion, model.TargetUserType_All, mockNow).Return(mockEventEndTime, nil).Once()
			},
			assert: func(res bool) {
				s.True(res)
			},
		},
		{
			desc: "user is blocked",
			args: args{
				viewer:     mockValidNewbieUser,
				streamerID: mockStreamerID,
				regionInfo: mockValidRegionInfo,
				deviceInfo: mockDeviceInfo,
				targetTime: mockNow,
			},
			setup: func(args args) {
				s.mockFuncs.On("IsBlocked", args.viewer.UserID, args.deviceInfo.DeviceID, args.deviceInfo.PublicIP).Return(true).Once()
			},
			assert: func(res bool) {
				s.False(res)
			},
		},
		{
			desc: "no ongoing 17box event",
			args: args{
				viewer:     mockValidNewbieUser,
				streamerID: mockStreamerID,
				regionInfo: mockValidRegionInfo,
				deviceInfo: mockDeviceInfo,
				targetTime: mockNow,
			},
			setup: func(args args) {
				s.mockFuncs.On("IsBlocked", args.viewer.UserID, args.deviceInfo.DeviceID, args.deviceInfo.PublicIP).Return(false).Once()
				s.mockUser.On("GetRegionByUserID", anyCTX, args.streamerID).Return(mockStreamRegion).Once()
				// mock im.isValidNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.viewer.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(30).Once()
				s.mockFuncs.On("GetEventAt", mockStreamRegion, model.TargetUserType_Newbie, mockNow).Return(mockNewbieEvent, nil).Once()
				s.mockFuncs.On("GetEventAt", mockStreamRegion, model.TargetUserType_All, mockNow).Return(mockAllEvent, nil).Once()
				// mock getEventEndTimeAt
				s.mockFuncs.On("GetEventEndTimeAt", mockStreamRegion, model.TargetUserType_Newbie, mockNow).Return(int64(0), model.ErrNoAvailableStreamRoomBoxEvent).Once()
			},
			assert: func(res bool) {
				s.False(res)
			},
		},
		{
			desc: "user's ip region is not same with streamer's app region",
			args: args{
				viewer:     mockValidNewbieUser,
				streamerID: mockStreamerID,
				regionInfo: mockInvalidRegionInfo,
				deviceInfo: mockDeviceInfo,
				targetTime: mockNow,
			},
			setup: func(args args) {
				s.mockFuncs.On("IsBlocked", args.viewer.UserID, args.deviceInfo.DeviceID, args.deviceInfo.PublicIP).Return(false).Once()
				s.mockUser.On("GetRegionByUserID", anyCTX, args.streamerID).Return(mockStreamRegion).Once()
				// mock im.isValidNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.viewer.RegisterRegion).Return(int64(28800), nil)
				s.mockFuncs.On("GetNewbieDay").Return(30).Once()
				s.mockFuncs.On("GetEventAt", mockStreamRegion, model.TargetUserType_Newbie, mockNow).Return(mockNewbieEvent, nil).Once()
				s.mockFuncs.On("GetEventAt", mockStreamRegion, model.TargetUserType_All, mockNow).Return(mockAllEvent, nil).Once()
				// mock getEventEndTimeAt
				s.mockFuncs.On("GetEventEndTimeAt", mockStreamRegion, model.TargetUserType_Newbie, mockNow).Return(mockEventEndTime, nil).Once()
			},
			assert: func(res bool) {
				s.False(res)
			},
		},
	}

	for _, test := range tests {
		s.Run(test.desc, func() {
			if test.setup != nil {
				test.setup(test.args)
			}

			isValid := s.im.isUserEligible(mockCTX, test.args.viewer, test.args.streamerID, test.args.regionInfo, test.args.deviceInfo, test.args.targetTime)
			test.assert(isValid)
		})
	}
}

func (s *streamRoomBoxTestSuite) TestIsClaimingLastDay() {
	type args struct {
		user       *models.User
		region     string
		targetTime time.Time
	}

	tests := []struct {
		desc   string
		args   args
		setup  func(args args)
		expRes bool
	}{
		{
			desc: "no events",
			args: args{
				user: &models.User{
					UserID: "userID",
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(30).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return((*streamRoomBoxConfig.StreamRoomBoxEvent)(nil), model.ErrNoAvailableStreamRoomBoxEvent).Once()
			},
			expRes: false,
		},
		{
			desc: "only type newbie, last day of newbie user",
			args: args{
				user: &models.User{
					UserID:       "userID",
					RegisterTime: time.Date(2024, 7, 22, 0, 0, 0, 0, taipeiLoc).Unix(),
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(2).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_Newbie, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_Newbie,
					EndTime:    time.Date(2024, 9, 30, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return((*streamRoomBoxConfig.StreamRoomBoxEvent)(nil), model.ErrNoAvailableStreamRoomBoxEvent).Once()
				// mock im.isEndDayOfNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(2).Once()
			},
			expRes: true,
		},
		{
			desc: "only type newbie, last day of newbie event",
			args: args{
				user: &models.User{
					UserID:       "userID",
					RegisterTime: time.Date(2024, 7, 22, 0, 0, 0, 0, taipeiLoc).Unix(),
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_Newbie, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_Newbie,
					EndTime:    time.Date(2024, 7, 24, 23, 59, 59, 0, taipeiLoc).Unix(),
				}, nil).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return((*streamRoomBoxConfig.StreamRoomBoxEvent)(nil), model.ErrNoAvailableStreamRoomBoxEvent).Once()
				// mock im.isEndDayOfNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
			},
			expRes: true,
		},
		{
			desc: "only type newbie, not last day",
			args: args{
				user: &models.User{
					UserID:       "userID",
					RegisterTime: time.Date(2024, 7, 22, 0, 0, 0, 0, taipeiLoc).Unix(),
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_Newbie, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_Newbie,
					EndTime:    time.Date(2024, 7, 25, 18, 1, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return((*streamRoomBoxConfig.StreamRoomBoxEvent)(nil), model.ErrNoAvailableStreamRoomBoxEvent).Once()
				// mock im.isEndDayOfNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
			},
			expRes: false,
		},
		{
			desc: "only type all, last day of all event",
			args: args{
				user: &models.User{
					UserID:       "userID",
					RegisterTime: time.Date(2023, 7, 22, 0, 0, 0, 0, taipeiLoc).Unix(),
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(30).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_All,
					EndTime:    time.Date(2024, 7, 25, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
			},
			expRes: true,
		},
		{
			desc: "only type all, not last day of event",
			args: args{
				user: &models.User{
					UserID:       "userID",
					RegisterTime: time.Date(2023, 7, 22, 0, 0, 0, 0, taipeiLoc).Unix(),
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_All,
					EndTime:    time.Date(2024, 7, 26, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
			},
			expRes: false,
		},
		{
			desc: "type newbie and all, last day of newbie user but not last day of newbie or all events",
			args: args{
				user: &models.User{
					UserID:       "userID",
					RegisterTime: time.Date(2024, 7, 22, 0, 0, 0, 0, taipeiLoc).Unix(),
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(2).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_Newbie, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_Newbie,
					EndTime:    time.Date(2024, 7, 26, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_All,
					EndTime:    time.Date(2024, 7, 26, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				// mock im.isEndDayOfNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(2).Once()
			},
			expRes: false,
		},
		{
			desc: "type newbie and all, last day of newbie event but not last day of all events",
			args: args{
				user: &models.User{
					UserID:       "userID",
					RegisterTime: time.Date(2024, 7, 22, 0, 0, 0, 0, taipeiLoc).Unix(),
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_Newbie, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_Newbie,
					EndTime:    time.Date(2024, 7, 25, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_All,
					EndTime:    time.Date(2024, 7, 26, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				// mock im.isEndDayOfNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
			},
			expRes: false,
		},
		{
			desc: "type newbie and all, last day of all event but not last day of newbie",
			args: args{
				user: &models.User{
					UserID:       "userID",
					RegisterTime: time.Date(2024, 7, 22, 0, 0, 0, 0, taipeiLoc).Unix(),
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_Newbie, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_Newbie,
					EndTime:    time.Date(2024, 7, 26, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_All,
					EndTime:    time.Date(2024, 7, 25, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				// mock im.isEndDayOfNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
			},
			expRes: false,
		},
		{
			desc: "type newbie and all, last day of newbie user and last day of all event",
			args: args{
				user: &models.User{
					UserID:       "userID",
					RegisterTime: time.Date(2024, 7, 22, 0, 0, 0, 0, taipeiLoc).Unix(),
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(2).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_Newbie, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_Newbie,
					EndTime:    time.Date(2024, 7, 26, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_All,
					EndTime:    time.Date(2024, 7, 25, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				// mock im.isEndDayOfNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(2).Once()
			},
			expRes: true,
		},
		{
			desc: "type newbie and all, last day of newbie event and last day of all event",
			args: args{
				user: &models.User{
					UserID:       "userID",
					RegisterTime: time.Date(2024, 7, 22, 0, 0, 0, 0, taipeiLoc).Unix(),
				},
				region:     "TW",
				targetTime: time.Date(2024, 7, 24, 18, 0, 0, 0, taipeiLoc),
			},
			setup: func(args args) {
				// mock im.getEligibleEvents
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_Newbie, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_Newbie,
					EndTime:    time.Date(2024, 7, 25, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				s.mockFuncs.On("GetEventAt", args.region, model.TargetUserType_All, args.targetTime).Return(&streamRoomBoxConfig.StreamRoomBoxEvent{
					TargetUser: model.TargetUserType_All,
					EndTime:    time.Date(2024, 7, 25, 0, 0, 0, 0, taipeiLoc).Unix(),
				}, nil).Once()
				// mock im.isEndDayOfNewbieUser
				s.mockRegion.On("GetTimeZoneOffset", anyCTX, args.user.RegisterRegion).Return(int64(28800), nil).Once()
				s.mockFuncs.On("GetNewbieDay").Return(10).Once()
			},
			expRes: true,
		},
	}

	for _, test := range tests {
		s.Run(test.desc, func() {
			if test.setup != nil {
				test.setup(test.args)
			}

			isLastDay := s.im.isClaimingLastDay(mockCTX, test.args.user, test.args.region, test.args.targetTime)
			s.Equal(test.expRes, isLastDay)
		})
	}
}
