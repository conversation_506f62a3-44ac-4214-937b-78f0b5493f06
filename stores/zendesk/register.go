package zendesk

import (
	"github.com/17media/dig"

	subTopicModel "github.com/17media/gomodel/models/subTopic"

	"github.com/17media/api/service/queryv2"
	"github.com/17media/api/service/queue"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/setup/dimanager"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of zendesk object to the manager
func Register(m *dimanager.Manager) {
	m.RegisterString("zendesk_endpoint", "", "endpoint for the service of sign in with Zendesk")
	m.RegisterString("zendesk_jwt_key_id", "", "jwt key ID for the service of sign in with Zendesk")
	m.RegisterString("zendesk_jwt_sign_key", "", "jwt sign key for the service of sign in with Zendesk")
	m.RegisterString("zendesk_api_token", "", "API Token for the service of sign in with Zendesk")
	m.RegisterString("zendesk_ticket_webhook_secret_key", "", "ticket webhook secret key for the service of sign in with Zendesk")

	type params struct {
		dig.In
		ZendeskEndpoint               *string `name:"zendesk_endpoint"`
		ZendeskJWTKeyID               *string `name:"zendesk_jwt_key_id"`
		ZendeskJWTSignKey             *string `name:"zendesk_jwt_sign_key"`
		ZendeskAPIToken               *string `name:"zendesk_api_token"`
		ZendeskTicketWebhookSecretKey *string `name:"zendesk_ticket_webhook_secret_key"`

		Query           queryv2.Mongo `name:"queryv2"`
		RedisPersistent redis.Service `name:"redisPersistent"`
	}

	fn := func(p params) Store {
		return New(
			*p.ZendeskEndpoint,
			*p.ZendeskJWTKeyID,
			*p.ZendeskJWTSignKey,
			*p.ZendeskAPIToken,
			*p.ZendeskTicketWebhookSecretKey,
			p.Query,
			p.RedisPersistent,
			queue.NewPubsubPublisher(subTopicModel.SubTopics[subTopicModel.KeyZendeskTicketWebhook].Topic),
		)
	}
	m.ProvideConstructor(fn, `zendesk`)
}

// GetZendesk returns the zendesk object
func GetZendesk(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"zendesk"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
