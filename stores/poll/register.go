package poll

import (
	"github.com/17media/dig"

	"github.com/17media/api/service/kv"
	"github.com/17media/api/service/messengerv2"
	"github.com/17media/api/service/queryv2"
	"github.com/17media/api/service/redis"
	_ "github.com/17media/api/service/redis/redispersistent"
	"github.com/17media/api/setup/dimanager"
	pkHelper "github.com/17media/api/stores/pk/helper"
	"github.com/17media/api/stores/vote"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of poll object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		Kv              kv.KV               `name:"kv"`
		Query           queryv2.Mongo       `name:"queryv2"`
		RedisPersistent redis.Service       `name:"redisPersistent"`
		Messenger       messengerv2.Service `name:"messengerv2"`
		Vote            vote.Store          `name:"vote"`
		PKHelper        pkHelper.Helper     `name:"pkH<PERSON>per"`
	}

	fn := func(p params) Store {
		return New(
			p.Kv,
			p.Que<PERSON>,
			p.RedisP<PERSON>istent,
			p.<PERSON>,
			p.Vote,
			p.PKHelper,
		)
	}
	m.ProvideConstructor(fn, `poll`)
}

// GetPoll returns the poll object
func GetPoll(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"poll"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
