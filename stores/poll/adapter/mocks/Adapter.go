// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	mock "github.com/stretchr/testify/mock"

	models "github.com/17media/api/models/queue"

	poll "github.com/17media/poll/models/poll"
)

// Adapter is an autogenerated mock type for the Adapter type
type Adapter struct {
	mock.Mock
}

// GetActivePoll provides a mock function with given fields: context, srcID
func (_m *Adapter) GetActivePoll(context ctx.CTX, srcID string) (*poll.PollExt, error) {
	ret := _m.Called(context, srcID)

	var r0 *poll.PollExt
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) *poll.PollExt); ok {
		r0 = rf(context, srcID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*poll.PollExt)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, srcID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestPoll provides a mock function with given fields: context, srcID
func (_m *Adapter) GetLatestPoll(context ctx.CTX, srcID string) (*poll.PollExt, error) {
	ret := _m.Called(context, srcID)

	var r0 *poll.PollExt
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) *poll.PollExt); ok {
		r0 = rf(context, srcID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*poll.PollExt)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, srcID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IncrOptionCount provides a mock function with given fields: context, pollID, optionID
func (_m *Adapter) IncrOptionCount(context ctx.CTX, pollID string, optionID string) (*poll.PollExt, error) {
	ret := _m.Called(context, pollID, optionID)

	var r0 *poll.PollExt
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) *poll.PollExt); ok {
		r0 = rf(context, pollID, optionID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*poll.PollExt)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string) error); ok {
		r1 = rf(context, pollID, optionID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateInfo provides a mock function with given fields: context, data, option
func (_m *Adapter) UpdateInfo(context ctx.CTX, data []byte, option models.CallbackOption) error {
	ret := _m.Called(context, data, option)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []byte, models.CallbackOption) error); ok {
		r0 = rf(context, data, option)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}
