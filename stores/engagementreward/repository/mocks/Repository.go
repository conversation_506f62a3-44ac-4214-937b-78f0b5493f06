// Code generated by mockery v1.1.3-0.20250311022958-4653efadba67. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	engagementreward "github.com/17media/api/models/engagementreward"
	mock "github.com/stretchr/testify/mock"
)

// Repository is an autogenerated mock type for the Repository type
type Repository struct {
	mock.Mock
}

type Repository_Expecter struct {
	mock *mock.Mock
}

func (_m *Repository) EXPECT() *Repository_Expecter {
	return &Repository_Expecter{mock: &_m.Mock}
}

// DeleteEngagementPhase provides a mock function with given fields: context, engageID
func (_m *Repository) DeleteEngagementPhase(context ctx.CTX, engageID string) error {
	ret := _m.Called(context, engageID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEngagementPhase")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) error); ok {
		r0 = rf(context, engageID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_DeleteEngagementPhase_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteEngagementPhase'
type Repository_DeleteEngagementPhase_Call struct {
	*mock.Call
}

// DeleteEngagementPhase is a helper method to define mock.On call
//   - context ctx.CTX
//   - engageID string
func (_e *Repository_Expecter) DeleteEngagementPhase(context interface{}, engageID interface{}) *Repository_DeleteEngagementPhase_Call {
	return &Repository_DeleteEngagementPhase_Call{Call: _e.mock.On("DeleteEngagementPhase", context, engageID)}
}

func (_c *Repository_DeleteEngagementPhase_Call) Run(run func(context ctx.CTX, engageID string)) *Repository_DeleteEngagementPhase_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ctx.CTX), args[1].(string))
	})
	return _c
}

func (_c *Repository_DeleteEngagementPhase_Call) Return(_a0 error) *Repository_DeleteEngagementPhase_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_DeleteEngagementPhase_Call) RunAndReturn(run func(ctx.CTX, string) error) *Repository_DeleteEngagementPhase_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteEngagementPhases provides a mock function with given fields: context, engageIDs
func (_m *Repository) DeleteEngagementPhases(context ctx.CTX, engageIDs []string) error {
	ret := _m.Called(context, engageIDs)

	if len(ret) == 0 {
		panic("no return value specified for DeleteEngagementPhases")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []string) error); ok {
		r0 = rf(context, engageIDs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_DeleteEngagementPhases_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteEngagementPhases'
type Repository_DeleteEngagementPhases_Call struct {
	*mock.Call
}

// DeleteEngagementPhases is a helper method to define mock.On call
//   - context ctx.CTX
//   - engageIDs []string
func (_e *Repository_Expecter) DeleteEngagementPhases(context interface{}, engageIDs interface{}) *Repository_DeleteEngagementPhases_Call {
	return &Repository_DeleteEngagementPhases_Call{Call: _e.mock.On("DeleteEngagementPhases", context, engageIDs)}
}

func (_c *Repository_DeleteEngagementPhases_Call) Run(run func(context ctx.CTX, engageIDs []string)) *Repository_DeleteEngagementPhases_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ctx.CTX), args[1].([]string))
	})
	return _c
}

func (_c *Repository_DeleteEngagementPhases_Call) Return(_a0 error) *Repository_DeleteEngagementPhases_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_DeleteEngagementPhases_Call) RunAndReturn(run func(ctx.CTX, []string) error) *Repository_DeleteEngagementPhases_Call {
	_c.Call.Return(run)
	return _c
}

// GetEngagementActionLogs provides a mock function with given fields: context, engageID, userID, streamID
func (_m *Repository) GetEngagementActionLogs(context ctx.CTX, engageID string, userID string, streamID string) ([]engagementreward.EngagementActionLog, error) {
	ret := _m.Called(context, engageID, userID, streamID)

	if len(ret) == 0 {
		panic("no return value specified for GetEngagementActionLogs")
	}

	var r0 []engagementreward.EngagementActionLog
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string) ([]engagementreward.EngagementActionLog, error)); ok {
		return rf(context, engageID, userID, streamID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string) []engagementreward.EngagementActionLog); ok {
		r0 = rf(context, engageID, userID, streamID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]engagementreward.EngagementActionLog)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, string) error); ok {
		r1 = rf(context, engageID, userID, streamID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_GetEngagementActionLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEngagementActionLogs'
type Repository_GetEngagementActionLogs_Call struct {
	*mock.Call
}

// GetEngagementActionLogs is a helper method to define mock.On call
//   - context ctx.CTX
//   - engageID string
//   - userID string
//   - streamID string
func (_e *Repository_Expecter) GetEngagementActionLogs(context interface{}, engageID interface{}, userID interface{}, streamID interface{}) *Repository_GetEngagementActionLogs_Call {
	return &Repository_GetEngagementActionLogs_Call{Call: _e.mock.On("GetEngagementActionLogs", context, engageID, userID, streamID)}
}

func (_c *Repository_GetEngagementActionLogs_Call) Run(run func(context ctx.CTX, engageID string, userID string, streamID string)) *Repository_GetEngagementActionLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ctx.CTX), args[1].(string), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *Repository_GetEngagementActionLogs_Call) Return(_a0 []engagementreward.EngagementActionLog, _a1 error) *Repository_GetEngagementActionLogs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_GetEngagementActionLogs_Call) RunAndReturn(run func(ctx.CTX, string, string, string) ([]engagementreward.EngagementActionLog, error)) *Repository_GetEngagementActionLogs_Call {
	_c.Call.Return(run)
	return _c
}

// GetEngagementPhase provides a mock function with given fields: context, engageID
func (_m *Repository) GetEngagementPhase(context ctx.CTX, engageID string) (engagementreward.EngagementPhase, error) {
	ret := _m.Called(context, engageID)

	if len(ret) == 0 {
		panic("no return value specified for GetEngagementPhase")
	}

	var r0 engagementreward.EngagementPhase
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (engagementreward.EngagementPhase, error)); ok {
		return rf(context, engageID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) engagementreward.EngagementPhase); ok {
		r0 = rf(context, engageID)
	} else {
		r0 = ret.Get(0).(engagementreward.EngagementPhase)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, engageID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_GetEngagementPhase_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEngagementPhase'
type Repository_GetEngagementPhase_Call struct {
	*mock.Call
}

// GetEngagementPhase is a helper method to define mock.On call
//   - context ctx.CTX
//   - engageID string
func (_e *Repository_Expecter) GetEngagementPhase(context interface{}, engageID interface{}) *Repository_GetEngagementPhase_Call {
	return &Repository_GetEngagementPhase_Call{Call: _e.mock.On("GetEngagementPhase", context, engageID)}
}

func (_c *Repository_GetEngagementPhase_Call) Run(run func(context ctx.CTX, engageID string)) *Repository_GetEngagementPhase_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ctx.CTX), args[1].(string))
	})
	return _c
}

func (_c *Repository_GetEngagementPhase_Call) Return(_a0 engagementreward.EngagementPhase, _a1 error) *Repository_GetEngagementPhase_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_GetEngagementPhase_Call) RunAndReturn(run func(ctx.CTX, string) (engagementreward.EngagementPhase, error)) *Repository_GetEngagementPhase_Call {
	_c.Call.Return(run)
	return _c
}

// GetEngagementPhasesByIDs provides a mock function with given fields: context, engageIDs
func (_m *Repository) GetEngagementPhasesByIDs(context ctx.CTX, engageIDs []string) ([]engagementreward.EngagementPhase, error) {
	ret := _m.Called(context, engageIDs)

	if len(ret) == 0 {
		panic("no return value specified for GetEngagementPhasesByIDs")
	}

	var r0 []engagementreward.EngagementPhase
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []string) ([]engagementreward.EngagementPhase, error)); ok {
		return rf(context, engageIDs)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, []string) []engagementreward.EngagementPhase); ok {
		r0 = rf(context, engageIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]engagementreward.EngagementPhase)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, []string) error); ok {
		r1 = rf(context, engageIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_GetEngagementPhasesByIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEngagementPhasesByIDs'
type Repository_GetEngagementPhasesByIDs_Call struct {
	*mock.Call
}

// GetEngagementPhasesByIDs is a helper method to define mock.On call
//   - context ctx.CTX
//   - engageIDs []string
func (_e *Repository_Expecter) GetEngagementPhasesByIDs(context interface{}, engageIDs interface{}) *Repository_GetEngagementPhasesByIDs_Call {
	return &Repository_GetEngagementPhasesByIDs_Call{Call: _e.mock.On("GetEngagementPhasesByIDs", context, engageIDs)}
}

func (_c *Repository_GetEngagementPhasesByIDs_Call) Run(run func(context ctx.CTX, engageIDs []string)) *Repository_GetEngagementPhasesByIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ctx.CTX), args[1].([]string))
	})
	return _c
}

func (_c *Repository_GetEngagementPhasesByIDs_Call) Return(_a0 []engagementreward.EngagementPhase, _a1 error) *Repository_GetEngagementPhasesByIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_GetEngagementPhasesByIDs_Call) RunAndReturn(run func(ctx.CTX, []string) ([]engagementreward.EngagementPhase, error)) *Repository_GetEngagementPhasesByIDs_Call {
	_c.Call.Return(run)
	return _c
}

// GetEngagementPhasesByRegion provides a mock function with given fields: context, region
func (_m *Repository) GetEngagementPhasesByRegion(context ctx.CTX, region string) ([]engagementreward.EngagementPhase, error) {
	ret := _m.Called(context, region)

	if len(ret) == 0 {
		panic("no return value specified for GetEngagementPhasesByRegion")
	}

	var r0 []engagementreward.EngagementPhase
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) ([]engagementreward.EngagementPhase, error)); ok {
		return rf(context, region)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) []engagementreward.EngagementPhase); ok {
		r0 = rf(context, region)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]engagementreward.EngagementPhase)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, region)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_GetEngagementPhasesByRegion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEngagementPhasesByRegion'
type Repository_GetEngagementPhasesByRegion_Call struct {
	*mock.Call
}

// GetEngagementPhasesByRegion is a helper method to define mock.On call
//   - context ctx.CTX
//   - region string
func (_e *Repository_Expecter) GetEngagementPhasesByRegion(context interface{}, region interface{}) *Repository_GetEngagementPhasesByRegion_Call {
	return &Repository_GetEngagementPhasesByRegion_Call{Call: _e.mock.On("GetEngagementPhasesByRegion", context, region)}
}

func (_c *Repository_GetEngagementPhasesByRegion_Call) Run(run func(context ctx.CTX, region string)) *Repository_GetEngagementPhasesByRegion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ctx.CTX), args[1].(string))
	})
	return _c
}

func (_c *Repository_GetEngagementPhasesByRegion_Call) Return(_a0 []engagementreward.EngagementPhase, _a1 error) *Repository_GetEngagementPhasesByRegion_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_GetEngagementPhasesByRegion_Call) RunAndReturn(run func(ctx.CTX, string) ([]engagementreward.EngagementPhase, error)) *Repository_GetEngagementPhasesByRegion_Call {
	_c.Call.Return(run)
	return _c
}

// InsertEngagementActionLogs provides a mock function with given fields: context, logParams
func (_m *Repository) InsertEngagementActionLogs(context ctx.CTX, logParams []engagementreward.EngagementActionLogParam) error {
	ret := _m.Called(context, logParams)

	if len(ret) == 0 {
		panic("no return value specified for InsertEngagementActionLogs")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []engagementreward.EngagementActionLogParam) error); ok {
		r0 = rf(context, logParams)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_InsertEngagementActionLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertEngagementActionLogs'
type Repository_InsertEngagementActionLogs_Call struct {
	*mock.Call
}

// InsertEngagementActionLogs is a helper method to define mock.On call
//   - context ctx.CTX
//   - logParams []engagementreward.EngagementActionLogParam
func (_e *Repository_Expecter) InsertEngagementActionLogs(context interface{}, logParams interface{}) *Repository_InsertEngagementActionLogs_Call {
	return &Repository_InsertEngagementActionLogs_Call{Call: _e.mock.On("InsertEngagementActionLogs", context, logParams)}
}

func (_c *Repository_InsertEngagementActionLogs_Call) Run(run func(context ctx.CTX, logParams []engagementreward.EngagementActionLogParam)) *Repository_InsertEngagementActionLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ctx.CTX), args[1].([]engagementreward.EngagementActionLogParam))
	})
	return _c
}

func (_c *Repository_InsertEngagementActionLogs_Call) Return(_a0 error) *Repository_InsertEngagementActionLogs_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_InsertEngagementActionLogs_Call) RunAndReturn(run func(ctx.CTX, []engagementreward.EngagementActionLogParam) error) *Repository_InsertEngagementActionLogs_Call {
	_c.Call.Return(run)
	return _c
}

// InsertEngagementPhases provides a mock function with given fields: context, phaseParams
func (_m *Repository) InsertEngagementPhases(context ctx.CTX, phaseParams []engagementreward.EngagementPhase) error {
	ret := _m.Called(context, phaseParams)

	if len(ret) == 0 {
		panic("no return value specified for InsertEngagementPhases")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []engagementreward.EngagementPhase) error); ok {
		r0 = rf(context, phaseParams)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_InsertEngagementPhases_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertEngagementPhases'
type Repository_InsertEngagementPhases_Call struct {
	*mock.Call
}

// InsertEngagementPhases is a helper method to define mock.On call
//   - context ctx.CTX
//   - phaseParams []engagementreward.EngagementPhase
func (_e *Repository_Expecter) InsertEngagementPhases(context interface{}, phaseParams interface{}) *Repository_InsertEngagementPhases_Call {
	return &Repository_InsertEngagementPhases_Call{Call: _e.mock.On("InsertEngagementPhases", context, phaseParams)}
}

func (_c *Repository_InsertEngagementPhases_Call) Run(run func(context ctx.CTX, phaseParams []engagementreward.EngagementPhase)) *Repository_InsertEngagementPhases_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ctx.CTX), args[1].([]engagementreward.EngagementPhase))
	})
	return _c
}

func (_c *Repository_InsertEngagementPhases_Call) Return(_a0 error) *Repository_InsertEngagementPhases_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_InsertEngagementPhases_Call) RunAndReturn(run func(ctx.CTX, []engagementreward.EngagementPhase) error) *Repository_InsertEngagementPhases_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateEngagementPhase provides a mock function with given fields: context, engageID, fields
func (_m *Repository) UpdateEngagementPhase(context ctx.CTX, engageID string, fields map[string]interface{}) error {
	ret := _m.Called(context, engageID, fields)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEngagementPhase")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, map[string]interface{}) error); ok {
		r0 = rf(context, engageID, fields)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_UpdateEngagementPhase_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateEngagementPhase'
type Repository_UpdateEngagementPhase_Call struct {
	*mock.Call
}

// UpdateEngagementPhase is a helper method to define mock.On call
//   - context ctx.CTX
//   - engageID string
//   - fields map[string]interface{}
func (_e *Repository_Expecter) UpdateEngagementPhase(context interface{}, engageID interface{}, fields interface{}) *Repository_UpdateEngagementPhase_Call {
	return &Repository_UpdateEngagementPhase_Call{Call: _e.mock.On("UpdateEngagementPhase", context, engageID, fields)}
}

func (_c *Repository_UpdateEngagementPhase_Call) Run(run func(context ctx.CTX, engageID string, fields map[string]interface{})) *Repository_UpdateEngagementPhase_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ctx.CTX), args[1].(string), args[2].(map[string]interface{}))
	})
	return _c
}

func (_c *Repository_UpdateEngagementPhase_Call) Return(_a0 error) *Repository_UpdateEngagementPhase_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_UpdateEngagementPhase_Call) RunAndReturn(run func(ctx.CTX, string, map[string]interface{}) error) *Repository_UpdateEngagementPhase_Call {
	_c.Call.Return(run)
	return _c
}

// NewRepository creates a new instance of Repository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *Repository {
	mock := &Repository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
