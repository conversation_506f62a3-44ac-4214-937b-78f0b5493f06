package config

import (
	"fmt"
	"sort"

	yaml "gopkg.in/yaml.v2"

	btime "github.com/17media/api/base/time"
	dailyModel "github.com/17media/api/models/daily"
	regionModel "github.com/17media/api/models/region"
	cfgSrv "github.com/17media/api/service/config"
	regionSrv "github.com/17media/api/service/region"
)

const (
	configID     = "config:referral"
	configBase   = "17app/referral/"
	configSuffix = "referral.yaml"

	debugConfigPath = "17app/referral/debug.yaml"
)

var (
	regions = []string{"TW", "JP", "HK", "MY", "US", "SG", "MENA", "PH", "ID", "TH", "VN", "MM", "IN", "OTHERS"}
	getCfg  = regionSrv.GetConfig

	debugConfig = DebugConfig{}
)

var (
	ErrQuestVersionNotFound = fmt.Errorf("quest version not found")
	ErrMissionNotFound      = fmt.Errorf("mission not found")
	ErrQuestNotFound        = fmt.Errorf("quest not found")
)

func init() {
	regionSrv.Register(configID, configBase, configSuffix, &Config{}, regions...)
	if err := cfgSrv.Register(debugConfigPath, &debugConfig); err != nil {
		panic(fmt.Sprintf("unable to register watcher %v, err %v", debugConfigPath, err))
	}
}

// Mission represents the config for a referral mission
type Mission struct {
	MissionID     string                    `yaml:"-" json:"missionID"`
	Type          dailyModel.DailyQuestType `yaml:"type" json:"type"`
	Point         int32                     `yaml:"point" json:"point"`
	Exp           int32                     `yaml:"exp" json:"exp"`
	Required      int32                     `yaml:"required" json:"required"`
	WithReminder  bool                      `yaml:"withReminder" json:"-"`
	VoucherReward *dailyModel.VoucherReward `yaml:"voucherReward" json:"voucherReward"`
}

func (m *Mission) IsFinished(count int64) bool {
	return count >= int64(m.Required)
}

// Quest represents the config for a quest
type Quest struct {
	Version          int32      `yaml:"version" json:"version"`
	StartTimeStr     string     `yaml:"startTime"`
	InviterRewardBBC int32      `yaml:"inviterRewardBBC" json:"inviterRewardBBC"`
	MissionDay       int32      `yaml:"missionDay" json:"missionDay"`
	Missions         []*Mission `yaml:"missions" json:"missions"`

	StartTime  int64 `yaml:"-" json:"startTime"`
	missionMap map[dailyModel.DailyQuestType]*Mission
}

// Config ...
type Config struct {
	InviterRewardLimitBBC int64    `yaml:"inviterRewardLimitBBC" json:"inviterRewardLimitBBC"`
	Quests                []*Quest `yaml:"quests" json:"quests"`

	questMap map[int32]*Quest
}

// Check checks config data
func (c *Config) Check(data []byte) (interface{}, []string, error) {
	conf := Config{}
	if err := yaml.Unmarshal(data, &conf); nil != err {
		return nil, nil, err
	}

	// Check inviter reward limit
	if conf.InviterRewardLimitBBC < 0 {
		return nil, nil, fmt.Errorf("inviterRewardLimitBBC must be equal to or greater than 0")
	}

	// Check quest
	var previousVersion int32
	var previousStartTime int64
	for i, quest := range conf.Quests {
		// Check version
		if quest.Version <= previousVersion {
			return nil, nil, fmt.Errorf("versions must be unique and increasing")
		}
		previousVersion = quest.Version

		// Check start time
		st, err := btime.Parse(quest.StartTimeStr)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to parse startTime: %s", err.Error())
		}
		startTime := st.Unix()
		if startTime <= previousStartTime {
			return nil, nil, fmt.Errorf("startTimes must be unique and increasing")
		}
		previousStartTime = startTime
		conf.Quests[i].StartTime = startTime

		// Check inviter reward
		if quest.InviterRewardBBC < 0 {
			return nil, nil, fmt.Errorf("inviterRewardBBC must be equal to or greater than 0")
		}

		// Check inviter reward
		if quest.MissionDay <= 0 {
			return nil, nil, fmt.Errorf("missionDay must be greater than 0")
		}

		// Check missions
		seenType := make(map[dailyModel.DailyQuestType]bool)
		for _, mission := range quest.Missions {
			if !mission.Type.IsValid() {
				return nil, nil, fmt.Errorf("invalid mission type")
			}
			if _, ok := seenType[mission.Type]; ok {
				return nil, nil, fmt.Errorf("mission type must be unique in each version")
			}
			seenType[mission.Type] = true
			if mission.Exp < 0 {
				return nil, nil, fmt.Errorf("mission exp must be equal to or greater than 0")
			}
			if mission.Point < 0 {
				return nil, nil, fmt.Errorf("mission point must be equal to or greater than 0")
			}
			if mission.Required < 0 {
				return nil, nil, fmt.Errorf("mission required must be equal to or greater than 0")
			}
			if (mission.Type == dailyModel.DailyQuestTypeBuyPoint || mission.Type == dailyModel.DailyQuestTypePhoneVerification) && mission.Required > 1 {
				return nil, nil, fmt.Errorf("required of buy-point-mission and phone-verification can only set to 1")
			}
		}
	}

	return conf, nil, nil
}

// Apply applies data to this config
func (c *Config) Apply(v interface{}) {
	*c = v.(Config)

	for _, quest := range c.Quests {
		quest.missionMap = make(map[dailyModel.DailyQuestType]*Mission)
		for _, mission := range quest.Missions {
			mission.MissionID = "ReferralV2-" + mission.Type.String()
			quest.missionMap[mission.Type] = mission
		}
	}

	c.questMap = make(map[int32]*Quest)
	for _, quest := range c.Quests {
		c.questMap[quest.Version] = quest
	}
}

// Get get the config instance
func (c *Config) Get() interface{} {
	return *c
}

// GetQuestByVersion get the quest by given version. Return ErrQuestVersionNotFound if quest version not exist.
func (c *Config) GetQuestByVersion(version int32) (quest *Quest, err error) {
	quest, ok := c.questMap[version]
	if !ok {
		return nil, ErrQuestVersionNotFound
	}
	return quest, nil
}

func (c *Config) GetMissionByVersion(version int32, missionType dailyModel.DailyQuestType) (mission *Mission, err error) {
	quest, err := c.GetQuestByVersion(version)
	if err != nil {
		return nil, err
	}
	mission, ok := quest.missionMap[missionType]
	if !ok {
		return nil, ErrMissionNotFound
	}
	return mission, nil
}

func (c *Config) GetVersionByStartTime(startTime int64) (int32, error) {
	idx := sort.Search(len(c.Quests), func(i int) bool {
		return c.Quests[i].StartTime > startTime
	})
	if idx == 0 {
		return 0, ErrQuestNotFound
	}
	return c.Quests[idx-1].Version, nil
}

func (c *Config) GetQuestByStartTime(startTime int64) (quest *Quest, err error) {
	version, err := c.GetVersionByStartTime(startTime)
	if err != nil {
		return nil, err
	}
	return c.GetQuestByVersion(version)
}

// GetCfg returns referral config
func GetCfg(registerRegion string) Config {
	regionInfo := regionModel.RegionInfo{
		Region:           registerRegion,
		RegionByIP:       registerRegion,
		RegionByRegister: registerRegion,
	}

	intf, err := getCfg(configID, &regionInfo)
	if err != nil {
		return Config{}
	}
	c := intf.(Config)

	return c
}

type DebugConfig struct {
	AcceptanceTime                  int64    `yaml:"acceptanceTime" json:"acceptanceTime"`
	MissionTime                     int64    `yaml:"missionTime" json:"missionTime"`
	WhiteListDeviceIDs              []string `yaml:"whiteListDeviceIDs" json:"whiteListDeviceIDs"`
	EnableDeviceIDCheckerWithBQData bool     `yaml:"enableDeviceIDCheckerWithBQData"`

	SetDeviceIDs map[string]struct{}
}

func (c *DebugConfig) Check(data []byte) (interData interface{}, warnings []string, err error) {
	conf := DebugConfig{}
	if err := yaml.Unmarshal(data, &conf); err != nil {
		return nil, nil, err
	}

	if conf.AcceptanceTime < 0 {
		return nil, nil, fmt.Errorf("acceptanceTime must be greater or equal than 0")
	}
	if conf.MissionTime < 0 {
		return nil, nil, fmt.Errorf("missionTime must be greater or equal than 0")
	}
	return conf, nil, nil
}

func (c *DebugConfig) Apply(v interface{}) {
	*c = v.(DebugConfig)

	c.SetDeviceIDs = make(map[string]struct{})
	for _, deviceIDs := range c.WhiteListDeviceIDs {
		c.SetDeviceIDs[deviceIDs] = struct{}{}
	}
}

func GetDebugConfig() DebugConfig {
	return debugConfig
}
