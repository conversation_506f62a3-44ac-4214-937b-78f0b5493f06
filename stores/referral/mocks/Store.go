// Code generated by mockery v2.50.0. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	config "github.com/17media/api/models/config"

	daily "github.com/17media/api/models/daily"

	mock "github.com/stretchr/testify/mock"

	models "github.com/17media/api/models"

	modelsreferral "github.com/17media/api/models/referral"

	mongo "go.mongodb.org/mongo-driver/mongo"

	queue "github.com/17media/api/models/queue"

	referral "github.com/17media/api/stores/referral"

	user "github.com/17media/api/models/user"
)

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// AcceptInvitation provides a mock function with given fields: context, _a1, deviceID
func (_m *Store) AcceptInvitation(context ctx.CTX, _a1 *models.User, deviceID string) error {
	ret := _m.Called(context, _a1, deviceID)

	if len(ret) == 0 {
		panic("no return value specified for AcceptInvitation")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, string) error); ok {
		r0 = rf(context, _a1, deviceID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DoMission provides a mock function with given fields: context, _a1, missionType, options
func (_m *Store) DoMission(context ctx.CTX, _a1 *models.User, missionType daily.DailyQuestType, options ...referral.OptionDoMission) error {
	_va := make([]interface{}, len(options))
	for _i := range options {
		_va[_i] = options[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, _a1, missionType)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DoMission")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, daily.DailyQuestType, ...referral.OptionDoMission) error); ok {
		r0 = rf(context, _a1, missionType, options...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ExpirationHandler provides a mock function with given fields: context, data, option
func (_m *Store) ExpirationHandler(context ctx.CTX, data []byte, option queue.CallbackOption) error {
	ret := _m.Called(context, data, option)

	if len(ret) == 0 {
		panic("no return value specified for ExpirationHandler")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []byte, queue.CallbackOption) error); ok {
		r0 = rf(context, data, option)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FollowStreamer provides a mock function with given fields: context, _a1, streamer
func (_m *Store) FollowStreamer(context ctx.CTX, _a1 *models.User, streamer *models.User) error {
	ret := _m.Called(context, _a1, streamer)

	if len(ret) == 0 {
		panic("no return value specified for FollowStreamer")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, *models.User) error); ok {
		r0 = rf(context, _a1, streamer)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetInfo provides a mock function with given fields: context, _a1
func (_m *Store) GetInfo(context ctx.CTX, _a1 *models.User) (*modelsreferral.Info, error) {
	ret := _m.Called(context, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetInfo")
	}

	var r0 *modelsreferral.Info
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User) (*modelsreferral.Info, error)); ok {
		return rf(context, _a1)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User) *modelsreferral.Info); ok {
		r0 = rf(context, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsreferral.Info)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, *models.User) error); ok {
		r1 = rf(context, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInvitation provides a mock function with given fields: context, _a1, deviceID
func (_m *Store) GetInvitation(context ctx.CTX, _a1 *models.User, deviceID string) (*user.DisplayInfo, error) {
	ret := _m.Called(context, _a1, deviceID)

	if len(ret) == 0 {
		panic("no return value specified for GetInvitation")
	}

	var r0 *user.DisplayInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, string) (*user.DisplayInfo, error)); ok {
		return rf(context, _a1, deviceID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, string) *user.DisplayInfo); ok {
		r0 = rf(context, _a1, deviceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*user.DisplayInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, *models.User, string) error); ok {
		r1 = rf(context, _a1, deviceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetMissions provides a mock function with given fields: context, _a1, _a2
func (_m *Store) GetMissions(context ctx.CTX, _a1 *models.User, _a2 *modelsreferral.Referral) ([]*daily.DailyNewbieQuest, error) {
	ret := _m.Called(context, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for GetMissions")
	}

	var r0 []*daily.DailyNewbieQuest
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, *modelsreferral.Referral) ([]*daily.DailyNewbieQuest, error)); ok {
		return rf(context, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, *modelsreferral.Referral) []*daily.DailyNewbieQuest); ok {
		r0 = rf(context, _a1, _a2)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*daily.DailyNewbieQuest)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, *models.User, *modelsreferral.Referral) error); ok {
		r1 = rf(context, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetMissionsByUser provides a mock function with given fields: context, _a1
func (_m *Store) GetMissionsByUser(context ctx.CTX, _a1 *models.User) ([]*daily.DailyNewbieQuest, error) {
	ret := _m.Called(context, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetMissionsByUser")
	}

	var r0 []*daily.DailyNewbieQuest
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User) ([]*daily.DailyNewbieQuest, error)); ok {
		return rf(context, _a1)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User) []*daily.DailyNewbieQuest); ok {
		r0 = rf(context, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*daily.DailyNewbieQuest)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, *models.User) error); ok {
		r1 = rf(context, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetReferral provides a mock function with given fields: context, inviteeID
func (_m *Store) GetReferral(context ctx.CTX, inviteeID string) (*modelsreferral.Referral, error) {
	ret := _m.Called(context, inviteeID)

	if len(ret) == 0 {
		panic("no return value specified for GetReferral")
	}

	var r0 *modelsreferral.Referral
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (*modelsreferral.Referral, error)); ok {
		return rf(context, inviteeID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) *modelsreferral.Referral); ok {
		r0 = rf(context, inviteeID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsreferral.Referral)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, inviteeID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetReferralFeatureInfo provides a mock function with given fields: context, _a1, deviceInfo
func (_m *Store) GetReferralFeatureInfo(context ctx.CTX, _a1 *models.User, deviceInfo config.DeviceInfo) *user.ReferralInfo {
	ret := _m.Called(context, _a1, deviceInfo)

	if len(ret) == 0 {
		panic("no return value specified for GetReferralFeatureInfo")
	}

	var r0 *user.ReferralInfo
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, config.DeviceInfo) *user.ReferralInfo); ok {
		r0 = rf(context, _a1, deviceInfo)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*user.ReferralInfo)
		}
	}

	return r0
}

// GetReferralsByCursor provides a mock function with given fields: context, userID, cursor, count, status
func (_m *Store) GetReferralsByCursor(context ctx.CTX, userID string, cursor string, count int, status modelsreferral.ReferralStatus) ([]*user.DisplayInfo, string, error) {
	ret := _m.Called(context, userID, cursor, count, status)

	if len(ret) == 0 {
		panic("no return value specified for GetReferralsByCursor")
	}

	var r0 []*user.DisplayInfo
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, int, modelsreferral.ReferralStatus) ([]*user.DisplayInfo, string, error)); ok {
		return rf(context, userID, cursor, count, status)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, int, modelsreferral.ReferralStatus) []*user.DisplayInfo); ok {
		r0 = rf(context, userID, cursor, count, status)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*user.DisplayInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, int, modelsreferral.ReferralStatus) string); ok {
		r1 = rf(context, userID, cursor, count, status)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(ctx.CTX, string, string, int, modelsreferral.ReferralStatus) error); ok {
		r2 = rf(context, userID, cursor, count, status)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// InsertInvitations provides a mock function with given fields: context, invitationPairs
func (_m *Store) InsertInvitations(context ctx.CTX, invitationPairs ...modelsreferral.InvitationPair) (*mongo.InsertManyResult, error) {
	_va := make([]interface{}, len(invitationPairs))
	for _i := range invitationPairs {
		_va[_i] = invitationPairs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for InsertInvitations")
	}

	var r0 *mongo.InsertManyResult
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, ...modelsreferral.InvitationPair) (*mongo.InsertManyResult, error)); ok {
		return rf(context, invitationPairs...)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, ...modelsreferral.InvitationPair) *mongo.InsertManyResult); ok {
		r0 = rf(context, invitationPairs...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*mongo.InsertManyResult)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, ...modelsreferral.InvitationPair) error); ok {
		r1 = rf(context, invitationPairs...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// QuestCompletionHandler provides a mock function with given fields: context, data, option
func (_m *Store) QuestCompletionHandler(context ctx.CTX, data []byte, option queue.CallbackOption) error {
	ret := _m.Called(context, data, option)

	if len(ret) == 0 {
		panic("no return value specified for QuestCompletionHandler")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []byte, queue.CallbackOption) error); ok {
		r0 = rf(context, data, option)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ReceiveMissionReward provides a mock function with given fields: context, userID, region, dqType
func (_m *Store) ReceiveMissionReward(context ctx.CTX, userID string, region string, dqType daily.DailyQuestType) (*daily.DailyReward, error) {
	ret := _m.Called(context, userID, region, dqType)

	if len(ret) == 0 {
		panic("no return value specified for ReceiveMissionReward")
	}

	var r0 *daily.DailyReward
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, daily.DailyQuestType) (*daily.DailyReward, error)); ok {
		return rf(context, userID, region, dqType)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, daily.DailyQuestType) *daily.DailyReward); ok {
		r0 = rf(context, userID, region, dqType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*daily.DailyReward)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, daily.DailyQuestType) error); ok {
		r1 = rf(context, userID, region, dqType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RejectInvitation provides a mock function with given fields: context, userID
func (_m *Store) RejectInvitation(context ctx.CTX, userID string) error {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for RejectInvitation")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) error); ok {
		r0 = rf(context, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewStore creates a new instance of Store. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *Store {
	mock := &Store{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
