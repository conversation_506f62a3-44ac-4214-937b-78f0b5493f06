package contract

import (
	"database/sql"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"strconv"
	"strings"
	"testing"
	"time"

	"cloud.google.com/go/bigquery"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"

	evModel "github.com/17media/api/app/eventory/models/event"
	evSnapshotModel "github.com/17media/api/app/eventory/models/snapshot"
	"github.com/17media/api/base/ctx"
	mdb "github.com/17media/api/base/db"
	bdocker "github.com/17media/api/base/docker"
	"github.com/17media/api/base/goroutine"
	baseTime "github.com/17media/api/base/time"
	"github.com/17media/api/models"
	accountingModel "github.com/17media/api/models/accounting"
	ctModel "github.com/17media/api/models/contract"
	"github.com/17media/api/models/intra"
	"github.com/17media/api/models/keys"
	paModel "github.com/17media/api/models/payoutaccount"
	snackRankModel "github.com/17media/api/models/snack/rank"
	sModel "github.com/17media/api/models/streamer"
	stlModel "github.com/17media/api/models/streamertrackinglist"
	taskProgressModol "github.com/17media/api/models/taskprogress"
	"github.com/17media/api/service/cache"
	"github.com/17media/api/service/intralog"
	mintralog "github.com/17media/api/service/intralog/mocks"
	"github.com/17media/api/service/localcache"
	"github.com/17media/api/service/localcache/primitive"
	"github.com/17media/api/service/payoutaccount"
	mpayoutaccount "github.com/17media/api/service/payoutaccount/mocks"
	"github.com/17media/api/service/queryv2"
	mockPublisher "github.com/17media/api/service/queue/mocks"
	mockRate "github.com/17media/api/service/rate/mocks"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/service/redis/rediscache"
	mockRegion "github.com/17media/api/service/region/mocks"
	mockSlack "github.com/17media/api/service/slack/mocks"
	mstorage "github.com/17media/api/service/storage/mocks"
	"github.com/17media/api/service/taskprogress"
	mTaskprogress "github.com/17media/api/service/taskprogress/mocks"
	fbq "github.com/17media/api/setup/bigquery/fake"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/setup/mysql/mysqladmin"
	mAgency "github.com/17media/api/stores/agency/mocks"
	contractConfig "github.com/17media/api/stores/contract/config"
	"github.com/17media/api/stores/contract/helper"
	mcontractRevenue "github.com/17media/api/stores/contract/revenue/mocks"
	"github.com/17media/api/stores/contract/template"
	"github.com/17media/api/stores/contractapplication"
	mContractApplication "github.com/17media/api/stores/contractapplication/mocks"
	meventory "github.com/17media/api/stores/eventory/mocks"
	mgiftRevenue "github.com/17media/api/stores/gift/revenue/mocks"
	mIntraUser "github.com/17media/api/stores/intra/user/mocks"
	mockLeaderboard "github.com/17media/api/stores/leaderboard/mocks"
	mStatistic "github.com/17media/api/stores/money/statistic/mocks"
	mSnackRank "github.com/17media/api/stores/snack/rank/mocks"
	mockStreamer "github.com/17media/api/stores/streamer/mocks"
	muser "github.com/17media/api/stores/user/mocks"
)

type mockFuncs struct {
	mock.Mock
}

var (
	mockCTX                   = ctx.Background()
	anyCTX                    = mock.AnythingOfType("ctx.CTX")
	mockUserID                = "b1e571ee-fce8-4ed2-add0-39f6a2844955" // momoisgood
	mockUserID2               = "b1e571ee-fce8-4ed2-add0-39f6a2844956" // momoisgood2
	mockOpenID                = "momoisgood"
	mockOpenID2               = "momoisgood2"
	mockPayoutAccountID       = int64(1)
	mockBBRichPayoutAccountID = int64(1234)
	mockTimezone              = "Asia/Taipei"
	twloc, _                  = time.LoadLocation(mockTimezone)
	currentTime               = time.Date(2018, time.February, 1, 0, 0, 0, 0, twloc)

	mockPayoutAccount = &paModel.PayoutAccount{
		Id:      1,
		OwnerID: mockUserID,
		Account: "testAccount",
	}
	mockBBRichPayoutAccount = &paModel.PayoutAccount{
		Id:          1234,
		OwnerID:     mockUserID,
		AccountType: paModel.AccountType_BBRICH_ACCOUNT,
	}

	mockContract = &ctModel.Contract{
		Id:               1,
		Type:             0,
		AgentID:          107,
		AgencyID:         107,
		StreamerID:       777,
		ParentContractID: 0,
		UserID:           "8de87814-05e3-4d9b-bb70-b70543b87fa9",
		DateStart:        "2017-04-01",
		DateEnd:          "2018-03-31",
		Exclusive:        0,
		PayToParent:      1,
		Others:           "0401-0630 保底61,200免拆帳,30hr 0401-0630 分潤102,264免拆帳",
		Attachment:       "",
		IsDeleted:        0,
		Region:           "TW",
		IsUnofficial:     0,
		Birthday:         "1988-05-01",
		Timezone:         "Asia/Taipei",
		StartTime:        **********,
		EndTime:          **********,
		Terms: []*ctModel.ContractTerm{
			{
				Id:           1,
				ContractID:   1,
				Condition:    "",
				Gte:          0,
				Lt:           0,
				Prop:         "HOURLY_WAGE",
				Value:        1000,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Id:           2,
				ContractID:   1,
				Condition:    "",
				Gte:          0,
				Lt:           0,
				Prop:         "MAX_PAID_HOURS",
				Value:        30,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Id:           3,
				ContractID:   1,
				Condition:    "STREAMED_HOURS",
				Gte:          0,
				Lt:           10,
				Prop:         "PERCENT_WAGE_RATIO",
				Value:        0,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Id:           4,
				ContractID:   1,
				Condition:    "STREAMED_HOURS",
				Gte:          10,
				Lt:           30,
				Prop:         "PERCENT_WAGE_RATIO",
				Value:        100,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Id:           5,
				ContractID:   1,
				Condition:    "STREAMED_HOURS",
				Gte:          30,
				Lt:           -1,
				Prop:         "PERCENT_WAGE_RATIO",
				Value:        100,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Id:           6,
				ContractID:   1,
				Condition:    "",
				Gte:          0,
				Lt:           0,
				Prop:         "PERMILLE_SUBRUN",
				Value:        600,
				ContractType: ctModel.StreamerContractType,
			},
		},
		PayoutType:     2,
		FeeOwnerType:   2, // "User",
		PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
	}

	mockContract8 = &ctModel.Contract{
		Id:               8,
		Type:             0,
		AgentID:          999,
		AgencyID:         999,
		StreamerID:       1234567,
		ParentContractID: 0,
		UserID:           "for delete and update",
		DateStart:        "2018-04-01",
		DateEnd:          "2019-03-31",
		Exclusive:        1,
		PayToParent:      1,
		Others:           "",
		Attachment:       "",
		IsDeleted:        0,
		Region:           "TW",
		IsUnofficial:     0,
		Birthday:         "88-04-03",
		Timezone:         "Asia/Taipei",
		StartTime:        **********,
		EndTime:          **********,
		Terms:            []*ctModel.ContractTerm{},
		PayoutAccounts:   map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
	}
	mockContractUserID1 = "8de87814-05e3-4d9b-bb70-b70543b87fa9"
	mockContractUserID2 = "408ffd58-bc4f-4c03-abc4-3c095d21d424"
	mockContractUserID3 = "1af4cba1-59de-45d0-b3cc-719c58eef0d7"
	mockContractUserID4 = "aaaaaaaa-59de-45d0-b3cc-719c58eef0d7"

	mockUser = models.User{
		UserID:  "1af4cba1-59de-45d0-b3cc-719c58eef0d7",
		OpenID:  "jen78go",
		RoomID:  ********,
		Picture: "mockUserPicture",
		Region:  "TW",
	}
)

type ContractSuite struct {
	suite.Suite
	mockStorage             *mstorage.Service
	Store                   *impl
	adminDb                 *sqlx.DB
	mockFuncs               *mockFuncs
	mockLeaderboard         *mockLeaderboard.Leaderboard
	localcache              localcache.Service
	cache                   redis.Service
	rate                    *mockRate.Service
	mockRegion              *mockRegion.Service
	mockUser                *muser.Store
	mockCTReport            *mStatistic.CTReport
	mockSlack               *mockSlack.Slack
	mockPayoutAccount       *mpayoutaccount.Service
	mockEventory            *meventory.Store
	bq                      *bigquery.Client
	query                   queryv2.Mongo
	giftRevenue             *mgiftRevenue.Worker
	contractRevenue         *mcontractRevenue.Store
	mockIntraLog            *mintralog.Service
	mockPublisher           *mockPublisher.Publisher
	mockStreamer            *mockStreamer.Store
	mockTaskprogress        *mTaskprogress.Service
	mockContractApplication *mContractApplication.Store
	mockSnackRank           *mSnackRank.Store
	mockAgency              *mAgency.Store
	// NOTE: test will call helper func so we have to import mock intra user because contract helper import it
	mockIntraUser *mIntraUser.Store

	localhost string
	redisPort string
	mongoPort string
	mysqlPort string

	manager *dimanager.Manager
}

func (cs *ContractSuite) SetupSuite() {
	// Start mysql
	localhost, ports, err := bdocker.RunExtDockers([]string{"mysql8", "mongo", "redis"})
	cs.Require().NoError(err)
	cs.localhost = localhost
	cs.mysqlPort = ports[0]
	cs.mongoPort = ports[1]
	cs.redisPort = ports[2]

	cs.manager = dimanager.DefaultManager
	cs.manager.ProvideString("rds_gift_uri", "root:@tcp("+localhost+":"+cs.mysqlPort+")/media17gift?charset=utf8mb4")
	cs.manager.ProvideString("rds_gift_reader_uri", "root:@tcp("+localhost+":"+cs.mysqlPort+")/media17gift?charset=utf8mb4")
	cs.manager.ProvideString("rds_money_uri", "root:@tcp("+localhost+":"+cs.mysqlPort+")/media17gift?charset=utf8mb4")
	cs.manager.ProvideString("rds_admin_uri", "root:@tcp("+localhost+":"+cs.mysqlPort+")/media17admin?charset=utf8mb4")
	cs.manager.ProvideString("redis_cache_uri", localhost+":"+cs.redisPort)
	cs.manager.ProvideString("redis_persist_uri", localhost+":"+cs.redisPort)
	cs.manager.ProvideString("mongo_uri", localhost+":"+cs.mongoPort)
	cs.manager.ProvideString("mongo_db", "17media")
	rediscache.ConnectRedisCluster(cs.manager)

	flag.Set("pubsub_emulator_host", "pubsub_emulator_host")
}

func (cs *ContractSuite) TearDownSuite() {
	cs.NoError(bdocker.RemExtDockers())
}

func (cs *ContractSuite) SetupTest() {
	cs.manager.ClearMock()
	cs.mockStorage = mstorage.RegisterProxyStoragePrivateMock(cs.manager)
	cs.mockLeaderboard = mockLeaderboard.RegisterMock(cs.manager)
	cs.mockUser = muser.RegisterMock(cs.manager)
	cs.rate = mockRate.RegisterMock(cs.manager)
	cs.mockRegion = mockRegion.RegisterMock(cs.manager)
	mockRegion.MockGetTimeLocation(cs.mockRegion)
	cs.mockSlack = mockSlack.RegisterMock(cs.manager)
	cs.mockPayoutAccount = mpayoutaccount.RegisterMock(cs.manager)
	cs.mockEventory = meventory.RegisterMock(cs.manager)
	cs.bq = fbq.RegisterFake(cs.manager)
	cs.giftRevenue = mgiftRevenue.RegisterMock(cs.manager)
	cs.contractRevenue = mcontractRevenue.RegisterMock(cs.manager)
	cs.mockIntraLog = mintralog.RegisterMock(cs.manager)
	cs.mockPublisher = &mockPublisher.Publisher{}
	cs.mockStreamer = mockStreamer.RegisterMock(cs.manager)
	cs.mockTaskprogress = mTaskprogress.RegisterMock(cs.manager)
	cs.mockContractApplication = mContractApplication.RegisterMock(cs.manager)
	cs.mockIntraUser = mIntraUser.RegisterMock(cs.manager)
	cs.mockSnackRank = mSnackRank.RegisterMock(cs.manager)
	cs.mockAgency = mAgency.RegisterMock(cs.manager)
	cs.manager.Compile()

	goroutineGo = func(f func()) (panicCh chan *goroutine.PanicEvent) {
		f()
		return nil
	}
	cs.mockCTReport = &mStatistic.CTReport{}
	cs.mockFuncs = &mockFuncs{}
	timeNow = cs.mockFuncs.TimeNow
	findContract = cs.mockFuncs.FindContract
	setContractByContractCSVValidation = cs.mockFuncs.SetContractByContractCSVValidation
	helper.TimeNow = timeNow
	batchCreate = cs.mockFuncs.batchCreate
	batchUpdate = cs.mockFuncs.batchUpdate
	parseCreateCSVToContracts = cs.mockFuncs.parseCreateCSVToContracts
	parseUpdateCSVToContracts = cs.mockFuncs.parseUpdateCSVToContracts
	sendEvents = cs.mockFuncs.sendEvents

	getConfig = func() contractConfig.Config {
		return contractConfig.Config{
			BirthdayAfterDays:     1,
			BirthdayCountdownDays: 3,
			NearOutdatedDays: map[string]int{
				"TW": 7,
				"JP": 60,
			},
			ExtendContractExcludeRegions: []string{"JP"},
			RenewContractIncludeRegions:  []string{"TW"},
			RenewContract: map[string]int{
				"TW": 7,
			},
			ExtendContract: map[string]int{
				"TW": 7,
			},
			MaxBatchUpdateSize: 1000,
			MaxBatchCreateSize: 1000,
		}
	}

	createGiftDB("media17gift", cs.localhost, cs.mysqlPort)
	createAdminDB("media17admin", cs.localhost, cs.mysqlPort)
	cs.query = queryv2.GetQueryV2(cs.manager)
	cs.adminDb = sqlx.NewDb(mysqladmin.GetMySQLAdmin(cs.manager), mdb.SQLDriver)
	cs.localcache = primitive.GetPrimitive(cs.manager)
	cs.cache = rediscache.GetRedisCache(cs.manager)

	cs.Store = GetContract(cs.manager).(*impl)
	cs.Store.contractDateChangedPublisher = cs.mockPublisher
	cs.Store.trackingListEventPublisher = cs.mockPublisher
}

func (cs *ContractSuite) TearDownTest() {
	cs.mockStorage.AssertExpectations(cs.T())
	cs.mockFuncs.AssertExpectations(cs.T())
	cs.mockLeaderboard.AssertExpectations(cs.T())
	cs.mockUser.AssertExpectations(cs.T())
	cs.rate.AssertExpectations(cs.T())
	cs.mockRegion.AssertExpectations(cs.T())
	cs.mockSlack.AssertExpectations(cs.T())
	cs.mockPayoutAccount.AssertExpectations(cs.T())
	cs.mockEventory.AssertExpectations(cs.T())
	cs.giftRevenue.AssertExpectations(cs.T())
	cs.mockIntraLog.AssertExpectations(cs.T())
	cs.NoError(bdocker.ClearRedis(cs.redisPort))
	cs.NoError(bdocker.ClearMySQL(cs.mysqlPort))
	cs.localcache.ClearLocal(mockCTX)
	cs.mockStreamer.AssertExpectations(cs.T())
	cs.mockTaskprogress.AssertExpectations(cs.T())
	cs.mockContractApplication.AssertExpectations(cs.T())
	cs.mockIntraUser.AssertExpectations(cs.T())
	cs.mockSnackRank.AssertExpectations(cs.T())
	cs.mockPublisher.AssertExpectations(cs.T())
	cs.mockAgency.AssertExpectations(cs.T())
	cache.ClearPfx()
}

func (cs *ContractSuite) TestInjectMonthValidStreamDuration() {
	mockContractUser := mockUser
	mockContractUser.Contract = &ctModel.Contract{
		Type:        0,
		UserID:      "ab122a68-abf6-45bf-aa5d-e5534e0a9276",
		DateStart:   "2020-08-18",
		DateEnd:     "2020-08-30",
		Exclusive:   1,
		PayToParent: 0,
		Region:      "JP",
		Timezone:    "Asia/Taipei",
		StartTime:   1597680000,
		EndTime:     1598803200,
		Terms:       nil,
	}
	mockUserStatus := []*ctModel.UserStats{}
	tests := []struct {
		desc   string
		user   models.User
		setup  func()
		want   int64
		expErr error
	}{
		{
			desc: "should not inject monthly valid stream duration when the user does not have a contract",
			user: mockUser,
			want: 0,
		},
		{
			desc: "should inject monthly valid stream duration when the user has a contract",
			user: mockContractUser,
			setup: func() {
				cs.contractRevenue.On("GetUserStatses", mock.AnythingOfType("ctx.CTX"), mockContractUser.UserID, mockContractUser.Contract.Timezone, "this_month").Return(mockUserStatus, nil)
				cs.contractRevenue.On("GetCachedStats", mock.AnythingOfType("ctx.CTX"), mockUserStatus, mockContractUser.Contract.Timezone).Return([]*ctModel.DayStats{
					{
						Date:       "2020-08-19",
						ValidHours: 1.5,
					},
					{
						Date:       "2020-08-24",
						ValidHours: 0.7,
					},
					{
						Date:       "2020-08-30",
						ValidHours: 1.9,
					},
				}, []*ctModel.UserStats{}, nil)
			},
			want: 14760,
		},
		{
			desc: "should not inject the valid stream duration of dates when the contract is not effect",
			user: mockContractUser,
			setup: func() {
				cs.contractRevenue.On("GetUserStatses", mock.AnythingOfType("ctx.CTX"), mockContractUser.UserID, mockContractUser.Contract.Timezone, "this_month").Return(mockUserStatus, nil)
				cs.contractRevenue.On("GetCachedStats", mock.AnythingOfType("ctx.CTX"), mockUserStatus, mockContractUser.Contract.Timezone).Return([]*ctModel.DayStats{
					{
						Date:       "2020-08-10",
						ValidHours: 0.4,
					},
					{
						Date:       "2020-08-19",
						ValidHours: 1.5,
					},
					{
						Date:       "2020-08-24",
						ValidHours: 0.7,
					},
					{
						Date:       "2020-08-30",
						ValidHours: 1.9,
					},
					{
						Date:       "2020-08-31",
						ValidHours: 1,
					},
				}, []*ctModel.UserStats{}, nil)
			},
			want: 14760,
		},
	}

	for _, t := range tests {
		fmt.Printf("test: %s\n", t.desc)
		// arrange
		cs.TearDownTest()
		cs.SetupTest()
		if t.setup != nil {
			t.setup()
		}

		// act
		cs.Require().NoError(cs.Store.InjectMonthValidStreamDuration(mockCTX, &t.user))

		// assert
		cs.Require().Equal(t.want, t.user.ValidMonthStreamDuration)
	}
}

func (cs *ContractSuite) TestCreate() {
	tests := []struct {
		Desc         string
		adminIDs     []int
		adminID      int
		contractInit *ctModel.Contract
		MockFunc     func()
		ExpError     error
	}{
		{
			"No permission",
			[]int{},
			0,
			&ctModel.Contract{},
			func() {
			},
			ErrNoPermission,
		},
		{
			Desc:     "no permission to set agent",
			adminIDs: []int{107},
			adminID:  107,
			contractInit: &ctModel.Contract{
				AgentID: 999,
			},
			MockFunc: func() {
			},
			ExpError: ErrNoPermissionToSetAgent,
		},
		{
			"invalid date",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:        12345,
				StreamerID:     123456,
				Region:         "TW",
				DateStart:      "2020-11-14",
				DateEnd:        "2020-11-13",
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
			},
			func() {
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
			},
			ErrInvalidDate,
		},
		{
			"invalid payout account",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:     12345,
				StreamerID:  123456,
				DateStart:   "2020-11-13",
				DateEnd:     "2022-11-13",
				Exclusive:   0,
				PayToParent: 0,
				Others:      "",
				Region:      "TW",
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{
					ctModel.PayoutCategory_REVENUE: {
						Id: 123,
					},
				},
			},
			func() {
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{123},
				}).Return(nil, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
			},
			ErrInvalidPayoutAccount,
		},
		{
			"ErrContractMissProfitSharing",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:     12345,
				StreamerID:  123456,
				DateStart:   "2020-11-13",
				DateEnd:     "2022-11-13",
				Exclusive:   0,
				PayToParent: 0,
				Others:      "",
				Region:      "TW",
				Terms:       []*ctModel.ContractTerm{{}},
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{
					ctModel.PayoutCategory_REVENUE: {
						Id: 123,
					},
				},
			},
			func() {
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
			},
			ErrContractMissProfitSharing,
		},
		{
			"invalid agent region",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:     12345,
				StreamerID:  123456,
				DateStart:   "2020-11-13",
				DateEnd:     "2022-11-13",
				Exclusive:   0,
				PayToParent: 0,
				Others:      "",
				Region:      "TW",
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{
					ctModel.PayoutCategory_REVENUE: {
						Id: 123,
					},
				},
			},
			func() {
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "GLOBAL"}, nil).Times(1)
			},
			ErrInvalidAgentRegion,
		},
		{
			"invalid agencyID",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:     12345,
				StreamerID:  123456,
				DateStart:   "2020-11-13",
				DateEnd:     "2022-11-13",
				Exclusive:   0,
				PayToParent: 0,
				Others:      "",
				Region:      "TW",
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				AgencyID: 69,
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{
					ctModel.PayoutCategory_REVENUE: {
						Id: 123,
					},
				},
			},
			func() {
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Times(1)
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{69}).Return([]*ctModel.LegacyAgency{}, nil).Once()
			},
			ErrInvalidAgencyAdminID,
		},
		{
			"normal",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:     12345,
				StreamerID:  123456,
				DateStart:   "2020-11-13",
				DateEnd:     "2022-11-13",
				Exclusive:   0,
				PayToParent: 0,
				Others:      "",
				Region:      "TW",
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				AgencyID:       69,
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
			},
			func() {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Times(2)
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{69}).Return([]*ctModel.LegacyAgency{
					{
						Id:      1,
						AdminID: 69,
					},
				}, nil).Once()
				cs.mockUser.On("SetRegionInternal", mock.AnythingOfType("ctx.CTX"), mock.Anything, mock.Anything).Return(nil).Once()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockContractApplication.On("FinishContractApplication", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), int32(12345), mock.AnythingOfType("int32"), mock.AnythingOfType("string")).Return(nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
			},
			nil,
		},
		{
			"normal with BBFu payout type",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:     12345,
				StreamerID:  1234567,
				DateStart:   "2020-11-13",
				DateEnd:     "2022-11-13",
				Exclusive:   0,
				PayToParent: 0,
				Others:      "",
				Region:      "TW",
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{
					ctModel.PayoutCategory_REVENUE: {
						Id: mockBBRichPayoutAccountID,
					},
				},
			},
			func() {
				mockUser := models.User{UserID: "for delete and update", IsAllowedForBBFu: 0}
				mockPatch := map[string]interface{}{keys.FieldIsAllowedForBBFu.String(): 1}
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Times(2)
				cs.mockUser.On("GetUsers", mock.AnythingOfType("ctx.CTX"), "for delete and update").Return([]models.User{mockUser}, nil).Once()
				cs.mockUser.On("Update", mock.AnythingOfType("ctx.CTX"), &mockUser, mockPatch).Return(nil).Once()
				cs.mockUser.On("SetRegionInternal", mock.AnythingOfType("ctx.CTX"), mock.Anything, mock.Anything).Return(nil).Once()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockBBRichPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockBBRichPayoutAccount}, nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					Owners: &payoutaccount.StreamerOwners{
						UserIDs: []string{mockBBRichPayoutAccount.OwnerID},
					},
					AccountType: paModel.AccountType_LOCAL_ACCOUNT,
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockContractApplication.On("FinishContractApplication", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), int32(12345), mock.AnythingOfType("int32"), mock.AnythingOfType("string")).Return(nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
			},
			nil,
		},
		{
			"create unofficial",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:      12345,
				StreamerID:   123456,
				DateStart:    "2028-11-13",
				DateEnd:      "2030-11-12",
				Exclusive:    0,
				PayToParent:  0,
				Others:       "",
				Region:       "TW",
				IsUnofficial: 1,
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
			},
			func() {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Times(2)
				cs.mockUser.On("SetRegionInternal", mock.AnythingOfType("ctx.CTX"), mock.Anything, mock.Anything).Return(nil).Once()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockContractApplication.On("FinishContractApplication", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), int32(12345), mock.AnythingOfType("int32"), mock.AnythingOfType("string")).Return(nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
			},
			nil,
		},
		{
			"create unofficial",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:      12345,
				StreamerID:   123456,
				DateStart:    "2030-11-13",
				DateEnd:      "2032-11-13",
				Exclusive:    0,
				PayToParent:  0,
				Others:       "",
				Region:       "TW",
				IsUnofficial: 1,
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
			},
			func() {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Times(2)
				cs.mockUser.On("SetRegionInternal", mock.AnythingOfType("ctx.CTX"), mock.Anything, mock.Anything).Return(nil).Once()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockContractApplication.On("FinishContractApplication", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), int32(12345), mock.AnythingOfType("int32"), mock.AnythingOfType("string")).Return(nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
			},
			nil,
		},
		{
			"normal, finish application not found ongoing application",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:     12345,
				StreamerID:  123456,
				DateStart:   "2020-11-13",
				DateEnd:     "2022-11-13",
				Exclusive:   0,
				PayToParent: 0,
				Others:      "",
				Region:      "TW",
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
			},
			func() {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Times(2)
				cs.mockUser.On("SetRegionInternal", mock.AnythingOfType("ctx.CTX"), mock.Anything, mock.Anything).Return(nil).Once()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockContractApplication.On("FinishContractApplication", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), int32(12345), mock.AnythingOfType("int32"), mock.AnythingOfType("string")).Return(contractapplication.ErrNoOngoingApplication).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
			},
			nil,
		},
		{
			"failed, finish application has error",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:     12345,
				StreamerID:  123456,
				DateStart:   "2020-11-13",
				DateEnd:     "2022-11-13",
				Exclusive:   0,
				PayToParent: 0,
				Others:      "",
				Region:      "TW",
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
			},
			func() {
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Times(2)
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockContractApplication.On("FinishContractApplication", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), int32(12345), mock.AnythingOfType("int32"), mock.AnythingOfType("string")).Return(fmt.Errorf("failed")).Once()
			},
			fmt.Errorf("failed"),
		},
		{
			"create overlap contract",
			[]int{999},
			999,
			&ctModel.Contract{
				AgentID:      999,
				StreamerID:   1234567,
				DateStart:    "2019-04-01",
				DateEnd:      "2020-03-31",
				Exclusive:    0,
				PayToParent:  0,
				Others:       "",
				Region:       "JP",
				IsUnofficial: 1,
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
			},
			func() {
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "JP"}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "JP").Return("Asia/Tokyo", nil).Once()
			},
			ErrContractOverlap,
		},
		{
			"streamer not found",
			[]int{12345},
			12345,
			&ctModel.Contract{
				AgentID:     12345,
				StreamerID:  7788,
				DateStart:   "2020-11-13",
				DateEnd:     "2022-11-13",
				Exclusive:   0,
				PayToParent: 0,
				Others:      "",
				Region:      "TW",
				Terms: []*ctModel.ContractTerm{{
					Condition: "abc",
					Gte:       1,
					Lt:        -1,
					Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
					Value:     2,
					DateStart: "2020-11-13",
					DateEnd:   "2022-11-13",
				}},
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
			},
			func() {
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Times(1)
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
			},
			sql.ErrNoRows,
		},
	}

	for _, test := range tests {
		cs.Run(test.Desc, func() {
			mockCTX.Info(test.Desc)
			cs.TearDownTest()
			cs.SetupTest()
			cs.mockIntraLog.On("Log", mock.AnythingOfType("ctx.CTX"), intralog.TableContract, mock.Anything, mock.Anything, "", intralog.ActionCreate, mock.Anything, mock.Anything).Return(nil).Maybe()

			test.MockFunc()

			_, err := cs.Store.Create(mockCTX, test.adminIDs, test.adminID, test.contractInit)
			cs.Equal(test.ExpError, err)
		})
	}
}

func (cs *ContractSuite) TestBatchCreate() {
	tests := []struct {
		Desc      string
		AdminIDs  []int
		AdminID   int
		Contracts []*ctModel.Contract
		MockFunc  func()
		ExpError  error
	}{
		{
			"No permission",
			[]int{},
			0,
			[]*ctModel.Contract{},
			func() {
			},
			ErrNoPermission,
		},
		{
			"normal",
			[]int{12345},
			12345,
			[]*ctModel.Contract{
				{
					AgentID:     12345,
					StreamerID:  123456,
					DateStart:   "2020-11-13",
					DateEnd:     "2022-11-13",
					Exclusive:   0,
					PayToParent: 0,
					Others:      "",
					Region:      "TW",
					Terms: []*ctModel.ContractTerm{{
						Condition: "abc",
						Gte:       1,
						Lt:        -1,
						Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
						Value:     2,
						DateStart: "2020-11-13",
						DateEnd:   "2022-11-13",
					}},
					PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
				},
				{
					AgentID:      12345,
					StreamerID:   123456,
					DateStart:    "2030-11-13",
					DateEnd:      "2032-11-13",
					Exclusive:    0,
					PayToParent:  0,
					Others:       "",
					Region:       "TW",
					IsUnofficial: 1,
					Terms: []*ctModel.ContractTerm{{
						Condition: "abc",
						Gte:       1,
						Lt:        -1,
						Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
						Value:     2,
						DateStart: "2020-11-13",
						DateEnd:   "2022-11-13",
					}},
					PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
				},
			},
			func() {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Times(4)
				cs.mockUser.On("SetRegionInternal", mock.AnythingOfType("ctx.CTX"), mock.Anything, mock.Anything).Return(nil).Times(2)
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Times(2)
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Times(2)
				cs.mockContractApplication.On("FinishContractApplication", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), int32(12345), mock.AnythingOfType("int32"), mock.AnythingOfType("string")).Return(nil).Twice()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Twice()
			},
			nil,
		},
		{
			"create with overlap contract",
			[]int{12345},
			12345,
			[]*ctModel.Contract{
				{
					AgentID:     12345,
					StreamerID:  123456,
					DateStart:   "2020-11-13",
					DateEnd:     "2022-11-13",
					Exclusive:   0,
					PayToParent: 0,
					Others:      "",
					Region:      "TW",
					Terms: []*ctModel.ContractTerm{{
						Condition: "abc",
						Gte:       1,
						Lt:        -1,
						Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
						Value:     2,
						DateStart: "2020-11-13",
						DateEnd:   "2022-11-13",
					}},
					PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
				},
				{
					AgentID:      12345,
					StreamerID:   123456,
					DateStart:    "2022-11-01",
					DateEnd:      "2023-11-01",
					Exclusive:    0,
					PayToParent:  0,
					Others:       "",
					Region:       "TW",
					IsUnofficial: 1,
					Terms: []*ctModel.ContractTerm{{
						Condition: "abc",
						Gte:       1,
						Lt:        -1,
						Prop:      ctModel.ContractTermType_PERMILLE_SUBRUN.String(),
						Value:     2,
						DateStart: "2020-11-13",
						DateEnd:   "2022-11-13",
					}},
					PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
				},
			},
			func() {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 12345).Return(&intra.User{Region: "TW"}, nil).Times(3)
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Times(2)
				cs.mockContractApplication.On("FinishContractApplication", mock.AnythingOfType("ctx.CTX"), mock.AnythingOfType("*sqlx.Tx"), int32(12345), mock.AnythingOfType("int32"), mock.AnythingOfType("string")).Return(nil).Once()
			},
			ErrContractOverlap,
		},
	}

	for _, test := range tests {
		cs.TearDownTest()
		cs.SetupTest()
		cs.mockIntraLog.On("Log", mock.AnythingOfType("ctx.CTX"), intralog.TableContract, mock.Anything, mock.Anything, "", intralog.ActionCreate, mock.Anything, mock.Anything).Return(nil).Maybe()

		test.MockFunc()

		ids, _, err := cs.Store.BatchCreate(mockCTX, test.AdminIDs, test.AdminID, test.Contracts)
		cs.Equal(test.ExpError, err, test.Desc)
		if test.ExpError == nil {
			cs.Equal(len(test.Contracts), len(ids), test.Desc)
		}
	}
}

func (cs *ContractSuite) TestList() {
	bsonUser := map[string]interface{}{
		"userID": "1af4cba1-59de-45d0-b3cc-719c58eef0d7",
		"openID": "testOpenID1",
	}
	err := cs.query.Upsert(mockCTX, keys.TabUser, bson.M{"userID": "1af4cba1-59de-45d0-b3cc-719c58eef0d7"}, bsonUser)
	cs.Require().NoError(err)

	mockExecutorID := 66
	t := time.Date(2018, time.February, 25, 0, 0, 0, 0, time.UTC)
	future1 := time.Date(2019, time.March, 25, 0, 0, 0, 0, time.UTC)
	future2 := time.Date(2019, time.April, 1, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		Desc         string
		agentID      []int
		filter       string
		page         int
		limit        int
		outDatedDays int
		Now          *time.Time
		ExpRes       []int
		ExpTotal     int
		ExpError     error
	}{
		{
			Desc:     "no permission",
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{},
			ExpTotal: 0,
			ExpError: ErrNoPermission,
		},
		{
			Desc:     "empty as agentID",
			agentID:  []int{10000},
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{},
			ExpTotal: 0,
			ExpError: nil,
		},
		{
			Desc:     "empty as time",
			agentID:  []int{119},
			page:     0,
			limit:    10,
			Now:      &future2,
			ExpRes:   []int{},
			ExpTotal: 0,
			ExpError: nil,
		},
		{
			Desc:     "list one 1",
			agentID:  []int{119},
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{2},
			ExpTotal: 1,
			ExpError: nil,
		},
		{
			Desc:     "list three",
			agentID:  []int{107},
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{3, 1, 4},
			ExpTotal: 3,
			ExpError: nil,
		},
		{
			Desc:     "list first page",
			agentID:  []int{107},
			page:     0,
			limit:    1,
			Now:      &t,
			ExpRes:   []int{3},
			ExpTotal: 3,
			ExpError: nil,
		},
		{
			Desc:     "list second page",
			agentID:  []int{107},
			page:     1,
			limit:    1,
			Now:      &t,
			ExpRes:   []int{1},
			ExpTotal: 3,
			ExpError: nil,
		},
		{
			Desc:     "list last page",
			agentID:  []int{107},
			page:     2,
			limit:    1,
			Now:      &t,
			ExpRes:   []int{4},
			ExpTotal: 3,
			ExpError: nil,
		},
		{
			Desc:     "list wrong page",
			agentID:  []int{107},
			page:     3,
			limit:    1,
			Now:      &t,
			ExpRes:   []int{},
			ExpTotal: 3,
			ExpError: nil,
		},
		{
			Desc:         "list nearOutDated",
			agentID:      []int{107},
			page:         0,
			limit:        10,
			outDatedDays: 7,
			Now:          &future1,
			ExpRes:       []int{6},
			ExpTotal:     1,
			ExpError:     nil,
		},
		{
			Desc:         "list nearOutDated with filter",
			agentID:      []int{107},
			filter:       "6",
			page:         0,
			limit:        10,
			outDatedDays: 7,
			Now:          &future1,
			ExpRes:       []int{6},
			ExpTotal:     1,
			ExpError:     nil,
		},
		{
			Desc:         "list nearOutDated with filter",
			agentID:      []int{107},
			filter:       "6",
			page:         0,
			limit:        10,
			outDatedDays: 7,
			Now:          &future1,
			ExpRes:       []int{6},
			ExpTotal:     1,
			ExpError:     nil,
		},
		{
			Desc:     "list filter id",
			agentID:  []int{107},
			filter:   "4",
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{1, 4},
			ExpTotal: 2,
			ExpError: nil,
		},
		{
			Desc:     "list filter others",
			agentID:  []int{107},
			filter:   "當月進月榜",
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{3, 4},
			ExpTotal: 2,
			ExpError: nil,
		},
		{
			Desc:     "list filter streamer name",
			agentID:  []int{107},
			filter:   "momoisgood4",
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{4},
			ExpTotal: 1,
			ExpError: nil,
		},
		{
			Desc:     "list filter streamer idCardNumber",
			agentID:  []int{107},
			filter:   "C226861159",
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{3},
			ExpTotal: 1,
			ExpError: nil,
		},
		{
			Desc:     "list filter userID",
			agentID:  []int{107},
			filter:   "1af4cba1-59de-45d0-b3cc-719c58eef0d7",
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{3},
			ExpTotal: 1,
			ExpError: nil,
		},
		{
			Desc:     "list filter openID",
			agentID:  []int{107},
			filter:   "OpenID1",
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{3},
			ExpTotal: 1,
			ExpError: nil,
		},
		{
			Desc:     "list filter admin name 1",
			agentID:  []int{107},
			filter:   "Teddy Lu",
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{3, 1, 4},
			ExpTotal: 3,
			ExpError: nil,
		},
		{
			Desc:     "list filter admin name 2",
			agentID:  []int{107},
			filter:   "Jenny",
			page:     0,
			limit:    10,
			Now:      &t,
			ExpRes:   []int{},
			ExpTotal: 0,
			ExpError: nil,
		},
	}

	for _, test := range tests {
		cs.TearDownTest()
		cs.SetupTest()
		if test.agentID != nil {
			cs.mockFuncs.On("TimeNow").Return(*test.Now)
		}

		list, total, err := cs.Store.List(mockCTX, mockExecutorID, test.agentID, test.filter, test.page, test.limit, test.outDatedDays)
		cs.Require().Equal(test.ExpError, err, test.Desc)
		cs.Require().Equal(len(test.ExpRes), len(list), test.Desc)
		cs.Equal(test.ExpTotal, total, test.Desc)
		for i, c := range list {
			cs.Equal(test.ExpRes[i], c, test.Desc)
		}
	}
}

func (cs *ContractSuite) TestListByStreamer() {
	tests := []struct {
		Desc       string
		agentID    []int
		executorID int
		streamerID int
		ExpRes     []int
		ExpError   error
	}{
		{
			Desc:       "no permission",
			streamerID: 33,
			ExpRes:     []int{},
			ExpError:   ErrNoPermission,
		},
		{
			Desc:       "empty",
			agentID:    []int{59},
			executorID: 59,
			streamerID: 33,
			ExpRes:     []int{},
			ExpError:   nil,
		},
		{
			Desc:       "list",
			agentID:    []int{60},
			executorID: 60,
			streamerID: 33,
			ExpRes:     []int{3, 5, 6, 13},
			ExpError:   nil,
		},
	}

	for _, test := range tests {
		list, err := cs.Store.ListByStreamer(mockCTX, test.agentID, test.executorID, test.streamerID)
		cs.Equal(test.ExpError, err, test.Desc)
		cs.Equal(test.ExpRes, list, test.Desc)
	}
}

func (cs *ContractSuite) TestFind() {
	result := []*ctModel.Contract{
		{
			Id:               1,
			Type:             0,
			AgentID:          107,
			AgencyID:         107,
			StreamerID:       777,
			ParentContractID: 0,
			UserID:           "8de87814-05e3-4d9b-bb70-b70543b87fa9",
			Birthday:         "1988-05-01",
			DateStart:        "2017-04-01",
			DateEnd:          "2018-03-31",
			StartTime:        **********,
			EndTime:          **********,
			Exclusive:        0,
			PayToParent:      1,
			Others:           "0401-0630 保底61,200免拆帳,30hr 0401-0630 分潤102,264免拆帳",
			IsDeleted:        0,
			Region:           "TW",
			IsUnofficial:     0,
			Timezone:         "Asia/Taipei",
			Terms: []*ctModel.ContractTerm{
				{
					Id:           1,
					ContractID:   1,
					Prop:         "HOURLY_WAGE",
					Value:        1000,
					ContractType: ctModel.StreamerContractType,
				},
				{
					Id:           2,
					ContractID:   1,
					Prop:         "MAX_PAID_HOURS",
					Value:        30,
					ContractType: ctModel.StreamerContractType,
				},
				{
					Id:           3,
					ContractID:   1,
					Condition:    "STREAMED_HOURS",
					Gte:          0,
					Lt:           10,
					Prop:         "PERCENT_WAGE_RATIO",
					Value:        0,
					ContractType: ctModel.StreamerContractType,
				},
				{
					Id:           4,
					ContractID:   1,
					Condition:    "STREAMED_HOURS",
					Gte:          10,
					Lt:           30,
					Prop:         "PERCENT_WAGE_RATIO",
					Value:        100,
					ContractType: ctModel.StreamerContractType,
				},
				{
					Id:           5,
					ContractID:   1,
					Condition:    "STREAMED_HOURS",
					Gte:          30,
					Lt:           -1,
					Prop:         "PERCENT_WAGE_RATIO",
					Value:        100,
					ContractType: ctModel.StreamerContractType,
				},
				{
					Id:           6,
					ContractID:   1,
					Prop:         "PERMILLE_SUBRUN",
					Value:        600,
					ContractType: ctModel.StreamerContractType,
				},
			},
			PayoutType:     2,
			FeeOwnerType:   2, // "User",
			Payment:        "payment",
			PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
		},
		{
			Id:               2,
			Type:             0,
			AgentID:          119,
			StreamerID:       255,
			ParentContractID: 0,
			UserID:           "408ffd58-bc4f-4c03-abc4-3c095d21d424",
			Birthday:         "",
			DateStart:        "2017-04-01",
			DateEnd:          "2019-03-31",
			Exclusive:        1,
			PayToParent:      0,
			Others:           "",
			IsDeleted:        0,
			Region:           "TW",
			IsUnofficial:     0,
			Timezone:         "Asia/Taipei",
			Terms:            []*ctModel.ContractTerm{},
			FeeOwnerType:     2, // "User",
			PayoutAccounts:   map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
		},
		{
			Id:               3,
			Type:             0,
			AgentID:          107,
			AgencyID:         107,
			StreamerID:       33,
			ParentContractID: 0,
			UserID:           "1af4cba1-59de-45d0-b3cc-719c58eef0d7",
			Birthday:         "1988-04-03",
			DateStart:        "2017-04-01",
			DateEnd:          "2018-03-31",
			StartTime:        **********,
			EndTime:          **********,
			Exclusive:        0,
			PayToParent:      1,
			Others:           "當月進月榜前50名 發20,000元新台幣",
			IsDeleted:        0,
			Region:           "TW",
			IsUnofficial:     0,
			Timezone:         "Asia/Taipei",
			Terms: []*ctModel.ContractTerm{
				{
					Id:           7,
					ContractID:   3,
					Condition:    "CURRENT_POINTS",
					Gte:          0,
					Lt:           15000,
					Prop:         "PERMILLE_SUBRUN",
					Value:        400,
					ContractType: ctModel.StreamerContractType,
				},
				{
					Id:           8,
					ContractID:   3,
					Condition:    "CURRENT_POINTS",
					Gte:          15000,
					Lt:           30000,
					Prop:         "PERMILLE_SUBRUN",
					Value:        500,
					ContractType: ctModel.StreamerContractType,
				},
				{
					Id:           9,
					ContractID:   3,
					Condition:    "CURRENT_POINTS",
					Gte:          30000,
					Lt:           -1,
					Prop:         "PERMILLE_SUBRUN",
					Value:        600,
					ContractType: ctModel.StreamerContractType,
				},
			},
			FeeOwnerType:   2, //"User",
			PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
		},
		{
			Id:               4,
			Type:             0,
			AgentID:          107,
			AgencyID:         107,
			StreamerID:       34,
			ParentContractID: 0,
			UserID:           "aaaaaaaa-59de-45d0-b3cc-719c58eef0d7",
			Birthday:         "88-04-03",
			DateStart:        "2017-04-01",
			DateEnd:          "2018-02-28",
			StartTime:        **********,
			EndTime:          **********,
			Exclusive:        1,
			PayToParent:      1,
			Others:           "當月進月榜前50名 發20,000元新台幣",
			IsDeleted:        0,
			Region:           "TW",
			IsUnofficial:     0,
			Timezone:         "Asia/Taipei",
			Terms:            []*ctModel.ContractTerm{},
			FeeOwnerType:     2, // "User",
			PayoutAccounts:   map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
		},
		{
			Id:               5,
			Type:             0,
			AgentID:          107,
			AgencyID:         107,
			StreamerID:       33,
			ParentContractID: 0,
			UserID:           "1af4cba1-59de-45d0-b3cc-719c58eef0d7",
			Birthday:         "1988-04-03",
			DateStart:        "2016-04-01",
			DateEnd:          "2017-03-31",
			StartTime:        **********,
			EndTime:          **********,
			Exclusive:        0,
			PayToParent:      1,
			Others:           "當月進月榜前50名 發20,000元新台幣",
			IsDeleted:        0,
			Region:           "TW",
			IsUnofficial:     0,
			Timezone:         "Asia/Taipei",
			Terms:            []*ctModel.ContractTerm{},
			FeeOwnerType:     2, //"User",
			PayoutAccounts:   map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
		},
		{
			Id:               6,
			Type:             0,
			AgentID:          107,
			AgencyID:         107,
			StreamerID:       33,
			ParentContractID: 0,
			UserID:           "1af4cba1-59de-45d0-b3cc-719c58eef0d7",
			Birthday:         "1988-04-03",
			DateStart:        "2018-04-01",
			DateEnd:          "2019-03-31",
			StartTime:        **********,
			EndTime:          **********,
			Exclusive:        1,
			PayToParent:      1,
			Others:           "當月進月榜前50名 發20,000元新台幣",
			IsDeleted:        0,
			Region:           "TW",
			IsUnofficial:     0,
			Timezone:         "Asia/Taipei",
			Terms:            []*ctModel.ContractTerm{},
			FeeOwnerType:     2, //"User",
			PayoutAccounts:   map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
		},
		{
			Id:               13,
			Type:             0,
			AgentID:          107,
			AgencyID:         107,
			StreamerID:       33,
			ParentContractID: 0,
			UserID:           "1af4cba1-59de-45d0-b3cc-719c58eef0d7",
			Birthday:         "1988-04-03",
			DateStart:        "2018-04-01",
			DateEnd:          "2019-03-31",
			StartTime:        **********,
			EndTime:          **********,
			Exclusive:        1,
			PayToParent:      1,
			Others:           "當月進月榜前50名 發20,000元新台幣",
			IsDeleted:        1,
			Region:           "TW",
			IsUnofficial:     0,
			Timezone:         "Asia/Taipei",
			Terms:            []*ctModel.ContractTerm{},
			FeeOwnerType:     2, //"User",
			PayoutAccounts:   map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
		},
	}

	tests := []struct {
		Desc       string
		agentID    []int
		executorID int
		contractID []int
		ExpRes     []*ctModel.Contract
		ExpError   error
	}{
		{
			Desc:       "no permission 1",
			contractID: []int{1, 2, 3, 4},
			ExpRes:     []*ctModel.Contract{},
			ExpError:   ErrNoPermission,
		},
		{
			Desc:       "no permission 2",
			agentID:    []int{107},
			executorID: 107,
			contractID: []int{2},
			ExpRes:     []*ctModel.Contract{},
			ExpError:   nil,
		},
		{
			Desc:       "empty",
			agentID:    []int{107},
			executorID: 107,
			contractID: []int{10000},
			ExpRes:     []*ctModel.Contract{},
			ExpError:   nil,
		},
		{
			Desc:       "find one 1",
			agentID:    []int{107},
			executorID: 107,
			contractID: []int{3},
			ExpRes:     []*ctModel.Contract{result[2]},
			ExpError:   nil,
		},
		{
			Desc:       "find one 1 in date end",
			agentID:    []int{107},
			executorID: 107,
			contractID: []int{3},
			ExpRes:     []*ctModel.Contract{result[2]},
			ExpError:   nil,
		},
		{
			Desc:       "find one 2",
			agentID:    []int{107},
			executorID: 107,
			contractID: []int{4},
			ExpRes:     []*ctModel.Contract{result[3]},
			ExpError:   nil,
		},
		{
			Desc:       "find old one",
			agentID:    []int{107},
			executorID: 107,
			contractID: []int{5},
			ExpRes:     []*ctModel.Contract{result[4]},
			ExpError:   nil,
		},
		{
			Desc:       "find new one",
			agentID:    []int{107},
			executorID: 107,
			contractID: []int{6},
			ExpRes:     []*ctModel.Contract{result[5]},
			ExpError:   nil,
		},
		{
			Desc:       "find deleted one",
			agentID:    []int{107},
			executorID: 107,
			contractID: []int{13},
			ExpRes:     []*ctModel.Contract{result[6]},
			ExpError:   nil,
		},
		{
			Desc:       "find three",
			agentID:    []int{107},
			executorID: 107,
			contractID: []int{1, 3, 4},
			ExpRes:     []*ctModel.Contract{result[0], result[2], result[3]},
			ExpError:   nil,
		},
		{
			Desc:       "find three reverse order",
			agentID:    []int{107},
			executorID: 107,
			contractID: []int{4, 3, 1},
			ExpRes:     []*ctModel.Contract{result[3], result[2], result[0]},
			ExpError:   nil,
		},
	}

	for _, test := range tests {
		contracts, err := cs.Store.Find(mockCTX, test.agentID, test.executorID, test.contractID)
		cs.Require().Equal(test.ExpError, err, test.Desc)
		cs.Require().Equal(len(test.ExpRes), len(contracts), test.Desc)
		for i, c := range contracts {
			cs.Equal(test.ExpRes[i], c, test.Desc)
		}
	}
}

func (cs *ContractSuite) TestUpdate() {
	updateInvalidDate := *mockContract8
	updateInvalidDate.DateStart = "2019-04-01"

	updateMissTerm := *mockContract8
	updateV1 := *mockContract8
	updateV1.HideIncomeReport = true
	updateV1.PayoutType = 1
	updateV1.FeeOwnerType = 1 //"17Media"
	updateV1.Terms = []*ctModel.ContractTerm{
		{
			ContractID: 10000,
			Condition:  "",
			Gte:        0,
			Lt:         0,
			Prop:       "PERMILLE_SUBRUN",
			Value:      300,
		},
	}

	updateV2 := updateV1
	updateV2.Terms = []*ctModel.ContractTerm{
		{
			ContractID: 10000,
			Condition:  "",
			Gte:        0,
			Lt:         0,
			Prop:       "PERMILLE_SUBRUN",
			Value:      300,
		},
		{
			ContractID: 10000,
			Condition:  "POINTS",
			Gte:        0,
			Lt:         300000,
			Prop:       "BONUS",
			Value:      0,
		},
	}

	updateV3 := updateV2
	updateV3.Terms = []*ctModel.ContractTerm{
		{
			ContractID: 10000,
			Condition:  "POINTS",
			Gte:        0,
			Lt:         300000,
			Prop:       "BONUS",
			Value:      0,
		},
		{
			ContractID: 10000,
			Condition:  "POINTS",
			Gte:        300000,
			Lt:         500000,
			Prop:       "BONUS",
			Value:      20000,
		},
		{
			ContractID: 10000,
			Condition:  "POINTS",
			Gte:        500000,
			Lt:         1000000,
			Prop:       "BONUS",
			Value:      25000,
		},
		{
			ContractID: 10000,
			Condition:  "POINTS",
			Gte:        1000000,
			Lt:         -1,
			Prop:       "BONUS",
			Value:      30000,
		},
		{
			ContractID: 10000,
			Condition:  "",
			Gte:        0,
			Lt:         0,
			Prop:       "PERMILLE_SUBRUN",
			Value:      300,
		},
	}

	updateV4 := *mockContract8
	updateV4.PayoutType = 0
	updateV4.Terms = []*ctModel.ContractTerm{
		{
			ContractID: 10000,
			Condition:  "",
			Gte:        0,
			Lt:         0,
			Prop:       "PERMILLE_SUBRUN",
			Value:      300,
		},
	}

	updateV5 := *mockContract8
	updateV5.PayoutAccounts = map[ctModel.PayoutCategory]*paModel.PayoutAccount{
		ctModel.PayoutCategory_REVENUE: {
			Id: mockBBRichPayoutAccountID,
		},
	}
	updateV5.Terms = []*ctModel.ContractTerm{
		{
			ContractID: 10000,
			Condition:  "",
			Gte:        0,
			Lt:         0,
			Prop:       "PERMILLE_SUBRUN",
			Value:      300,
		},
	}

	updateV6 := *mockContract
	updateV6.Payment = ""
	updateV6.Terms = []*ctModel.ContractTerm{
		{
			ContractID: 10000,
			Condition:  "",
			Gte:        0,
			Lt:         0,
			Prop:       "PERMILLE_SUBRUN",
			Value:      300,
		},
	}

	updateV7 := *mockContract
	updateV7.DateEnd = "2018-05-31"
	updateV7.EndTime = **********
	updateV7.Terms = []*ctModel.ContractTerm{
		{
			ContractID: 10000,
			Condition:  "",
			Gte:        0,
			Lt:         0,
			Prop:       "PERMILLE_SUBRUN",
			Value:      300,
		},
	}

	invalidAgencyIDContract := *mockContract
	invalidAgencyIDContract.AgencyID = 69

	tests := []struct {
		Desc     string
		AgentID  []int
		AdminID  int
		Contract *ctModel.Contract
		MockFunc func()
		ExpError error
		ExpRes   *ctModel.Contract
	}{
		{
			Desc:     "no permission",
			Contract: mockContract8,
			MockFunc: func() {
			},
			ExpError: ErrNoPermission,
		},
		{
			Desc:    "no permission to set agent",
			AgentID: []int{107},
			Contract: &ctModel.Contract{
				Id:               5,
				Type:             0,
				AgentID:          999,
				AgencyID:         107,
				StreamerID:       33,
				ParentContractID: 0,
				UserID:           "1af4cba1-59de-45d0-b3cc-719c58eef0d7",
				DateStart:        "2016-04-01",
				DateEnd:          "2017-03-31",
				Exclusive:        0,
				PayToParent:      1,
				Others:           "當月進月榜前50名 發20,000元新台幣",
				Attachment:       "",
				IsDeleted:        0,
				Region:           "TW",
				IsUnofficial:     0,
				Birthday:         "",
				Timezone:         "Asia/Taipei",
				StartTime:        **********,
				EndTime:          **********,
				FeeOwnerType:     2, // "User",
			},
			MockFunc: func() {
			},
			ExpError: ErrNoPermissionToSetAgent,
		},
		{
			Desc:     "empty as agentID",
			AgentID:  []int{999},
			Contract: &ctModel.Contract{Id: 10000, AgentID: 12345},
			MockFunc: func() {
			},
			ExpError: ErrContractNotFound,
		},
		{
			Desc:     "update with invalid date",
			AgentID:  []int{999},
			Contract: &updateInvalidDate,
			MockFunc: func() {
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "TW"}, nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{999}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
			},
			ExpError: ErrInvalidDate,
		},
		{
			Desc:     "invalid agencyID",
			AgentID:  []int{107},
			Contract: &invalidAgencyIDContract,
			MockFunc: func() {
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()

				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 107).Return(&intra.User{Region: "TW"}, nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{69}).Return([]*ctModel.LegacyAgency{}, nil).Once()
			},
			ExpError: ErrInvalidAgencyAdminID,
		},
		{
			Desc:     "ErrContractMissProfitSharing",
			AgentID:  []int{999},
			Contract: &updateMissTerm,
			MockFunc: func() {
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "TW"}, nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()

				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{999}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
			},
			ExpError: ErrContractMissProfitSharing,
		},
		{
			Desc:     "update except terms",
			AgentID:  []int{999},
			Contract: &updateV1,
			MockFunc: func() {
				mockNow := time.Date(2018, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "TW"}, nil).Twice()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{999}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()

				cs.mockUser.On("GetUsers", anyCTX, "for delete and update").Return([]models.User{
					{UserID: "for delete and update", Region: "TW"},
				}, nil).Once()
			},
			ExpError: nil,
			ExpRes:   &updateV1,
		},
		{
			Desc:     "update add terms",
			AgentID:  []int{999},
			Contract: &updateV2,
			MockFunc: func() {
				mockNow := time.Date(2018, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "TW"}, nil).Twice()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{999}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
				cs.mockUser.On("GetUsers", anyCTX, "for delete and update").Return([]models.User{
					{UserID: "for delete and update", Region: "TW"},
				}, nil).Once()
			},
			ExpError: nil,
			ExpRes:   &updateV2,
		},
		{
			Desc:     "update add and remove terms",
			AgentID:  []int{999},
			Contract: &updateV3,
			MockFunc: func() {
				mockNow := time.Date(2018, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "TW"}, nil).Twice()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{999}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
				cs.mockUser.On("GetUsers", anyCTX, "for delete and update").Return([]models.User{
					{UserID: "for delete and update", Region: "TW"},
				}, nil).Once()
			},
			ExpError: nil,
			ExpRes:   &updateV3,
		},
		{
			Desc:     "update with BBFu payout account",
			AgentID:  []int{999},
			Contract: &updateV5,
			MockFunc: func() {
				mockNow := time.Date(2018, 3, 30, 0, 0, 0, 0, time.UTC)
				mockUser := models.User{UserID: "for delete and update", IsAllowedForBBFu: 0}
				mockPatch := map[string]interface{}{keys.FieldIsAllowedForBBFu.String(): 1}
				cs.mockFuncs.On("TimeNow").Return(mockNow)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "TW"}, nil).Twice()
				cs.mockUser.On("GetUsers", mock.AnythingOfType("ctx.CTX"), "for delete and update").Return([]models.User{mockUser}, nil).Once()
				cs.mockUser.On("Update", mock.AnythingOfType("ctx.CTX"), &mockUser, mockPatch).Return(nil).Once()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockBBRichPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockBBRichPayoutAccount}, nil).Times(2)
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					Owners: &payoutaccount.StreamerOwners{
						UserIDs: []string{mockBBRichPayoutAccount.OwnerID},
					},
					AccountType: paModel.AccountType_LOCAL_ACCOUNT,
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Times(2)
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{999}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
				cs.mockUser.On("GetUsers", anyCTX, "for delete and update").Return([]models.User{
					{UserID: "for delete and update", Region: "TW"},
				}, nil).Once()
			},
			ExpError: nil,
			ExpRes:   &updateV5,
		},
		{
			Desc:     "update remove label",
			AgentID:  []int{107},
			AdminID:  10,
			Contract: &updateV6,
			MockFunc: func() {
				mockNow := time.Date(2018, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 107).Return(&intra.User{Region: "TW"}, nil).Twice()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{107}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
				cs.mockUser.On("GetUsers", anyCTX, "8de87814-05e3-4d9b-bb70-b70543b87fa9").Return([]models.User{
					{UserID: "8de87814-05e3-4d9b-bb70-b70543b87fa9", Region: "TW"},
				}, nil).Once()
			},
			ExpError: nil,
			ExpRes:   &updateV6,
		},
		{
			Desc:     "update contract date range",
			AgentID:  []int{107},
			AdminID:  10,
			Contract: &updateV7,
			MockFunc: func() {
				mockNow := time.Date(2018, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 107).Return(&intra.User{Region: "TW"}, nil).Twice()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{107}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
				cs.mockPublisher.On("Publish", mock.AnythingOfType("ctx.CTX"), accountingModel.ContractDateChangedInfo{ContractID: updateV7.Id}).Return(nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
				cs.mockUser.On("GetUsers", anyCTX, "8de87814-05e3-4d9b-bb70-b70543b87fa9").Return([]models.User{
					{UserID: "8de87814-05e3-4d9b-bb70-b70543b87fa9", Region: "TW"},
				}, nil).Once()
			},
			ExpError: nil,
			ExpRes:   &updateV7,
		},
	}

	for _, test := range tests {
		cs.Run(test.Desc, func() {
			cs.TearDownTest()
			cs.SetupTest()
			test.MockFunc()
			cs.mockFuncs.On("TimeNow").Return(time.Now()).Maybe()
			cs.mockIntraLog.EXPECT().BatchLog(anyCTX, mock.Anything, mock.Anything).Return(nil).Maybe()
			getConfig = func() contractConfig.Config {
				return contractConfig.Config{
					EnableUpdateV2: true,
				}
			}
			res, err := cs.Store.Update(mockCTX, test.AgentID, test.AdminID, test.Contract)
			cs.Require().Equal(test.ExpError, err, test.Desc)
			if test.ExpError != nil {
				return
			}

			c, err := cs.Store.GetContract(mockCTX, test.Contract.Id)
			cs.Require().NoError(err)

			cs.Equal(test.ExpRes, res)
			cs.Require().Equal(len(test.ExpRes.Terms), len(c.Terms))
			for i := range test.ExpRes.Terms {
				c.Terms[i].Id = test.ExpRes.Terms[i].Id // skip check id
				cs.Equal(*test.ExpRes.Terms[i], *c.Terms[i])
			}

			tmp := *test.ExpRes
			tmp.Terms = nil
			c.Terms = nil
			cs.Equal(tmp, *c)
		})
	}
}

func (cs *ContractSuite) TestBatchUpdate() {
	updateV1 := *mockContract8
	updateV1.HideIncomeReport = true
	updateV1.PayoutType = 1
	updateV1.FeeOwnerType = 1 //"17Media"
	updateV1.PayoutAccounts = map[ctModel.PayoutCategory]*paModel.PayoutAccount{}
	updateV1.Terms = []*ctModel.ContractTerm{
		{Prop: ctModel.ContractTermType_PERMILLE_SUBRUN.String()},
	}

	updateV2 := *mockContract
	updateV2.IsUnofficial = 1
	updateV2.PayoutAccounts = map[ctModel.PayoutCategory]*paModel.PayoutAccount{}
	updateV2.Terms = []*ctModel.ContractTerm{
		{Prop: ctModel.ContractTermType_PERMILLE_SUBRUN.String()},
	}

	tests := []struct {
		Desc      string
		AgentID   []int
		AdminID   int
		Contracts []*ctModel.Contract
		MockFunc  func()
		ExpError  error
	}{
		{
			Desc:    "no permission",
			AgentID: []int{999},
			Contracts: []*ctModel.Contract{
				&updateV1,
				&updateV2,
			},
			MockFunc: func() {
				mockNow := time.Date(2018, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "TW"}, nil).Times(2)
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil)
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{999}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
			},
			ExpError: ErrNoPermission,
		},
		{
			Desc:    "normal",
			AgentID: []int{107, 999},
			AdminID: 10,
			Contracts: []*ctModel.Contract{
				&updateV1,
				&updateV2,
			},
			MockFunc: func() {
				mockNow := time.Date(2018, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 107).Return(&intra.User{Region: "TW"}, nil).Twice()
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "TW"}, nil).Twice()
				cs.mockUser.On("SetRegionInternal", mock.AnythingOfType("ctx.CTX"), mock.Anything, mock.Anything).Return(nil)
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil)
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Times(2)
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{999}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{107}).Return([]*ctModel.LegacyAgency{
					{Id: 2},
				}, nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Twice()
			},
			ExpError: nil,
		},
	}

	for _, test := range tests {
		cs.Run(test.Desc, func() {
			cs.TearDownTest()
			cs.SetupTest()
			test.MockFunc()
			cs.mockIntraLog.On("Log", mock.AnythingOfType("ctx.CTX"), intralog.TableContract, mock.Anything, mock.Anything, "", intralog.ActionUpdate, mock.Anything, mock.Anything).Return(nil).Maybe()

			_, err := cs.Store.BatchUpdate(mockCTX, test.AgentID, test.AdminID, test.Contracts)
			cs.Require().Equal(test.ExpError, err, test.Desc)

			if test.ExpError != nil {
				return
			}
			for _, contract := range test.Contracts {
				c, err := cs.Store.GetContract(mockCTX, contract.Id)
				cs.Require().NoError(err, test.Desc)

				cs.Require().Equal(len(contract.Terms), len(c.Terms), test.Desc)
				for i := range contract.Terms {
					c.Terms[i].Id = contract.Terms[i].Id // skip check id
					cs.Equal(*contract.Terms[i], *c.Terms[i], test.Desc)
				}

				tmp := *contract
				tmp.Terms = nil
				c.Terms = nil
				cs.Equal(tmp, *c, test.Desc)
			}
		})
	}
}

func (cs *ContractSuite) TestBatchUpdateV2() {
	updateV1 := *mockContract8
	updateV1.HideIncomeReport = true
	updateV1.PayoutType = 1
	updateV1.FeeOwnerType = 1 //"17Media"
	updateV1.PayoutAccounts = map[ctModel.PayoutCategory]*paModel.PayoutAccount{}
	updateV1.Terms = []*ctModel.ContractTerm{
		{Prop: ctModel.ContractTermType_PERMILLE_SUBRUN.String()},
	}

	updateV2 := *mockContract
	updateV2.IsUnofficial = 1
	updateV2.PayoutAccounts = map[ctModel.PayoutCategory]*paModel.PayoutAccount{}
	updateV2.Terms = []*ctModel.ContractTerm{
		{Prop: ctModel.ContractTermType_PERMILLE_SUBRUN.String()},
	}

	tests := []struct {
		Desc      string
		AgentID   []int
		AdminID   int
		Contracts []*ctModel.Contract
		MockFunc  func()
		ExpError  error
	}{
		{
			Desc:    "no permission",
			AgentID: []int{999},
			Contracts: []*ctModel.Contract{
				&updateV1,
				&updateV2,
			},
			MockFunc: func() {
				mockNow := time.Date(2018, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "TW"}, nil).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil)
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{999}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
			},
			ExpError: ErrNoPermission,
		},
		{
			Desc:    "normal",
			AgentID: []int{107, 999},
			AdminID: 10,
			Contracts: []*ctModel.Contract{
				&updateV1,
				&updateV2,
			},
			MockFunc: func() {
				mockNow := time.Date(2018, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow)
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 107).Return(&intra.User{Region: "TW"}, nil).Twice()
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), 999).Return(&intra.User{Region: "TW"}, nil).Twice()

				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil)
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Times(2)
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{999}).Return([]*ctModel.LegacyAgency{
					{Id: 1},
				}, nil).Once()
				cs.mockAgency.On("ListByAdminID", mock.AnythingOfType("ctx.CTX"), []int{107}).Return([]*ctModel.LegacyAgency{
					{Id: 2},
				}, nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Twice()
				cs.mockUser.On("GetUsers", anyCTX, "for delete and update", "8de87814-05e3-4d9b-bb70-b70543b87fa9").Return([]models.User{
					{UserID: "for delete and update", Region: "TW"},
					{UserID: "8de87814-05e3-4d9b-bb70-b70543b87fa9", Region: "JP"},
				}, nil).Once()
				cs.mockUser.On("SetRegionInternal", mock.AnythingOfType("ctx.CTX"), "8de87814-05e3-4d9b-bb70-b70543b87fa9", "TW").Return(nil).Once()
			},
			ExpError: nil,
		},
	}

	for _, test := range tests {
		cs.Run(test.Desc, func() {
			cs.TearDownTest()
			cs.SetupTest()
			test.MockFunc()
			cs.mockIntraLog.EXPECT().BatchLog(anyCTX, mock.Anything, mock.Anything).Return(nil).Maybe()

			_, err := cs.Store.batchUpdateV2(mockCTX, test.AgentID, test.AdminID, test.Contracts)
			cs.Require().Equal(test.ExpError, err, test.Desc)

			if test.ExpError != nil {
				return
			}
			for _, contract := range test.Contracts {
				c, err := cs.Store.GetContract(mockCTX, contract.Id)
				cs.Require().NoError(err, test.Desc)

				cs.Require().Equal(len(contract.Terms), len(c.Terms), test.Desc)
				for i := range contract.Terms {
					c.Terms[i].Id = contract.Terms[i].Id // skip check id
					cs.Equal(*contract.Terms[i], *c.Terms[i], test.Desc)
				}

				tmp := *contract
				tmp.Terms = nil
				c.Terms = nil
				cs.Equal(tmp, *c, test.Desc)
			}
		})
	}
}

func (cs *ContractSuite) TestDelete() {
	tests := []struct {
		Desc       string
		AgentID    []int
		AdminID    int
		ContractID int
		MockFunc   func()
		ExpError   error
	}{
		{
			Desc:       "no permission",
			ContractID: int(mockContract8.Id),
			MockFunc: func() {
			},
			ExpError: ErrNoPermission,
		},
		{
			Desc:       "empty as agentID",
			AgentID:    []int{999},
			ContractID: 10000,
			MockFunc: func() {
			},
			ExpError: ErrContractNotFound,
		},
		{
			Desc:       "delete one",
			AgentID:    []int{999},
			ContractID: int(mockContract8.Id),
			MockFunc: func() {
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Once()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil)
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
			},
			ExpError: nil,
		},
	}

	for _, test := range tests {
		cs.TearDownTest()
		cs.SetupTest()
		test.MockFunc()
		cs.mockIntraLog.On("Log", mock.AnythingOfType("ctx.CTX"), intralog.TableContract, mock.Anything, mock.Anything, "", intralog.ActionDelete, mock.Anything, mock.Anything).Return(nil).Maybe()

		err := cs.Store.Delete(mockCTX, test.AgentID, test.AdminID, test.ContractID)
		cs.Equal(test.ExpError, err, test.Desc)
	}
}

func (cs *ContractSuite) TestTerminate() {
	mockExecUserID := 100
	mockContract := &ctModel.Contract{
		Id:               14,
		Type:             0,
		AgentID:          999,
		AgencyID:         999,
		StreamerID:       100,
		ParentContractID: 0,
		UserID:           "terminate",
		DateStart:        "2022-08-01",
		DateEnd:          "2022-10-31",
		Exclusive:        1,
		PayToParent:      1,
		Others:           "",
		Attachment:       "",
		IsDeleted:        0,
		Region:           "TW",
		IsUnofficial:     0,
		Birthday:         "88-04-03",
		Timezone:         "Asia/Taipei",
		StartTime:        **********,
		EndTime:          **********,
		Terms:            []*ctModel.ContractTerm{},
		PayoutAccounts:   map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
	}

	type test struct {
		desc                     string
		agentID                  []int
		execUserID               int
		contractID               int
		mockFunc                 func()
		expIsTerminated          int32
		expDateEnd               string
		expContractTerminateInfo ctModel.ContractTerminateInfo
		expErr                   error
	}

	tests := []test{
		{
			desc:       "no permission",
			contractID: int(mockContract.Id),
			mockFunc:   func() {},
			expErr:     ErrNoPermission,
		},
		{
			desc:       "succ, terminate contract, end date to 2022/09/30",
			agentID:    []int{999},
			execUserID: mockExecUserID,
			contractID: int(mockContract.Id),
			mockFunc: func() {
				locationTW, _ := time.LoadLocation("Asia/Taipei")
				mockNow := time.Date(2022, 9, 30, 3, 0, 0, 0, locationTW)
				cs.mockFuncs.On("TimeNow").Return(mockNow).Maybe()
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil)
				cs.mockIntraLog.On("Log", mock.AnythingOfType("ctx.CTX"), intralog.TableContract, mock.Anything, mock.Anything, "", intralog.ActionTerminate, mock.Anything, mock.Anything).Return(nil).Once()
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
			},
			expIsTerminated: 1,
			expDateEnd:      "2022-09-30",
			expContractTerminateInfo: ctModel.ContractTerminateInfo{
				ID:              1,
				ContractID:      mockContract.Id,
				ExecAdminID:     mockExecUserID,
				CreateTimeMs:    1664478000000,
				OriginalDateEnd: "2022-10-31",
			},
			expErr: nil,
		},
	}

	for _, t := range tests {
		cs.TearDownTest()
		cs.SetupTest()
		t.mockFunc()
		err := cs.Store.Terminate(mockCTX, t.agentID, t.execUserID, t.contractID)
		cs.Equal(t.expErr, err, t.desc)
		if err != nil {
			continue
		}

		// check db
		// check Contract isTerminated/dateEnd
		type resRow struct {
			IsTerminated int32  `db:"isTerminated"`
			DateEnd      string `db:"dateEnd"`
		}
		getContractStr := `
			SELECT dateEnd, isTerminated
			FROM Contract
			WHERE id=?;
		`
		res := resRow{}
		err = cs.adminDb.Get(&res, getContractStr, t.contractID)
		cs.Require().NoError(err, t.desc)
		cs.Require().Equal(t.expIsTerminated, res.IsTerminated, t.desc)
		cs.Require().Equal(t.expDateEnd, res.DateEnd, t.desc)

		// check ContractTerminateInfo
		CTIRes := ctModel.ContractTerminateInfo{}
		getContractTerminateInfoStr := `
			SELECT id, contractID, execAdminID, createTimeMs, originalDateEnd
			FROM ContractTerminateInfo
			WHERE contractID=?;
		`
		err = cs.adminDb.Get(&CTIRes, getContractTerminateInfoStr, t.contractID)
		cs.Require().NoError(err, t.desc)
		cs.Require().Equal(t.expContractTerminateInfo, CTIRes, t.desc)
	}
}

func (cs *ContractSuite) TestExtend() {
	tests := []struct {
		Desc       string
		agentID    []int
		adminID    int
		contractID int
		SetupMock  func()
		CleanMock  func()
		ExpError   error
		ExpID      int
		Exp        *ctModel.Contract
	}{
		{
			Desc:       "no permission",
			contractID: int(mockContract8.Id),
			SetupMock: func() {
			},
			CleanMock: func() {
			},
			ExpError: ErrNoPermission,
		},
		{
			Desc:       "empty as agentID",
			agentID:    []int{123},
			contractID: int(mockContract8.Id),
			SetupMock: func() {
				mockNow := time.Date(2019, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow).Once()
			},
			CleanMock: func() {
				cs.mockFuncs.AssertExpectations(cs.T())
			},
			ExpError: ErrNoPermission,
		},
		{
			Desc:       "contract not found",
			agentID:    []int{999},
			contractID: int(123),
			SetupMock: func() {
			},
			CleanMock: func() {
			},
			ExpError: ErrContractNotFound,
		},
		{
			Desc:       "extend unofficial",
			agentID:    []int{999},
			contractID: int(11),
			SetupMock: func() {
			},
			CleanMock: func() {
			},
			ExpError: ErrExtendRenewUnofficialContract,
		},
		{
			Desc:       "extend jp",
			agentID:    []int{368},
			contractID: int(12),
			SetupMock: func() {
				mockNow := time.Date(2019, 1, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow).Once()
			},
			CleanMock: func() {
				cs.mockFuncs.AssertExpectations(cs.T())
			},
			ExpError: ErrExtendRenewContract,
		},
		{
			Desc:       "extend out of date range",
			agentID:    []int{999},
			contractID: int(mockContract8.Id),
			SetupMock: func() {
				mockNow := time.Date(2019, 1, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow).Once()
			},
			CleanMock: func() {
				cs.mockFuncs.AssertExpectations(cs.T())
			},
			ExpError: ErrExtendRenewContract,
		},
		{
			Desc:       "extend",
			agentID:    []int{999},
			contractID: int(mockContract8.Id),
			SetupMock: func() {
				mockNow := time.Date(2019, 3, 31, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Times(3)

				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
			},
			CleanMock: func() {
				cs.mockFuncs.AssertExpectations(cs.T())
				cs.mockUser.AssertExpectations(cs.T())
			},
			ExpError: nil,
			ExpID:    100,
			Exp: &ctModel.Contract{
				Id:               100,
				Type:             0,
				AgentID:          999,
				AgencyID:         999,
				StreamerID:       1234567,
				ParentContractID: 0,
				UserID:           "for delete and update",
				DateStart:        "2019-04-01",
				DateEnd:          "2019-04-30",
				Exclusive:        1,
				PayToParent:      1,
				Others:           "Extended Contract",
				Attachment:       "",
				IsDeleted:        0,
				Region:           "TW",
				IsUnofficial:     1,
				Birthday:         "88-04-03",
				Timezone:         "Asia/Taipei",
				StartTime:        **********,
				EndTime:          **********,
				Terms: []*ctModel.ContractTerm{
					{
						Id:           100,
						ContractID:   100,
						Condition:    "CURRENT_POINTS",
						Gte:          0,
						Lt:           15000,
						Prop:         "PERMILLE_SUBRUN",
						Value:        999,
						ContractType: ctModel.StreamerContractType,
					},
				},
				FeeOwnerType: 2, // "User",
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{
					ctModel.PayoutCategory_REVENUE: mockPayoutAccount,
				},
			},
		},
	}

	for _, test := range tests {
		test.SetupMock()
		cs.mockIntraLog.On("Log", mock.AnythingOfType("ctx.CTX"), intralog.TableContract, mock.Anything, mock.Anything, "", intralog.ActionExtend, mock.Anything, mock.Anything).Return(nil).Maybe()

		res, err := cs.Store.Extend(mockCTX, test.agentID, test.adminID, test.contractID)
		cs.Equal(test.ExpError, err, test.Desc)
		cs.Equal(test.ExpID, res, test.Desc)
		if test.ExpError == nil {
			c, err := cs.Store.GetContract(mockCTX, int32(res))
			cs.Require().NoError(err, test.Desc)

			cs.Equal(test.ExpID, res, test.Desc)
			cs.Equal(len(test.Exp.Terms), len(c.Terms), test.Desc)
			for i := range test.Exp.Terms {
				cs.Equal(*test.Exp.Terms[i], *c.Terms[i], test.Desc)
			}

			tmp := *test.Exp
			tmp.Terms = nil
			c.Terms = nil
			cs.Equal(tmp, *c, test.Desc)
		}
		test.CleanMock()
	}
}

func (cs *ContractSuite) TestRenew() {
	tests := []struct {
		Desc       string
		agentID    []int
		adminID    int
		contractID int
		SetupMock  func()
		CleanMock  func()
		ExpError   error
		ExpID      int
		Exp        *ctModel.Contract
	}{
		{
			Desc:       "no permission",
			contractID: int(mockContract8.Id),
			SetupMock: func() {
			},
			CleanMock: func() {
			},
			ExpError: ErrNoPermission,
		},
		{
			Desc:       "empty as agentID",
			agentID:    []int{123},
			contractID: int(mockContract8.Id),
			SetupMock: func() {
				mockNow := time.Date(2019, 3, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow).Once()
			},
			CleanMock: func() {
				cs.mockFuncs.AssertExpectations(cs.T())
			},
			ExpError: ErrNoPermission,
		},
		{
			Desc:       "renew unofficial",
			agentID:    []int{999},
			contractID: int(11),
			SetupMock: func() {
			},
			CleanMock: func() {
			},
			ExpError: ErrExtendRenewUnofficialContract,
		},
		{
			Desc:       "renew jp",
			agentID:    []int{368},
			contractID: int(12),
			SetupMock: func() {
				mockNow := time.Date(2019, 1, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow).Once()
			},
			CleanMock: func() {
				cs.mockFuncs.AssertExpectations(cs.T())
			},
			ExpError: ErrExtendRenewContract,
		},
		{
			Desc:       "renew out of date range",
			agentID:    []int{999},
			contractID: int(mockContract8.Id),
			SetupMock: func() {
				mockNow := time.Date(2019, 1, 30, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow).Once()
			},
			CleanMock: func() {
				cs.mockFuncs.AssertExpectations(cs.T())
			},
			ExpError: ErrExtendRenewContract,
		},
		{
			Desc:       "renew",
			agentID:    []int{999},
			contractID: int(mockContract8.Id),
			SetupMock: func() {
				mockNow := time.Date(2019, 3, 25, 0, 0, 0, 0, time.UTC)
				cs.mockFuncs.On("TimeNow").Return(mockNow).Once()
				cs.mockPayoutAccount.On("ListByQuery", mock.AnythingOfType("ctx.CTX"), payoutaccount.Query{
					IDs: []int64{mockPayoutAccountID},
				}).Return([]*paModel.PayoutAccount{mockPayoutAccount}, nil).Times(3)
				cs.mockUser.On("DelUserCache", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return(nil).Once()
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockFuncs.On("sendEvents", cs.Store, anyCTX, mock.Anything).Once()
			},
			CleanMock: func() {
				cs.mockFuncs.AssertExpectations(cs.T())
				cs.mockUser.AssertExpectations(cs.T())
			},
			ExpError: nil,
			ExpID:    100,
			Exp: &ctModel.Contract{
				Id:               100,
				Type:             0,
				AgentID:          999,
				AgencyID:         999,
				StreamerID:       1234567,
				ParentContractID: 0,
				UserID:           "for delete and update",
				DateStart:        "2019-04-01",
				DateEnd:          "2020-03-31",
				Exclusive:        1,
				PayToParent:      1,
				Others:           "Renew Contract",
				Attachment:       "",
				IsDeleted:        0,
				Region:           "TW",
				IsUnofficial:     0,
				Birthday:         "88-04-03",
				Timezone:         "Asia/Taipei",
				StartTime:        **********,
				EndTime:          **********,
				Terms: []*ctModel.ContractTerm{
					{
						Id:           100,
						ContractID:   100,
						Condition:    "CURRENT_POINTS",
						Gte:          0,
						Lt:           15000,
						Prop:         "PERMILLE_SUBRUN",
						Value:        999,
						ContractType: ctModel.StreamerContractType,
					},
				},
				FeeOwnerType: 2, // "User",
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{
					ctModel.PayoutCategory_REVENUE: mockPayoutAccount,
				},
			},
		},
	}

	for _, test := range tests {
		test.SetupMock()

		cs.mockIntraLog.On("Log", mock.AnythingOfType("ctx.CTX"), intralog.TableContract, mock.Anything, mock.Anything, "", intralog.ActionRenew, mock.Anything, mock.Anything).Return(nil).Maybe()
		res, err := cs.Store.Renew(mockCTX, test.agentID, test.adminID, test.contractID)
		cs.Equal(test.ExpError, err, test.Desc)
		cs.Equal(test.ExpID, res, test.Desc)
		if test.ExpError == nil {
			c, err := cs.Store.GetContract(mockCTX, int32(res))
			cs.Require().NoError(err, test.Desc)

			cs.Equal(test.ExpID, res, test.Desc)
			cs.Equal(len(test.Exp.Terms), len(c.Terms), test.Desc)
			for i := range test.Exp.Terms {
				cs.Equal(*test.Exp.Terms[i], *c.Terms[i], test.Desc)
			}

			tmp := *test.Exp
			tmp.Terms = nil
			c.Terms = nil
			cs.Equal(tmp, *c, test.Desc)
		}
		test.CleanMock()
	}
}

func (cs *ContractSuite) TestFindTerms() {
	tests := []struct {
		Desc     string
		agentID  []int
		termID   []int
		ExpRes   []*ctModel.ContractTerm
		ExpError error
	}{
		{
			Desc:     "no permission 1",
			termID:   []int{1, 2, 3, 4, 5, 6},
			ExpRes:   []*ctModel.ContractTerm{},
			ExpError: ErrNoPermission,
		},
		{
			Desc:     "no permission 2",
			agentID:  []int{119},
			termID:   []int{1, 2, 3, 4, 5, 6},
			ExpRes:   []*ctModel.ContractTerm{},
			ExpError: nil,
		},
		{
			Desc:     "empty",
			agentID:  []int{107},
			termID:   []int{10000},
			ExpRes:   []*ctModel.ContractTerm{},
			ExpError: nil,
		},
		{
			Desc:     "find one",
			agentID:  []int{107},
			termID:   []int{1},
			ExpRes:   []*ctModel.ContractTerm{mockContract.Terms[0]},
			ExpError: nil,
		},
		{
			Desc:     "find all",
			agentID:  []int{107},
			termID:   []int{1, 2, 3, 4, 5, 6},
			ExpRes:   mockContract.Terms,
			ExpError: nil,
		},
		{
			Desc:    "find by agency",
			agentID: []int{456},
			termID:  []int{11},
			ExpRes: []*ctModel.ContractTerm{
				{
					Id:           11,
					ContractID:   10,
					Condition:    "CURRENT_POINTS",
					Gte:          0,
					Lt:           15000,
					Prop:         "PERMILLE_SUBRUN",
					Value:        999,
					ContractType: ctModel.StreamerContractType,
				},
			},
			ExpError: nil,
		},
	}

	for _, test := range tests {
		terms, err := cs.Store.FindTerms(mockCTX, test.agentID, test.termID)
		cs.Equal(test.ExpError, err, test.Desc)
		cs.Equal(len(test.ExpRes), len(terms), test.Desc)
		for i, t := range terms {
			cs.Equal(test.ExpRes[i], t, test.Desc)
		}
	}
}

func (cs *ContractSuite) TestGetUserID() {
	tests := []struct {
		Desc      string
		ID        int
		adminIDs  []int
		ExpErr    error
		ExpUserID string
	}{
		{
			Desc:   "empty adminIDs",
			ExpErr: ErrNoPermission,
		},
		{
			Desc:     "empty result",
			adminIDs: []int{0},
			ExpErr:   ErrNoPermission,
		},
		{
			Desc:      "found one",
			ID:        1,
			adminIDs:  []int{107},
			ExpErr:    nil,
			ExpUserID: mockContractUserID1,
		},
	}

	for _, test := range tests {
		userID, err := cs.Store.GetUserID(mockCTX, test.ID, test.adminIDs)
		cs.Equal(test.ExpErr, err, test.Desc)
		cs.Equal(test.ExpUserID, userID, test.Desc)
	}
}

func (cs *ContractSuite) TestGetBirthday() {
	tpeLocation, _ := baseTime.LocationTaipei()
	mockNow := time.Date(2017, 4, 1, 0, 0, 0, 0, time.UTC)
	cs.mockFuncs.On("TimeNow").Return(mockNow)
	birthday := time.Date(1988, 5, 1, 0, 0, 0, 0, tpeLocation)

	tests := []struct {
		Desc     string
		Input    string
		ExpRes   *time.Time
		ExpError error
	}{
		{
			Desc:     "UserID exist",
			Input:    mockContractUserID1,
			ExpRes:   &birthday,
			ExpError: nil,
		},
		{
			Desc:     "UserID not found",
			Input:    mockUserID2,
			ExpRes:   nil,
			ExpError: ErrBirthdayNotFound,
		},
	}
	for _, test := range tests {
		data, err := cs.Store.GetBirthday(mockCTX, test.Input)
		cs.Equal(test.ExpError, err, test.Desc)
		cs.Equal(test.ExpRes, data, test.Desc)
		// From cache
		data, err = cs.Store.GetBirthday(mockCTX, test.Input)
		cs.Equal(test.ExpError, err, test.Desc)
		cs.Equal(test.ExpRes, data, test.Desc)
	}
}

func (cs *ContractSuite) TestGetUpcomingBirthday() {
	tpeLocation, _ := baseTime.LocationTaipei()

	now1 := time.Date(2017, 4, 2, 0, 0, 0, 0, time.UTC)
	day1 := time.Date(2017, 5, 1, 0, 0, 0, 0, tpeLocation)

	now2 := time.Date(2017, 5, 2, 0, 0, 0, 0, time.UTC)
	day2 := time.Date(2018, 5, 1, 0, 0, 0, 0, tpeLocation)

	now3 := time.Date(2017, 5, 1, 0, 0, 0, 0, tpeLocation)
	day3 := now3

	tests := []struct {
		Desc     string
		Input    time.Time
		ExpRes   *time.Time
		ExpError error
	}{
		{
			Desc:     "Same year",
			Input:    now1,
			ExpRes:   &day1,
			ExpError: nil,
		},
		{
			Desc:     "Next year",
			Input:    now2,
			ExpRes:   &day2,
			ExpError: nil,
		},
		{
			Desc:     "Same day",
			Input:    now3,
			ExpRes:   &day3,
			ExpError: nil,
		},
	}

	for _, test := range tests {
		cs.mockFuncs.On("TimeNow").Return(test.Input)
		data, err := cs.Store.getUpcomingBirthday(mockCTX, test.Input, mockContractUserID1)
		cs.Equal(test.ExpError, err, test.Desc)
		cs.Equal(test.ExpRes, data, test.Desc)

		// clear cache
		cs.Store.Helper.DeleteContractCaches(mockCTX, mockContractUserID1)
	}
}

func (cs *ContractSuite) TestGetBirthdayState() {
	mockUser1 := models.User{
		UserID: mockContractUserID1,
		OpenID: mockOpenID,
	}
	mockUser2 := models.User{
		UserID: mockContractUserID2,
		OpenID: mockOpenID2,
	}
	mockUser3 := models.User{
		UserID: mockContractUserID3,
		OpenID: mockOpenID2,
	}
	mockUser4 := models.User{
		UserID: mockContractUserID4,
		OpenID: mockOpenID2,
	}

	tests := []struct {
		Desc         string
		Input        models.User
		Now          time.Time
		ExpRes       ctModel.BirthdayState
		ExpDiffInDay int
		ExpError     error
	}{
		{
			Desc:         "Today is birthday",
			Input:        mockUser1,
			Now:          time.Date(2017, 5, 1, 0, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_TODAY,
			ExpDiffInDay: 0,
			ExpError:     nil,
		},
		{
			Desc:         "In birthday countdown range (3)",
			Input:        mockUser1,
			Now:          time.Date(2017, 4, 27, 16, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_COUNTDOWN_RANGE,
			ExpDiffInDay: 3,
			ExpError:     nil,
		},
		{
			Desc:         "In birthday countdown range (2)",
			Input:        mockUser1,
			Now:          time.Date(2017, 4, 28, 16, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_COUNTDOWN_RANGE,
			ExpDiffInDay: 2,
			ExpError:     nil,
		},
		{
			Desc:         "In birthday countdown range (1)",
			Input:        mockUser1,
			Now:          time.Date(2017, 4, 29, 16, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_COUNTDOWN_RANGE,
			ExpDiffInDay: 1,
			ExpError:     nil,
		},
		{
			Desc:         "Not in birthday range",
			Input:        mockUser1,
			Now:          time.Date(2017, 4, 1, 0, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_No,
			ExpDiffInDay: 30,
			ExpError:     nil,
		},
		{
			Desc:         "Today is birthday because of timezone",
			Input:        mockUser1,
			Now:          time.Date(2017, 4, 30, 16, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_TODAY,
			ExpDiffInDay: 0,
			ExpError:     nil,
		},
		{
			Desc:         "User birthday not exist",
			Input:        mockUser2,
			Now:          time.Date(2017, 12, 12, 16, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_NOT_EXIST,
			ExpDiffInDay: 0,
			ExpError:     nil,
		},
		{
			Desc:         "Edge case of birthday countdown range",
			Input:        mockUser1,
			Now:          time.Date(2017, 4, 28, 16, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_COUNTDOWN_RANGE,
			ExpDiffInDay: 2,
			ExpError:     nil,
		},
		{
			Desc:         "After and in the birthday range",
			Input:        mockUser1,
			Now:          time.Date(2017, 5, 2, 0, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_AFTER_RANGE,
			ExpDiffInDay: -1,
			ExpError:     nil,
		},
		{
			Desc:         "birthday is yesterday",
			Input:        mockUser1,
			Now:          time.Date(2017, 5, 1, 16, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_AFTER_RANGE,
			ExpDiffInDay: -1,
			ExpError:     nil,
		},
		{
			Desc:         "User in the last day of contract and also the countdown range",
			Input:        mockUser3,
			Now:          time.Date(2018, 3, 31, 8, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_COUNTDOWN_RANGE,
			ExpDiffInDay: 3,
			ExpError:     nil,
		},
		{
			Desc:         "wrong birthday format",
			Input:        mockUser4,
			Now:          time.Date(2017, 5, 2, 0, 0, 0, 0, time.UTC),
			ExpRes:       ctModel.BirthdayState_NOT_EXIST,
			ExpDiffInDay: 0,
			ExpError:     ErrBirthdayFormatInvalid,
		},
	}

	for _, test := range tests {
		cs.mockFuncs.On("TimeNow").Return(test.Now).Twice()
		data, diff, err := cs.Store.GetBirthdayState(mockCTX, test.Input.UserID)
		cs.Equal(test.ExpError, err, test.Desc)
		cs.Equal(test.ExpRes, data, test.Desc)
		cs.Equal(test.ExpDiffInDay, diff, test.Desc)
		cs.mockFuncs.AssertExpectations(cs.T())
	}
}

func (cs *ContractSuite) TestGetOverlap() {
	cts, err := cs.Store.GetOverlap(mockCTX, 1233, "2017-06-12", "2018-06-12", "Asia/Taipei")
	cs.Require().NoError(err)
	cs.Require().Len(cts, 1)
	cs.Equal(cts[0].DateStart, "2017-04-01")

	cts, err = cs.Store.GetOverlap(mockCTX, 1233, "2019-04-01", "2019-06-12", "Asia/Tokyo")
	cs.Require().NoError(err)
	cs.Require().Len(cts, 1)
	cs.Equal(cts[0].DateStart, "2017-04-01")

	cts, err = cs.Store.GetOverlap(mockCTX, 1233, "2019-04-01", "2019-06-12", "America/Los_Angeles")
	cs.Require().NoError(err)
	cs.Require().Len(cts, 0)

	cts, err = cs.Store.GetOverlap(mockCTX, 1233, "2017-01-01", "2017-03-31", "America/Los_Angeles")
	cs.Require().NoError(err)
	cs.Require().Len(cts, 1)
	cs.Equal(cts[0].DateStart, "2017-04-01")

	cts, err = cs.Store.GetOverlap(mockCTX, 1233, "2017-01-01", "2017-03-31", "Asia/Tokyo")
	cs.Require().NoError(err)
	cs.Require().Len(cts, 0)
}

func (cs *ContractSuite) TestGetContract() {
	contract, err := cs.Store.GetContract(mockCTX, mockContract.Id)
	cs.Require().NoError(err)
	cs.Equal(mockContract, contract)
}

func createGiftDB(name, localhost, port string) {
	db, err := sql.Open("mysql", "root:@tcp("+localhost+":"+port+")/")
	if err != nil {
		panic(err)
	}
	defer db.Close()

	_, err = db.Exec("CREATE DATABASE " + name)
	if err != nil {
		panic(err)
	}

	_, err = db.Exec("USE " + name)
	if err != nil {
		panic(err)
	}
}

func createAdminDB(name, localhost, port string) {
	db, err := sql.Open("mysql", "root:@tcp("+localhost+":"+port+")/")
	if err != nil {
		panic(err)
	}
	defer db.Close()

	_, err = db.Exec("CREATE DATABASE " + name)
	if err != nil {
		panic(err)
	}

	_, err = db.Exec("USE " + name)
	if err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`CREATE TABLE Contract (
			id int(11) unsigned NOT NULL AUTO_INCREMENT,
			type int(4) unsigned NOT NULL COMMENT '0:Individual, 1:Group',
			agentID int(11) unsigned NOT NULL COMMENT 'Agent id',
			agencyID int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'agency id, agency who signed this streamer',
			streamerID int(11) unsigned NOT NULL COMMENT 'ContractedStreamer id',
			parentContractID int(11) unsigned NOT NULL COMMENT 'ContractGroup id',
			userID varchar(50) NOT NULL DEFAULT '' COMMENT 'User(mongo) id',
			timeCreate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
			timeModified datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			dateStart date NOT NULL,
			dateEnd date NOT NULL,
			exclusive tinyint(1) NOT NULL,
			payToParent tinyint(1) NOT NULL,
			others text NOT NULL,
			attachment varchar(80) NOT NULL DEFAULT '',
			isDeleted tinyint(1) NOT NULL,
			isTerminated tinyint(1) NOT NULL DEFAULT 0,
			region varchar(8) DEFAULT NULL,
			city varchar(32) NOT NULL DEFAULT '' COMMENT 'location city',
			isUnofficial tinyint(1) NOT NULL,
			hideIncomeReport tinyint(1) NOT NULL DEFAULT 0 COMMENT 'control App to hide income report',
			timezone varchar(36) NOT NULL DEFAULT '',
			timezoneShift int(11),
			payoutType tinyint(1) NOT NULL DEFAULT 0 COMMENT '0: UNKNOWN; 1: paypal, 2: offline, 3: BANK_TBB, 4: BANK_ESUN, 5: PAYPAL_MANUAL',
			feeOwnerType tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: UNKNOWN; 1: 17Media, 2: User define in stores.accounting',
			agencyContractTemplateID int(11) unsigned NOT NULL DEFAULT '0',
			revenuePayoutAccountID bigint(20) NOT NULL DEFAULT '0',
			salaryPayoutAccountID bigint(20) NOT NULL DEFAULT '0',
			eventPayoutAccountID bigint(20) NOT NULL DEFAULT '0',
			note text,
			isSemiContract tinyint(1) NOT NULL DEFAULT '0',
			PRIMARY KEY (id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(`CREATE TABLE ContractTerm (
			id int(11) unsigned NOT NULL AUTO_INCREMENT,
			contractID int(11) unsigned NOT NULL,
			` + "`condition`" + `varchar(50) NOT NULL DEFAULT '',
			gte int(11) NOT NULL DEFAULT '0' COMMENT 'Greater than or equal',
			lt int(11) NOT NULL DEFAULT '-1' COMMENT 'Less than',
			prop varchar(50) NOT NULL DEFAULT '',
			` + "`value`" + `int(11) NOT NULL,
			dateStart varchar(10) NOT NULL DEFAULT '',
			dateEnd varchar(10) NOT NULL DEFAULT '',
			contractType tinyint(1) unsigned NOT NULL DEFAULT '0',
			PRIMARY KEY (id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`CREATE TABLE ContractedStreamer (
			id int(11) unsigned NOT NULL AUTO_INCREMENT,
			name varchar(50) NOT NULL DEFAULT '',
			userID varchar(50) NOT NULL DEFAULT '' COMMENT 'User(mongo) id',
			swagUserID varchar(50) NOT NULL DEFAULT '' COMMENT 'Swag username',
			agentID int(11) unsigned NOT NULL COMMENT 'Agent id, agent who signed this streamer',
			idCardNumber varchar(20) DEFAULT NULL COMMENT 'Taiwan ID card number',
			dob varchar(20) DEFAULT NULL COMMENT 'Date of birth',
			phoneNumber varchar(20) DEFAULT NULL COMMENT 'Phone number',
			address varchar(100) DEFAULT NULL COMMENT 'Address',
			email varchar(50) DEFAULT NULL COMMENT 'Email',
			snsFacebook varchar(50) DEFAULT NULL COMMENT 'Facebook user id',
			snsLine varchar(50) DEFAULT NULL COMMENT 'Line user id',
			snsInstagram varchar(50) DEFAULT NULL COMMENT 'Instagram user id',
			bankName varchar(50) DEFAULT NULL COMMENT 'Bank name',
			bankBranch varchar(50) DEFAULT NULL COMMENT 'Bank branch',
			bankCode varchar(50) DEFAULT NULL,
			bankBranchCode varchar(50) DEFAULT NULL,
			bankAccount varchar(50) DEFAULT NULL COMMENT 'Bank account, should be numbers',
			payeeName varchar(50) NOT NULL DEFAULT '' COMMENT 'Payee Name',
			payeeIDNumber varchar(20) NOT NULL DEFAULT '' COMMENT 'Payee ID Number',
			paypalAccount varchar(50) DEFAULT NULL COMMENT 'Paypal account',
			others text,
			isDeleted tinyint(1) DEFAULT NULL,
			idCustom varchar(11) NOT NULL DEFAULT '',
			bankNameFK varchar(50) NOT NULL DEFAULT '' COMMENT 'Foreign Bank name',
			bankBranchFK varchar(50) NOT NULL DEFAULT '' COMMENT 'Foreign Bank branch',
			bankCodeFK varchar(50) NOT NULL DEFAULT '' COMMENT 'ForeignBank code',
			bankBranchCodeFK varchar(50) NOT NULL DEFAULT '' COMMENT 'ForeignBank branch code',
			bankAccountFK varchar(50) NOT NULL DEFAULT '' COMMENT 'ForeignBank account, should be numbers',
			payeeNameFK varchar(50) NOT NULL DEFAULT '' COMMENT 'Foreign Payee Name',
			feeOwnerType tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: UNKNOWN; 1: 17Media, 2: User define in stores.accounting',
			agencyID int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'agency id, agency who signed this streamer',
			payeeType tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: Others, 1: User, 2: Legal',
			bankBranchAddrFK varchar(100) NOT NULL DEFAULT '' COMMENT 'ForeignBank branch address',
			healthInsuranceType tinyint(1) NOT NULL DEFAULT 0,
			applicationApproved tinyint(1) NOT NULL DEFAULT 1,
			streamType tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Unknown|Real|Game|Vliver',
			vliverName varchar(50) NOT NULL DEFAULT '',
			vliverDob varchar(20) NOT NULL DEFAULT '',
			vliverAgentName varchar(50) NOT NULL DEFAULT '',
			nationality tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Unknown|Native|Foreigner',
			residencePlace varchar(50) NOT NULL DEFAULT '',
			residenceAddress varchar(100) NOT NULL DEFAULT '',
			prizeShippingAddress varchar(100) NOT NULL DEFAULT '',
			parentAddress varchar(100) NOT NULL DEFAULT '',
			countryCode varchar(10) NOT NULL DEFAULT '',
 			PRIMARY KEY (id)
		) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8;`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`CREATE TABLE ContractAttachment (
			id int(11) unsigned NOT NULL AUTO_INCREMENT,
			contractID int(11) unsigned NOT NULL COMMENT 'Contract id',
			filename varchar(150) NOT NULL DEFAULT '' COMMENT 'Original filename',
			endpoint varchar(150) NOT NULL DEFAULT '' COMMENT 'Filename in storage',
			size int(11) unsigned NOT NULL,
			timeCreate datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
			type varchar(11) NOT NULL DEFAULT 'Contract' COMMENT 'Contract|Agency',
			isDeleted tinyint(1) NOT NULL,
			PRIMARY KEY (id)
		) ENGINE=InnoDB AUTO_INCREMENT=3614 DEFAULT CHARSET=utf8;`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`CREATE TABLE IF NOT EXISTS AdminUsers (
			id int(11) unsigned NOT NULL AUTO_INCREMENT,
			username varchar(50) NOT NULL DEFAULT '',
			password varchar(100) NOT NULL DEFAULT '',
			permissionCode int(11) unsigned NOT NULL,
			email varchar(50) NOT NULL DEFAULT '',
			name varchar(100) NOT NULL DEFAULT '',
			picture varchar(150) NOT NULL DEFAULT '',
			region varchar(11) NOT NULL DEFAULT '',
			type varchar(20) NOT NULL DEFAULT '',
			jwtToken varchar(2000) NOT NULL DEFAULT '',
			userID17 varchar(40) DEFAULT NULL,
			PRIMARY KEY (id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`CREATE TABLE IF NOT EXISTS Agency (
			id int(11) unsigned NOT NULL AUTO_INCREMENT,
			name varchar(50) NOT NULL DEFAULT '' COMMENT 'The name of the agency',
			adminID int(11) unsigned NOT NULL COMMENT 'the adminID of this agency',
			agentID int(11) unsigned NOT NULL COMMENT 'The adminID of the BD who manage this agency',
			serialNumber varchar(20) NOT NULL DEFAULT '' COMMENT 'Company tax ID',
			phoneNumber varchar(20) NOT NULL DEFAULT '' COMMENT 'Phone number',
			address varchar(100) NOT NULL DEFAULT '' COMMENT 'Address',
			email varchar(50) NOT NULL COMMENT 'Address',
			bankName varchar(50) NOT NULL DEFAULT '' COMMENT 'Bank name',
			bankBranch varchar(50) NOT NULL DEFAULT '' COMMENT 'Bank branch',
			bankCode varchar(50) NOT NULL DEFAULT '' COMMENT 'Bank code',
			bankBranchCode varchar(50) NOT NULL DEFAULT '' COMMENT 'Bank branch code',
			bankAccount varchar(50) NOT NULL DEFAULT '' COMMENT 'Bank account, should be numbers',
			payeeName varchar(50) NOT NULL DEFAULT '' COMMENT 'Payee Name',
			payeeIDNumber varchar(20) NOT NULL DEFAULT '' COMMENT 'Payee ID Number',
			paypalAccount varchar(50) NOT NULL DEFAULT '' COMMENT 'Paypal account',
			others text NOT NULL,
			isDeleted tinyint(1) NOT NULL,
			bankNameFK varchar(50) NOT NULL DEFAULT '' COMMENT 'Foreign Bank name',
			bankBranchFK varchar(50) NOT NULL DEFAULT '' COMMENT 'Foreign Bank branch',
			bankCodeFK varchar(50) NOT NULL DEFAULT '' COMMENT 'ForeignBank code',
			bankBranchCodeFK varchar(50) NOT NULL DEFAULT '' COMMENT 'ForeignBank branch code',
			bankAccountFK varchar(50) NOT NULL DEFAULT '' COMMENT 'ForeignBank account, should be numbers',
			payeeNameFK varchar(50) NOT NULL DEFAULT '' COMMENT 'Foreign Payee Name',
			feeOwnerType tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: UNKNOWN; 1: 17Media, 2: User define in stores.accounting',
			bankBranchAddrFK varchar(100) NOT NULL DEFAULT '' COMMENT 'ForeignBank branch address',
			healthInsuranceType tinyint(1) NOT NULL DEFAULT 0,
			payoutAccountID bigint(20) NOT NULL DEFAULT 0,
			userID varchar(36) NOT NULL DEFAULT '',
			PRIMARY KEY (id)
		) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`CREATE TABLE RegionSetting (
			code varchar(8) NOT NULL DEFAULT '' COMMENT 'ISO 1366-1',
			name varchar(50) NOT NULL DEFAULT '' COMMENT 'English short name',
			timezone varchar(50) NOT NULL DEFAULT '' COMMENT 'Region default timezone',
			currency varchar(3) NOT NULL DEFAULT '' COMMENT 'ISO 4217',
			currencySymbol varchar(6) NOT NULL DEFAULT '',
			weight int(11) NOT NULL COMMENT 'orders in UI',
			common tinyint(4) NOT NULL COMMENT 'filter in UI',` +
			"`group`" + `varchar(50) DEFAULT 'OTHERS',
  			groupTimezone varchar(50) DEFAULT '',
  			groupStartTime bigint(20) DEFAULT '0',
  			groupEndTime bigint(20) DEFAULT '***********',
			PRIMARY KEY (code)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`INSERT INTO Contract (
			type, agentID, agencyID, streamerID, parentContractID, userID, timeCreate, timeModified, dateStart, dateEnd,
			exclusive, payToParent, others, attachment, isDeleted, region, isUnofficial, timezone, payoutType, feeOwnerType,
			revenuePayoutAccountID, salaryPayoutAccountID, eventPayoutAccountID
		)
		VALUES (
			0, 107, 107, 777, 0, '8de87814-05e3-4d9b-bb70-b70543b87fa9', '2017-07-19 03:23:30', '2017-07-19 03:23:30',
			'2017-04-01', '2018-03-31', 0, 1,
			'0401-0630 保底61,200免拆帳,30hr 0401-0630 分潤102,264免拆帳', '', 0, 'TW', 0, 'Asia/Taipei', 2, 2,
			0, 0, 0
		), (
			0, 119, 0, 255, 0, '408ffd58-bc4f-4c03-abc4-3c095d21d424', '2017-07-13 09:06:14', '2017-07-13 09:06:14',
			'2017-04-01', '2019-03-31', 1, 0, '', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 107, 107, 33, 0, '1af4cba1-59de-45d0-b3cc-719c58eef0d7', '2017-07-11 08:27:43', '2017-07-11 08:31:20',
			'2017-04-01', '2018-03-31', 0, 1, '當月進月榜前50名 發20,000元新台幣', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 107, 107, 34, 0, 'aaaaaaaa-59de-45d0-b3cc-719c58eef0d7', '2017-07-11 08:27:43', '2017-07-11 08:31:20',
			'2017-04-01', '2018-02-28', 1, 1, '當月進月榜前50名 發20,000元新台幣', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 107, 107, 33, 0, '1af4cba1-59de-45d0-b3cc-719c58eef0d7', '2016-07-11 08:27:43', '2016-07-11 08:31:20',
			'2016-04-01', '2017-03-31', 0, 1, '當月進月榜前50名 發20,000元新台幣', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 107, 107, 33, 0, '1af4cba1-59de-45d0-b3cc-719c58eef0d7', '2018-07-11 08:27:43', '2018-07-11 08:31:20',
			'2018-04-01', '2019-03-31', 1, 1, '當月進月榜前50名 發20,000元新台幣', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 0, 0, 1233, 0, 'a1f4cba1-59de-45d0-b3cc-719c58eef0d7', '2017-07-11 08:27:43', '2018-07-11 08:31:20',
			'2017-04-01', '2019-03-31', 1, 0, '當月進月榜前50名 發20,000元新台幣', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 999, 999, 1234567, 0, 'for delete and update', '2018-07-11 08:27:43', '2018-07-11 08:31:20',
			'2018-04-01', '2019-03-31', 1, 1, '', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			1, 0, 0
		), (
			0, 999, 999, 999, 0, 'non exist streamer and agency', '2016-07-11 08:27:43', '2017-07-11 08:31:20',
			'2017-04-01', '2018-03-31', 1, 1, '', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 123, 456, 789, 0, 'test agency', '2016-07-11 08:27:43', '2017-07-11 08:31:20',
			'2017-04-01', '2018-03-31', 1, 1, '', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 123, 456, 789, 0, 'test agency', '2016-07-11 08:27:43', '2017-07-11 08:31:20',
			'2018-04-01', '2019-03-31', 1, 1, '', '', 0, 'TW', 1, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 368, 456, 876, 0, 'test jp', '2016-07-11 08:27:43', '2017-07-11 08:31:20',
			'2018-04-01', '2019-03-31', 1, 1, '', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 107, 107, 33, 0, '1af4cba1-59de-45d0-b3cc-719c58eef0d7', '2018-07-11 08:27:43', '2018-07-11 08:31:20',
			'2018-04-01', '2019-03-31', 1, 1, '當月進月榜前50名 發20,000元新台幣', '', 1, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		), (
			0, 999, 999, 100, 0, 'terminate', '2022-07-11 08:27:43', '2022-07-11 08:31:20',
			'2022-08-01', '2022-10-31', 1, 1, '', '', 0, 'TW', 0, 'Asia/Taipei', 0, 2,
			0, 0, 0
		);`); err != nil {
		panic(err)
	}

	if _, err = db.Exec(`ALTER TABLE Contract AUTO_INCREMENT=100;`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`INSERT INTO ContractTerm (
			contractID, ContractTerm.condition, gte, lt, prop, ContractTerm.value, contractType
		)
		VALUES
			(1, '', 0, 0, 'HOURLY_WAGE', 1000, 1),
			(1, '', 0, 0, 'MAX_PAID_HOURS', 30, 1),
			(1, 'STREAMED_HOURS', 0, 10, 'PERCENT_WAGE_RATIO', 0, 1),
			(1, 'STREAMED_HOURS', 10, 30, 'PERCENT_WAGE_RATIO', 100, 1),
			(1, 'STREAMED_HOURS', 30, -1, 'PERCENT_WAGE_RATIO', 100, 1),
			(1, '', 0, 0, 'PERMILLE_SUBRUN', 600, 1),
			(3, 'CURRENT_POINTS', 0, 15000, 'PERMILLE_SUBRUN', 400, 1),
			(3, 'CURRENT_POINTS', 15000, 30000, 'PERMILLE_SUBRUN', 500, 1),
			(3, 'CURRENT_POINTS', 30000, -1, 'PERMILLE_SUBRUN', 600, 1),
			(8, 'CURRENT_POINTS', 0, 15000, 'PERMILLE_SUBRUN', 999, 1),
			(10, 'CURRENT_POINTS', 0, 15000, 'PERMILLE_SUBRUN', 999, 1
		);`); err != nil {
		panic(err)
	}

	if _, err = db.Exec(`ALTER TABLE ContractTerm AUTO_INCREMENT=100;`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`INSERT INTO ContractedStreamer (
			id, name, userID, swagUserID, agentID, idCardNumber, dob, phoneNumber, address, email, snsFacebook, snsLine, snsInstagram, bankName, bankBranch, bankCode, bankBranchCode, bankAccount, payeeName, payeeIDNumber, paypalAccount, bankNameFK, bankBranchFK, bankCodeFK, bankBranchCodeFK, bankAccountFK, payeeNameFK, others, isDeleted, idCustom, agencyID)
		VALUES
			(777, 'momoisgood', '8de87814-05e3-4d9b-bb70-b70543b87fa9', '', 58, 'A226861159', '1988-05-01', '**********', '111臺北市士林區天母西路79號五樓', '', NULL, NULL, NULL, '中國信託', '北天母分行', '822', '3201', '************', '', '', '<EMAIL>', '', '', '', '', '', '', '', NULL, 'M170020', 0),
			(255, 'momoisgood2', '408ffd58-bc4f-4c03-abc4-3c095d21d424', '', 59, 'B226861159', '', '**********', '111臺北市士林區天母西路79號五樓', '', NULL, NULL, NULL, '中國信託', '北天母分行', '822', '3201', '************', '', '', '', '', '', '', '', '', '', '<EMAIL>', NULL, 'M170020', 0),
			(33, 'momoisgood3', '1af4cba1-59de-45d0-b3cc-719c58eef0d7', '', 60, 'C226861159', '1988-04-03', '**********', '111臺北市士林區天母西路79號五樓', '', NULL, NULL, NULL, '中國信託', '北天母分行', '822', '3201', '************', '', '', '<EMAIL>', '', '', '', '', '', '', '', NULL, 'M170020', 0),
			(34, 'momoisgood4', 'aaaaaaaa-59de-45d0-b3cc-719c58eef0d7', '', 60, 'D226861159', '88-04-03', '**********', '111臺北市士林區天母西路79號五樓', '', NULL, NULL, NULL, '中國信託', '北天母分行', '822', '3201', '************', '', '', '<EMAIL>', '', '', '', '', '', '', '', NULL, 'M170020', 0),
			(1233, 'momoisgood3', 'a1f4cba1-59de-45d0-b3cc-719c58eef0d7', '', 123456, 'C226861159', '1988-04-03', '**********', '111臺北市士林區天母西路79號五樓', '', NULL, NULL, NULL, '中國信託', '北天母分行', '822', '3201', '************', 'mockPayeeName', 'mockPayeeIDNumber', '<EMAIL>', 'mockBankName', 'mockBankBranchFK', '777', '7777', '77777', 'mockPayeeNameFK', '', NULL, 'M170020', 0),
			(123456, 'momoisgood5', 'aaaaaaaa-59de-45d0-b3cc-719c58eef0d7', '', 12345, 'D226861159', '88-04-03', '**********', '111臺北市士林區天母西路79號五樓', '', NULL, NULL, NULL, '中國信託', '北天母分行', '822', '3201', '************', '', '', '<EMAIL>', '', '', '', '', '', '', '', NULL, 'M170020', 0),
			(1234567, 'momoisgood6', 'for delete and update', '', 999, 'D226861159', '88-04-03', '**********', '111臺北市士林區天母西路79號五樓', '', NULL, NULL, NULL, '中國信託', '北天母分行', '822', '3201', '************', '', '', '<EMAIL>', '', '', '', '', '', '', '', NULL, 'M170020', 0),
			(100, 'momoisgood7', 'terminate', '', 999, 'Z226861159', '88-04-03', '**********', '111臺北市士林區天母西路79號五樓', '', NULL, NULL, NULL, '中國信託', '北天母分行', '822', '3201', '************', '', '', '<EMAIL>', '', '', '', '', '', '', '', NULL, 'M170020', 0);`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`CREATE TABLE IF NOT EXISTS Labels (
			id int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
			label varchar(20) NOT NULL,
			region varchar(20) NOT NULL,
			type int(11) UNSIGNED NOT NULL,
			createTimeMs bigint(20) NOT NULL,
			PRIMARY KEY (id),
			UNIQUE KEY region_label (region, label),
			INDEX type (type)
		  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`CREATE TABLE IF NOT EXISTS ContractLabels (
			contractID int(11) UNSIGNED NOT NULL,
			labelID int(11) UNSIGNED NOT NULL,
			createTimeMs bigint(20) NOT NULL,
			PRIMARY KEY (contractID, labelID),
			INDEX label (labelID)
		  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`CREATE TABLE IF NOT EXISTS ContractLabelLogs (
			id int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
			action tinyint(1) NOT NULL,
			contractID int(11) UNSIGNED NOT NULL,
			labelID int(11) UNSIGNED NOT NULL,
			execAdminID int(11) UNSIGNED NOT NULL DEFAULT 0,
			timeMs bigint(20) NOT NULL,
			PRIMARY KEY (id),
			INDEX contract (contractID)
		  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`INSERT INTO Agency (id, name, adminID, agentID, email, bankName, bankBranch, bankCode, bankBranchCode, bankAccount, payeeName, payeeIDNumber, paypalAccount, bankNameFK, bankBranchFK, bankCodeFK, bankBranchCodeFK, bankAccountFK, payeeNameFK, feeOwnerType, others, isDeleted, userID)
		VALUES
			(1, '<EMAIL>', 107, 0, '<EMAIL>', '中國信託', '北天母分行', '822', '3201', '************', 'agencyPayeeName', 'agencyPayeeIDNumber', '<EMAIL>', 'agencyBankNameFK', 'agencyBankBranchFK', '999', '9999', '99999', 'agencyPayeeNameFK', 1, '', 0, ''
		);`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`INSERT INTO AdminUsers (id, username, password, permissionCode, email, name, picture, region, type, jwtToken, userID17)
		VALUES
			(107, '<EMAIL>', '', 0, '', 'Teddy Lu', '', 'TW', '', '', NULL),
			(119, '<EMAIL>', '', 0, '', 'Jenny', '', 'TW', '', '', NULL),
			(368, '<EMAIL>', '', 0, '', 'Test JP', '', 'JP', '', '', NULL),
			(999, '<EMAIL>', '', 0, '', 'Test', '', 'TW', '', '', NULL
		);`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`INSERT INTO RegionSetting (code, name, timezone, currency, currencySymbol, weight, common, ` + "`group`" + `, groupTimezone, groupStartTime, groupEndTime)
		VALUES
			('TW', 'Taiwan', 'Asia/Taipei', 'TWD', 'NT$', 2900, 1, 'TW', 'Asia/Taipei', 0, ***********),
			('JP', 'Japan', 'Asia/Tokyo', 'JPY', '¥', 2600, 1, 'JP', 'Asia/Tokyo', 0, ***********
		);`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`INSERT INTO Labels (id, label, region, type, createTimeMs)
		VALUES
			(1, 'dance', 'TW', 1, 1556092800000),
			(2, 'funny', 'TW', 1, 1556092800000),
			(3, 'student', 'TW', 2, 1556092800000),
			(4, 'payment', 'TW', 3, 1556092800000),
			(5, 'payToAgency', 'JP', 3, 1556092800000),
			(6, 'payToStreamer', 'TW', 3, 1556092800000
		);`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`INSERT INTO ContractLabels (contractID, labelID, createTimeMs)
		VALUES
			(1, 4, 1556100000000 
		);`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`INSERT INTO ContractLabelLogs (id, action, contractID, labelID, execAdminID, timeMs)
		VALUES
			(1, 0, 1, 4, 107, 1552100000000),
			(2, 1, 1, 4, 107, 1554100000000),
			(3, 0, 1, 4, 107, 1556100000000 
		);`); err != nil {
		panic(err)
	}

	if _, err := db.Exec(
		`CREATE TABLE IF NOT EXISTS ContractTerminateInfo (
			id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
			contractID int(11) UNSIGNED NOT NULL,
			execAdminID int(11) UNSIGNED NOT NULL DEFAULT 0,
			createTimeMs bigint(20) NOT NULL,
			originalDateEnd date NOT NULL,
			PRIMARY KEY (id),
			INDEX contractID (contractID)
		  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;`); err != nil {
		panic(err)
	}
}

func (cs *ContractSuite) TestGetContractByUserID() {
	tests := []struct {
		Desc    string
		UserID  string
		mockNow time.Time
		ExpErr  error
		ExpRes  *ctModel.Contract
	}{
		{
			Desc:    "no valid contract",
			UserID:  mockContract.UserID,
			mockNow: time.Date(2020, 4, 2, 16, 0, 0, 0, time.UTC),
			ExpErr:  ErrContractNotFound,
			ExpRes:  nil,
		},
		{
			Desc:    "valid contract",
			UserID:  mockContract.UserID,
			mockNow: time.Date(2017, 4, 2, 16, 0, 0, 0, time.UTC),
			ExpErr:  nil,
			ExpRes:  mockContract,
		},
		{
			Desc:    "upcoming valid contract",
			UserID:  mockContract.UserID,
			mockNow: time.Date(2017, 3, 30, 16, 0, 0, 0, time.UTC),
			ExpErr:  ErrContractNotFound,
			ExpRes:  nil,
		},
	}

	for _, test := range tests {
		cs.mockFuncs.On("TimeNow").Return(test.mockNow).Once()
		var contract *ctModel.Contract
		var err error

		contract, err = cs.Store.GetContractByUserID(mockCTX, test.UserID)

		cs.Equal(test.ExpErr, err, test.Desc)
		cs.Equal(test.ExpRes, contract, test.Desc)

		// clear cache
		cs.Store.Helper.DeleteContractCaches(mockCTX, test.UserID)
	}
}

func (cs *ContractSuite) TestGetContractWithoutTermsByUserIDs() {
	mockC := &ctModel.Contract{
		Id:               3,
		Type:             0,
		AgentID:          107,
		AgencyID:         107,
		StreamerID:       33,
		ParentContractID: 0,
		UserID:           "1af4cba1-59de-45d0-b3cc-719c58eef0d7",
		DateStart:        "2017-04-01",
		DateEnd:          "2018-03-31",
		Exclusive:        0,
		PayToParent:      1,
		Others:           "當月進月榜前50名 發20,000元新台幣",
		Attachment:       "",
		IsDeleted:        0,
		Region:           "TW",
		IsUnofficial:     0,
		Birthday:         "1988-04-03",
		Timezone:         "Asia/Taipei",
		StartTime:        **********,
		EndTime:          **********,
		FeeOwnerType:     2, // "User",
		PayoutAccounts:   map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
	}

	tests := []struct {
		Desc    string
		UserIDs []string
		mockNow time.Time
		opt     time.Time
		ExpErr  error
		ExpRes  map[string]*ctModel.Contract
	}{
		{
			Desc:    "no opt, no valid contract",
			UserIDs: []string{mockContract.UserID},
			mockNow: time.Date(2020, 4, 2, 16, 0, 0, 0, time.UTC),
			ExpErr:  ErrContractNotFound,
			ExpRes:  nil,
		},
		{
			Desc:    "no opt, valid contract",
			UserIDs: []string{mockContract.UserID},
			mockNow: time.Date(2017, 4, 2, 16, 0, 0, 0, time.UTC),
			ExpErr:  nil,
			ExpRes: map[string]*ctModel.Contract{
				mockContract.UserID: mockContract,
			},
		},
		{
			Desc:    "no opt, return one valid contract",
			UserIDs: []string{mockContract.UserID, "for delete and update"},
			mockNow: time.Date(2017, 4, 2, 16, 0, 0, 0, time.UTC),
			ExpErr:  nil,
			ExpRes: map[string]*ctModel.Contract{
				mockContract.UserID: mockContract,
			},
		},
		{
			Desc:    "no opt, valid contracts",
			UserIDs: []string{mockContract.UserID, mockContractUserID3},
			mockNow: time.Date(2017, 4, 2, 16, 0, 0, 0, time.UTC),
			ExpErr:  nil,
			ExpRes: map[string]*ctModel.Contract{
				mockContract.UserID: mockContract,
				mockContractUserID3: mockC,
			},
		},
		{
			Desc:    "opt, valid contracts",
			UserIDs: []string{mockContract.UserID, mockContractUserID3},
			mockNow: time.Date(2016, 4, 2, 16, 0, 0, 0, time.UTC),
			opt:     time.Date(2017, 4, 2, 16, 0, 0, 0, time.UTC),
			ExpErr:  nil,
			ExpRes: map[string]*ctModel.Contract{
				mockContract.UserID: mockContract,
				mockContractUserID3: mockC,
			},
		},
		{
			Desc:    "opt, return two valid contracts",
			UserIDs: []string{mockContract.UserID, "for delete and update", mockContractUserID3},
			mockNow: time.Date(2016, 4, 2, 16, 0, 0, 0, time.UTC),
			opt:     time.Date(2017, 4, 2, 16, 0, 0, 0, time.UTC),
			ExpErr:  nil,
			ExpRes: map[string]*ctModel.Contract{
				mockContract.UserID: mockContract,
				mockContractUserID3: mockC,
			},
		},
	}

	for _, test := range tests {
		cs.mockFuncs.On("TimeNow").Return(test.mockNow).Once()
		var res map[string]*ctModel.Contract
		var err error

		if test.opt.IsZero() {
			res, err = cs.Store.GetContractWithoutTermsByUserIDs(mockCTX, test.UserIDs)
		} else {
			res, err = cs.Store.GetContractWithoutTermsByUserIDs(mockCTX, test.UserIDs, test.opt)
		}

		cs.Equal(test.ExpErr, err, test.Desc)
		cs.Equal(len(test.ExpRes), len(res), test.Desc)

		for k, v := range test.ExpRes {
			if _, ok := res[k]; !ok {
				cs.Equal(true, false, test.Desc)
			}

			cs.Nil(res[k].Terms, test.Desc)

			// don't cate terms and birthday
			tmp := *v
			tmp.Terms = nil
			tmp.Birthday = ""
			cs.Equal(tmp, *res[k], test.Desc)
		}
	}
}

func (cs *ContractSuite) TestGetLogs() {
	tests := []struct {
		Desc     string
		IDs      []int
		MockFunc func()
		ExpErr   error
		ExpRes   map[int32][]*ctModel.ResLogs
	}{
		{
			Desc: "empty",
			IDs:  []int{int(mockContract.Id)},
			MockFunc: func() {
				cs.mockIntraLog.On("Get", mockCTX, intralog.TableContract, []int64{int64(mockContract.Id)}).Return([]*intralog.AuditLog{}, nil).Once()
			},
			ExpErr: nil,
			ExpRes: map[int32][]*ctModel.ResLogs{},
		},
		{
			Desc: "success",
			IDs:  []int{9},
			MockFunc: func() {
				cs.mockIntraLog.On("Get", mockCTX, intralog.TableContract, []int64{int64(9)}).Return([]*intralog.AuditLog{
					{
						Table:   intralog.TableContract,
						RowID:   9,
						AdminID: 999,
						Time:    time.Date(2017, 7, 19, 3, 23, 30, 0, time.UTC),
						Action:  "update",
						Data:    "data",
					},
				}, nil).Once()
				cs.mockUser.On("GetAdminUserByIDs", mockCTX, []int{999}).Return(map[int]*intra.User{
					999: {ID: 999, Name: "Test"},
				}, nil).Once()
			},
			ExpErr: nil,
			ExpRes: map[int32][]*ctModel.ResLogs{
				9: {
					{
						AdminID:   999,
						Time:      "2017-07-19 03:23:30",
						Action:    "update",
						Data:      "data",
						AgentName: "Test",
						RowID:     9,
					},
				},
			},
		},
		{
			Desc: "no admin user",
			IDs:  []int{9},
			MockFunc: func() {
				cs.mockIntraLog.On("Get", mockCTX, intralog.TableContract, []int64{int64(9)}).Return([]*intralog.AuditLog{
					{
						Table:   intralog.TableContract,
						RowID:   9,
						AdminID: 999,
						Time:    time.Date(2017, 7, 19, 3, 23, 30, 0, time.UTC),
						Action:  "update",
						Data:    "data",
					},
				}, nil).Once()
				cs.mockUser.On("GetAdminUserByIDs", mockCTX, []int{999}).Return(map[int]*intra.User{}, nil).Once()
			},
			ExpErr: nil,
			ExpRes: map[int32][]*ctModel.ResLogs{
				9: {
					{
						AdminID:   999,
						Time:      "2017-07-19 03:23:30",
						Action:    "update",
						Data:      "data",
						AgentName: "",
						RowID:     9,
					},
				},
			},
		},
	}

	for _, test := range tests {
		test.MockFunc()
		account, err := cs.Store.GetLogs(mockCTX, test.IDs)
		cs.Equal(test.ExpErr, err, test.Desc)
		cs.Equal(test.ExpRes, account, test.Desc)
	}
}

func (cs *ContractSuite) TestGetDashboardMonthData() {
	expRes := []*ctModel.MonthContractData{
		{
			Year:      2016,
			Month:     7,
			Contracts: 4,
		},
		{
			Year:      2017,
			Month:     7,
			Contracts: 5,
		},
		{
			Year:      2018,
			Month:     7,
			Contracts: 2,
		},
		{
			Year:      2022,
			Month:     7,
			Contracts: 1,
		},
	}
	res, err := cs.Store.GetDashboardMonthData(mockCTX)
	cs.NoError(err)
	cs.Equal(len(expRes), len(res), "Data length is not equal")
	for i, data := range res {
		cs.Equal(expRes[i].Year, data.Year, "Year is not equal")
		cs.Equal(expRes[i].Month, data.Month, "Month is not equal")
		cs.Equal(expRes[i].Contracts, data.Contracts, "Contracts number is not equal")
	}
}

func (cs *ContractSuite) TestGetOfficalContractCount() {
	mockNow := time.Date(2019, 3, 30, 0, 0, 0, 0, time.UTC)
	cs.mockFuncs.On("TimeNow").Return(mockNow)
	official, unOfficial, err := cs.Store.GetOfficalContractCount(mockCTX)
	cs.NoError(err)
	cs.Equal(5, official, "offical streamer count is not equal")
	cs.Equal(1, unOfficial, "unOffical streamer count is not equal")
}

func (cs *ContractSuite) TestContractDiff() {
	mockWithoutTerms := *mockContract
	mockWithoutTerms.Terms = nil

	tests := []struct {
		desc           string
		original       *ctModel.Contract
		new            *ctModel.Contract
		expAddTerms    []*ctModel.ContractTerm
		expRemoveTerms []*ctModel.ContractTerm
		expRes         string
	}{
		{
			desc:           "both nil",
			expRes:         "{}",
			expAddTerms:    []*ctModel.ContractTerm{},
			expRemoveTerms: []*ctModel.ContractTerm{},
		},
		{
			desc:           "equal",
			original:       &mockWithoutTerms,
			new:            &mockWithoutTerms,
			expRes:         "{}",
			expAddTerms:    []*ctModel.ContractTerm{},
			expRemoveTerms: []*ctModel.ContractTerm{},
		},
		{
			desc:           "all change except terms",
			original:       &ctModel.Contract{},
			new:            &mockWithoutTerms,
			expRes:         `{"agencyID":"0 -> 107","agentID":"0 -> 107","dateEnd":" -> 2018-03-31","dateStart":" -> 2017-04-01","feeOwnerType":"0 -> 2","others":" -> 0401-0630 保底61,200免拆帳,30hr 0401-0630 分潤102,264免拆帳","payToParent":"0 -> 1","region":" -> TW","streamerID":"0 -> 777","timezone":" -> Asia/Taipei","userID":" -> 8de87814-05e3-4d9b-bb70-b70543b87fa9"}`,
			expAddTerms:    []*ctModel.ContractTerm{},
			expRemoveTerms: []*ctModel.ContractTerm{},
		},
		{
			desc:     "add terms",
			original: &ctModel.Contract{Id: 10000},
			new: &ctModel.Contract{
				Terms: []*ctModel.ContractTerm{
					{
						ContractID: 10000,
						Condition:  "",
						Gte:        0,
						Lt:         0,
						Prop:       "PERMILLE_SUBRUN",
						Value:      0,
					},
				},
			},
			expAddTerms: []*ctModel.ContractTerm{
				{
					ContractID: 10000,
					Condition:  "",
					Gte:        0,
					Lt:         0,
					Prop:       "PERMILLE_SUBRUN",
					Value:      0,
				},
			},
			expRemoveTerms: []*ctModel.ContractTerm{},
			expRes:         `{"terms":{"add":[{"id":0,"contractID":10000,"condition":"","gte":0,"lt":0,"prop":"PERMILLE_SUBRUN","value":0,"dateStart":"","dateEnd":"","contractType":0}]}}`,
		},
		{
			desc: "remove terms",
			original: &ctModel.Contract{
				Terms: []*ctModel.ContractTerm{
					{
						Id:         6,
						ContractID: 10000,
						Condition:  "",
						Gte:        0,
						Lt:         0,
						Prop:       "PERMILLE_SUBRUN",
						Value:      0,
					},
				},
			},
			new:         &ctModel.Contract{},
			expRes:      `{"terms":{"remove":[{"id":6,"contractID":10000,"condition":"","gte":0,"lt":0,"prop":"PERMILLE_SUBRUN","value":0,"dateStart":"","dateEnd":"","contractType":0}]}}`,
			expAddTerms: []*ctModel.ContractTerm{},
			expRemoveTerms: []*ctModel.ContractTerm{
				{
					Id:         6,
					ContractID: 10000,
					Condition:  "",
					Gte:        0,
					Lt:         0,
					Prop:       "PERMILLE_SUBRUN",
					Value:      0,
				},
			},
		},
		{
			desc: "add and remove terms",
			original: &ctModel.Contract{
				Terms: []*ctModel.ContractTerm{
					{
						Id:         6,
						ContractID: 10000,
						Condition:  "",
						Gte:        0,
						Lt:         0,
						Prop:       "PERMILLE_SUBRUN",
						Value:      300,
					},
				},
			},
			new: &ctModel.Contract{
				Terms: []*ctModel.ContractTerm{
					{
						ContractID: 10000,
						Condition:  "",
						Gte:        0,
						Lt:         0,
						Prop:       "PERMILLE_SUBRUN",
						Value:      250,
					},
				},
			},
			expRes: `{"terms":{"add":[{"id":0,"contractID":10000,"condition":"","gte":0,"lt":0,"prop":"PERMILLE_SUBRUN","value":250,"dateStart":"","dateEnd":"","contractType":0}],"remove":[{"id":6,"contractID":10000,"condition":"","gte":0,"lt":0,"prop":"PERMILLE_SUBRUN","value":300,"dateStart":"","dateEnd":"","contractType":0}]}}`,
			expAddTerms: []*ctModel.ContractTerm{
				{
					ContractID: 10000,
					Condition:  "",
					Gte:        0,
					Lt:         0,
					Prop:       "PERMILLE_SUBRUN",
					Value:      250,
				},
			},
			expRemoveTerms: []*ctModel.ContractTerm{
				{
					Id:         6,
					ContractID: 10000,
					Condition:  "",
					Gte:        0,
					Lt:         0,
					Prop:       "PERMILLE_SUBRUN",
					Value:      300,
				},
			},
		},
		{
			desc: "Extended Contract case",
			original: &ctModel.Contract{
				Id:           10000,
				AgentID:      107,
				Exclusive:    0,
				Others:       "Extended Contract",
				Attachment:   "",
				IsUnofficial: 1,
				Terms: []*ctModel.ContractTerm{
					{
						Id:         6,
						ContractID: 10000,
						Condition:  "",
						Gte:        0,
						Lt:         0,
						Prop:       "PERMILLE_SUBRUN",
						Value:      300,
					},
					{
						Id:         8,
						ContractID: 10000,
						Condition:  "POINTS",
						Gte:        0,
						Lt:         300000,
						Prop:       "BONUS",
						Value:      0,
					},
				},
			},
			new: &ctModel.Contract{
				Id:           10000,
				AgentID:      127,
				Exclusive:    1,
				Others:       "",
				Attachment:   "null",
				IsUnofficial: 0,
				Terms: []*ctModel.ContractTerm{
					{
						ContractID: 10000,
						Condition:  "POINTS",
						Gte:        0,
						Lt:         300000,
						Prop:       "BONUS",
						Value:      0,
					},
					{
						ContractID: 10000,
						Condition:  "POINTS",
						Gte:        300000,
						Lt:         500000,
						Prop:       "BONUS",
						Value:      20000,
					},
					{
						ContractID: 10000,
						Condition:  "POINTS",
						Gte:        500000,
						Lt:         1000000,
						Prop:       "BONUS",
						Value:      25000,
					},
					{
						ContractID: 10000,
						Condition:  "POINTS",
						Gte:        1000000,
						Lt:         -1,
						Prop:       "BONUS",
						Value:      30000,
					},
				},
			},
			expRes: `{"agentID":"107 -> 127","exclusive":"0 -> 1","isUnofficial":"1 -> 0","others":"Extended Contract -> ","terms":{"add":[{"id":0,"contractID":10000,"condition":"POINTS","gte":300000,"lt":500000,"prop":"BONUS","value":20000,"dateStart":"","dateEnd":"","contractType":0},{"id":0,"contractID":10000,"condition":"POINTS","gte":500000,"lt":1000000,"prop":"BONUS","value":25000,"dateStart":"","dateEnd":"","contractType":0},{"id":0,"contractID":10000,"condition":"POINTS","gte":1000000,"lt":-1,"prop":"BONUS","value":30000,"dateStart":"","dateEnd":"","contractType":0}],"remove":[{"id":6,"contractID":10000,"condition":"","gte":0,"lt":0,"prop":"PERMILLE_SUBRUN","value":300,"dateStart":"","dateEnd":"","contractType":0}]}}`,
			expAddTerms: []*ctModel.ContractTerm{
				{
					ContractID: 10000,
					Condition:  "POINTS",
					Gte:        300000,
					Lt:         500000,
					Prop:       "BONUS",
					Value:      20000,
				},
				{
					ContractID: 10000,
					Condition:  "POINTS",
					Gte:        500000,
					Lt:         1000000,
					Prop:       "BONUS",
					Value:      25000,
				},
				{
					ContractID: 10000,
					Condition:  "POINTS",
					Gte:        1000000,
					Lt:         -1,
					Prop:       "BONUS",
					Value:      30000,
				},
			},
			expRemoveTerms: []*ctModel.ContractTerm{
				{
					Id:         6,
					ContractID: 10000,
					Condition:  "",
					Gte:        0,
					Lt:         0,
					Prop:       "PERMILLE_SUBRUN",
					Value:      300,
				},
			},
		},
	}

	for _, t := range tests {
		res, addTerms, removeTerms, err := contractDiff(t.original, t.new)
		cs.NoError(err)
		cs.Equal(t.expRes, res, t.desc)
		cs.NotNil(t.expAddTerms, t.desc)
		cs.NotNil(t.expRemoveTerms, t.desc)
		cs.Equal(len(t.expAddTerms), len(addTerms), t.desc)
		for i, c := range addTerms {
			cs.Equal(t.expAddTerms[i], c, t.desc)
		}
		cs.Equal(len(t.expRemoveTerms), len(removeTerms), t.desc)
		for i, c := range removeTerms {
			cs.Equal(t.expRemoveTerms[i], c, t.desc)
		}
	}
}

func (cs *ContractSuite) TestCalculateIncomes() {
	mockMetrics := &ctModel.Metrics{
		Points:            10000,
		ValidLiveDuration: 30,
		PeakFollowers:     300,
	}

	cs.rate.On("GetRate", mockCTX, "TW", "201812").Return(30.0, nil)
	cs.mockUser.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return("TW", nil)
	cs.giftRevenue.On("GetPointToRevenueRatio", mock.AnythingOfType("ctx.CTX"), "TW", mock.AnythingOfType("time.Time")).Return(0.004555393586006, nil)
	incomes, err := cs.Store.CalculateIncomes(mockCTX, mockContract, mockMetrics, "201812")
	cs.Require().NoError(err)
	cs.Len(incomes, 5)
}

func (cs *ContractSuite) TestCalculateStreamerAgencyIncomes() {
	mockMetrics := &ctModel.Metrics{
		Points:            10000,
		ValidLiveDuration: 30,
		PeakFollowers:     300,
	}
	cs.rate.On("GetRate", mockCTX, "TW", "201812").Return(30.0, nil)
	cs.mockUser.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return("TW", nil)
	cs.giftRevenue.On("GetPointToRevenueRatio", mock.AnythingOfType("ctx.CTX"), "TW", mock.AnythingOfType("time.Time")).Return(0.004555393586006, nil)
	incomes, err := cs.Store.CalculateStreamerAgencyIncomes(mockCTX, mockContract, mockMetrics, "201812")
	cs.Require().NoError(err)
	cs.Len(incomes, 5)
}

func (cs *ContractSuite) TestCalculateAgencyIncomes() {
	mockTermRecords := ctModel.TermRecords{
		ctModel.AgencyContractType: map[ctModel.ContractTermType][]*ctModel.TermRecord{
			ctModel.ContractTermType_AGCY_AVERAGE_POINT_STREAMER_COUNT_CRITERIA: {
				{
					Prop:         "AGENCY_BONUS_AVERAGE_POINT_STREAMER_COUNT_CRITERIA",
					Condition:    "",
					Gte:          0,
					Lt:           0,
					Value:        2,
					ContractType: ctModel.AgencyContractType,
				},
			},
			ctModel.ContractTermType_AGCY_AVERAGE_POINT_AVERAGE_POINT_CRITERIA: {
				{
					Prop:         "AGENCY_BONUS_AVERAGE_POINT_AVERAGE_POINT_CRITERIA",
					Condition:    "",
					Gte:          0,
					Lt:           0,
					Value:        1000,
					ContractType: ctModel.AgencyContractType,
				},
			},
			ctModel.ContractTermType_AGCY_AVERAGE_POINT_AVERAGE_HOURS_CRITERIA: {
				{
					Prop:         "AGENCY_BONUS_AVERAGE_POINT_AVERAGE_HOURS_CRITERIA",
					Condition:    "",
					Gte:          0,
					Lt:           0,
					Value:        10,
					ContractType: ctModel.AgencyContractType,
				},
			},
			ctModel.ContractTermType_AGCY_BONUS_AVERAGE_POINT: {
				{
					Prop:         "AGENCY_BONUS_AVERAGE_POINT",
					Condition:    "",
					Gte:          0,
					Lt:           300000,
					Value:        0,
					ContractType: ctModel.AgencyContractType,
				},
				{
					Prop:         "AGENCY_BONUS_AVERAGE_POINT",
					Condition:    "",
					Gte:          300000,
					Lt:           500000,
					Value:        20000,
					ContractType: ctModel.AgencyContractType,
				},
				{
					Prop:         "AGENCY_BONUS_AVERAGE_POINT",
					Condition:    "",
					Gte:          500000,
					Lt:           1000000,
					Value:        25000,
					ContractType: ctModel.AgencyContractType,
				},
				{
					Prop:         "AGENCY_BONUS_AVERAGE_POINT",
					Condition:    "",
					Gte:          1000000,
					Lt:           -1,
					Value:        30000,
					ContractType: ctModel.AgencyContractType,
				},
			},
		},
	}
	mockStreamerMetrics := []ctModel.Metrics{
		{
			Points:            10000,
			ValidLiveDuration: 30,
			PeakFollowers:     300,
		},
		{
			Points:            20000,
			ValidLiveDuration: 10,
			PeakFollowers:     400,
		},
	}
	mockAgencyMetric := ctModel.AgencyMetrics{
		StreamerCount:     2,
		Points:            30000,
		ValidLiveDuration: 40,
	}

	incomes, err := cs.Store.CalculateAgencyIncomes(mockCTX, mockTermRecords, mockStreamerMetrics, mockAgencyMetric)
	cs.Require().NoError(err)
	cs.Len(incomes, 1)
}

func (cs *ContractSuite) TestGetStreamerTermOverview() {
	mockMetrics := &ctModel.Metrics{
		Points:            10000,
		ValidLiveDuration: 30,
		PeakFollowers:     300,
	}

	overview, err := cs.Store.GetTermOverview(mockCTX, mockContract, mockMetrics, ctModel.StreamerContractType)
	cs.Require().NoError(err)
	cs.Len(overview, 4)
	cs.Equal("0-10hr 0%\n10-30hr 100%\n30+hr 100%", overview[ctModel.ContractTermType_PERCENT_WAGE_RATIO].Description)
}

func (cs *ContractSuite) TestGetLabelsByContractIDs() {
	tests := []struct {
		Desc        string
		ContractIDs []int32
		Opt         []time.Time
		ExpLabels   map[int32][]*sModel.Label
		ExpErr      error
	}{
		{
			Desc:        "Normal",
			ContractIDs: []int32{1},
			Opt:         []time.Time{},
			ExpLabels: map[int32][]*sModel.Label{
				1: {
					{
						ID:           4,
						Label:        "payment",
						Region:       "TW",
						Type:         sModel.Payment,
						CreateTimeMs: 1556092800000,
					},
				},
			},
			ExpErr: nil,
		},
		{
			Desc:        "Empty",
			ContractIDs: []int32{},
			Opt:         []time.Time{},
			ExpLabels:   map[int32][]*sModel.Label{},
			ExpErr:      nil,
		},
		{
			Desc:        "Labels with timestamp",
			ContractIDs: []int32{1},
			Opt:         []time.Time{time.Unix(1552100000, 0)},
			ExpLabels: map[int32][]*sModel.Label{
				1: {
					{
						ID:           4,
						Label:        "payment",
						Region:       "TW",
						Type:         sModel.Payment,
						CreateTimeMs: 1556092800000,
					},
				},
			},
			ExpErr: nil,
		},
		{
			Desc:        "Empty labels with timestamp",
			ContractIDs: []int32{1},
			Opt:         []time.Time{time.Unix(1554100000, 0)},
			ExpLabels:   map[int32][]*sModel.Label{},
			ExpErr:      nil,
		},
	}

	for _, t := range tests {
		labels, err := cs.Store.GetLabelsByContractIDs(mockCTX, t.ContractIDs, t.Opt...)
		cs.Equal(t.ExpLabels, labels, t.Desc)
		cs.Equal(t.ExpErr, err, t.Desc)
	}
}

func (cs *ContractSuite) TestAddLabel() {
	tests := []struct {
		Desc      string
		Region    string
		Label     string
		labelType sModel.LabelType
		ExpID     int32
		ExpErr    error
		SetupMock func()
		CleanMock func()
	}{
		{
			Desc:      "Normal",
			Region:    "JP",
			Label:     "RC專案",
			labelType: sModel.Payment,
			ExpID:     7,
			ExpErr:    nil,
			SetupMock: func() {
				cs.mockFuncs.On("TimeNow").Return(currentTime).Once()
			},
			CleanMock: func() {
				cs.mockFuncs.AssertExpectations(cs.T())
			},
		},
		{
			Desc:      "Duplicated Label",
			Region:    "TW",
			Label:     "payment",
			labelType: sModel.Payment,
			ExpErr:    ErrLabelDuplicated,
			SetupMock: func() {
				cs.mockFuncs.On("TimeNow").Return(currentTime).Once()
			},
			CleanMock: func() {
				cs.mockFuncs.AssertExpectations(cs.T())
			},
		},
	}

	for _, t := range tests {
		t.SetupMock()

		ID, err := cs.Store.AddLabel(mockCTX, t.Region, t.Label, t.labelType)
		if t.ExpErr == nil {
			cs.Equal(t.ExpID, ID, t.Desc)
		}
		cs.Equal(t.ExpErr, err, t.Desc)

		t.CleanMock()
	}
}

func (cs *ContractSuite) TestListLabels() {
	tests := []struct {
		Desc      string
		Options   []ListLabelsOption
		ExpLabels []*sModel.Label
		ExpErr    error
	}{
		{
			Desc:    "Normal",
			Options: []ListLabelsOption{},
			ExpLabels: []*sModel.Label{
				{
					ID:           4,
					Label:        "payment",
					Region:       "TW",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
				{
					ID:           5,
					Label:        "payToAgency",
					Region:       "JP",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
				{
					ID:           6,
					Label:        "payToStreamer",
					Region:       "TW",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
			},
			ExpErr: nil,
		},
		{
			Desc: "List with type",
			Options: []ListLabelsOption{
				WithLabelTypes([]sModel.LabelType{sModel.Payment}),
			},
			ExpLabels: []*sModel.Label{
				{
					ID:           4,
					Label:        "payment",
					Region:       "TW",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
				{
					ID:           5,
					Label:        "payToAgency",
					Region:       "JP",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
				{
					ID:           6,
					Label:        "payToStreamer",
					Region:       "TW",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
			},
			ExpErr: nil,
		},
		{
			Desc: "List with region",
			Options: []ListLabelsOption{
				WithRegions([]string{"TW"}),
			},
			ExpLabels: []*sModel.Label{
				{
					ID:           4,
					Label:        "payment",
					Region:       "TW",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
				{
					ID:           6,
					Label:        "payToStreamer",
					Region:       "TW",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
			},
			ExpErr: nil,
		},
		{
			Desc: "List with region and type",
			Options: []ListLabelsOption{
				WithRegions([]string{"JP"}),
				WithLabelTypes([]sModel.LabelType{sModel.Payment}),
			},
			ExpLabels: []*sModel.Label{
				{
					ID:           5,
					Label:        "payToAgency",
					Region:       "JP",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
			},
			ExpErr: nil,
		},
		{
			Desc: "List with invalid type",
			Options: []ListLabelsOption{
				WithLabelTypes([]sModel.LabelType{sModel.Genre}),
			},
			ExpLabels: []*sModel.Label{
				{
					ID:           4,
					Label:        "payment",
					Region:       "TW",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
				{
					ID:           5,
					Label:        "payToAgency",
					Region:       "JP",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
				{
					ID:           6,
					Label:        "payToStreamer",
					Region:       "TW",
					Type:         sModel.Payment,
					CreateTimeMs: 1556092800000,
				},
			},
			ExpErr: nil,
		},
	}

	for _, t := range tests {
		labels, err := cs.Store.ListLabels(mockCTX, t.Options...)
		if t.ExpErr == nil {
			cs.Equal(t.ExpLabels, labels, t.Desc)
		}
		cs.Equal(t.ExpErr, err, t.Desc)
	}
}

func (cs *ContractSuite) TestGetOwnerIDs() {
	tests := []struct {
		Desc           string
		ContractIDs    []int
		ExpOwnerIDsMap intra.OwnerIDs
		ExpErr         error
	}{
		{
			Desc:        "Good",
			ContractIDs: []int{2, 10, 12},
			ExpOwnerIDsMap: intra.OwnerIDs{
				2:  []int{0, 119},
				10: []int{456, 123},
				12: []int{456, 368},
			},
			ExpErr: nil,
		},
	}

	for _, t := range tests {
		ownerIDsMap, err := cs.Store.GetOwnerIDs(mockCTX, t.ContractIDs)
		cs.Equal(ownerIDsMap, t.ExpOwnerIDsMap, t.Desc+", compare OwnerIDsMap")
		cs.Equal(err, t.ExpErr, t.Desc+", compare Error")
	}
}

func (cs *ContractSuite) TestSendEvents() {
	mockC0 := ctModel.Contract{
		UserID:    "mockUser0",
		DateStart: "2019-09-10",
		DateEnd:   "2020-05-15",
		Timezone:  "Asia/Taipei",
	}
	// end+1
	mockC1 := ctModel.Contract{
		UserID:    "mockUser0",
		DateStart: "2019-09-10",
		DateEnd:   "2020-05-16",
		Timezone:  "Asia/Taipei",
	}
	// start+1
	mockC2 := ctModel.Contract{
		UserID:    "mockUser0",
		DateStart: "2019-09-11",
		DateEnd:   "2020-05-15",
		Timezone:  "Asia/Taipei",
	}
	// start+1, end+1
	mockC3 := ctModel.Contract{
		UserID:    "mockUser0",
		DateStart: "2019-09-11",
		DateEnd:   "2020-05-16",
		Timezone:  "Asia/Taipei",
	}

	type tbc struct {
		timestamp int64
		value     evSnapshotModel.ContractValue
	}

	tests := []struct {
		desc   string
		inOP   operation
		mockF  func(chan tbc)
		expRes []tbc
	}{
		{
			desc: "create",
			inOP: operation{
				typ: opCreate,
				old: nil,
				new: &myContract{&mockC0},
			},
			mockF: func(res chan tbc) {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockPublisher.On("Publish", anyCTX, stlModel.CriteriasEvent{
					UserIDs: []string{mockC0.UserID},
				}).Return(nil).Once()
				cs.mockEventory.On("SendEvent", mock.AnythingOfType("ctx.CTX"), mock.Anything).Run(func(args mock.Arguments) {
					res <- tbc{
						timestamp: args[1].(*evModel.Event).StateChangeEvent.Timestamp,
						value:     args[1].(*evModel.Event).StateChangeEvent.Value.(evSnapshotModel.ContractValue),
					}
				}).Return(nil).Twice()
			},
			expRes: []tbc{{1568044801, 1}, {1589558400, 0}},
		},
		{
			desc: "delete",
			inOP: operation{
				typ: opDelete,
				old: &myContract{&mockC0},
				new: nil,
			},
			mockF: func(res chan tbc) {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockPublisher.On("Publish", anyCTX, stlModel.CriteriasEvent{
					UserIDs: []string{mockC0.UserID},
				}).Return(nil).Once()
				cs.mockEventory.On("SendEvent", mock.AnythingOfType("ctx.CTX"), mock.Anything).Run(func(args mock.Arguments) {
					res <- tbc{
						timestamp: args[1].(*evModel.Event).StateChangeEvent.Timestamp,
						value:     args[1].(*evModel.Event).StateChangeEvent.Value.(evSnapshotModel.ContractValue),
					}
				}).Return(nil).Twice()
			},
			expRes: []tbc{{1568044801, -1}, {1589558400, -1}},
		},
		{
			desc: "update without any changes",
			inOP: operation{
				typ: opUpdate,
				old: &myContract{&mockC0},
				new: &myContract{&mockC0},
			},
			mockF: func(res chan tbc) {
			},
		},
		{
			desc: "update with start date changed",
			inOP: operation{
				typ: opUpdate,
				old: &myContract{&mockC0},
				new: &myContract{&mockC2},
			},
			mockF: func(res chan tbc) {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockPublisher.On("Publish", anyCTX, stlModel.CriteriasEvent{
					UserIDs: []string{mockC2.UserID},
				}).Return(nil).Once()
				cs.mockEventory.On("SendEvent", mock.AnythingOfType("ctx.CTX"), mock.Anything).Run(func(args mock.Arguments) {
					// 1 ignore, 1 new
					res <- tbc{
						timestamp: args[1].(*evModel.Event).StateChangeEvent.Timestamp,
						value:     args[1].(*evModel.Event).StateChangeEvent.Value.(evSnapshotModel.ContractValue),
					}
				}).Return(nil).Twice()
			},
			expRes: []tbc{{1568044801, -1}, {1568131201, 1}},
		},
		{
			desc: "update with end date changed",
			inOP: operation{
				typ: opUpdate,
				old: &myContract{&mockC0},
				new: &myContract{&mockC1},
			},
			mockF: func(res chan tbc) {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockPublisher.On("Publish", anyCTX, stlModel.CriteriasEvent{
					UserIDs: []string{mockC1.UserID},
				}).Return(nil).Once()
				cs.mockEventory.On("SendEvent", mock.AnythingOfType("ctx.CTX"), mock.Anything).Run(func(args mock.Arguments) {
					// 1 ignore, 1 new
					res <- tbc{
						timestamp: args[1].(*evModel.Event).StateChangeEvent.Timestamp,
						value:     args[1].(*evModel.Event).StateChangeEvent.Value.(evSnapshotModel.ContractValue),
					}
				}).Return(nil).Twice()
			},
			expRes: []tbc{{1589558400, -1}, {1589644800, 0}},
		},
		{
			desc: "update with both date changed",
			inOP: operation{
				typ: opUpdate,
				old: &myContract{&mockC0},
				new: &myContract{&mockC3},
			},
			mockF: func(res chan tbc) {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockPublisher.On("Publish", anyCTX, stlModel.CriteriasEvent{
					UserIDs: []string{mockC3.UserID},
				}).Return(nil).Once()
				cs.mockEventory.On("SendEvent", mock.AnythingOfType("ctx.CTX"), mock.Anything).Run(func(args mock.Arguments) {
					// 2 ignore, 2 new
					res <- tbc{
						timestamp: args[1].(*evModel.Event).StateChangeEvent.Timestamp,
						value:     args[1].(*evModel.Event).StateChangeEvent.Value.(evSnapshotModel.ContractValue),
					}
				}).Return(nil).Times(4)
			},
			expRes: []tbc{{1568044801, -1}, {1589558400, -1}, {1568131201, 1}, {1589644800, 0}},
		},
		{
			desc: "terminate",
			inOP: operation{
				typ: opTerminate,
				old: &myContract{&mockC0},
				new: &myContract{&mockC1},
			},
			mockF: func(res chan tbc) {
				cs.mockFuncs.On("TimeNow").Return(currentTime)
				cs.mockPublisher.On("Publish", anyCTX, stlModel.CriteriasEvent{
					UserIDs: []string{mockC1.UserID},
				}).Return(nil).Once()
				cs.mockEventory.On("SendEvent", mock.AnythingOfType("ctx.CTX"), mock.Anything).Run(func(args mock.Arguments) {
					// 1 ignore, 1 new
					res <- tbc{
						timestamp: args[1].(*evModel.Event).StateChangeEvent.Timestamp,
						value:     args[1].(*evModel.Event).StateChangeEvent.Value.(evSnapshotModel.ContractValue),
					}
				}).Return(nil).Twice()
			},
			expRes: []tbc{{1589558400, -1}, {1589644800, 0}},
		},
	}

	for _, t := range tests {
		cs.Run(t.desc, func() {
			res := make(chan tbc, len(t.expRes))
			t.mockF(res)
			cs.Store.sendEvents(mockCTX, &t.inOP)
			for _, exp := range t.expRes {
				cs.Equal(exp, <-res)
			}
		})
	}
}

func TestContractSuite(t *testing.T) {
	c := new(ContractSuite)

	suite.Run(t, c)
}

func TestGetSplitIDs(t *testing.T) {
	pIDs := pairedIDs{
		&pairedID{
			AgentID:    1,
			AgencyID:   2,
			ContractID: 3,
		},
		&pairedID{
			AgentID:    4,
			AgencyID:   5,
			ContractID: 6,
		},
	}
	expOwnerIDs := []int64{1, 2, 4, 5}
	expContractIDs := []int{3, 3, 6, 6}
	ownerIDs, contractIDs := pIDs.getSplitIDs()
	assert.Equal(t, expOwnerIDs, ownerIDs)
	assert.Equal(t, expContractIDs, contractIDs)
}

func (cs *ContractSuite) TestGetPayoutAccounts() {
	getRegionGroup = func(context ctx.CTX, region string) (string, error) {
		return "TW", nil
	}
	getAvailableAccountType = func(group string) []paModel.AccountType {
		return []paModel.AccountType{paModel.AccountType_LOCAL_ACCOUNT, paModel.AccountType_FOREIGN_ACCOUNT}
	}
	type testCase struct {
		desc     string
		region   string
		agencyID int32
		mockFunc func(testCase)
		expRet   map[string]*paModel.PayoutAccount
	}

	tests := []testCase{
		{
			desc:     "simple case",
			region:   "TW",
			agencyID: 0,
			mockFunc: func(t testCase) {
				cs.mockPayoutAccount.
					On("List", mock.AnythingOfType("ctx.CTX"), paModel.OwnerType_OWNER_TYPE_STREAMER, mockUserID).
					Return([]*paModel.PayoutAccount{
						{Id: 1, AccountType: paModel.AccountType_LOCAL_ACCOUNT},
						{Id: 2, AccountType: paModel.AccountType_COMPANY_ACCOUNT},
					}, nil).Once()
			},
			expRet: map[string]*paModel.PayoutAccount{
				"STREAMER-LOCAL": {Id: 1, AccountType: paModel.AccountType_LOCAL_ACCOUNT},
			},
		},
		{
			desc:     "simple case, agencyID != 0",
			region:   "TW",
			agencyID: 1,
			mockFunc: func(t testCase) {
				cs.mockPayoutAccount.
					On("List", mock.AnythingOfType("ctx.CTX"), paModel.OwnerType_OWNER_TYPE_STREAMER, mockUserID).
					Return([]*paModel.PayoutAccount{
						{Id: 1, AccountType: paModel.AccountType_LOCAL_ACCOUNT},
						{Id: 2, AccountType: paModel.AccountType_COMPANY_ACCOUNT},
					}, nil).Once()
				cs.mockPayoutAccount.
					On("List", mock.AnythingOfType("ctx.CTX"), paModel.OwnerType_OWNER_TYPE_AGENCY, strconv.Itoa(int(t.agencyID))).
					Return([]*paModel.PayoutAccount{
						{Id: 3, AccountType: paModel.AccountType_FOREIGN_ACCOUNT},
					}, nil).Once()
			},
			expRet: map[string]*paModel.PayoutAccount{
				"STREAMER-LOCAL":   {Id: 1, AccountType: paModel.AccountType_LOCAL_ACCOUNT},
				"STREAMER-FOREIGN": {Id: 3, AccountType: paModel.AccountType_FOREIGN_ACCOUNT},
			},
		},
	}

	for _, t := range tests {
		if t.mockFunc != nil {
			t.mockFunc(t)
		}
		ret, err := cs.Store.getPayoutAccounts(mockCTX, mockUserID, t.region, t.agencyID)
		cs.Require().NoError(err)
		cs.Require().Equal(t.expRet, ret, t.desc)
	}
}

func (cs *ContractSuite) TestSetContractByContractCSVValidation() {
	getPayoutAccounts = func(im *impl, context ctx.CTX, userID string, region string, agencyID int32) (map[string]*paModel.PayoutAccount, error) {
		return map[string]*paModel.PayoutAccount{
			"STREAMER-LOCAL":   {Id: 1},
			"STREAMER-OFFLINE": {Id: 2},
			"STREAMER-FOREIGN": {Id: 3},
		}, nil
	}
	findTemplate = func(im *impl, context ctx.CTX, adminIDs []int, templateID int, optFuncs ...template.OptFunc) (*ctModel.ContractTemplate, error) {
		return &ctModel.ContractTemplate{
			Region: "TW",
			Terms: []*ctModel.ContractTermTemplate{
				{Prop: ctModel.ContractTermType_PERMILLE_SUBRUN.String()}, {Condition: "cond2"},
			},
		}, nil
	}
	type testCase struct {
		desc        string
		user        *intra.User
		adminIds    []int
		validation  contractCSVValidation
		contract    *ctModel.Contract
		mockFunc    func(testCase)
		expCols     []string
		expContract *ctModel.Contract
	}

	pStr := func(a string) *string {
		return &a
	}
	tests := []testCase{
		{
			desc: "Fully field success update",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			validation: contractCSVValidation{
				TemplateID:               pStr("1"),
				StreamerID:               pStr("66"),
				Agent:                    pStr("<EMAIL>"),
				Agency:                   pStr("<EMAIL>"),
				AgencyContractTemplateID: pStr("2"),
				DateStart:                pStr("2021-01-01"),
				DateEnd:                  pStr("2021-02-01"),
				Region:                   pStr("TW"),
				Exclusive:                pStr("Exclusive"),
				Unofficial:               pStr("unofficial"),
				SemiContract:             pStr("SemiContract"),
				Others:                   pStr("test"),
				RevenuePayoutType:        pStr("STREAMER-LOCAL"),
				SalaryPayoutType:         pStr("STREAMER-OFFLINE"),
				EventPayoutType:          pStr("streamer-FOREIGN"),
				PaymentRemark:            pStr("label,label2"),
			},
			contract: &ctModel.Contract{
				Id: 1,
			},
			mockFunc: func(t testCase) {
				cs.mockStreamer.On("Find", mock.AnythingOfType("ctx.CTX"), []int{66}, t.adminIds, int(t.user.ID)).Return([]*ctModel.LegacyStreamer{
					{Id: 66},
				}, nil).Once()
				cs.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), *t.validation.Agent).Return(&intra.User{
					ID:     2,
					Region: "TW",
				}, nil).Once()
				cs.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), *t.validation.Agency).Return(&intra.User{
					ID: 3,
				}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
			},
			expContract: &ctModel.Contract{
				Id:                       1,
				StreamerID:               66,
				AgentID:                  2,
				AgencyID:                 3,
				AgencyContractTemplateID: 2,
				DateStart:                "2021-01-01",
				DateEnd:                  "2021-02-01",
				Region:                   "TW",
				Timezone:                 "Asia/Taipei",
				Exclusive:                1,
				IsUnofficial:             1,
				IsSemiContract:           1,
				Others:                   "test",
				Payment:                  "label,label2",
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{
					ctModel.PayoutCategory_REVENUE: {Id: 1},
					ctModel.PayoutCategory_SALARY:  {Id: 2},
					ctModel.PayoutCategory_EVENT:   {Id: 3},
				},
				Terms: []*ctModel.ContractTerm{{Prop: ctModel.ContractTermType_PERMILLE_SUBRUN.String()}, {Condition: "cond2"}},
			},
		},
		{
			desc: "streamer is userID",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			validation: contractCSVValidation{
				StreamerID: pStr(mockUserID),
			},
			contract: &ctModel.Contract{
				Id: 1,
			},
			mockFunc: func(t testCase) {
				cs.mockStreamer.On("FindByUserID", mock.AnythingOfType("ctx.CTX"), mockUserID).Return(&ctModel.LegacyStreamer{
					Id: 10,
				}, nil).Once()
			},
			expContract: &ctModel.Contract{
				Id:         1,
				StreamerID: 10,
			},
		},
		{
			desc: "fail case",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			validation: contractCSVValidation{
				TemplateID:               pStr("a"),
				StreamerID:               pStr(mockUserID),
				Agent:                    pStr("a"),
				Agency:                   pStr("a"),
				AgencyContractTemplateID: pStr("a"),
				DateStart:                pStr("a"),
				DateEnd:                  pStr("a"),
				Region:                   pStr("a"),
				Exclusive:                pStr("a"),
				Unofficial:               pStr("a"),
				SemiContract:             pStr("a"),
				RevenuePayoutType:        pStr("a"),
				SalaryPayoutType:         pStr("a"),
				EventPayoutType:          pStr("a"),
				PaymentRemark:            pStr("a,b,c,d,e"),
			},
			contract: &ctModel.Contract{
				Id: 1,
			},
			mockFunc: func(t testCase) {
				cs.mockStreamer.On("FindByUserID", mock.AnythingOfType("ctx.CTX"), mockUserID).Return(nil, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "a").Return("", fmt.Errorf("error")).Once()
			},
			expCols: []string{
				"Template ID", "StreamerID / UserID", "Streamer Agent", "Streamer Agency", "Agency Contract Template ID", "Date Start", "Date End",
				"Located Region", "Exclusive", "Unofficial", "SemiContract", "Revenue Payout Type", "Salary Payout Type", "Event Payout Type", "Payment Remark",
			},
		},
		{
			desc: "update case, agentRegion != templateRegion - 1",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			validation: contractCSVValidation{
				TemplateID:               pStr("1"),
				StreamerID:               pStr("66"),
				Agent:                    pStr("<EMAIL>"),
				Agency:                   pStr("<EMAIL>"),
				AgencyContractTemplateID: pStr("2"),
				DateStart:                pStr("2021-01-01"),
				DateEnd:                  pStr("2021-02-01"),
				Region:                   pStr("TW"),
				Exclusive:                pStr("Exclusive"),
				Unofficial:               pStr("unofficial"),
				SemiContract:             pStr("SemiContract"),
				Others:                   pStr("test"),
				RevenuePayoutType:        pStr("STREAMER-LOCAL"),
				SalaryPayoutType:         pStr("STREAMER-OFFLINE"),
				EventPayoutType:          pStr("streamer-FOREIGN"),
				PaymentRemark:            pStr("label,label2"),
			},
			contract: &ctModel.Contract{
				Id: 1,
			},
			mockFunc: func(t testCase) {
				cs.mockStreamer.On("Find", mock.AnythingOfType("ctx.CTX"), []int{66}, t.adminIds, int(t.user.ID)).Return([]*ctModel.LegacyStreamer{
					{Id: 66},
				}, nil).Once()
				cs.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), *t.validation.Agent).Return(&intra.User{
					ID:     2,
					Region: "JP",
				}, nil).Once()
				cs.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), *t.validation.Agency).Return(&intra.User{
					ID: 3,
				}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
			},
			expCols: []string{
				"Template ID",
			},
		},
		{
			desc: "update case, agentRegion != templateRegion - 2",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			validation: contractCSVValidation{
				TemplateID:               pStr("1"),
				StreamerID:               pStr("66"),
				Agency:                   pStr("<EMAIL>"),
				AgencyContractTemplateID: pStr("2"),
				DateStart:                pStr("2021-01-01"),
				DateEnd:                  pStr("2021-02-01"),
				Region:                   pStr("TW"),
				Exclusive:                pStr("Exclusive"),
				Unofficial:               pStr("unofficial"),
				SemiContract:             pStr("SemiContract"),
				Others:                   pStr("test"),
				RevenuePayoutType:        pStr("STREAMER-LOCAL"),
				SalaryPayoutType:         pStr("STREAMER-OFFLINE"),
				EventPayoutType:          pStr("streamer-FOREIGN"),
				PaymentRemark:            pStr("label,label2"),
			},
			contract: &ctModel.Contract{
				Id:      1,
				AgentID: 2,
			},
			mockFunc: func(t testCase) {
				cs.mockStreamer.On("Find", mock.AnythingOfType("ctx.CTX"), []int{66}, t.adminIds, int(t.user.ID)).Return([]*ctModel.LegacyStreamer{
					{Id: 66},
				}, nil).Once()
				cs.mockUser.On("GetAdminUserByID", mock.AnythingOfType("ctx.CTX"), int(t.contract.AgentID)).Return(&intra.User{
					ID:     2,
					Region: "JP",
				}, nil).Once()
				cs.mockUser.On("Get", mock.AnythingOfType("ctx.CTX"), *t.validation.Agency).Return(&intra.User{
					ID: 3,
				}, nil).Once()
				cs.mockRegion.On("GetGroupTimeZoneByRegion", mock.AnythingOfType("ctx.CTX"), "TW").Return("Asia/Taipei", nil).Once()
			},
			expCols: []string{
				"Template ID",
			},
		},
	}

	for _, t := range tests {
		if t.mockFunc != nil {
			t.mockFunc(t)
		}
		cols, _ := cs.Store.setContractByContractCSVValidation(mockCTX, t.user, t.adminIds, t.validation, t.contract)
		cs.Require().Equal(t.expCols, cols, t.desc)
		if len(cols) > 0 {
			continue
		}
		cs.Require().Equal(t.expContract, t.contract, t.desc)
	}
}

func (cs *ContractSuite) TestParseUpdateCSVToContracts() {
	type testCase struct {
		desc               string
		user               *intra.User
		adminIds           []int
		file               io.ReadSeeker
		maxBatchUpdateSize int
		mockFunc           func(testCase)
		expContract        []*ctModel.Contract
	}

	pStr := func(a string) *string {
		return &a
	}
	tests := []testCase{
		{
			desc: "normal case",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			file: strings.NewReader(`Contract ID,Template ID,Streamer Agent,Streamer Agency,Agency Contract Template ID,Date Start,Date End,Exclusive,Unofficial,SemiContract,Others,Revenue Payout Type,Salary Payout Type,Event Payout Type,Payment Remark
2000,null,null,null,null,null,null,null,null,null,null,null,null,null,null
2001,405,null,<EMAIL>,null,null,null,null,null,null,null,null,null,null,null
2002,405,<EMAIL>,<EMAIL>,1,2021-01-01,2021-01-31,Exclusive,official,SemiContract,NoteForTest,Streamer-OFFLINE,Streamer-OFFLINE2,Streamer-OFFLINE3,"RC專案,神秘人專案,ADA專案"`),
			maxBatchUpdateSize: 1000,
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("FindContract", cs.Store, mock.AnythingOfType("ctx.CTX"), t.adminIds, int(t.user.ID), []int{2000, 2001, 2002}).
					Return([]*ctModel.Contract{
						{Id: 2000}, {Id: 2001}, {Id: 2002},
					}, nil).Once()
				cs.mockFuncs.On("SetContractByContractCSVValidation", cs.Store, mock.AnythingOfType("ctx.CTX"), t.user, t.adminIds, contractCSVValidation{}, &ctModel.Contract{
					Id: 2000,
				}).Return([]string{}, []string{}).Once()
				cs.mockFuncs.On("SetContractByContractCSVValidation", cs.Store, mock.AnythingOfType("ctx.CTX"), t.user, t.adminIds, contractCSVValidation{
					TemplateID: pStr("405"),
					Agency:     pStr("<EMAIL>"),
				}, &ctModel.Contract{
					Id: 2001,
				}).Return([]string{}, []string{}).Once()
				cs.mockFuncs.On("SetContractByContractCSVValidation", cs.Store, mock.AnythingOfType("ctx.CTX"), t.user, t.adminIds, contractCSVValidation{
					TemplateID:               pStr("405"),
					Agent:                    pStr("<EMAIL>"),
					Agency:                   pStr("<EMAIL>"),
					AgencyContractTemplateID: pStr("1"),
					DateStart:                pStr("2021-01-01"),
					DateEnd:                  pStr("2021-01-31"),
					Exclusive:                pStr("Exclusive"),
					Unofficial:               pStr("official"),
					SemiContract:             pStr("SemiContract"),
					Others:                   pStr("NoteForTest"),
					RevenuePayoutType:        pStr("Streamer-OFFLINE"),
					SalaryPayoutType:         pStr("Streamer-OFFLINE2"),
					EventPayoutType:          pStr("Streamer-OFFLINE3"),
					PaymentRemark:            pStr("RC專案,神秘人專案,ADA專案"),
				}, &ctModel.Contract{
					Id: 2002,
				}).Return([]string{}, []string{}).Once()
			},
			expContract: []*ctModel.Contract{
				{Id: 2000}, {Id: 2001}, {Id: 2002},
			},
		},
		{
			desc: "equal maxBatchUpdateSize",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			file: strings.NewReader(`Contract ID,Template ID,Streamer Agent,Streamer Agency,Agency Contract Template ID,Date Start,Date End,Exclusive,Unofficial,SemiContract,Others,Revenue Payout Type,Salary Payout Type,Event Payout Type,Payment Remark
2000,null,null,null,null,null,null,null,null,null,null,null,null,null,null`),
			maxBatchUpdateSize: 1,
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("FindContract", cs.Store, mock.AnythingOfType("ctx.CTX"), t.adminIds, int(t.user.ID), []int{2000}).
					Return([]*ctModel.Contract{
						{Id: 2000},
					}, nil).Once()
				cs.mockFuncs.On("SetContractByContractCSVValidation", cs.Store, mock.AnythingOfType("ctx.CTX"), t.user, t.adminIds, contractCSVValidation{}, &ctModel.Contract{
					Id: 2000,
				}).Return([]string{}, []string{}).Once()
			},
			expContract: []*ctModel.Contract{
				{Id: 2000},
			},
		},
		{
			desc: "exceed maxBatchUpdateSize",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			file: strings.NewReader(`Contract ID,Template ID,Streamer Agent,Streamer Agency,Agency Contract Template ID,Date Start,Date End,Exclusive,Unofficial,SemiContract,Others,Revenue Payout Type,Salary Payout Type,Event Payout Type,Payment Remark
2000,null,null,null,null,null,null,null,null,null,null,null,null,null,null`),
			maxBatchUpdateSize: 0,
			mockFunc: func(t testCase) {
			},
		},
		{
			desc: "invalid template",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			file: strings.NewReader(`Contract ID,Template ID2,Streamer Agent,Streamer Agency,Agency Contract Template ID,Date Start,Date End,Exclusive,Unofficial,SemiContract,Others,Revenue Payout Type,Salary Payout Type,Event Payout Type,Payment Remark
2000,null,null,null,null,null,null,null,null,null,null,null,null,null,null`),
			maxBatchUpdateSize: 1,
		},
		{
			desc: "invalid template, extra field",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			file: strings.NewReader(`Contract ID,Template ID,Streamer Agent,Streamer Agency,Agency Contract Template ID,Date Start,Date End,Exclusive,Unofficial,SemiContract,Others,Revenue Payout Type,Salary Payout Type,Event Payout Type,Payment Remark,aaa
2000,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null`),
			maxBatchUpdateSize: 1,
		},
	}

	for _, t := range tests {
		if t.mockFunc != nil {
			t.mockFunc(t)
		}
		getConfig = func() contractConfig.Config {
			return contractConfig.Config{
				MaxBatchUpdateSize: t.maxBatchUpdateSize,
			}
		}
		contracts, _, err := cs.Store.ParseUpdateCSVToContracts(mockCTX, t.file, t.user, t.adminIds)
		cs.Require().NoError(err, t.desc)
		cs.Require().Equal(t.expContract, contracts, t.desc)
	}
}

func (cs *ContractSuite) TestParseCreateCSVToContracts() {
	type testCase struct {
		desc               string
		user               *intra.User
		adminIds           []int
		file               io.ReadSeeker
		maxBatchCteateSize int
		mockFunc           func(testCase)
		expContract        []*ctModel.Contract
	}

	pStr := func(a string) *string {
		return &a
	}
	tests := []testCase{
		{
			desc: "normal case",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			file: strings.NewReader(`Template ID,StreamerID / UserID,Streamer Agent,Streamer Agency,Agency Contract Template ID,Date Start,Date End,Located Region,Exclusive,Unofficial,SemiContract,Others,Revenue Payout Type,Salary Payout Type,Event Payout Type,Payment Remark
405,99999,<EMAIL>,<EMAIL>,1,2021-01-01,2021-01-31,EG,non-Exclusive,Unofficial,Non-SemiContract,NoteForTest,Streamer-OFFLINE,Streamer-OFFLINE2,Streamer-OFFLINE3,"RC專案,神秘人專案,ADA專案"`),
			maxBatchCteateSize: 1000,
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("SetContractByContractCSVValidation", cs.Store, mock.AnythingOfType("ctx.CTX"), t.user, t.adminIds, contractCSVValidation{
					TemplateID:               pStr("405"),
					StreamerID:               pStr("99999"),
					Agent:                    pStr("<EMAIL>"),
					Agency:                   pStr("<EMAIL>"),
					AgencyContractTemplateID: pStr("1"),
					DateStart:                pStr("2021-01-01"),
					DateEnd:                  pStr("2021-01-31"),
					Region:                   pStr("EG"),
					Exclusive:                pStr("non-Exclusive"),
					Unofficial:               pStr("Unofficial"),
					SemiContract:             pStr("Non-SemiContract"),
					Others:                   pStr("NoteForTest"),
					RevenuePayoutType:        pStr("Streamer-OFFLINE"),
					SalaryPayoutType:         pStr("Streamer-OFFLINE2"),
					EventPayoutType:          pStr("Streamer-OFFLINE3"),
					PaymentRemark:            pStr("RC專案,神秘人專案,ADA專案"),
				}, &ctModel.Contract{
					PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
				}).Return([]string{}, []string{}).Once()
			},
			expContract: []*ctModel.Contract{
				{
					PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{},
				},
			},
		},
		{
			desc: "exceed maxBatchCreateSize",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			file: strings.NewReader(`Template ID,StreamerID / UserID,Streamer Agent,Streamer Agency,Agency Contract Template ID,Date Start,Date End,Located Region,Exclusive,Unofficial,SemiContract,Others,Revenue Payout Type,Salary Payout Type,Event Payout Type,Payment Remark
405,99999,<EMAIL>,<EMAIL>,1,2021-01-01,2021-01-31,EG,non-Exclusive,Unofficial,Non-SemiContract,NoteForTest,Streamer-OFFLINE,Streamer-OFFLINE2,Streamer-OFFLINE3,"RC專案,神秘人專案,ADA專案"`),
			maxBatchCteateSize: 0,
			mockFunc: func(t testCase) {
			},
		},
		{
			desc: "invalid template",
			user: &intra.User{
				ID: 1,
			},
			adminIds: []int{1},
			file: strings.NewReader(`Template ID,StreamerID / UserID2,Streamer Agent,Streamer Agency,Agency Contract Template ID,Date Start,Date End,Located Region,Exclusive,Unofficial,SemiContract,Others,Revenue Payout Type,Salary Payout Type,Event Payout Type,Payment Remark
405,99999,<EMAIL>,<EMAIL>,1,2021-01-01,2021-01-31,EG,non-Exclusive,Unofficial,Non-SemiContract,NoteForTest,Streamer-OFFLINE,Streamer-OFFLINE2,Streamer-OFFLINE3,"RC專案,神秘人專案,ADA專案"`),
			maxBatchCteateSize: 1,
		},
	}

	for _, t := range tests {
		if t.mockFunc != nil {
			t.mockFunc(t)
		}
		getConfig = func() contractConfig.Config {
			return contractConfig.Config{
				MaxBatchCreateSize: t.maxBatchCteateSize,
			}
		}
		contracts, _, err := cs.Store.ParseCreateCSVToContracts(mockCTX, t.file, t.user, t.adminIds)
		cs.Require().NoError(err, t.desc)
		cs.Require().Equal(t.expContract, contracts, t.desc)
	}
}

func (cs *ContractSuite) TestGetAsyncBatchTaskProgress() {
	batchResult := &BatchResult{
		Msg: []BatchMsg{{ID: 2}},
	}
	json, _ := json.Marshal(batchResult)
	type testCase struct {
		desc      string
		taskID    string
		mockFunc  func(testCase)
		task      *taskprogress.Task
		expResult *BatchResult
		expErr    error
	}
	tests := []testCase{
		{
			desc:   "success case",
			taskID: "test",
			mockFunc: func(t testCase) {
				t.task.UpdateStatus(taskProgressModol.TaskStatusSucceed)
				cs.mockTaskprogress.On("Get", mock.AnythingOfType("ctx.CTX"), t.taskID).Return(t.task, nil).Once()
			},
			task:      &taskprogress.Task{},
			expResult: &BatchResult{},
		},
		{
			desc:   "error case - batchResult",
			taskID: "test",
			mockFunc: func(t testCase) {
				t.task.UpdateResult(map[string]string{"batchResult": string(json)})
				t.task.UpdateStatus(taskProgressModol.TaskStatusFailed)
				cs.mockTaskprogress.On("Get", mock.AnythingOfType("ctx.CTX"), t.taskID).Return(t.task, nil).Once()
			},
			task:      &taskprogress.Task{},
			expResult: batchResult,
		},
		{
			desc:   "error case - system error",
			taskID: "test",
			mockFunc: func(t testCase) {
				t.task.UpdateMsg("error")
				t.task.UpdateStatus(taskProgressModol.TaskStatusFailed)
				cs.mockTaskprogress.On("Get", mock.AnythingOfType("ctx.CTX"), t.taskID).Return(t.task, nil).Once()
			},
			task:   &taskprogress.Task{},
			expErr: fmt.Errorf("error"),
		},
	}

	for _, t := range tests {
		if t.mockFunc != nil {
			t.mockFunc(t)
		}
		_, result, err := cs.Store.GetAsyncBatchTaskProgress(mockCTX, t.taskID)
		cs.Require().Equal(t.expErr, err, t.desc)
		cs.Require().Equal(t.expResult, result, t.desc)
	}
}

func (cs *ContractSuite) TestAsyncExecuteBatchTaskByCSV() {
	type testCase struct {
		desc           string
		mockFunc       func(testCase)
		task           *taskprogress.Task
		file           io.ReadSeeker
		execUser       *intra.User
		adminIDs       []int
		createOrUpdate string
		expTaskID      string
	}
	tests := []testCase{
		{
			desc:           "success case",
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "update",
			mockFunc: func(t testCase) {
				t.task.GetProgress().TaskID = "taskID"
				cs.mockTaskprogress.
					On("Run", mock.AnythingOfType("ctx.CTX"), 100, taskprogress.TaskOption{}, mock.Anything, t.file, t.execUser, t.adminIDs, t.createOrUpdate).
					Return(t.task, nil).Once()
			},
			task:      &taskprogress.Task{},
			expTaskID: "taskID",
		},
	}

	for _, t := range tests {
		if t.mockFunc != nil {
			t.mockFunc(t)
		}
		taskID, err := cs.Store.AsyncExecuteBatchTaskByCSV(mockCTX, t.file, t.execUser, t.adminIDs, t.createOrUpdate)
		cs.Require().NoError(err, t.desc)
		cs.Require().Equal(t.expTaskID, taskID, t.desc)
	}
}

func (cs *ContractSuite) TestExecuteBatchCSVTask() {
	type testCase struct {
		desc           string
		task           *taskprogress.Task
		file           io.ReadSeeker
		contracts      []*ctModel.Contract
		execUser       *intra.User
		adminIDs       []int
		createOrUpdate string
		mockFunc       func(testCase)
		expResult      map[string]string
		expError       error
	}
	tests := []testCase{
		{
			desc:           "create - success case",
			task:           &taskprogress.Task{},
			file:           strings.NewReader("test"),
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "create",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("parseCreateCSVToContracts", cs.Store, mock.AnythingOfType("ctx.CTX"), t.file, t.execUser, t.adminIDs).
					Return(t.contracts, (*BatchResult)(nil), nil).Once()
				cs.mockFuncs.On("batchCreate", cs.Store, mock.AnythingOfType("ctx.CTX"), t.adminIDs, int(t.execUser.ID), t.contracts).
					Return(([]int)(nil), 0, nil).Once()
			},
		},
		{
			desc:           "create - system error case",
			task:           &taskprogress.Task{},
			file:           strings.NewReader("test"),
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "create",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("parseCreateCSVToContracts", cs.Store, mock.AnythingOfType("ctx.CTX"), t.file, t.execUser, t.adminIDs).
					Return(t.contracts, (*BatchResult)(nil), fmt.Errorf("error")).Once()
			},
			expError: fmt.Errorf("error"),
		},
		{
			desc:           "create - system error case - 2",
			task:           &taskprogress.Task{},
			file:           strings.NewReader("test"),
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "create",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("parseCreateCSVToContracts", cs.Store, mock.AnythingOfType("ctx.CTX"), t.file, t.execUser, t.adminIDs).
					Return(t.contracts, (*BatchResult)(nil), nil).Once()
				cs.mockFuncs.On("batchCreate", cs.Store, mock.AnythingOfType("ctx.CTX"), t.adminIDs, int(t.execUser.ID), t.contracts).
					Return(([]int)(nil), 0, fmt.Errorf("error")).Once()
			},
			expError: fmt.Errorf("error"),
		},
		{
			desc:           "create - parse error case",
			task:           &taskprogress.Task{},
			file:           strings.NewReader("test"),
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "create",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("parseCreateCSVToContracts", cs.Store, mock.AnythingOfType("ctx.CTX"), t.file, t.execUser, t.adminIDs).
					Return(t.contracts, &BatchResult{
						Msg: []BatchMsg{{ID: 1}},
					}, nil).Once()
			},
			expError: fmt.Errorf("parse csv error"),
			expResult: map[string]string{
				"batchResult": "{\"Msg\":[{\"id\":1,\"col\":null,\"msg\":null}]}",
			},
		},
		{
			desc:           "create - batchCreate case",
			task:           &taskprogress.Task{},
			file:           strings.NewReader("test"),
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "create",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("parseCreateCSVToContracts", cs.Store, mock.AnythingOfType("ctx.CTX"), t.file, t.execUser, t.adminIDs).
					Return(t.contracts, (*BatchResult)(nil), nil).Once()
				cs.mockFuncs.On("batchCreate", cs.Store, mock.AnythingOfType("ctx.CTX"), t.adminIDs, int(t.execUser.ID), t.contracts).
					Return(([]int)(nil), 10, fmt.Errorf("error")).Once()
			},
			expError: fmt.Errorf("batchCreate error"),
			expResult: map[string]string{
				"batchResult": "{\"Msg\":[{\"id\":10,\"col\":[\"-1\"],\"msg\":[\"error\"]}]}",
			},
		},
		{
			desc:           "update - success case",
			task:           &taskprogress.Task{},
			file:           strings.NewReader("test"),
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "update",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("parseUpdateCSVToContracts", cs.Store, mock.AnythingOfType("ctx.CTX"), t.file, t.execUser, t.adminIDs).
					Return(t.contracts, (*BatchResult)(nil), nil).Once()
				cs.mockFuncs.On("batchUpdate", cs.Store, mock.AnythingOfType("ctx.CTX"), t.adminIDs, int(t.execUser.ID), t.contracts).
					Return(int32(0), nil).Once()
			},
		},
		{
			desc:           "update - system error case",
			task:           &taskprogress.Task{},
			file:           strings.NewReader("test"),
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "update",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("parseUpdateCSVToContracts", cs.Store, mock.AnythingOfType("ctx.CTX"), t.file, t.execUser, t.adminIDs).
					Return(t.contracts, (*BatchResult)(nil), fmt.Errorf("error")).Once()
			},
			expError: fmt.Errorf("error"),
		},
		{
			desc:           "update - system error case - 2",
			task:           &taskprogress.Task{},
			file:           strings.NewReader("test"),
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "update",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("parseUpdateCSVToContracts", cs.Store, mock.AnythingOfType("ctx.CTX"), t.file, t.execUser, t.adminIDs).
					Return(t.contracts, (*BatchResult)(nil), nil).Once()
				cs.mockFuncs.On("batchUpdate", cs.Store, mock.AnythingOfType("ctx.CTX"), t.adminIDs, int(t.execUser.ID), t.contracts).
					Return(int32(0), fmt.Errorf("error")).Once()
			},
			expError: fmt.Errorf("error"),
		},
		{
			desc:           "update - parse error case",
			task:           &taskprogress.Task{},
			file:           strings.NewReader("test"),
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "update",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("parseUpdateCSVToContracts", cs.Store, mock.AnythingOfType("ctx.CTX"), t.file, t.execUser, t.adminIDs).
					Return(t.contracts, &BatchResult{
						Msg: []BatchMsg{{ID: 1}},
					}, nil).Once()
			},
			expError: fmt.Errorf("parse csv error"),
			expResult: map[string]string{
				"batchResult": "{\"Msg\":[{\"id\":1,\"col\":null,\"msg\":null}]}",
			},
		},
		{
			desc:           "update - batchUpdate case",
			task:           &taskprogress.Task{},
			file:           strings.NewReader("test"),
			execUser:       &intra.User{ID: 1},
			adminIDs:       []int{1, 2, 3},
			createOrUpdate: "update",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("parseUpdateCSVToContracts", cs.Store, mock.AnythingOfType("ctx.CTX"), t.file, t.execUser, t.adminIDs).
					Return(t.contracts, (*BatchResult)(nil), nil).Once()
				cs.mockFuncs.On("batchUpdate", cs.Store, mock.AnythingOfType("ctx.CTX"), t.adminIDs, int(t.execUser.ID), t.contracts).
					Return(int32(10), fmt.Errorf("error")).Once()
			},
			expError: fmt.Errorf("batchUpdate error"),
			expResult: map[string]string{
				"batchResult": "{\"Msg\":[{\"id\":10,\"col\":[\"-1\"],\"msg\":[\"error\"]}]}",
			},
		},
	}

	for _, t := range tests {
		if t.mockFunc != nil {
			t.mockFunc(t)
		}
		taskID, err := cs.Store.executeBatchCSVTask(mockCTX, t.task, t.file, t.execUser, t.adminIDs, t.createOrUpdate)
		cs.Require().Equal(t.expError, err, t.desc)
		cs.Require().Equal(t.expResult, taskID, t.desc)
	}
}

func (cs *ContractSuite) TestValidateContractDateAndOperation() {
	mockContractID := int32(1)
	locationTW, _ := time.LoadLocation("Asia/Taipei")
	cs.mockFuncs.On("TimeNow").Return(time.Date(2022, 5, 23, 3, 0, 0, 0, locationTW)).Maybe()
	getConfig = func() contractConfig.Config {
		return contractConfig.Config{
			EnableValidateContractDateAndOperation: true,
		}
	}
	type testCase struct {
		desc         string
		origContract *ctModel.Contract
		newContract  *ctModel.Contract
		op           opType
		expErr       error
	}

	tests := []testCase{
		// create
		{
			desc: "success create not started contract",
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-25",
				DateEnd:   "2022-05-30",
			},
			op: opCreate,
		},
		{
			desc: "succ, create upcoming contract",
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-30",
			},
			op: opCreate,
		},
		{
			desc: "failed, create history contract",
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-21",
				DateEnd:   "2022-05-22",
			},
			op:     opCreate,
			expErr: ErrModifyPastContract,
		},
		{
			desc: "failed, create ongoing contract",
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-21",
				DateEnd:   "2022-05-23",
			},
			op:     opCreate,
			expErr: ErrModifyOngoingContract,
		},
		{
			desc: "failed, create termianted contract",
			newContract: &ctModel.Contract{
				Timezone:     "Asia/Taipei",
				DateStart:    "2022-05-21",
				DateEnd:      "2022-05-23",
				IsTerminated: 1,
			},
			op:     opCreate,
			expErr: ErrModifyTerminatedContract,
		},
		// update
		{
			desc: "success, update a not started contract",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-06-25",
				DateEnd:   "2022-06-30",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-25",
				DateEnd:   "2022-05-30",
			},
			op: opUpdate,
		},
		{
			desc: "failed, not started contract update to past time",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-25",
				DateEnd:   "2022-05-30",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-21",
				DateEnd:   "2022-05-30",
			},
			op:     opUpdate,
			expErr: ErrModifyContractToPastDate,
		},
		{
			desc: "update history contract but only change unimportant field",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-22",
				Payment:   "aaa",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-22",
				Payment:   "bbb",
			},
			op: opUpdate,
		},
		{
			desc: "update history contract, field - payout type",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-22",
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{
					ctModel.PayoutCategory_REVENUE: {
						Id: 1,
					},
				},
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-22",
				PayoutAccounts: map[ctModel.PayoutCategory]*paModel.PayoutAccount{
					ctModel.PayoutCategory_REVENUE: {
						Id: 2,
					},
				},
			},
			op: opUpdate,
		},
		{
			desc: "failed, update history contract, field - agencyContractTemplateID",
			origContract: &ctModel.Contract{
				Timezone:                 "Asia/Taipei",
				DateStart:                "2022-05-01",
				DateEnd:                  "2022-05-22",
				AgencyContractTemplateID: 0,
			},
			newContract: &ctModel.Contract{
				Timezone:                 "Asia/Taipei",
				DateStart:                "2022-05-01",
				DateEnd:                  "2022-05-22",
				AgencyContractTemplateID: 1,
			},
			op:     opUpdate,
			expErr: ErrModifyPastContract,
		},
		{
			desc: "failed, update history contract, field - date",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-22",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-30",
				DateEnd:   "2022-06-01",
			},
			op:     opUpdate,
			expErr: ErrModifyPastContract,
		},
		{
			desc: "failed, update history contract, field - terms",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-20",
				Terms: []*ctModel.ContractTerm{
					{
						Condition:    "abc",
						ContractType: ctModel.StreamerContractType,
					},
				},
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-20",
				Terms: []*ctModel.ContractTerm{
					{
						Condition:    "cde",
						ContractType: ctModel.StreamerContractType,
					},
				},
			},
			op:     opUpdate,
			expErr: ErrModifyPastContract,
		},
		{
			desc: "update history contract, field - terms no change",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-20",
				Terms: []*ctModel.ContractTerm{
					{
						Id:        1,
						Condition: "abc",
					},
					{
						Id:        2,
						Condition: "cde",
					},
					{
						Id:           3,
						Condition:    "agency term, should skip",
						ContractType: ctModel.StreamerAgencyContractType,
					},
				},
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-20",
				Terms: []*ctModel.ContractTerm{
					{
						Id:        2,
						Condition: "cde",
					},
					{
						Id:        1,
						Condition: "abc",
					},
					{
						Id:           4,
						Condition:    "agency term, should skip",
						ContractType: ctModel.StreamerAgencyContractType,
					},
				},
			},
			op: opUpdate,
		},
		{
			desc: "succ, update ongoing contract endDate to future date",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-23",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-30",
			},
			op: opUpdate,
		},
		{
			desc: "succ, update ongoing contract endDate to today",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-23",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-23",
			},
			op: opUpdate,
		},
		{
			desc: "failed, update ongoing contract, change timezone (change start time)",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-21",
				DateEnd:   "2022-05-30",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Tokyo",
				DateStart: "2022-05-21",
				DateEnd:   "2022-05-30",
			},
			op:     opUpdate,
			expErr: ErrModifyOngoingContract,
		},
		{
			desc: "failed, update ongoing contract endDate with past time",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-23",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-22",
			},
			op:     opUpdate,
			expErr: ErrModifyContractToPastDate,
		},
		{
			desc: "update ongoing contract dateEnd, with terms",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-28",
				Terms: []*ctModel.ContractTerm{
					{
						Id:           1,
						ContractID:   mockContractID,
						Condition:    "abc",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Id:           2,
						ContractID:   mockContractID,
						Condition:    "cde",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Id:           3,
						ContractID:   mockContractID,
						Condition:    "agency term, should skip",
						ContractType: ctModel.StreamerAgencyContractType,
					},
				},
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-29",
				Terms: []*ctModel.ContractTerm{
					{
						Condition:    "cde",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Condition:    "abc",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Condition:    "agency term, should skip",
						ContractType: ctModel.StreamerAgencyContractType,
					},
				},
			},
			op: opUpdate,
		},
		{
			desc: "failed, update ongoing contract dateEnd, with terms not equal",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-28",
				Terms: []*ctModel.ContractTerm{
					{
						Id:           1,
						ContractID:   mockContractID,
						Condition:    "abc",
						Value:        int32(100),
						ContractType: ctModel.StreamerContractType,
					},
					{
						Id:           2,
						ContractID:   mockContractID,
						Condition:    "cde",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Id:           3,
						ContractID:   mockContractID,
						Condition:    "agency term, should skip",
						ContractType: ctModel.StreamerAgencyContractType,
					},
				},
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-01",
				DateEnd:   "2022-05-29",
				Terms: []*ctModel.ContractTerm{
					{
						Condition:    "cde",
						Value:        int32(1000),
						ContractType: ctModel.StreamerContractType,
					},
					{
						Condition:    "abc",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Condition:    "agency term, should skip",
						ContractType: ctModel.StreamerAgencyContractType,
					},
				},
			},
			op:     opUpdate,
			expErr: ErrModifyOngoingContract,
		},
		{
			desc: "update upcoming contract dateEnd, with terms",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-28",
				Terms: []*ctModel.ContractTerm{
					{
						Id:           1,
						ContractID:   mockContractID,
						Condition:    "abc",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Id:           2,
						ContractID:   mockContractID,
						Condition:    "cde",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Id:           3,
						ContractID:   mockContractID,
						Condition:    "agency term, should skip",
						ContractType: ctModel.StreamerAgencyContractType,
					},
				},
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-29",
				Terms: []*ctModel.ContractTerm{
					{
						Condition:    "cde",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Condition:    "abc",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Condition:    "agency term, should skip",
						ContractType: ctModel.StreamerAgencyContractType,
					},
				},
			},
			op: opUpdate,
		},
		{
			desc: "failed, update upcoming contract dateEnd, with terms not equal",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-28",
				Terms: []*ctModel.ContractTerm{
					{
						Id:           1,
						ContractID:   mockContractID,
						Gte:          int32(100),
						Condition:    "abc",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Id:           2,
						ContractID:   mockContractID,
						Condition:    "cde",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Id:           3,
						ContractID:   mockContractID,
						Condition:    "agency term, should skip",
						ContractType: ctModel.StreamerAgencyContractType,
					},
				},
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-29",
				Terms: []*ctModel.ContractTerm{
					{
						Condition:    "cde",
						Gte:          int32(200),
						ContractType: ctModel.StreamerContractType,
					},
					{
						Condition:    "abc",
						ContractType: ctModel.StreamerContractType,
					},
					{
						Condition:    "agency term, should skip",
						ContractType: ctModel.StreamerAgencyContractType,
					},
				},
			},
			op:     opUpdate,
			expErr: ErrModifyUpcomingContract,
		},
		{
			desc: "succ, update upcoming contract endDate to future",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-30",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-24",
			},
			op: opUpdate,
		},
		{
			desc: "succ, update upcoming contract endDate to today",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-30",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-23",
			},
			op: opUpdate,
		},
		{
			desc: "succ, update upcoming contract endDate, different timezone (+7)",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Bangkok",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-30",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Bangkok",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-24",
			},
			op: opUpdate,
		},
		{
			desc: "failed, update upcoming contract endDate with past time, different timezone (+7)",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Bangkok",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-30",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Bangkok",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-22",
			},
			op:     opUpdate,
			expErr: ErrModifyContractToPastDate,
		},
		{
			desc: "failed, update upcoming contract endDate with past time",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-30",
			},
			newContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-22",
			},
			op:     opUpdate,
			expErr: ErrModifyContractToPastDate,
		},
		{
			desc: "failed, updated termianted contract",
			origContract: &ctModel.Contract{
				Timezone:     "Asia/Taipei",
				DateStart:    "2022-05-23",
				DateEnd:      "2022-05-30",
				IsTerminated: 1,
			},
			newContract: &ctModel.Contract{
				Timezone:     "Asia/Taipei",
				DateStart:    "2022-05-21",
				DateEnd:      "2022-05-23",
				IsTerminated: 1,
			},
			op:     opCreate,
			expErr: ErrModifyTerminatedContract,
		},
		// delete
		{
			desc: "success delete",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-25",
				DateEnd:   "2022-05-30",
			},
			op: opDelete,
		},
		{
			desc: "failed, delete history contract",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-21",
				DateEnd:   "2022-05-21",
			},
			op:     opDelete,
			expErr: ErrModifyPastContract,
		},
		{
			desc: "failed, delete ongoing contract",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-21",
				DateEnd:   "2022-05-23",
			},
			op:     opDelete,
			expErr: ErrModifyOngoingContract,
		},
		{
			desc: "failed, delete upcoming contract",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-24",
			},
			op:     opDelete,
			expErr: ErrModifyUpcomingContract,
		},
		{
			desc: "failed, delete upcoming contract, different timezone(+7)",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Bangkok",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-24",
			},
			op:     opDelete,
			expErr: ErrModifyUpcomingContract,
		},
		{
			desc: "failed, delete terminated contract",
			origContract: &ctModel.Contract{
				Timezone:     "Asia/Taipei",
				DateStart:    "2022-05-23",
				DateEnd:      "2022-05-24",
				IsTerminated: 1,
			},
			op:     opDelete,
			expErr: ErrModifyTerminatedContract,
		},
		// terminate
		{
			desc: "succ, terminate ongoing contract",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-21",
				DateEnd:   "2022-05-23",
			},
			op: opTerminate,
		},
		{
			desc: "succ, terminate upcoming contract",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-23",
				DateEnd:   "2022-05-24",
			},
			op: opTerminate,
		},
		{
			desc: "failed, terminate not started contract",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-25",
				DateEnd:   "2022-05-30",
			},
			op:     opTerminate,
			expErr: ErrTerminateNotStartedContract,
		},
		{
			desc: "failed, terminate history contract",
			origContract: &ctModel.Contract{
				Timezone:  "Asia/Taipei",
				DateStart: "2022-05-21",
				DateEnd:   "2022-05-21",
			},
			op:     opTerminate,
			expErr: ErrModifyPastContract,
		},
		{
			desc: "failed, terminate terminated contract",
			origContract: &ctModel.Contract{
				Timezone:     "Asia/Taipei",
				DateStart:    "2022-05-21",
				DateEnd:      "2022-05-21",
				IsTerminated: 1,
			},
			op:     opTerminate,
			expErr: ErrModifyTerminatedContract,
		},
	}

	for _, t := range tests {
		err := validateContractDateAndOperation(mockCTX, t.origContract, t.newContract, t.op)
		cs.Require().Equal(t.expErr, err, t.desc)
	}
}

func (cs *ContractSuite) TestGetRankSystemBonusConf() {
	mockNowTime := time.Date(2024, 4, 11, 0, 0, 0, 0, time.UTC)
	type testCase struct {
		desc      string
		region    string
		mockFunc  func(testCase)
		expResult []ctModel.RankSetting
	}
	tests := []testCase{
		{
			desc:   "success",
			region: "JP",
			mockFunc: func(t testCase) {
				cs.mockFuncs.On("TimeNow").Return(mockNowTime).Once()
				cs.rate.On("GetCurrency", mock.AnythingOfType("ctx.CTX"), t.region, "202404").Return("JPY", nil).Once()
				cs.mockSnackRank.On("GetStreamerRankSalarySettings", anyCTX, t.region, mockNowTime, mockNowTime).Return(
					[]snackRankModel.StreamerRankSalarySetting{
						{
							StartTime: time.Date(2024, 4, 1, 0, 0, 0, 0, time.UTC),
						},
						{
							StartTime: time.Date(2024, 4, 10, 0, 0, 0, 0, time.UTC),
							RankSettings: []snackRankModel.StreamerRankSalary{
								{
									Rank:     0,
									RankName: "1",
									HourlySalaries: snackRankModel.HourlySalaries{
										Streamer: map[string]float64{
											"USD": 0.1,
											"JPY": 35,
										},
										Agency: map[string]float64{
											"USD": 0.01,
											"JPY": 3,
										},
										StreamerAgency: map[string]float64{
											"USD": 0.11,
											"JPY": 38,
										},
									},
								},
								{
									Rank:     1,
									RankName: "2",
									HourlySalaries: snackRankModel.HourlySalaries{
										Streamer: map[string]float64{
											"USD": 0.2,
											"JPY": 70,
										},
										Agency: map[string]float64{
											"USD": 0.02,
											"JPY": 6,
										},
										StreamerAgency: map[string]float64{
											"USD": 0.22,
											"JPY": 76,
										},
									},
								},
							},
						},
					}, nil,
				).Once()
			},
			expResult: []ctModel.RankSetting{
				{
					Rank:     0,
					RankName: "1",
					Streamer: ctModel.RankConf{
						Bonus: 35,
					},
					Agency: ctModel.RankConf{
						Bonus: 3,
					},
					StreamerAgency: ctModel.RankConf{
						Bonus: 38,
					},
				},
				{
					Rank:     1,
					RankName: "2",
					Streamer: ctModel.RankConf{
						Bonus: 70,
					},
					Agency: ctModel.RankConf{
						Bonus: 6,
					},
					StreamerAgency: ctModel.RankConf{
						Bonus: 76,
					},
				},
			},
		},
	}

	for _, t := range tests {
		cs.Run(t.desc, func() {
			if t.mockFunc != nil {
				t.mockFunc(t)
			}
			result, err := cs.Store.GetRankSystemBonusConf(mockCTX, t.region)
			cs.Require().NoError(err)
			cs.Require().Equal(t.expResult, result, t.desc)
		})
	}
}

func (m *mockFuncs) TimeNow() time.Time {
	ret := m.Called()
	return ret.Get(0).(time.Time)
}

func (m *mockFuncs) FindContract(im *impl, context ctx.CTX, adminIDs []int, executorID int, contractIDs []int) ([]*ctModel.Contract, error) {
	ret := m.Called(im, context, adminIDs, executorID, contractIDs)
	return ret.Get(0).([]*ctModel.Contract), ret.Error(1)
}

func (m *mockFuncs) SetContractByContractCSVValidation(
	im *impl,
	context ctx.CTX,
	execUser *intra.User, adminIDs []int,
	validation contractCSVValidation, contract *ctModel.Contract,
) ([]string, []string) {
	ret := m.Called(im, context, execUser, adminIDs, validation, contract)
	return ret.Get(0).([]string), ret.Get(1).([]string)
}

func (m *mockFuncs) parseCreateCSVToContracts(
	im *impl,
	context ctx.CTX,
	file io.ReadSeeker,
	execUser *intra.User,
	adminIDs []int,
) ([]*ctModel.Contract, *BatchResult, error) {
	ret := m.Called(im, context, file, execUser, adminIDs)
	return ret.Get(0).([]*ctModel.Contract), ret.Get(1).(*BatchResult), ret.Error(2)
}

func (m *mockFuncs) parseUpdateCSVToContracts(
	im *impl,
	context ctx.CTX,
	file io.ReadSeeker,
	execUser *intra.User,
	adminIDs []int,
) ([]*ctModel.Contract, *BatchResult, error) {
	ret := m.Called(im, context, file, execUser, adminIDs)
	return ret.Get(0).([]*ctModel.Contract), ret.Get(1).(*BatchResult), ret.Error(2)
}

func (m *mockFuncs) batchCreate(im *impl, context ctx.CTX, adminIDs []int, executorID int, contracts []*ctModel.Contract) ([]int, int, error) {
	ret := m.Called(im, context, adminIDs, executorID, contracts)
	return ret.Get(0).([]int), ret.Get(1).(int), ret.Error(2)
}

func (m *mockFuncs) batchUpdate(im *impl, context ctx.CTX, adminIDs []int, executorID int, contracts []*ctModel.Contract) (int32, error) {
	ret := m.Called(im, context, adminIDs, executorID, contracts)
	return ret.Get(0).(int32), ret.Error(1)
}

func (m *mockFuncs) sendEvents(im *impl, context ctx.CTX, op *operation) {
	m.Called(im, context, op)
}
