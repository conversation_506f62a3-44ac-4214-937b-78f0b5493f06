package overall

import (
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"github.com/17media/api/base/ctx"
	ctModel "github.com/17media/api/models/contract"
	mockRate "github.com/17media/api/service/rate/mocks"
	mRegion "github.com/17media/api/service/region/mocks"
	"github.com/17media/api/stores/contract/performance"
	bonusFollower "github.com/17media/api/stores/contract/performance/calculator/follower/bonus"
	bonusMinimumLiveday "github.com/17media/api/stores/contract/performance/calculator/liveday/bonusminimum"
	bonusHours "github.com/17media/api/stores/contract/performance/calculator/liveduration/bonus"
	bonusMinimumLiveduration "github.com/17media/api/stores/contract/performance/calculator/liveduration/bonusminimum"
	guaranteeRatioLiveduration "github.com/17media/api/stores/contract/performance/calculator/liveduration/guaranteeratio"
	guaranteeRevenueLiveduration "github.com/17media/api/stores/contract/performance/calculator/liveduration/guaranteerevenue"
	guaranteeSalaryLiveduration "github.com/17media/api/stores/contract/performance/calculator/liveduration/guaranteesalary"
	minimumLiveduration "github.com/17media/api/stores/contract/performance/calculator/liveduration/minimum"
	wageRatioLiveduration "github.com/17media/api/stores/contract/performance/calculator/liveduration/wageratio"
	maxHour "github.com/17media/api/stores/contract/performance/calculator/misc/maxhour"
	bonusPoint "github.com/17media/api/stores/contract/performance/calculator/point/bonus"
	guaranteeRatioPoint "github.com/17media/api/stores/contract/performance/calculator/point/guaranteeratio"
	hourlyWagePoint "github.com/17media/api/stores/contract/performance/calculator/point/hourlywage"
	operationCost "github.com/17media/api/stores/contract/performance/calculator/point/operationcost"
	ratioPoint "github.com/17media/api/stores/contract/performance/calculator/point/ratio"
	rankSystem "github.com/17media/api/stores/contract/performance/calculator/snack/ranksystem"
	mgiftRevenue "github.com/17media/api/stores/gift/revenue/mocks"
	muser "github.com/17media/api/stores/user/mocks"
)

var (
	mockCTX      = ctx.Background()
	mockContract = ctModel.Contract{
		AgentID: 12345,
		Terms: []*ctModel.ContractTerm{
			{
				Prop:         "PERMILLE_SUBRUN",
				Condition:    "",
				Gte:          0,
				Lt:           0,
				Value:        400,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "GUARANTEE_REVENUE",
				Condition:    "STREAMED_HOURS",
				Gte:          0,
				Lt:           10,
				Value:        0,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "GUARANTEE_REVENUE",
				Condition:    "STREAMED_HOURS",
				Gte:          10,
				Lt:           -1,
				Value:        1000,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "BONUS_FOLLOWER",
				Condition:    "FOLLOWERS",
				Gte:          0,
				Lt:           500,
				Value:        0,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "BONUS_FOLLOWER",
				Condition:    "FOLLOWERS",
				Gte:          500,
				Lt:           1000,
				Value:        50,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "BONUS_FOLLOWER",
				Condition:    "FOLLOWERS",
				Gte:          1000,
				Lt:           2000,
				Value:        150,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "BONUS_FOLLOWER",
				Condition:    "FOLLOWERS",
				Gte:          2000,
				Lt:           -1,
				Value:        300,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "HOURLY_WAGE",
				Condition:    "",
				Gte:          0,
				Lt:           0,
				Value:        1000,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "MAX_PAID_HOURS",
				Condition:    "",
				Gte:          0,
				Lt:           0,
				Value:        50,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "PERCENT_WAGE_RATIO",
				Condition:    "STREAMED_HOURS",
				Gte:          0,
				Lt:           10,
				Value:        0,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "PERCENT_WAGE_RATIO",
				Condition:    "STREAMED_HOURS",
				Gte:          10,
				Lt:           -1,
				Value:        100,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "MINIMUM_REQUIREMENT_VALID_HOUR",
				Condition:    "",
				Gte:          0,
				Lt:           0,
				Value:        20,
				ContractType: ctModel.StreamerContractType,
			},
		},
	}
	mockContract1 = ctModel.Contract{
		AgentID: 12345,
		Terms: []*ctModel.ContractTerm{
			{
				Prop:         "PERMILLE_SUBRUN",
				Condition:    "",
				Gte:          0,
				Lt:           0,
				Value:        400,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "GUARANTEE_REVENUE",
				Condition:    "STREAMED_HOURS",
				Gte:          0,
				Lt:           10,
				Value:        0,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "GUARANTEE_REVENUE",
				Condition:    "STREAMED_HOURS",
				Gte:          10,
				Lt:           -1,
				Value:        1000,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "BONUS_FOLLOWER",
				Condition:    "FOLLOWERS",
				Gte:          0,
				Lt:           500,
				Value:        0,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "BONUS_FOLLOWER",
				Condition:    "FOLLOWERS",
				Gte:          500,
				Lt:           1000,
				Value:        50,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "BONUS_FOLLOWER",
				Condition:    "FOLLOWERS",
				Gte:          1000,
				Lt:           2000,
				Value:        150,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "BONUS_FOLLOWER",
				Condition:    "FOLLOWERS",
				Gte:          2000,
				Lt:           -1,
				Value:        300,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "HOURLY_WAGE",
				Condition:    "",
				Gte:          0,
				Lt:           0,
				Value:        1000,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "MAX_PAID_HOURS",
				Condition:    "",
				Gte:          0,
				Lt:           0,
				Value:        50,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "PERCENT_WAGE_RATIO",
				Condition:    "STREAMED_HOURS",
				Gte:          0,
				Lt:           10,
				Value:        0,
				ContractType: ctModel.StreamerContractType,
			},
			{
				Prop:         "PERCENT_WAGE_RATIO",
				Condition:    "STREAMED_HOURS",
				Gte:          10,
				Lt:           -1,
				Value:        100,
				ContractType: ctModel.StreamerContractType,
			},
		},
	}
)

type overallSuite struct {
	suite.Suite

	Store       *impl
	rate        *mockRate.Service
	us          *muser.Store
	giftRevenue *mgiftRevenue.Worker
	regionServ  *mRegion.Service
}

func (s *overallSuite) SetupTest() {
	calculators := map[ctModel.ContractTermType]performance.Calculator{
		ctModel.ContractTermType_BONUS_FOLLOWER:                  bonusFollower.New(),
		ctModel.ContractTermType_BONUS_POINT:                     bonusPoint.New(),
		ctModel.ContractTermType_BONUS_HOURS:                     bonusHours.New(),
		ctModel.ContractTermType_GUARANTEE_PERMILLE_SUBRUN:       guaranteeRatioPoint.New(),
		ctModel.ContractTermType_GUARANTEE_REVENUE:               guaranteeRevenueLiveduration.New(),
		ctModel.ContractTermType_GUARANTEE_SALARY:                guaranteeSalaryLiveduration.New(),
		ctModel.ContractTermType_HOURLY_WAGE:                     hourlyWagePoint.New(),
		ctModel.ContractTermType_MAX_PAID_HOURS:                  maxHour.New(),
		ctModel.ContractTermType_MINIMUM_REQUIREMENT_VALID_HOUR:  minimumLiveduration.New(),
		ctModel.ContractTermType_PERCENT_WAGE_RATIO:              wageRatioLiveduration.New(),
		ctModel.ContractTermType_PERMILLE_SUBRUN:                 ratioPoint.New(),
		ctModel.ContractTermType_GUARANTEE_PERMILLE_SUBRUN_HOURS: guaranteeRatioLiveduration.New(),
		ctModel.ContractTermType_MIN_REQ_BONUS_VALID_HOURS:       bonusMinimumLiveduration.New(),
		ctModel.ContractTermType_MIN_REQ_BONUS_VALID_DAYS:        bonusMinimumLiveday.New(),
		ctModel.ContractTermType_OPERATION_COST:                  operationCost.New(),
		ctModel.ContractTermType_RANK_SYSTEM:                     rankSystem.New(),
	}

	reviewer := performance.New(calculators)
	s.rate = &mockRate.Service{}
	s.us = &muser.Store{}
	s.giftRevenue = &mgiftRevenue.Worker{}
	s.regionServ = &mRegion.Service{}
	s.Store = New(s.rate, s.us, reviewer, s.giftRevenue, s.regionServ).(*impl)

	mRegion.MockGetTimeLocation(s.regionServ)
}

func (s *overallSuite) TearDownTest() {
	s.us.AssertExpectations(s.T())
	s.rate.AssertExpectations(s.T())
}

func (s *overallSuite) TestGet() {
	tests := []struct {
		Desc      string
		Contract  ctModel.Contract
		Metrics   ctModel.Metrics
		YearMonth string
		SetupMock func()
		CleanMock func()
		ExpIncome float64
		ExpError  error
	}{
		{
			Desc:     "overall with 0 income",
			Contract: mockContract,
			Metrics: ctModel.Metrics{
				Points:            1000,
				PeakFollowers:     1500,
				ValidLiveDuration: 5,
			},
			YearMonth: "201812",
			SetupMock: func() {
				s.us.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return("TW", nil).Twice()
				s.giftRevenue.On("GetPointToRevenueRatio", mockCTX, "TW", mock.AnythingOfType("time.Time")).Return(0.004555393586006, nil).Once()
				s.rate.On("GetRate", mock.AnythingOfType("ctx.CTX"), "TW", "201812").Return(30.0, nil).Once()
			},
			CleanMock: func() {
				s.us.AssertExpectations(s.T())
				s.rate.AssertExpectations(s.T())
			},
			ExpIncome: 0.00,
			ExpError:  nil,
		},
		{
			Desc:     "overall with income",
			Contract: mockContract,
			Metrics: ctModel.Metrics{
				Points:            1000,
				PeakFollowers:     1500,
				ValidLiveDuration: 19.998,
			},
			YearMonth: "201812",
			SetupMock: func() {
				s.us.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return("TW", nil).Twice()
				s.giftRevenue.On("GetPointToRevenueRatio", mockCTX, "TW", mock.AnythingOfType("time.Time")).Return(0.004555393586006, nil).Once()
				s.rate.On("GetRate", mock.AnythingOfType("ctx.CTX"), "TW", "201812").Return(30.0, nil).Once()
			},
			CleanMock: func() {
				s.us.AssertExpectations(s.T())
				s.rate.AssertExpectations(s.T())
			},
			// FIXME this test will be failed every time we modify PointToUSD
			// PointToUSD = 1/7/31.36 = 0.004555393586006
			// PointToUSD * Points = 0.004555393586006 * 1000 = 4.555393586006
			// BaseRevenue = 4.555393586006 * 0.4 = 1.8221574344024
			// Revenue = GuaranteeRevenue - BaseRevenue = 998.17425655976
			// LocalRevenue = 998.17425655976 * 30.0 = 29945.335276967928
			// Bonus = 150
			// Salary = 20000
			ExpIncome: 50095.335276967928,
			ExpError:  nil,
		},
		{
			Desc:     "overall without minimum requirement term",
			Contract: mockContract1,
			Metrics: ctModel.Metrics{
				Points:            0,
				PeakFollowers:     1500,
				ValidLiveDuration: 20,
			},
			YearMonth: "201812",
			SetupMock: func() {
				s.us.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return("TW", nil).Twice()
				s.giftRevenue.On("GetPointToRevenueRatio", mockCTX, "TW", mock.AnythingOfType("time.Time")).Return(0.004555393586006, nil).Once()
				s.rate.On("GetRate", mock.AnythingOfType("ctx.CTX"), "TW", "201812").Return(30.0, nil).Once()
			},
			CleanMock: func() {
				s.us.AssertExpectations(s.T())
				s.rate.AssertExpectations(s.T())
			},
			// Revenue = GuaranteeRevenue = 1000
			// LocalRevenue = 1000 * 30.0 = 30000
			// Bonus = 150
			// Salary = 20000
			ExpIncome: 50150.00,
			ExpError:  nil,
		},
		{
			Desc:     "overall without minimum requirement term and bonus income",
			Contract: mockContract1,
			Metrics: ctModel.Metrics{
				Points:            0,
				PeakFollowers:     10,
				ValidLiveDuration: 20,
			},
			YearMonth: "201812",
			SetupMock: func() {
				s.us.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return("TW", nil).Twice()
				s.giftRevenue.On("GetPointToRevenueRatio", mockCTX, "TW", mock.AnythingOfType("time.Time")).Return(0.004555393586006, nil).Once()
				s.rate.On("GetRate", mock.AnythingOfType("ctx.CTX"), "TW", "201812").Return(30.0, nil).Once()
			},
			CleanMock: func() {
				s.us.AssertExpectations(s.T())
				s.rate.AssertExpectations(s.T())
			},
			// Revenue = GuaranteeRevenue = 1000
			// LocalRevenue = 1000 * 30.0 = 30000
			// Bonus = 0
			// Salary = 20000
			ExpIncome: 50000.00,
			ExpError:  nil,
		},
		{
			Desc:     "overall without minimum requirement term, salary and revenue",
			Contract: mockContract1,
			Metrics: ctModel.Metrics{
				Points:            0,
				PeakFollowers:     1500,
				ValidLiveDuration: 5,
			},
			YearMonth: "201812",
			SetupMock: func() {
				s.us.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return("TW", nil).Twice()
				s.giftRevenue.On("GetPointToRevenueRatio", mockCTX, "TW", mock.AnythingOfType("time.Time")).Return(0.004555393586006, nil).Once()
				s.rate.On("GetRate", mock.AnythingOfType("ctx.CTX"), "TW", "201812").Return(30.0, nil).Once()
			},
			CleanMock: func() {
				s.us.AssertExpectations(s.T())
				s.rate.AssertExpectations(s.T())
			},
			// Revenue = 0
			// LocalRevenue = 0
			// Bonus = 150
			// Salary = 0
			ExpIncome: 150.00,
			ExpError:  nil,
		},

		{
			Desc:     "overall with 0 income and without minimum requirement term",
			Contract: mockContract1,
			Metrics: ctModel.Metrics{
				Points:            0,
				PeakFollowers:     10,
				ValidLiveDuration: 5,
			},
			YearMonth: "201812",
			SetupMock: func() {
				s.us.On("GetContractAgentRegion", mock.AnythingOfType("ctx.CTX"), mock.Anything).Return("TW", nil).Twice()
				s.giftRevenue.On("GetPointToRevenueRatio", mockCTX, "TW", mock.AnythingOfType("time.Time")).Return(0.004555393586006, nil).Once()
				s.rate.On("GetRate", mock.AnythingOfType("ctx.CTX"), "TW", "201812").Return(30.0, nil).Once()
			},
			CleanMock: func() {
				s.us.AssertExpectations(s.T())
				s.rate.AssertExpectations(s.T())
			},
			// Revenue = 0
			// LocalRevenue = 0
			// Bonus = 0
			// Salary = 0
			ExpIncome: 0.00,
			ExpError:  nil,
		},
	}

	for _, test := range tests {
		test.SetupMock()

		income, err := s.Store.Get(mockCTX, test.Contract, test.Metrics, test.YearMonth)
		s.Equal(test.ExpError, err, test.Desc)
		s.InDelta(test.ExpIncome, income, 0.000001, test.Desc)

		test.CleanMock()
	}
}

func TestOverallSuite(t *testing.T) {
	c := new(overallSuite)

	suite.Run(t, c)
}
