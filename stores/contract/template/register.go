package template

import (
	"database/sql"

	"github.com/17media/dig"

	"github.com/17media/api/service/intralog"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/stores/user"

	// trigger init
	_ "github.com/17media/api/setup/mysql"
	_ "github.com/17media/api/setup/mysql/mysqladmin"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of contract template object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		MysqlAdmin *sql.DB          `name:"mysqlAdmin"`
		User       user.Store       `name:"user"`
		IntraLog   intralog.Service `name:"intraLog"`
	}

	fn := func(p params) Template {
		return New(
			p.MysqlAdmin,
			p.User,
			p.IntraLog,
		)
	}
	m.ProvideConstructor(fn, `contractTemplate`)
}

// GetContractTemplate returns the contract template object
func GetContractTemplate(m *dimanager.Manager) Template {
	var output Template
	type params struct {
		dig.In
		Output Template `name:"contractTemplate"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
