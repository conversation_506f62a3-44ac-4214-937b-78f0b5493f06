package quota

import (
	"strconv"

	"github.com/17media/api/models/purchase"
	payModel "github.com/17media/api/models/purchase/pay"
	qualificationModel "github.com/17media/api/models/purchase/qualification"
)

type purchaseLimitDBRow struct {
	UserID       string                                    `json:"userID"`
	AmountLimit  map[string]qualificationModel.AmountLimit `json:"amountLimit"`
	CreateTimeMs int64                                     `json:"createTimeMs"`
	UpdateTimeMs int64                                     `json:"updateTimeMs"`
}

func (r *purchaseLimitDBRow) toPurchaseLimit() (*qualificationModel.PurchaseLimit, error) {
	payMethodPurchaseValueMap := map[payModel.Method]purchase.Value{}
	for methodStr, amountLimit := range r.AmountLimit {
		payMethodInt, err := strconv.Atoi(methodStr)
		if err != nil {
			return nil, err
		}
		value, err := purchase.NewValue(amountLimit.Currency, strconv.FormatFloat(amountLimit.Amount, 'f', -1, 64))
		if err != nil {
			return nil, err
		}
		payMethodPurchaseValueMap[payModel.Method(payMethodInt)] = value
	}

	return &qualificationModel.PurchaseLimit{
		UserID:       r.UserID,
		AmountLimit:  payMethodPurchaseValueMap,
		CreateTimeMs: r.CreateTimeMs,
		UpdateTimeMs: r.UpdateTimeMs,
	}, nil
}
