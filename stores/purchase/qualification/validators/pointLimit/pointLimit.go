package pointLimit

import (
	"github.com/17media/logrus"

	"github.com/17media/api/base/ctx"
	legacyPayModel "github.com/17media/api/models/pay"
	qModel "github.com/17media/api/models/purchase/qualification"
	qConfig "github.com/17media/api/stores/purchase/qualification/config"
	"github.com/17media/api/stores/purchase/qualification/quota"
	"github.com/17media/api/stores/purchase/qualification/validators"
)

var (
	isEnablePointLimitValidator = qConfig.IsEnablePointLimitValidator
)

type impl struct {
	quota quota.Store
}

func New(quota quota.Store) validators.Validator {
	return &impl{
		quota: quota,
	}
}

func (im *impl) IsValid(context ctx.CTX, input qModel.CheckUserQualificationInput) (bool, error) {
	if !isEnablePointLimitValidator() {
		return true, nil
	}

	if input.MerchandiseWithPrice == nil || !input.MerchandiseWithPrice.IsContainProductType(legacyPayModel.ProductType_INTERNAL_ORDER_POINT) {
		context.WithFields(logrus.Fields{
			"payMethod":        input.PayMethod,
			"merchandiseID":    input.MerchandiseID,
			"sellingChannel":   input.SellingChannel,
			"isMerchandiseNil": input.MerchandiseWithPrice == nil,
		}).Info("miss merchandise or non point product type")
		return true, nil
	}

	// check limit of payMethod exists
	limit, err := im.quota.GetPurchaseLimit(context, input.UserID)
	if err != nil && err != quota.ErrPurchaseLimitNotFound {
		context.WithFields(logrus.Fields{
			"err": err,
		}).Error("quota.GetPurchaseLimit failed")
		return false, err
	}
	if err == quota.ErrPurchaseLimitNotFound {
		context.Info("purchaseLimit not found")
		return true, nil
	}
	_, ok := limit.AmountLimit[input.PayMethod]
	if !ok {
		context.WithFields(logrus.Fields{
			"AmountLimit": limit.AmountLimit,
		}).Info("purchaseLimit payMethod not found")
		return true, nil
	}

	q, err := im.quota.GetUserBBCQuotaByPayMethod(context, input.UserID, input.PayMethod, input.Price.Currency().String())
	if err != nil {
		context.WithFields(logrus.Fields{
			"err":       err,
			"payMethod": input.PayMethod,
			"userID":    input.UserID,
		}).Error("quota.GetUserBBCQuotaByPayMethod failed")
		return false, err
	}

	if input.Price.Amount().GreaterThan(q.Remaining.Amount().Floor()) {
		return false, qModel.ErrOverBBCPurchaseLimit
	}

	context.WithFields(logrus.Fields{
		"inputPrice":             input.Price.Amount().String(),
		"quotaRemaining":         q.Remaining.Amount().String(),
		"inputPriceCurrency":     input.Price.Currency().String(),
		"quotaRemainingCurrency": q.Remaining.Currency().String(),
	}).Info("pass, price is under quota remaining")

	return true, nil
}

func (im *impl) DoAfterBuy(context ctx.CTX, input validators.DoAfterBuyInput) error {
	if input.Merchandise == nil || !input.Merchandise.IsContainProductType(legacyPayModel.ProductType_INTERNAL_ORDER_POINT) {
		return nil
	}

	for _, p := range input.PayMethods {
		if err := im.quota.CleanBBCSpendingCache(context, input.UserID, p); err != nil {
			context.WithFields(logrus.Fields{
				"err":       err,
				"payMethod": p,
				"userID":    input.UserID,
			}).Error("quota.CleanBBCSpendingCache failed")
			// ignore err
		}
	}

	return nil
}

func (im *impl) DoAfterRefund(context ctx.CTX, input validators.DoAfterRefundInput) error {
	if input.Merchandise == nil || !input.Merchandise.IsContainProductType(legacyPayModel.ProductType_INTERNAL_ORDER_POINT) {
		return nil
	}

	for _, p := range input.PayMethods {
		if err := im.quota.CleanBBCSpendingCache(context, input.UserID, p); err != nil {
			context.WithFields(logrus.Fields{
				"err":       err,
				"payMethod": p,
				"userID":    input.UserID,
			}).Error("quota.CleanBBCSpendingCache failed")
			// ignore err
		}
	}

	return nil
}
