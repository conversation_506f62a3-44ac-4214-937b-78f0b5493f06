package playstore

import (
	"database/sql"
	"testing"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/suite"

	bdocker "github.com/17media/api/base/docker"
	payModel "github.com/17media/api/models/pay"
	newpayModel "github.com/17media/api/models/purchase/pay"
	payTest "github.com/17media/api/stores/pay/testing"
	payVerifier "github.com/17media/api/stores/pay/verifier"
)

type makeupSuite struct {
	suite.Suite

	im        *makeupVerifier
	db        *sql.DB
	dbx       *sqlx.DB
	localhost string
	mysqlPort string
	mysqlName string
	mockFuncs *mockFuncs
}

func (s *makeupSuite) SetupSuite() {
	localhost, ports, err := bdocker.RunExtDockers([]string{"mysql8"})
	s.Require().NoError(err)
	s.localhost = localhost
	s.mysqlPort = ports[0]
	s.mysqlName = "media17gift"
	s.db, err = sql.Open("mysql", "root:@tcp("+localhost+":"+ports[0]+")/"+s.mysqlName+"?charset=utf8mb4")
	s.NoError(err)
	s.dbx = sqlx.NewDb(s.db, "mysql")
	prepareMySQL(s.mysqlName, s.localhost, s.mysqlPort)
}

func prepareMySQL(name, localhost, port string) {
	payTest.CreatePayDbSchema(name, localhost, port)
}

func (s *makeupSuite) SetupTest() {
	s.mockFuncs = &mockFuncs{}
	vs := NewMakeUp(s.db)
	s.im = vs[newpayModel.MethodManualPointMakeupAndroid].(*makeupVerifier)
	s.im.timeNow = s.mockFuncs.TimeNow
}

func (s *makeupSuite) TearDownTest() {
	payTest.TruncatePayDb(s.mysqlName, s.localhost, s.mysqlPort)
}

func (s *makeupSuite) TearDownSuite() {
	payTest.DropPayDb(s.mysqlName, s.localhost, s.mysqlPort)
}

func TestMakeupSuite(t *testing.T) {
	suite.Run(t, new(makeupSuite))
}

func (s *makeupSuite) TestVerify() {
	tests := []struct {
		Desc        string
		Payload     string
		ExpReceipts []newpayModel.RawReceipt
		ExpErr      error
	}{
		{
			Desc:        "invalid orderID payload",
			Payload:     `{"AcquiringBy17":false,"Sandbox":false,"Platform":16,"ID":"GPA.INVALID_ID","VendorOrderID":"","Quantity":1,"ProductID":"media17_points_1100","Subscribe":false,"PurchasedTime":"2018-10-02T04:28:01.811395033Z","OriginalPurchasedTime":"2018-10-02T04:28:01.811395033Z","ExpiredTime":"0001-01-01T00:00:00Z","AndroidOrderID":"GPA.INVALID_ID","Token":""}`,
			ExpReceipts: nil,
			ExpErr:      payVerifier.ErrNotValidFormatOfOrderID,
		},
		{
			Desc:    "legacy normal payload",
			Payload: `{"AcquiringBy17":false,"Sandbox":false,"Platform":16,"ID":"GPA.3338-9504-8590-05299","VendorOrderID":"","Quantity":1,"ProductID":"media17_points_1100","Subscribe":false,"PurchasedTime":"2018-10-02T04:28:01.811395033Z","OriginalPurchasedTime":"2018-10-02T04:28:01.811395033Z","ExpiredTime":"0001-01-01T00:00:00Z","AndroidOrderID":"GPA.3338-9504-8590-05299","Token":""}`,
			ExpReceipts: []newpayModel.RawReceipt{
				{
					AcquiringBy17:         false,
					Sandbox:               false,
					PayMethod:             newpayModel.MethodManualPointMakeupAndroid,
					ID:                    "GPA.3338-9504-8590-05299",
					VendorOrderID:         "",
					Quantity:              1,
					ProductID:             "media17_points_1100",
					Subscribe:             false,
					PurchasedTime:         time.Date(2018, time.October, 2, 4, 28, 1, 811395033, time.UTC),
					OriginalPurchasedTime: time.Time{},
					ExpiredTime:           time.Time{},
					AndroidOrderID:        "GPA.3338-9504-8590-05299",
					Token:                 "",
				},
			},
			ExpErr: nil,
		},
		{
			Desc:    "normal payload",
			Payload: `{"id":"GPA.3347-0799-5774-49252","quantity":1,"productID":"media17_points_14000","purchasedTime":"2018-10-11T09:22:27.734358853Z"}`,
			ExpReceipts: []newpayModel.RawReceipt{
				{
					AcquiringBy17:         false,
					Sandbox:               false,
					PayMethod:             newpayModel.MethodManualPointMakeupAndroid,
					ID:                    "GPA.3347-0799-5774-49252",
					VendorOrderID:         "",
					Quantity:              1,
					ProductID:             "media17_points_14000",
					Subscribe:             false,
					PurchasedTime:         time.Date(2018, time.October, 11, 9, 22, 27, 734358853, time.UTC),
					OriginalPurchasedTime: time.Time{},
					ExpiredTime:           time.Time{},
					AndroidOrderID:        "GPA.3347-0799-5774-49252",
					Token:                 "",
				},
			},
			ExpErr: nil,
		},
	}
	for _, test := range tests {
		s.SetupTest()
		receipts, err := s.im.Verify(mockCTX, test.Payload)
		s.Equal(test.ExpErr, err, test.Desc)
		s.Equal(test.ExpReceipts, receipts, test.Desc)
		s.TearDownTest()
	}
}

func (s *makeupSuite) TestVerifierImplementVerifierAttrGenPayload() {
	s.Implements((*payVerifier.VerifierAttrGenPayload)(nil), s.im)
}

func (s *makeupSuite) TestGenPayload() {
	mProductID := "media17_points_500"
	mOrigOrderID := "GPA.3393-4426-0000-00000"
	mInput := payModel.GenPayloadInput{ProductID: mProductID, PlayStoreAddon: &payModel.GenPayloadInputPlayStoreAddon{OrigOrderID: mOrigOrderID}}

	// 1st
	s.mockFuncs.On("TimeNow").Return(time.Date(2018, 10, 9, 17, 0, 0, 0, locTW)).Once()
	expPayload := `{"id":"GPA.3393-4426-0000-00000","quantity":1,"productID":"media17_points_500","purchasedTime":"2018-10-09T17:00:00+08:00"}`
	expOrderID := mOrigOrderID
	payload, orderID, err := s.im.GenPayload(mockCTX, mInput)
	s.NoError(err)
	s.Equal(expPayload, payload)
	s.Equal(expOrderID, orderID)

	// 2nd
	s.dbx.Exec(`INSERT INTO ProductPurchaseLog (vendor,transactionID) VALUES (?,?);`, newpayModel.MethodManualPointMakeupAndroid, mOrigOrderID)
	s.mockFuncs.On("TimeNow").Return(time.Date(2018, 10, 9, 17, 1, 0, 0, locTW)).Once()
	expPayload = `{"id":"GPA.3393-4426-0000-00000.1","quantity":1,"productID":"media17_points_500","purchasedTime":"2018-10-09T17:01:00+08:00"}`
	expOrderID = mOrigOrderID + ".1"
	payload, orderID, err = s.im.GenPayload(mockCTX, mInput)
	s.NoError(err)
	s.Equal(expPayload, payload)
	s.Equal(expOrderID, orderID)

	// 3rd
	s.dbx.Exec(`INSERT INTO ProductPurchaseLog (vendor,transactionID) VALUES (?,?);`, newpayModel.MethodManualPointMakeupAndroid, mOrigOrderID)
	s.mockFuncs.On("TimeNow").Return(time.Date(2018, 10, 9, 17, 2, 0, 0, locTW)).Once()
	expPayload = `{"id":"GPA.3393-4426-0000-00000.2","quantity":1,"productID":"media17_points_500","purchasedTime":"2018-10-09T17:02:00+08:00"}`
	expOrderID = mOrigOrderID + ".2"
	payload, orderID, err = s.im.GenPayload(mockCTX, mInput)
	s.NoError(err)
	s.Equal(expPayload, payload)
	s.Equal(expOrderID, orderID)
}
