// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	mock "github.com/stretchr/testify/mock"

	models "github.com/17media/api/models"

	order "github.com/17media/api/models/purchase/order"

	product "github.com/17media/api/models/purchase/product"

	purchase "github.com/17media/api/models/purchase"

	reward "github.com/17media/api/models/purchase/reward"
)

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// GetRewards provides a mock function with given fields: context, sellingChannel, merchandiseID, price, user, ipRegion, execUserRegion, timeMs
func (_m *Store) GetRewards(context ctx.CTX, sellingChannel order.SellingChannel, merchandiseID string, price purchase.Value, user models.User, ipRegion string, execUserRegion string, timeMs int64) ([]*product.Product, *reward.EventInfo, error) {
	ret := _m.Called(context, sellingChannel, merchandiseID, price, user, ipRegion, execUserRegion, timeMs)

	var r0 []*product.Product
	if rf, ok := ret.Get(0).(func(ctx.CTX, order.SellingChannel, string, purchase.Value, models.User, string, string, int64) []*product.Product); ok {
		r0 = rf(context, sellingChannel, merchandiseID, price, user, ipRegion, execUserRegion, timeMs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*product.Product)
		}
	}

	var r1 *reward.EventInfo
	if rf, ok := ret.Get(1).(func(ctx.CTX, order.SellingChannel, string, purchase.Value, models.User, string, string, int64) *reward.EventInfo); ok {
		r1 = rf(context, sellingChannel, merchandiseID, price, user, ipRegion, execUserRegion, timeMs)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*reward.EventInfo)
		}
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, order.SellingChannel, string, purchase.Value, models.User, string, string, int64) error); ok {
		r2 = rf(context, sellingChannel, merchandiseID, price, user, ipRegion, execUserRegion, timeMs)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GetRewardsByMerchandiseIDs provides a mock function with given fields: context, sellingChannel, merchandiseIDs, prices, user, ipRegion, execUserRegion, timeMs
func (_m *Store) GetRewardsByMerchandiseIDs(context ctx.CTX, sellingChannel order.SellingChannel, merchandiseIDs []string, prices []purchase.Value, user models.User, ipRegion string, execUserRegion string, timeMs int64) (map[string][]*product.Product, *reward.EventInfo, error) {
	ret := _m.Called(context, sellingChannel, merchandiseIDs, prices, user, ipRegion, execUserRegion, timeMs)

	var r0 map[string][]*product.Product
	if rf, ok := ret.Get(0).(func(ctx.CTX, order.SellingChannel, []string, []purchase.Value, models.User, string, string, int64) map[string][]*product.Product); ok {
		r0 = rf(context, sellingChannel, merchandiseIDs, prices, user, ipRegion, execUserRegion, timeMs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string][]*product.Product)
		}
	}

	var r1 *reward.EventInfo
	if rf, ok := ret.Get(1).(func(ctx.CTX, order.SellingChannel, []string, []purchase.Value, models.User, string, string, int64) *reward.EventInfo); ok {
		r1 = rf(context, sellingChannel, merchandiseIDs, prices, user, ipRegion, execUserRegion, timeMs)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*reward.EventInfo)
		}
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, order.SellingChannel, []string, []purchase.Value, models.User, string, string, int64) error); ok {
		r2 = rf(context, sellingChannel, merchandiseIDs, prices, user, ipRegion, execUserRegion, timeMs)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// RegisterSecretID provides a mock function with given fields: context, user, ipRegion, param
func (_m *Store) RegisterSecretID(context ctx.CTX, user *models.User, ipRegion string, param *reward.RegisterSecretIDParam) error {
	ret := _m.Called(context, user, ipRegion, param)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, string, *reward.RegisterSecretIDParam) error); ok {
		r0 = rf(context, user, ipRegion, param)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}
