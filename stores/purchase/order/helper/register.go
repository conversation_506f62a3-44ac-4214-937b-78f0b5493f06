package helper

import (
	"database/sql"

	"github.com/jmoiron/sqlx"

	"github.com/17media/dig"

	mdb "github.com/17media/api/base/db"
	"github.com/17media/api/service/cache"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/stores/exchange"

	// trigger init
	_ "github.com/17media/api/service/redis/redispersistent"
	_ "github.com/17media/api/setup/mysql/mysqlgiftreader"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of order helper object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		MysqlGiftReader *sql.DB       `name:"mysqlGiftReader"`
		Cache           cache.Service `name:"cache"`
		ExchangeRate    exchange.Rate `name:"exchangeRate"`
	}

	fn := func(p params) Store {
		return New(
			sqlx.NewDb(p.<PERSON>sql<PERSON>iftReader, mdb.SQLDriver),
			p.<PERSON><PERSON>,
			p.ExchangeRate,
		)
	}
	m.ProvideConstructor(fn, `purchaseOrderHelper`)
}

// GetOrderHelper returns the order helper object
func GetOrderHelper(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"purchaseOrderHelper"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
