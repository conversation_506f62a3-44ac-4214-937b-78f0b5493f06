package order

/*
	person in charge:
		<EMAIL>

	Description:
		Order is created when an user wants to buy a product
		and is designed to deal with everything related to other backend modules (bank, pay...etc).
		Client uses displayOrderID to specific the order, but we use orderID in backend system.
		We could use GetOrderIDs(displayOrderIDs) to conversion.
*/

import (
	"fmt"

	"github.com/jmoiron/sqlx"

	"github.com/17media/api/base/ctx"
	moneyModel "github.com/17media/api/models/money"
	purchaseModel "github.com/17media/api/models/purchase"
	orderModel "github.com/17media/api/models/purchase/order"
	paymentModel "github.com/17media/api/models/purchase/payment"
	productModel "github.com/17media/api/models/purchase/product"
	"github.com/17media/api/stores/money"
)

var (
	// ErrProductAlreadyShipped will be returned if this product is already shipped
	ErrProductAlreadyShipped = fmt.Errorf("this product is already shipped")
	// ErrOrderNotFound this order is not existed in db
	ErrOrderNotFound = fmt.Errorf("order not found")
	// ErrBundleNotFound this bundle is not existed in db
	ErrBundleNotFound = fmt.Errorf("bundle not found")
)

// CreateInput is the input to create an order
type CreateInput struct {
	SellingChannel orderModel.SellingChannel
	OrderRegion    string
	IncomeRegion   string
	PointProductID string
	Items          orderModel.OrderItems
	UserID         string
	// TODO: deprecate PartialBundlePrice, since it replace by Items.TotalPrice()
	PartialBundlePrice purchaseModel.Value
	Remark             string
	// NOTE: it's a workaround for VIP first purchase bonus point
	SkipVipFirstPurchaseBonus bool
	MerchandiseID             string
	RecurrenceID              string
}

// Manager is interface to control order and bundle of order.
type Manager interface {
	// Order operations
	// Create creates an order that belongs to given bundleID
	// NOTE: this method locks the corresponding bundle in whole tx
	Create(context ctx.CTX, tx *sqlx.Tx, execUserID string, timeMs int64, input CreateInput) (*orderModel.Order, error)
	// Get gets order info
	Get(context ctx.CTX, tx *sqlx.Tx, orderID string) (*orderModel.Order, error)
	// Gets gets orders by orderIDs
	Gets(context ctx.CTX, tx *sqlx.Tx, orderIDs []string) ([]*orderModel.Order, error)
	// Ship gives the product to the recipient
	Ship(context ctx.CTX, tx money.Tx, execUserID, orderID string, payments []*paymentModel.Payment, ipRegion string, options ...ShipOption) (afterCommitFunc func(), err error)
	// Return take back order from user (user returns the product)
	// set `temporarily` to true means this return might be reversible
	// that is, this return cannot be canceled anymore
	Return(context ctx.CTX, execUserID string, orderIDs []string, refundPolicy moneyModel.RefundPolicy, temporarily bool, withFunc func(context ctx.CTX, tx *sqlx.Tx, timeMs int64) error) error
	// DetermineReturn determine a temporarily return
	// return error if you try to confirm an permanent return
	// set `isAgreed` to true wil make this temporarily return be irreversible
	// set `isAgreed` to false will cancel this temporarily return and give back order to user
	DetermineReturn(context ctx.CTX, execUserID string, orderIDs []string, isAgreed bool, withFunc func(context ctx.CTX, tx *sqlx.Tx, timeMs int64) error) error
	// Cancel cancels the order
	Cancel(context ctx.CTX, tx *sqlx.Tx, execUserID string, orderID string) error
	// HasOrder returns if users have at least one purchase order in given conditions
	HasOrder(context ctx.CTX, userID string, options ...HasOrderOption) (bool, error)
	// Remark stores remark for an order.
	// FIXME displayOrderID is for downward compatibility. Remove it when we finish.
	Remark(context ctx.CTX, execUserID, orderID, remark string, displayOrderID int64) error
	// GetRemarks returns order remarks that are ordered by time descending
	GetRemarks(context ctx.CTX, orderID string, category orderModel.RemarkCategory) (timeDescendingRemarks []string, err error)

	// Bundle operations
	// GetBundle gets the bundle of orders by bundleID
	// NOTE: if the bundle is abandoned, its members are kept at last status
	GetBundle(context ctx.CTX, tx *sqlx.Tx, bundleID string) (bundle *orderModel.Bundle, productIDToProductMap map[string]*productModel.Product, err error)
	// GetBundles gets the bundles by bundleIDs
	// NOTE: if the bundle is abandoned, its members are kept at last status
	GetBundles(context ctx.CTX, firstBundleID string, restBundleIDs ...string) (bundle []*orderModel.Bundle, productIDToProductMap map[string]*productModel.Product, err error)

	// UpdateBundleStatus update bundle status and will lock this bundle through this tx
	UpdateBundleStatus(context ctx.CTX, tx *sqlx.Tx, execUserID string, timeMs int64, bundleID string, transition orderModel.BundleStatusTrans) error
	// CheckOrdersPricePerPointValid sum all order's partial price and product point which will be used
	// to calculate price per point value is valid or not
	CheckOrdersPricePerPointValid(context ctx.CTX, orders []*orderModel.Order, timeMs int64) (bool, error)
	// CreateBundle creates an empty bundle
	CreateBundle(context ctx.CTX, tx *sqlx.Tx, execUserID string, timeMs int64,
		enablePricePerPointRiskControl bool, opts ...CreateBundleOption) (bundle *orderModel.Bundle,
		productIDToProductMap map[string]*productModel.Product, err error)

	// ID conversion operations
	// NOTE: some clients communicate with us using displayOrderID and displayBundleID, we use orderID and bundleID in backend system.
	// GetBundleIDs gets bundleIDs by displayBundleIDs
	GetBundleIDs(context ctx.CTX, displayBundleIDs []int64) ([]string, error)
	// GetOrderIDsAndBundleIDs gets orderIDs and bundleIDs by displayOrderIDs
	GetOrderIDsAndBundleIDs(context ctx.CTX, displayOrderIDs []int64) (orderIDs []string, bundleIDs []string, err error)

	// SetInvoice set invoiceID to order
	SetInvoice(context ctx.CTX, tx *sqlx.Tx, orderID string, invoiceID int64) error

	// GetExclusiveRecurrenceIDs returns exclusive recurrenceIDs with given orderID
	GetExclusiveRecurrenceIDs(context ctx.CTX, tx *sqlx.Tx, orderID string) (recurrenceIDs []string, err error)
}
