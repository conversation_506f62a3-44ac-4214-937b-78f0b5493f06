package premiumbaby

import (
	"database/sql"

	"github.com/17media/dig"

	"github.com/17media/api/setup/dimanager"
	_ "github.com/17media/api/setup/mysql/mysqlgiftwriter"
	"github.com/17media/api/stores/premiumbaby"
	"github.com/17media/api/stores/purchase/order/shipper"
	tm "github.com/17media/api/stores/purchase/product/productmanager/typemanager"
	"github.com/17media/api/stores/user"
)

func init() {
	RegisterPTM(dimanager.DefaultManager)
	RegisterShipper(dimanager.DefaultManager)
}

// RegisterPTM registers the constructor of product type manager premium baby object to the manager
func RegisterPTM(m *dimanager.Manager) {
	type params struct {
		dig.In
		GiftWriter *sql.DB `name:"mysqlGiftWriter"`
	}

	fn := func(p params) tm.ProductTypeManager {
		return NewPTM(p.GiftWriter)
	}
	m.ProvideConstructor(fn, `productTypeManagerPremiumBaby`)
}

// RegisterShipper registers the constructor of shipper premium baby object to the manager
func RegisterShipper(m *dimanager.Manager) {
	type params struct {
		dig.In

		PremiumBaby premiumbaby.Store `name:"premiumBaby"`
		User        user.Store        `name:"user"`
	}

	fn := func(p params) shipper.Shipper {
		return NewShipper(p.PremiumBaby, p.User)
	}
	m.ProvideConstructor(fn, `shipperPremiumBaby`)
}

// GetPTM returns the product type manager premium baby object
func GetPTM(m *dimanager.Manager) tm.ProductTypeManager {
	var output tm.ProductTypeManager
	type params struct {
		dig.In
		Output tm.ProductTypeManager `name:"productTypeManagerPremiumBaby"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}

// GetShipper returns the shipper premium baby object
func GetShipper(m *dimanager.Manager) shipper.Shipper {
	var output shipper.Shipper
	type params struct {
		dig.In
		Output shipper.Shipper `name:"shipperPremiumBaby"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
