package pontapoint

import (
	"database/sql"

	"github.com/17media/dig"

	"github.com/17media/api/setup/dimanager"
	_ "github.com/17media/api/setup/mysql/mysqlgiftwriter"
	"github.com/17media/api/stores/ponta"
	"github.com/17media/api/stores/purchase/order/shipper"
	tm "github.com/17media/api/stores/purchase/product/productmanager/typemanager"
)

func init() {
	RegisterPTM(dimanager.DefaultManager)
	RegisterShipper(dimanager.DefaultManager)
}

// RegisterPTM registers the constructor of product type manager ponta point object to the manager
func RegisterPTM(m *dimanager.Manager) {
	type params struct {
		dig.In
		GiftWriter *sql.DB     `name:"mysqlGiftWriter"`
		Ponta      ponta.Store `name:"ponta"`
	}

	fn := func(p params) tm.ProductTypeManager {
		return NewPTM(p.GiftWriter, p.<PERSON>a)
	}
	m.ProvideConstructor(fn, `productTypeManagerPontaPoint`)
}

// RegisterShipper registers the constructor of shipper ponta point object to the manager
func RegisterShipper(m *dimanager.Manager) {
	type params struct {
		dig.In
		Ponta ponta.Store `name:"ponta"`
	}

	fn := func(p params) shipper.Shipper {
		return NewShipper(p.Ponta)
	}
	m.ProvideConstructor(fn, `shipperPontaPoint`)
}

// GetPTM returns the product type manager ponta point object
func GetPTM(m *dimanager.Manager) tm.ProductTypeManager {
	var output tm.ProductTypeManager
	type params struct {
		dig.In
		Output tm.ProductTypeManager `name:"productTypeManagerPontaPoint"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}

// GetShipper returns the shipper ponta point object
func GetShipper(m *dimanager.Manager) shipper.Shipper {
	var output shipper.Shipper
	type params struct {
		dig.In
		Output shipper.Shipper `name:"shipperPontaPoint"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
