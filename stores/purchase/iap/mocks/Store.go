// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	iap "github.com/17media/api/stores/purchase/iap"

	merchandise "github.com/17media/api/models/purchase/merchandise"

	mock "github.com/stretchr/testify/mock"

	order "github.com/17media/api/models/purchase/order"

	pay "github.com/17media/api/models/purchase/pay"

	purchase "github.com/17media/api/models/purchase"

	purchaseiap "github.com/17media/api/models/purchase/iap"

	sqlx "github.com/jmoiron/sqlx"
)

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// AcknowledgeAndroidOrder provides a mock function with given fields: context, receiptInfo
func (_m *Store) AcknowledgeAndroidOrder(context ctx.CTX, receiptInfo *pay.IAPReceiptInfo) error {
	ret := _m.Called(context, receiptInfo)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *pay.IAPReceiptInfo) error); ok {
		r0 = rf(context, receiptInfo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CheckIsSameIAPGroup provides a mock function with given fields: context, preReceiptID, newGroupID
func (_m *Store) CheckIsSameIAPGroup(context ctx.CTX, preReceiptID string, newGroupID string) (bool, error) {
	ret := _m.Called(context, preReceiptID, newGroupID)

	var r0 bool
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) bool); ok {
		r0 = rf(context, preReceiptID, newGroupID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string) error); ok {
		r1 = rf(context, preReceiptID, newGroupID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateIAPSubscriptionPayment provides a mock function with given fields: context, tx, input
func (_m *Store) CreateIAPSubscriptionPayment(context ctx.CTX, tx *sqlx.Tx, input purchaseiap.CreateIAPSubscriptionPaymentInput) error {
	ret := _m.Called(context, tx, input)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, purchaseiap.CreateIAPSubscriptionPaymentInput) error); ok {
		r0 = rf(context, tx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DelCancelSubscription provides a mock function with given fields: context, userID, streamerID
func (_m *Store) DelCancelSubscription(context ctx.CTX, userID string, streamerID string) error {
	ret := _m.Called(context, userID, streamerID)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) error); ok {
		r0 = rf(context, userID, streamerID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetCancelSubscription provides a mock function with given fields: context, userID, streamerID
func (_m *Store) GetCancelSubscription(context ctx.CTX, userID string, streamerID string) (*iap.CancelSubscription, error) {
	ret := _m.Called(context, userID, streamerID)

	var r0 *iap.CancelSubscription
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) *iap.CancelSubscription); ok {
		r0 = rf(context, userID, streamerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*iap.CancelSubscription)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string) error); ok {
		r1 = rf(context, userID, streamerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetIAPSubscriptionPaymentsByPreReceiptIDs provides a mock function with given fields: context, preReceiptIDs
func (_m *Store) GetIAPSubscriptionPaymentsByPreReceiptIDs(context ctx.CTX, preReceiptIDs []string) ([]*purchaseiap.IAPSubscriptionPayment, error) {
	ret := _m.Called(context, preReceiptIDs)

	var r0 []*purchaseiap.IAPSubscriptionPayment
	if rf, ok := ret.Get(0).(func(ctx.CTX, []string) []*purchaseiap.IAPSubscriptionPayment); ok {
		r0 = rf(context, preReceiptIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*purchaseiap.IAPSubscriptionPayment)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, []string) error); ok {
		r1 = rf(context, preReceiptIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestIAPSubscriptionPayment provides a mock function with given fields: context, originalTransactionID
func (_m *Store) GetLatestIAPSubscriptionPayment(context ctx.CTX, originalTransactionID string) (purchaseiap.IAPSubscriptionPayment, error) {
	ret := _m.Called(context, originalTransactionID)

	var r0 purchaseiap.IAPSubscriptionPayment
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) purchaseiap.IAPSubscriptionPayment); ok {
		r0 = rf(context, originalTransactionID)
	} else {
		r0 = ret.Get(0).(purchaseiap.IAPSubscriptionPayment)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, originalTransactionID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestValidIAPSubscriptionPayment provides a mock function with given fields: context, originalTransactionID
func (_m *Store) GetLatestValidIAPSubscriptionPayment(context ctx.CTX, originalTransactionID string) (purchaseiap.IAPSubscriptionPayment, error) {
	ret := _m.Called(context, originalTransactionID)

	var r0 purchaseiap.IAPSubscriptionPayment
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) purchaseiap.IAPSubscriptionPayment); ok {
		r0 = rf(context, originalTransactionID)
	} else {
		r0 = ret.Get(0).(purchaseiap.IAPSubscriptionPayment)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, originalTransactionID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetMerchandiseIDByIAPProductID provides a mock function with given fields: context, payMethod, sellingChannel, IAPProductID, isSubscribe
func (_m *Store) GetMerchandiseIDByIAPProductID(context ctx.CTX, payMethod pay.Method, sellingChannel order.SellingChannel, IAPProductID string, isSubscribe bool) (string, error) {
	ret := _m.Called(context, payMethod, sellingChannel, IAPProductID, isSubscribe)

	var r0 string
	if rf, ok := ret.Get(0).(func(ctx.CTX, pay.Method, order.SellingChannel, string, bool) string); ok {
		r0 = rf(context, payMethod, sellingChannel, IAPProductID, isSubscribe)
	} else {
		r0 = ret.Get(0).(string)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, pay.Method, order.SellingChannel, string, bool) error); ok {
		r1 = rf(context, payMethod, sellingChannel, IAPProductID, isSubscribe)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetMerchandisePrice provides a mock function with given fields: context, merchandiseID, country
func (_m *Store) GetMerchandisePrice(context ctx.CTX, merchandiseID string, country string) (purchase.Value, error) {
	ret := _m.Called(context, merchandiseID, country)

	var r0 purchase.Value
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) purchase.Value); ok {
		r0 = rf(context, merchandiseID, country)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(purchase.Value)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string) error); ok {
		r1 = rf(context, merchandiseID, country)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPreIAPSubscriptionPaymentInSameGroup provides a mock function with given fields: context, tx, currentReceiptID
func (_m *Store) GetPreIAPSubscriptionPaymentInSameGroup(context ctx.CTX, tx *sqlx.Tx, currentReceiptID string) (*purchaseiap.IAPSubscriptionPayment, error) {
	ret := _m.Called(context, tx, currentReceiptID)

	var r0 *purchaseiap.IAPSubscriptionPayment
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, string) *purchaseiap.IAPSubscriptionPayment); ok {
		r0 = rf(context, tx, currentReceiptID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*purchaseiap.IAPSubscriptionPayment)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *sqlx.Tx, string) error); ok {
		r1 = rf(context, tx, currentReceiptID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductPool provides a mock function with given fields: context, sellingChannel
func (_m *Store) GetProductPool(context ctx.CTX, sellingChannel order.SellingChannel) ([]purchaseiap.SubscriptionGroup, error) {
	ret := _m.Called(context, sellingChannel)

	var r0 []purchaseiap.SubscriptionGroup
	if rf, ok := ret.Get(0).(func(ctx.CTX, order.SellingChannel) []purchaseiap.SubscriptionGroup); ok {
		r0 = rf(context, sellingChannel)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]purchaseiap.SubscriptionGroup)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, order.SellingChannel) error); ok {
		r1 = rf(context, sellingChannel)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductPoolID provides a mock function with given fields: context, sellingChannel, groupID, planID
func (_m *Store) GetProductPoolID(context ctx.CTX, sellingChannel order.SellingChannel, groupID string, planID string) (string, error) {
	ret := _m.Called(context, sellingChannel, groupID, planID)

	var r0 string
	if rf, ok := ret.Get(0).(func(ctx.CTX, order.SellingChannel, string, string) string); ok {
		r0 = rf(context, sellingChannel, groupID, planID)
	} else {
		r0 = ret.Get(0).(string)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, order.SellingChannel, string, string) error); ok {
		r1 = rf(context, sellingChannel, groupID, planID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTierByCountryPrice provides a mock function with given fields: context, country, price
func (_m *Store) GetTierByCountryPrice(context ctx.CTX, country string, price purchase.Value) (int, error) {
	ret := _m.Called(context, country, price)

	var r0 int
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, purchase.Value) int); ok {
		r0 = rf(context, country, price)
	} else {
		r0 = ret.Get(0).(int)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, purchase.Value) error); ok {
		r1 = rf(context, country, price)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetValidProductPool provides a mock function with given fields: context, sellingChannel, userID, streamerID
func (_m *Store) GetValidProductPool(context ctx.CTX, sellingChannel order.SellingChannel, userID string, streamerID string) ([]purchaseiap.SubscriptionGroup, error) {
	ret := _m.Called(context, sellingChannel, userID, streamerID)

	var r0 []purchaseiap.SubscriptionGroup
	if rf, ok := ret.Get(0).(func(ctx.CTX, order.SellingChannel, string, string) []purchaseiap.SubscriptionGroup); ok {
		r0 = rf(context, sellingChannel, userID, streamerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]purchaseiap.SubscriptionGroup)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, order.SellingChannel, string, string) error); ok {
		r1 = rf(context, sellingChannel, userID, streamerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HandleIAPPreReceipt provides a mock function with given fields: context, param, _a2, payMethod, rawReceipt, preReceiptsMap
func (_m *Store) HandleIAPPreReceipt(context ctx.CTX, param *pay.IAPNotifParam, _a2 *merchandise.MerchandiseWithPrice, payMethod pay.Method, rawReceipt *pay.RawReceipt, preReceiptsMap map[string]string) (string, *purchase.RefPrice, error) {
	ret := _m.Called(context, param, _a2, payMethod, rawReceipt, preReceiptsMap)

	var r0 string
	if rf, ok := ret.Get(0).(func(ctx.CTX, *pay.IAPNotifParam, *merchandise.MerchandiseWithPrice, pay.Method, *pay.RawReceipt, map[string]string) string); ok {
		r0 = rf(context, param, _a2, payMethod, rawReceipt, preReceiptsMap)
	} else {
		r0 = ret.Get(0).(string)
	}

	var r1 *purchase.RefPrice
	if rf, ok := ret.Get(1).(func(ctx.CTX, *pay.IAPNotifParam, *merchandise.MerchandiseWithPrice, pay.Method, *pay.RawReceipt, map[string]string) *purchase.RefPrice); ok {
		r1 = rf(context, param, _a2, payMethod, rawReceipt, preReceiptsMap)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(*purchase.RefPrice)
		}
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, *pay.IAPNotifParam, *merchandise.MerchandiseWithPrice, pay.Method, *pay.RawReceipt, map[string]string) error); ok {
		r2 = rf(context, param, _a2, payMethod, rawReceipt, preReceiptsMap)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// HandleIOSDowngradeNotif provides a mock function with given fields: context, originalTransactionID, thirdPartyPayingIntentID, iapProductID, timeMs
func (_m *Store) HandleIOSDowngradeNotif(context ctx.CTX, originalTransactionID string, thirdPartyPayingIntentID string, iapProductID string, timeMs int64) error {
	ret := _m.Called(context, originalTransactionID, thirdPartyPayingIntentID, iapProductID, timeMs)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, int64) error); ok {
		r0 = rf(context, originalTransactionID, thirdPartyPayingIntentID, iapProductID, timeMs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IsArmyProduct provides a mock function with given fields: context, productID
func (_m *Store) IsArmyProduct(context ctx.CTX, productID string) bool {
	ret := _m.Called(context, productID)

	var r0 bool
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) bool); ok {
		r0 = rf(context, productID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MarkCancelSubscription provides a mock function with given fields: context, payMethod, userID, streamerID, originalTransactionID, preReceiptID
func (_m *Store) MarkCancelSubscription(context ctx.CTX, payMethod pay.Method, userID string, streamerID string, originalTransactionID string, preReceiptID string) error {
	ret := _m.Called(context, payMethod, userID, streamerID, originalTransactionID, preReceiptID)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, pay.Method, string, string, string, string) error); ok {
		r0 = rf(context, payMethod, userID, streamerID, originalTransactionID, preReceiptID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetMerchandiseTier provides a mock function with given fields: context, merchandiseID, tier
func (_m *Store) SetMerchandiseTier(context ctx.CTX, merchandiseID string, tier int) error {
	ret := _m.Called(context, merchandiseID, tier)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, int) error); ok {
		r0 = rf(context, merchandiseID, tier)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetTierCountryPriceInfo provides a mock function with given fields: context, inputs
func (_m *Store) SetTierCountryPriceInfo(context ctx.CTX, inputs []*purchaseiap.AppStoreTierCountryPrice) error {
	ret := _m.Called(context, inputs)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []*purchaseiap.AppStoreTierCountryPrice) error); ok {
		r0 = rf(context, inputs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateIAPSubscriptionPayment provides a mock function with given fields: context, tx, input
func (_m *Store) UpdateIAPSubscriptionPayment(context ctx.CTX, tx *sqlx.Tx, input purchaseiap.UpdateIAPSubscriptionPaymentInput) error {
	ret := _m.Called(context, tx, input)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *sqlx.Tx, purchaseiap.UpdateIAPSubscriptionPaymentInput) error); ok {
		r0 = rf(context, tx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}
