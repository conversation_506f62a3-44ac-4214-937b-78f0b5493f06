package payment

import (
	purchaseModel "github.com/17media/api/models/purchase"
	payModel "github.com/17media/api/models/purchase/pay"
	paymentModel "github.com/17media/api/models/purchase/payment"
)

// PaymentDBRow presents fields in `Payment` Table
type PaymentDBRow struct {
	ID                int64                  `db:"id" xorm:"'id' bigint(20) pk notnull autoincr"`
	PaymentID         string                 `db:"paymentID" xorm:"'paymentID' varchar(50) unique notnull default ''"`
	PayMethod         payModel.Method        `db:"payMethod" xorm:"'payMethod' smallint(5) notnull default 0"`
	Paycode           string                 `db:"paycode" xorm:"'paycode' varchar(100) default null"`
	ExecUserID        string                 `db:"execUserID" xorm:"'execUserID' varchar(36) notnull default ''"`
	UserID            string                 `db:"userID" xorm:"'userID' varchar(36) notnull default ''"`
	Amount            string                 `db:"amount" xorm:"'amount' varchar(20) notnull default ''"`
	Currency          string                 `db:"currency" xorm:"'currency' varchar(11) notnull default ''"`
	OrderType         paymentModel.OrderType `db:"orderType" xorm:"'orderType' tinyint(4) notnull default 0"`
	OrderID           string                 `db:"orderID" xorm:"'orderID' varchar(36) notnull default ''"`
	CreateTimeMs      int64                  `db:"createTimeMs" xorm:"'createTimeMs' bigint(20) notnull default 0"`
	UpdateTimeMs      int64                  `db:"updateTimeMs" xorm:"'updateTimeMs' bigint(20) notnull default 0"`
	IsAbandoned       bool                   `db:"isAbandoned" xorm:"'isAbandoned' tinyint(1) notnull default 0"`
	AbandonExecUserID string                 `db:"abandonExecUserID" xorm:"'abandonExecUserID' varchar(36) default null"`
	AbandonReason     string                 `db:"abandonReason" xorm:"'abandonReason' varchar(200) default null"`
	Remark            string                 `db:"remark" xorm:"'remark' text"`
	ReceiptID         string                 `db:"receiptID" xorm:"'receiptID' varchar(36) default null"`
}

// TableName provides customized tablename for orm engine
func (p PaymentDBRow) TableName() string { return "Payment" }

type paymentDBRows []*PaymentDBRow

func (ps paymentDBRows) toPayments() ([]*paymentModel.Payment, error) {
	res := []*paymentModel.Payment{}
	for _, p := range ps {
		value, err := purchaseModel.NewValue(p.Currency, p.Amount)
		if err != nil {
			return nil, err
		}
		res = append(res, &paymentModel.Payment{
			DisplayID:         p.ID,
			ID:                p.PaymentID,
			PayMethod:         p.PayMethod,
			Paycode:           p.Paycode,
			ExecUserID:        p.ExecUserID,
			UserID:            p.UserID,
			Value:             value,
			OrderType:         p.OrderType,
			OrderID:           p.OrderID,
			CreateTimeMs:      p.CreateTimeMs,
			UpdateTimeMs:      p.UpdateTimeMs,
			IsAbandoned:       p.IsAbandoned,
			AbandonExecUserID: p.AbandonExecUserID,
			AbandonReason:     p.AbandonReason,
			Remark:            p.Remark,
			ReceiptID:         p.ReceiptID,
		})
	}
	return res, nil
}
