package overview

type (
	// OptFunc offers functional arguments
	OptFunc func(*option)
	option  struct {
	}
)

func parseOptions(optFuncs ...OptFunc) *option {
	o := &option{}
	for _, optFunc := range optFuncs {
		optFunc(o)
	}
	return o
}

// GenerateCacheOption is functional parameter to specify generate streamer statistics cache option
type GenerateCacheOption func(*genCacheOpt) error

type genCacheOpt struct {
	Async bool
}

// GenerateCacheByAsync specifies generate streamer statistics cache by async
func GenerateCacheByAsync() GenerateCacheOption {
	return func(o *genCacheOpt) error {
		o.Async = true
		return nil
	}
}

func extractGenCacheOptions(options ...GenerateCacheOption) (*genCacheOpt, error) {
	o := &genCacheOpt{}
	for _, option := range options {
		if err := option(o); err != nil {
			return nil, err
		}
	}
	return o, nil
}

// SendSupplierOption is functional parameter to specify send supplier cache option
type SendSupplierOption func(*sendSupOpt) error

type sendSupOpt struct {
	Async bool
}

// SendSupplierByAsync specifies send supplier by async
func SendSupplierByAsync() SendSupplierOption {
	return func(o *sendSupOpt) error {
		o.Async = true
		return nil
	}
}

func extractSendSupplierOptions(options ...SendSupplierOption) (*sendSupOpt, error) {
	o := &sendSupOpt{}
	for _, option := range options {
		if err := option(o); err != nil {
			return nil, err
		}
	}
	return o, nil
}

type injectResultOption func(*injectResultOpt) error

type injectResultOpt struct {
	isEstimateMode bool
}

func injectResultEstimateMode() injectResultOption {
	return func(o *injectResultOpt) error {
		o.isEstimateMode = true
		return nil
	}
}
func extractInjectResultOption(options ...injectResultOption) (*injectResultOpt, error) {
	o := &injectResultOpt{}
	for _, option := range options {
		if err := option(o); err != nil {
			return nil, err
		}
	}
	return o, nil
}
