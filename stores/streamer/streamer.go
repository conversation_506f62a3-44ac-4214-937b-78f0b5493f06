/*
Package streamer handles contracted streamer related info.
It connects to the same database shared by BD tool
*/
package streamer

import (
	"errors"
	"fmt"
	"io"
	"regexp"

	"github.com/jmoiron/sqlx"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models"
	acModel "github.com/17media/api/models/accounting"
	ctModel "github.com/17media/api/models/contract"
	"github.com/17media/api/models/intra"
	queueModel "github.com/17media/api/models/queue"
	sModel "github.com/17media/api/models/streamer"
	sgmodel "github.com/17media/api/models/streamergrowth"
	taskProgressModol "github.com/17media/api/models/taskprogress"
	"github.com/17media/api/stores/intra/attachment"
	"github.com/17media/api/stores/streamer/blacklist"
)

var (
	// ErrNoPermission means there is no permission with the given adminID.
	ErrNoPermission = errors.New("no permission")

	// ErrNoPermissionToSetAgent means there is no permission to set streamer to that agent.
	ErrNoPermissionToSetAgent = errors.New("you are not permitted to set that agent")

	// ErrInvalidParam means parameter is invalid
	ErrInvalidParam = errors.New("parameter invalid")

	// ErrPaypalAcountModify means  modify paypalAcount is not allowed
	ErrPaypalAcountModify = errors.New("paypalAcount cant not modify")

	// ErrStreamerNotFound is the error for streamer not exist
	ErrStreamerNotFound = errors.New("streamer not found")

	// ErrUserIDDuplicated is the error for user id exist
	ErrUserIDDuplicated = errors.New("user id exist")

	// ErrNameDuplicated is the error for no user id and name exist
	ErrNameDuplicated = errors.New("no user id and name exist")

	// ErrNoModify represents not modify with the parameter
	ErrNoModify = errors.New("not modify with the parameter")

	// ErrLabelDuplicated means label already exist
	ErrLabelDuplicated = errors.New("label exist")

	// ErrLabelNotFound means label not exist
	ErrLabelNotFound = errors.New("label not found")

	// ErrExceedMaxLabelCnt means label count exceed
	ErrExceedMaxLabelCnt = errors.New("exceed max label count")

	// ErrLabelNotSupported means streamer label function not supported
	ErrLabelNotSupported = errors.New("streamer label not support")

	// ErrInvalidLabelType return error when trying to add invalid contract label type
	ErrInvalidLabelType = errors.New("invalid streamer label type")

	// ErrInvalidOptions return error when calling function with invalid option
	ErrInvalidOptions = fmt.Errorf("invalid options")
)

var (
	listOrderRegex = regexp.MustCompile(`^(-)?(\w+)$`)
)

// ListOption is functional option to specify list option
type ListOption func(*listOpt) error

type listOpt struct {
	OrderBy   string
	OrderDesc bool
}

func extractListOptions(options ...ListOption) (*listOpt, error) {
	o := &listOpt{}
	for _, option := range options {
		if err := option(o); err != nil {
			return nil, err
		}
	}
	return o, nil
}

// WithListOrder is an optional function option to indicate list with specific ordering
func WithListOrder(order string) ListOption {
	return func(o *listOpt) error {
		match := listOrderRegex.FindStringSubmatch(order)
		if len(match) < 3 {
			return fmt.Errorf("invalid list ordering: %s", order)
		}

		o.OrderBy = match[2]
		if match[1] == "-" {
			o.OrderDesc = true
		}

		if o.OrderBy != "" && o.OrderBy != "contractEndDate" {
			return fmt.Errorf("invalid list ordering: %s", order)
		}

		return nil
	}
}

// ListLabelsOption is functional option to specify list labels option
type ListLabelsOption func(*labelOpt) error

type labelOpt struct {
	Types               []sModel.LabelType
	Regions             []string
	UserID              string
	ID                  int
	ExcludeHiddenLabels bool
}

func extractLabelOptions(options ...ListLabelsOption) (*labelOpt, error) {
	o := &labelOpt{}
	for _, option := range options {
		if err := option(o); err != nil {
			return nil, err
		}
	}
	return o, nil
}

// WithLabelTypes is an optional function option to indicate list labels with label types
func WithLabelTypes(types []sModel.LabelType) ListLabelsOption {
	return func(o *labelOpt) error {
		o.Types = types
		return nil
	}
}

// WithRegions is an optional function option to indicate list labels with regions
func WithRegions(regions []string) ListLabelsOption {
	return func(o *labelOpt) error {
		o.Regions = regions
		return nil
	}
}

// WithUserID is an optional function option to indicate list labels of streamer by userID
func WithUserID(userID string) ListLabelsOption {
	return func(o *labelOpt) error {
		o.UserID = userID
		return nil
	}
}

// WithStreamerID is an optional function option to indicate list labels of streamer by streamerID
func WithStreamerID(ID int) ListLabelsOption {
	return func(o *labelOpt) error {
		o.ID = ID
		return nil
	}
}

// WithExcludeHiddenLabels is an optional function option to indicate list labels without hidden labels
func WithExcludeHiddenLabels() ListLabelsOption {
	return func(o *labelOpt) error {
		o.ExcludeHiddenLabels = true
		return nil
	}
}

// GetByOption is functional option to specify get by option
type GetByOption func(*getByOpt) error

type getByOpt struct {
	Limit  int
	Offset int
}

func extractGetByOptions(options ...GetByOption) (*getByOpt, error) {
	o := &getByOpt{}
	for _, option := range options {
		if err := option(o); err != nil {
			return nil, err
		}
	}

	if o.Offset < 0 || o.Limit < 0 || (o.Limit == 0 && o.Offset != 0) {
		return nil, ErrInvalidOptions
	}

	return o, nil
}

// WithLimitOffset is an optional function option to indicate get by with limit and offset
func WithLimitOffset(limit, offset int) GetByOption {
	return func(o *getByOpt) error {
		o.Limit = limit
		o.Offset = offset
		return nil
	}
}

// Store is used for CRUD of table ContractedStreamer
// https://github.com/17media/story17_admin/blob/dev/backend/models/bdModels/ContractedStreamerModel.js
type Store interface {
	blacklist.Store
	attachment.Service

	// LegacyCreate creates a new streamer
	LegacyCreate(context ctx.CTX, adminIDs []int, execAdminID int, streamer *ctModel.LegacyStreamer) (int, error)

	// LegacyUpdate updates a existed streamer
	LegacyUpdate(context ctx.CTX, adminIDs []int, execAdminID int, streamer *ctModel.LegacyStreamer) error

	// Create creates a new streamer
	Create(context ctx.CTX, tx *sqlx.Tx, adminIDs []int, execAdminID int, streamer *ctModel.Streamer) (int, error)

	// Update updates a existed streamer
	Update(context ctx.CTX, tx *sqlx.Tx, adminIDs []int, execAdminID int, streamer *ctModel.Streamer) error

	// BatchUpdate perform batch update streamer
	BatchUpdate(context ctx.CTX, adminIDs []int, execAdminID int, streamers []*ctModel.Streamer) error

	// List returns a list of streamerIDs and totalCount
	List(context ctx.CTX, adminIDs []int, execAdminID int, filter string, pages, limit int, opts ...ListOption) ([]int32, int, error)

	// Get return contract streamers
	Find(context ctx.CTX, IDs []int, adminIDs []int, execAdminID int) ([]*ctModel.LegacyStreamer, error)

	// BatchCreate process batch create streamer info and return process result
	BatchCreate(context ctx.CTX, adminIDs []int, execAdminID int, file io.Reader) (*BatchResult, error)

	// BatchUpdateByCSV process batch update streamer info and return process result
	BatchUpdateByCSV(context ctx.CTX, adminIDs []int, execAdminID int, file io.ReadSeeker) (*BatchResult, error)

	// AsyncExecuteBatchCreateTaskByCSV batch create or update task asynchronously
	AsyncExecuteBatchCreateTaskByCSV(context ctx.CTX, adminIDs []int, execAdminID int, file io.Reader) (string, error)

	// AsyncExecuteBatchUpdateTaskByCSV batch create or update task asynchronously
	AsyncExecuteBatchUpdateTaskByCSV(context ctx.CTX, adminIDs []int, execAdminID int, file io.ReadSeeker) (string, error)

	// GetAsyncBatchTaskProgress get asynchronous task progress
	GetAsyncBatchTaskProgress(context ctx.CTX, taskID string) (*taskProgressModol.TaskProgress, *BatchResult, error)

	// GetUserID returns userID with given id
	GetUserID(context ctx.CTX, ID int, adminIDs []int, execAdminID int) (string, error)

	// Delete deletes a streamer with given id
	Delete(context ctx.CTX, adminIDs []int, execAdminID int, streamerID int) error

	// InjectStreamerInfo inject streamer information for bd system display purpose
	InjectStreamerInfo(context ctx.CTX, streamers []*ctModel.StreamerProfile) error

	// Query returns a list of contract streamers
	Query(context ctx.CTX, openID, filter string) ([]*ctModel.StreamerProfile, error)

	// GetLogs return logs of streamer
	GetLogs(context ctx.CTX, contractIDs []int) (map[int32][]*ctModel.ResLogs, error)

	// GetOwnerIDs returns the a map that map streamerID to ID of its owners
	GetOwnerIDs(context ctx.CTX, contractIDs []int) (ownerIDsMap intra.OwnerIDs, err error)

	// FindByUserID returns contract streamer with given userID
	FindByUserID(context ctx.CTX, userID string) (*ctModel.LegacyStreamer, error)

	// FindByUserID returns contract streamers with given userIDs
	FindByUserIDs(context ctx.CTX, userIDs []string) ([]*ctModel.LegacyStreamer, error)

	// GetBy return contract streamers with given field and value
	GetBy(context ctx.CTX, field string, value interface{}, opts ...GetByOption) (streamers []*ctModel.LegacyStreamer, totalCount int, err error)

	// GetUserIDsBy return contract streamers userIDs with given field and value
	GetUserIDsBy(context ctx.CTX, field string, value interface{}, opts ...GetByOption) ([]string, error)

	// SearchOpenID returns streamers with query and userIDs
	SearchOpenID(context ctx.CTX, userIDs []string, query string, limit, offset int) ([]*ctModel.LegacyStreamer, error)

	GetTopNStreamers(context ctx.CTX, userID, region string, topN int) ([]models.User, error)
	GetGuestTopNStreamers(context ctx.CTX, ipRegion string, topN int) ([]models.User, error)

	// GetTotalContractedStreamers returns total contracted streamers
	GetTotalContractedStreamers(context ctx.CTX) (int, error)

	// GetPayoutAccount returns payout account according to streamer
	GetPayoutAccount(context ctx.CTX, userID string) (*acModel.PayoutAccount, error)

	// GetLabel returns label
	GetLabel(context ctx.CTX, region, label string) (*sModel.Label, error)

	// SetLabelVisible set label visible or not
	SetLabelVisible(context ctx.CTX, labelIDs []int32, visible bool) error

	// GetUserIDsByLabel returns streamer user ids by region and label
	GetUserIDsByLabel(context ctx.CTX, region string, labelID int32) (userIDs []string, err error)

	// AddLabel adds a new label and returns new label id
	AddLabel(context ctx.CTX, region, label string, labelType sModel.LabelType) (int32, error)

	// ListLabels list labels by list option
	ListLabels(context ctx.CTX, opts ...ListLabelsOption) ([]*sModel.Label, error)

	// ListContactRecords return contact records of assigned streamer
	ListContactRecords(context ctx.CTX, userID string) ([]*sModel.ContactRecord, error)

	// GetContactRecord return a contact record with assigned recordID
	GetContactRecord(context ctx.CTX, recordID int64) (*sModel.ContactRecord, error)

	// CreateContactRecord create a contact record of assigned streamer
	CreateContactRecord(context ctx.CTX, executorID int32, userID string, record *sModel.ContactRecord) (*sModel.ContactRecord, error)

	// UpdateContactRecord update assigned contact record
	UpdateContactRecord(context ctx.CTX, executorID int32, recordID int64, userID string, record *sModel.ContactRecord) (*sModel.ContactRecord, error)

	// GetDailyTaskInfo get daily task info
	GetDailyTaskInfo(context ctx.CTX, region, language string) *sgmodel.DailyTask

	// GetStreamerDx returns the numbers of refresh hours since firstLiveStreamTime
	GetStreamerDx(context ctx.CTX, userID string, refreshHour int64) (int64, error)

	// IsNewStreamer checks if this streamer whether is new streamer (D0 streamer/new streamer)
	IsNewStreamer(context ctx.CTX, streamerUserID string, region string) (bool, error)

	// KeepStreamTask handles streamer keep opening stream
	KeepStreamTask(context ctx.CTX, data []byte, option queueModel.CallbackOption) error
}
