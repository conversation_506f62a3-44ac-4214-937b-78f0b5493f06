// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import ctx "github.com/17media/api/base/ctx"
import mock "github.com/stretchr/testify/mock"
import stats "github.com/17media/api/stores/streamer/stats"
import streamer "github.com/17media/api/models/streamer"

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// DeleteStreamerStats provides a mock function with given fields: context, startDate, endDate, userIDs
func (_m *Store) DeleteStreamerStats(context ctx.CTX, startDate string, endDate string, userIDs []string) (int64, error) {
	ret := _m.Called(context, startDate, endDate, userIDs)

	var r0 int64
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, []string) int64); ok {
		r0 = rf(context, startDate, endDate, userIDs)
	} else {
		r0 = ret.Get(0).(int64)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, []string) error); ok {
		r1 = rf(context, startDate, endDate, userIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStreamerStats provides a mock function with given fields: context, userID, yyyyMM, region, opts
func (_m *Store) GetStreamerStats(context ctx.CTX, userID string, yyyyMM string, region string, opts ...stats.GetStatsOption) (*streamer.StreamerStats, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, userID, yyyyMM, region)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 *streamer.StreamerStats
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string, ...stats.GetStatsOption) *streamer.StreamerStats); ok {
		r0 = rf(context, userID, yyyyMM, region, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*streamer.StreamerStats)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, string, ...stats.GetStatsOption) error); ok {
		r1 = rf(context, userID, yyyyMM, region, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}
