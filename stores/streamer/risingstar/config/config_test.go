package config

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"

	btime "github.com/17media/api/base/time"
	"github.com/17media/api/models"
	i18nModel "github.com/17media/api/models/i18n"
	missionModel "github.com/17media/api/models/mission"
	streamergrowthModel "github.com/17media/api/models/streamergrowth"
	risingstarModel "github.com/17media/api/models/streamergrowth/risingstar"
	timeswitch "github.com/17media/api/models/timeswitch"
)

const (
	mockUserID   = "123-456-abc"
	mockTWRegion = "TW"
)

var (
	mockRisingStarYaml = []byte(`
risingStarEnable: true
levels:
  - level: 1
    metrics:
      - id: f4b94347-2674-4f3d-aec3-cd8ee83062dc
        type: 14
        title: "streamer_loyalty_streaming_performance_validstreamedhours"
        threshold: 20
      - id: b86cad4d-ff6e-46cf-ae9c-a18cf6ea00fa
        type: 22
        title: "streamer_growth_risingstar_metrics_scheduletime"
        threshold: 2
      - id: fdb9bc01-8d71-4b36-96bc-c6a678aedf38
        type: 25
        title: "streamer_loyalty_streaming_performance_uniquegiftsender"
        threshold: 50
      - id: 9d230e92-7ff2-4756-b3c6-cefc9e713f47
        type: 26
        title: "streamer_growth_risingstar_metrics_accumfollowers"
        threshold: 1000
      - id: dc0148e2-8a1f-40cf-8219-66c7e9f2f1f4
        type: 21
        title: "streamer_loyalty_streaming_performance_points"
        threshold: 30000
      - id: e84d2bb0-195e-43f7-b706-02f15e7ca57b
        type: 23
        title: "streamer_loyalty_streaming_performance_newpost"
        threshold: 2
    rewards:
      - type: 9 # Badge
        iconURL: https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png
        value: 
        id:
        i18nName: streamer_growth_risingstar_rewards_badge
        i18nDescription: 
        i18nDescriptionParams:
      - type: 10 # Stickers
        iconURL: https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png
        value: 
        id: StickersID
        i18nName: streamer_growth_risingstar_rewards_stickers
        i18nDescription: 
        i18nDescriptionParams:
      - type: 5 # Score
        iconURL: https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png
        value: 10000
        id:
        i18nName: streamer_growth_risingstar_rewards_score
        i18nDescription: 
        i18nDescriptionParams:
      - type: 2 # Coins
        iconURL: https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png
        value:
        id:
        i18nName: streamer_growth_risingstar_rewards_babycoins
        i18nDescription: 
        i18nDescriptionParams:
    coinsProbability:
      - value: 1500
        weight: 100
      - value: 2000
        weight: 125
      - value: 2500
        weight: 125
      - value: 3000
        weight: 150
      - value: 3500
        weight: 150
      - value: 4000
        weight: 125
      - value: 4500
        weight: 125
      - value: 5000
        weight: 100
  - level: 2
    metrics:
      - id: f4b94347-2674-4f3d-aec3-cd8ee83062dc
        type: 14
        title: "streamer_loyalty_streaming_performance_validstreamedhours"
        threshold: 25
      - id: b86cad4d-ff6e-46cf-ae9c-a18cf6ea00fa
        type: 22
        title: "streamer_growth_risingstar_metrics_scheduletime"
        threshold: 5
      - id: fdb9bc01-8d71-4b36-96bc-c6a678aedf38
        type: 25
        title: "streamer_loyalty_streaming_performance_uniquegiftsender"
        threshold: 200
      - id: 9d230e92-7ff2-4756-b3c6-cefc9e713f47
        type: 26
        title: "streamer_growth_risingstar_metrics_accumfollowers"
        threshold: 30000
      - id: dc0148e2-8a1f-40cf-8219-66c7e9f2f1f4
        type: 21
        title: "streamer_loyalty_streaming_performance_points"
        threshold: 300000
      - id: e84d2bb0-195e-43f7-b706-02f15e7ca57b
        type: 23
        title: "streamer_loyalty_streaming_performance_newpost"
        threshold: 6
    rewards:
      - type: 9 # Badge
        iconURL: https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png
        value: 
        id:
        i18nName: streamer_growth_risingstar_rewards_badge
        i18nDescription: 
        i18nDescriptionParams:
      - type: 10 # Stickers
        iconURL: https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png
        value: 
        id: StickersID
        i18nName: streamer_growth_risingstar_rewards_stickers
        i18nDescription: 
        i18nDescriptionParams:
      - type: 5 # Score
        iconURL: https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png
        value: 20000
        id:
        i18nName: streamer_growth_risingstar_rewards_score
        i18nDescription: 
        i18nDescriptionParams:
      - type: 2 # Coins
        iconURL: https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png
        value:
        id:
        i18nName: streamer_growth_risingstar_rewards_babycoins
        i18nDescription: 
        i18nDescriptionParams:
    coinsProbability:
      - value: 2000
        weight: 50
      - value: 3000
        weight: 200
      - value: 4000
        weight: 250
      - value: 5000
        weight: 250
      - value: 6000
        weight: 250
  - level: 3
    metrics:
      - id: f4b94347-2674-4f3d-aec3-cd8ee83062dc
        type: 14
        title: "streamer_loyalty_streaming_performance_validstreamedhours"
        threshold: 30
      - id: b86cad4d-ff6e-46cf-ae9c-a18cf6ea00fa
        type: 22
        title: "streamer_growth_risingstar_metrics_scheduletime"
        threshold: 10
      - id: fdb9bc01-8d71-4b36-96bc-c6a678aedf38
        type: 25
        title: "streamer_loyalty_streaming_performance_uniquegiftsender"
        threshold: 300
      - id: 9d230e92-7ff2-4756-b3c6-cefc9e713f47
        type: 26
        title: "streamer_growth_risingstar_metrics_accumfollowers"
        threshold: 300000
      - id: dc0148e2-8a1f-40cf-8219-66c7e9f2f1f4
        type: 21
        title: "streamer_loyalty_streaming_performance_points"
        threshold: 1000000
      - id: e84d2bb0-195e-43f7-b706-02f15e7ca57b
        type: 23
        title: "streamer_loyalty_streaming_performance_newpost"
        threshold: 10
    rewards:
      - type: 9 # Badge
        iconURL: https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png
        value: 
        id:
        i18nName: streamer_growth_risingstar_rewards_badge
        i18nDescription: 
        i18nDescriptionParams:
      - type: 10 # Stickers
        iconURL: https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png
        value: 
        id: StickersID
        i18nName: streamer_growth_risingstar_rewards_stickers
        i18nDescription: 
        i18nDescriptionParams:
      - type: 5 # Score
        iconURL: https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png
        value: 30000
        id:
        i18nName: streamer_growth_risingstar_rewards_score
        i18nDescription: 
        i18nDescriptionParams:
      - type: 2 # Coins
        iconURL: https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png
        value:
        id:
        i18nName: streamer_growth_risingstar_rewards_babycoins
        i18nDescription: 
        i18nDescriptionParams:
      - type: 11 # RedEnvelope
        iconURL: https://cdn.17app.co/923b96d6-92e0-4e60-a7a9-fe8230c10234.png
        value:
        id: RedEnvelopeID
        i18nName: streamer_growth_risingstar_rewards_redenvelope
        i18nDescription: 
        i18nDescriptionParams:
    coinsProbability:
      - value: 5000
        weight: 200
      - value: 6000
        weight: 300
      - value: 7000
        weight: 300
      - value: 8000
        weight: 200
  - level: 4
    metrics:
      - id: f4b94347-2674-4f3d-aec3-cd8ee83062dc
        type: 14
        title: "streamer_loyalty_streaming_performance_validstreamedhours"
        threshold: 35
      - id: b86cad4d-ff6e-46cf-ae9c-a18cf6ea00fa
        type: 22
        title: "streamer_growth_risingstar_metrics_scheduletime"
        threshold: 15
      - id: fdb9bc01-8d71-4b36-96bc-c6a678aedf38
        type: 25
        title: "streamer_loyalty_streaming_performance_uniquegiftsender"
        threshold: 400
      - id: 9d230e92-7ff2-4756-b3c6-cefc9e713f47
        type: 26
        title: "streamer_growth_risingstar_metrics_accumfollowers"
        threshold: 800000
      - id: dc0148e2-8a1f-40cf-8219-66c7e9f2f1f4
        type: 21
        title: "streamer_loyalty_streaming_performance_points"
        threshold: 20000000
      - id: e84d2bb0-195e-43f7-b706-02f15e7ca57b
        type: 23
        title: "streamer_loyalty_streaming_performance_newpost"
        threshold: 15
    rewards:
      - type: 9 # Badge
        iconURL: https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png
        value: 
        id:
        i18nName: streamer_growth_risingstar_rewards_badge
        i18nDescription: 
        i18nDescriptionParams:
      - type: 10 # Stickers
        iconURL: https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png
        value: 
        id: StickersID
        i18nName: streamer_growth_risingstar_rewards_stickers
        i18nDescription: 
        i18nDescriptionParams:
      - type: 5 # Score
        iconURL: https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png
        value: 40000
        id:
        i18nName: streamer_growth_risingstar_rewards_score
        i18nDescription: 
        i18nDescriptionParams:
      - type: 2 # Coins
        iconURL: https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png
        value:
        id:
        i18nName: streamer_growth_risingstar_rewards_babycoins
        i18nDescription: 
        i18nDescriptionParams:
      - type: 11 # RedEnvelope
        iconURL: https://cdn.17app.co/923b96d6-92e0-4e60-a7a9-fe8230c10234.png
        value:
        id: RedEnvelopeID
        i18nName: streamer_growth_risingstar_rewards_redenvelope
        i18nDescription: 
        i18nDescriptionParams:
      - type: 7 # DirectGift
        iconURL: https://cdn.17app.co/31328f42-0409-4b2b-a186-24b2b5125c5d.png
        value: 
        id: DirectGiftID
        i18nName: streamer_growth_risingstar_rewards_gift
        i18nDescription: 
        i18nDescriptionParams:
    coinsProbability:
      - value: 6000
        weight: 200
      - value: 7000
        weight: 200
      - value: 8000
        weight: 200
      - value: 9000
        weight: 200
      - value: 10000
        weight: 200
  - level: 5
    metrics:
      - id: f4b94347-2674-4f3d-aec3-cd8ee83062dc
        type: 14
        title: "streamer_loyalty_streaming_performance_validstreamedhours"
        threshold: 40
      - id: b86cad4d-ff6e-46cf-ae9c-a18cf6ea00fa
        type: 22
        title: "streamer_growth_risingstar_metrics_scheduletime"
        threshold: 20
      - id: fdb9bc01-8d71-4b36-96bc-c6a678aedf38
        type: 25
        title: "streamer_loyalty_streaming_performance_uniquegiftsender"
        threshold: 500
      - id: 9d230e92-7ff2-4756-b3c6-cefc9e713f47
        type: 26
        title: "streamer_growth_risingstar_metrics_accumfollowers"
        threshold: 1200000
      - id: dc0148e2-8a1f-40cf-8219-66c7e9f2f1f4
        type: 21
        title: "streamer_loyalty_streaming_performance_points"
        threshold: 60000000
      - id: e84d2bb0-195e-43f7-b706-02f15e7ca57b
        type: 23
        title: "streamer_loyalty_streaming_performance_newpost"
        threshold: 20
    rewards:
      - type: 9 # Badge
        iconURL: https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png
        value: 
        id:
        i18nName: streamer_growth_risingstar_rewards_badge
        i18nDescription: 
        i18nDescriptionParams:
      - type: 10 # Stickers
        iconURL: https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png
        value: 
        id: StickersID
        i18nName: streamer_growth_risingstar_rewards_stickers
        i18nDescription: 
        i18nDescriptionParams:
      - type: 5 # Score
        iconURL: https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png
        value: 50000
        id:
        i18nName: streamer_growth_risingstar_rewards_score
        i18nDescription: 
        i18nDescriptionParams:
      - type: 2 # Coins
        iconURL: https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png
        value:
        id:
        i18nName: streamer_growth_risingstar_rewards_babycoins
        i18nDescription: 
        i18nDescriptionParams:
      - type: 11 # RedEnvelope
        iconURL: https://cdn.17app.co/923b96d6-92e0-4e60-a7a9-fe8230c10234.png
        value:
        id: RedEnvelopeID
        i18nName: streamer_growth_risingstar_rewards_redenvelope
        i18nDescription: 
        i18nDescriptionParams:
      - type: 7 # DirectGift
        iconURL: https://cdn.17app.co/31328f42-0409-4b2b-a186-24b2b5125c5d.png
        value: 
        id: DirectGiftID
        i18nName: streamer_growth_risingstar_rewards_gift
        i18nDescription: 
        i18nDescriptionParams:
      - type: 12 # Marquee
        iconURL: https://cdn.17app.co/0a70d730-d578-44b4-aaff-5a37fe48ba45.png
        value: 
        id:
        i18nName: streamer_growth_risingstar_rewards_marquee
        i18nDescription: streamer_growth_risingstar_marquee_wording
        i18nDescriptionParams:
    coinsProbability:
      - value: 8000
        weight: 150
      - value: 9000
        weight: 150
      - value: 10000
        weight: 150
      - value: 11000
        weight: 150
      - value: 12000
        weight: 150
      - value: 13000
        weight: 100
      - value: 14000
        weight: 100
      - value: 15000
        weight: 50
`)

	mockRisingStarYamlFailCase = []byte(`
levels:
  - level: 1
    metrics:
    rewards:
`)

	mockRisingStarConfig = StreamerRisingStarConfig{
		RisingStarEnable: true,
		Levels: []LevelConfig{
			{
				Level: 1,
				Metrics: []MetricConfig{
					{ID: "f4b94347-2674-4f3d-aec3-cd8ee83062dc", Type: 14, Title: "streamer_loyalty_streaming_performance_validstreamedhours", Threshold: 20},
					{ID: "b86cad4d-ff6e-46cf-ae9c-a18cf6ea00fa", Type: 22, Title: "streamer_growth_risingstar_metrics_scheduletime", Threshold: 2},
					{ID: "fdb9bc01-8d71-4b36-96bc-c6a678aedf38", Type: 25, Title: "streamer_loyalty_streaming_performance_uniquegiftsender", Threshold: 50},
					{ID: "9d230e92-7ff2-4756-b3c6-cefc9e713f47", Type: 26, Title: "streamer_growth_risingstar_metrics_accumfollowers", Threshold: 1000},
					{ID: "dc0148e2-8a1f-40cf-8219-66c7e9f2f1f4", Type: 21, Title: "streamer_loyalty_streaming_performance_points", Threshold: 30000},
					{ID: "e84d2bb0-195e-43f7-b706-02f15e7ca57b", Type: 23, Title: "streamer_loyalty_streaming_performance_newpost", Threshold: 2},
				},
				Rewards: []RewardConfig{
					{Type: 9, IconURL: "https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png", I18nName: "streamer_growth_risingstar_rewards_badge"},
					{Type: 10, IconURL: "https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png", ID: "StickersID", I18nName: "streamer_growth_risingstar_rewards_stickers"},
					{Type: 5, IconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png", Value: 10000, I18nName: "streamer_growth_risingstar_rewards_score"},
					{Type: 2, IconURL: "https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png", I18nName: "streamer_growth_risingstar_rewards_babycoins"},
				},
				CoinsProbability: []ProbabilityConfig{
					{Value: 1500, Weight: 100, CumulativeWeight: 100},
					{Value: 2000, Weight: 125, CumulativeWeight: 225},
					{Value: 2500, Weight: 125, CumulativeWeight: 350},
					{Value: 3000, Weight: 150, CumulativeWeight: 500},
					{Value: 3500, Weight: 150, CumulativeWeight: 650},
					{Value: 4000, Weight: 125, CumulativeWeight: 775},
					{Value: 4500, Weight: 125, CumulativeWeight: 900},
					{Value: 5000, Weight: 100, CumulativeWeight: 1000},
				},
			},
			{
				Level: 2,
				Metrics: []MetricConfig{
					{ID: "f4b94347-2674-4f3d-aec3-cd8ee83062dc", Type: 14, Title: "streamer_loyalty_streaming_performance_validstreamedhours", Threshold: 25},
					{ID: "b86cad4d-ff6e-46cf-ae9c-a18cf6ea00fa", Type: 22, Title: "streamer_growth_risingstar_metrics_scheduletime", Threshold: 5},
					{ID: "fdb9bc01-8d71-4b36-96bc-c6a678aedf38", Type: 25, Title: "streamer_loyalty_streaming_performance_uniquegiftsender", Threshold: 200},
					{ID: "9d230e92-7ff2-4756-b3c6-cefc9e713f47", Type: 26, Title: "streamer_growth_risingstar_metrics_accumfollowers", Threshold: 30000},
					{ID: "dc0148e2-8a1f-40cf-8219-66c7e9f2f1f4", Type: 21, Title: "streamer_loyalty_streaming_performance_points", Threshold: 300000},
					{ID: "e84d2bb0-195e-43f7-b706-02f15e7ca57b", Type: 23, Title: "streamer_loyalty_streaming_performance_newpost", Threshold: 6},
				},
				Rewards: []RewardConfig{
					{Type: 9, IconURL: "https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png", I18nName: "streamer_growth_risingstar_rewards_badge"},
					{Type: 10, IconURL: "https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png", ID: "StickersID", I18nName: "streamer_growth_risingstar_rewards_stickers"},
					{Type: 5, IconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png", Value: 20000, I18nName: "streamer_growth_risingstar_rewards_score"},
					{Type: 2, IconURL: "https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png", I18nName: "streamer_growth_risingstar_rewards_babycoins"},
				},
				CoinsProbability: []ProbabilityConfig{
					{Value: 2000, Weight: 50, CumulativeWeight: 50},
					{Value: 3000, Weight: 200, CumulativeWeight: 250},
					{Value: 4000, Weight: 250, CumulativeWeight: 500},
					{Value: 5000, Weight: 250, CumulativeWeight: 750},
					{Value: 6000, Weight: 250, CumulativeWeight: 1000},
				},
			},
			{
				Level: 3,
				Metrics: []MetricConfig{
					{ID: "f4b94347-2674-4f3d-aec3-cd8ee83062dc", Type: 14, Title: "streamer_loyalty_streaming_performance_validstreamedhours", Threshold: 30},
					{ID: "b86cad4d-ff6e-46cf-ae9c-a18cf6ea00fa", Type: 22, Title: "streamer_growth_risingstar_metrics_scheduletime", Threshold: 10},
					{ID: "fdb9bc01-8d71-4b36-96bc-c6a678aedf38", Type: 25, Title: "streamer_loyalty_streaming_performance_uniquegiftsender", Threshold: 300},
					{ID: "9d230e92-7ff2-4756-b3c6-cefc9e713f47", Type: 26, Title: "streamer_growth_risingstar_metrics_accumfollowers", Threshold: 300000},
					{ID: "dc0148e2-8a1f-40cf-8219-66c7e9f2f1f4", Type: 21, Title: "streamer_loyalty_streaming_performance_points", Threshold: 1000000},
					{ID: "e84d2bb0-195e-43f7-b706-02f15e7ca57b", Type: 23, Title: "streamer_loyalty_streaming_performance_newpost", Threshold: 10},
				},
				Rewards: []RewardConfig{
					{Type: 9, IconURL: "https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png", I18nName: "streamer_growth_risingstar_rewards_badge"},
					{Type: 10, IconURL: "https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png", ID: "StickersID", I18nName: "streamer_growth_risingstar_rewards_stickers"},
					{Type: 5, IconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png", Value: 30000, I18nName: "streamer_growth_risingstar_rewards_score"},
					{Type: 2, IconURL: "https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png", I18nName: "streamer_growth_risingstar_rewards_babycoins"},
					{Type: 11, IconURL: "https://cdn.17app.co/923b96d6-92e0-4e60-a7a9-fe8230c10234.png", ID: "RedEnvelopeID", I18nName: "streamer_growth_risingstar_rewards_redenvelope"},
				},
				CoinsProbability: []ProbabilityConfig{
					{Value: 5000, Weight: 200, CumulativeWeight: 200},
					{Value: 6000, Weight: 300, CumulativeWeight: 500},
					{Value: 7000, Weight: 300, CumulativeWeight: 800},
					{Value: 8000, Weight: 200, CumulativeWeight: 1000},
				},
			},
			{
				Level: 4,
				Metrics: []MetricConfig{
					{ID: "f4b94347-2674-4f3d-aec3-cd8ee83062dc", Type: 14, Title: "streamer_loyalty_streaming_performance_validstreamedhours", Threshold: 35},
					{ID: "b86cad4d-ff6e-46cf-ae9c-a18cf6ea00fa", Type: 22, Title: "streamer_growth_risingstar_metrics_scheduletime", Threshold: 15},
					{ID: "fdb9bc01-8d71-4b36-96bc-c6a678aedf38", Type: 25, Title: "streamer_loyalty_streaming_performance_uniquegiftsender", Threshold: 400},
					{ID: "9d230e92-7ff2-4756-b3c6-cefc9e713f47", Type: 26, Title: "streamer_growth_risingstar_metrics_accumfollowers", Threshold: 800000},
					{ID: "dc0148e2-8a1f-40cf-8219-66c7e9f2f1f4", Type: 21, Title: "streamer_loyalty_streaming_performance_points", Threshold: 20000000},
					{ID: "e84d2bb0-195e-43f7-b706-02f15e7ca57b", Type: 23, Title: "streamer_loyalty_streaming_performance_newpost", Threshold: 15},
				},
				Rewards: []RewardConfig{
					{Type: 9, IconURL: "https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png", I18nName: "streamer_growth_risingstar_rewards_badge"},
					{Type: 10, IconURL: "https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png", ID: "StickersID", I18nName: "streamer_growth_risingstar_rewards_stickers"},
					{Type: 5, IconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png", Value: 40000, I18nName: "streamer_growth_risingstar_rewards_score"},
					{Type: 2, IconURL: "https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png", I18nName: "streamer_growth_risingstar_rewards_babycoins"},
					{Type: 11, IconURL: "https://cdn.17app.co/923b96d6-92e0-4e60-a7a9-fe8230c10234.png", ID: "RedEnvelopeID", I18nName: "streamer_growth_risingstar_rewards_redenvelope"},
					{Type: 7, IconURL: "https://cdn.17app.co/31328f42-0409-4b2b-a186-24b2b5125c5d.png", ID: "DirectGiftID", I18nName: "streamer_growth_risingstar_rewards_gift"},
				},
				CoinsProbability: []ProbabilityConfig{
					{Value: 6000, Weight: 200, CumulativeWeight: 200},
					{Value: 7000, Weight: 200, CumulativeWeight: 400},
					{Value: 8000, Weight: 200, CumulativeWeight: 600},
					{Value: 9000, Weight: 200, CumulativeWeight: 800},
					{Value: 10000, Weight: 200, CumulativeWeight: 1000},
				},
			},
			{
				Level: 5,
				Metrics: []MetricConfig{
					{ID: "f4b94347-2674-4f3d-aec3-cd8ee83062dc", Type: 14, Title: "streamer_loyalty_streaming_performance_validstreamedhours", Threshold: 40},
					{ID: "b86cad4d-ff6e-46cf-ae9c-a18cf6ea00fa", Type: 22, Title: "streamer_growth_risingstar_metrics_scheduletime", Threshold: 20},
					{ID: "fdb9bc01-8d71-4b36-96bc-c6a678aedf38", Type: 25, Title: "streamer_loyalty_streaming_performance_uniquegiftsender", Threshold: 500},
					{ID: "9d230e92-7ff2-4756-b3c6-cefc9e713f47", Type: 26, Title: "streamer_growth_risingstar_metrics_accumfollowers", Threshold: 1200000},
					{ID: "dc0148e2-8a1f-40cf-8219-66c7e9f2f1f4", Type: 21, Title: "streamer_loyalty_streaming_performance_points", Threshold: 60000000},
					{ID: "e84d2bb0-195e-43f7-b706-02f15e7ca57b", Type: 23, Title: "streamer_loyalty_streaming_performance_newpost", Threshold: 20},
				},
				Rewards: []RewardConfig{
					{Type: 9, IconURL: "https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png", I18nName: "streamer_growth_risingstar_rewards_badge"},
					{Type: 10, IconURL: "https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png", ID: "StickersID", I18nName: "streamer_growth_risingstar_rewards_stickers"},
					{Type: 5, IconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png", Value: 50000, I18nName: "streamer_growth_risingstar_rewards_score"},
					{Type: 2, IconURL: "https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png", I18nName: "streamer_growth_risingstar_rewards_babycoins"},
					{Type: 11, IconURL: "https://cdn.17app.co/923b96d6-92e0-4e60-a7a9-fe8230c10234.png", ID: "RedEnvelopeID", I18nName: "streamer_growth_risingstar_rewards_redenvelope"},
					{Type: 7, IconURL: "https://cdn.17app.co/31328f42-0409-4b2b-a186-24b2b5125c5d.png", ID: "DirectGiftID", I18nName: "streamer_growth_risingstar_rewards_gift"},
					{Type: 12, IconURL: "https://cdn.17app.co/0a70d730-d578-44b4-aaff-5a37fe48ba45.png", I18nDescription: "streamer_growth_risingstar_marquee_wording", I18nName: "streamer_growth_risingstar_rewards_marquee"},
				},
				CoinsProbability: []ProbabilityConfig{
					{Value: 8000, Weight: 150, CumulativeWeight: 150},
					{Value: 9000, Weight: 150, CumulativeWeight: 300},
					{Value: 10000, Weight: 150, CumulativeWeight: 450},
					{Value: 11000, Weight: 150, CumulativeWeight: 600},
					{Value: 12000, Weight: 150, CumulativeWeight: 750},
					{Value: 13000, Weight: 100, CumulativeWeight: 850},
					{Value: 14000, Weight: 100, CumulativeWeight: 950},
					{Value: 15000, Weight: 50, CumulativeWeight: 1000},
				},
			},
		},
	}
)

type configSuite struct {
	suite.Suite
}

func (s *configSuite) SetupSuite() {
}

func (s *configSuite) SetupTest() {
	getStreamerRisingstarConfig = func(region string) StreamerRisingStarConfig {
		conf := StreamerRisingStarConfig{}
		intf, _, _ := conf.Check(mockRisingStarYaml)
		conf.Apply(intf)
		return conf
	}
}

func (s *configSuite) TestStreamerRisingStarYaml() {
	tests := []struct {
		Desc       string
		InputBytes []byte
		ExpErr     error
		ExpConf    StreamerRisingStarConfig
	}{
		{
			Desc:       "empty case",
			InputBytes: []byte(``),
			ExpErr:     nil,
			ExpConf: StreamerRisingStarConfig{
				RisingStarEnable: false,
			},
		},
		{
			Desc:       "normal case",
			InputBytes: mockRisingStarYaml,
			ExpErr:     nil,
			ExpConf:    mockRisingStarConfig,
		},
		{
			Desc: "test parse startTimestamp error",
			InputBytes: []byte(`
risingStarEnable: true
startTime: 0
`),
			ExpErr: fmt.Errorf("startTime parse failed: parsing time \"0\" as \"2006-01-02 15:04:05 (GMT-0700)\": cannot parse \"0\" as \"2006\""),
		},
		{
			Desc: "test parse startTimestamp",
			InputBytes: []byte(`
risingStarEnable: true
startTime: 2021-03-01 00:00:00 (GMT+0800)
`),
			ExpConf: StreamerRisingStarConfig{
				RisingStarEnable: true,
				StartTime:        "2021-03-01 00:00:00 (GMT+0800)",
				StartTimestamp:   int64(1614528000),
			},
		},
	}

	for _, t := range tests {
		fmt.Println("Cases: ", t.Desc)

		conf := StreamerRisingStarConfig{}
		intf, _, err := conf.Check(t.InputBytes)
		s.Require().Equal(t.ExpErr, err, t.Desc)

		if err == nil {
			conf.Apply(intf)
			s.Require().Equal(t.ExpConf, conf, t.Desc)
		}

		fmt.Printf("\n\n")
	}
}

func (s *configSuite) TestGetLevelUpRewards() {

	tests := []struct {
		Desc     string
		Level    int32
		Detail   bool
		Exp      []risingstarModel.RisingReward
		MockFunc func()
	}{
		{
			Desc:   "Level 1",
			Level:  1,
			Detail: true,
			Exp: []risingstarModel.RisingReward{
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_badge"},
					RewardType:        9,
					RewardTypeIconURL: "https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png",
					Count:             1,
					Level:             1,
				},
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_stickers"},
					RewardType:        10,
					RewardTypeIconURL: "https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png",
					Count:             1,
					RewardID:          "StickersID",
					Level:             1,
				},
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_score"},
					RewardType:        5,
					RewardTypeIconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png",
					Count:             10000,
					Level:             1,
				},
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_babycoins"},
					RewardType:        2,
					RewardTypeIconURL: "https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png",
					Count:             2000,
					Level:             1,
				},
			},
			MockFunc: func() {
				s.SetupTest()

				randInt31n = func(int32) int32 {
					return 150
				}
			},
		},
		{
			Desc:   "Level 5",
			Level:  5,
			Detail: true,
			Exp: []risingstarModel.RisingReward{
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_badge"},
					RewardType:        9,
					RewardTypeIconURL: "https://cdn.17app.co/85c45c9d-7c90-4d89-b363-1ce70f3dd747.png",
					Count:             1,
					Level:             5,
				},
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_stickers"},
					RewardType:        10,
					RewardTypeIconURL: "https://cdn.17app.co/e2c2a3b5-dded-4e5a-a367-2a4461dd9e51.png",
					Count:             1,
					RewardID:          "StickersID",
					Level:             5,
				},
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_score"},
					RewardType:        5,
					RewardTypeIconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png",
					Count:             50000,
					Level:             5,
				},
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_babycoins"},
					RewardType:        2,
					RewardTypeIconURL: "https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png",
					Count:             9000,
					Level:             5,
				},
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_redenvelope"},
					RewardType:        11,
					RewardTypeIconURL: "https://cdn.17app.co/923b96d6-92e0-4e60-a7a9-fe8230c10234.png",
					Count:             1,
					RewardID:          "RedEnvelopeID",
					Level:             5,
				},
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_gift"},
					RewardType:        7,
					RewardTypeIconURL: "https://cdn.17app.co/31328f42-0409-4b2b-a186-24b2b5125c5d.png",
					Count:             1,
					RewardID:          "DirectGiftID",
					Level:             5,
				},
				{
					RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_marquee"},
					RewardType:        12,
					RewardTypeIconURL: "https://cdn.17app.co/0a70d730-d578-44b4-aaff-5a37fe48ba45.png",
					Count:             1,
					RewardDescription: &i18nModel.Token{Key: "streamer_growth_risingstar_marquee_wording"},
					Level:             5,
				},
			},
			MockFunc: func() {
				s.SetupTest()

				randInt31n = func(int32) int32 {
					return 150
				}
			},
		},
	}

	for _, test := range tests {
		test.MockFunc()

		rewards := GetLevelUpRewards("TW", test.Level, test.Detail)

		s.Require().Equal(test.Exp, rewards, test.Desc)
	}
}

func (s *configSuite) TestComputeRisingMetric() {

	tests := []struct {
		Desc        string
		Type        int32
		RisingLevel int32
		ExpConfig   MetricConfig
		MockFunc    func()
	}{
		{
			Desc:        "should return level [2] metric type [MetricTypeValidStreamedHours] when rising level is [1]",
			Type:        int32(missionModel.Type_StreamerLiveXSeconds),
			RisingLevel: 1,
			ExpConfig: MetricConfig{
				ID:        "f4b94347-2674-4f3d-aec3-cd8ee83062dc",
				Type:      missionModel.Type_StreamerLiveXSeconds,
				Title:     "streamer_loyalty_streaming_performance_validstreamedhours",
				Threshold: 25,
			},
			MockFunc: func() {
				s.SetupTest()
			},
		},
		{
			Desc:        "should return level [1] metric type [MetricTypeValidStreamedHours] when rising level is [0]",
			Type:        int32(missionModel.Type_StreamerLiveXSeconds),
			RisingLevel: 0,
			ExpConfig: MetricConfig{
				ID:        "f4b94347-2674-4f3d-aec3-cd8ee83062dc",
				Type:      missionModel.Type_StreamerLiveXSeconds,
				Title:     "streamer_loyalty_streaming_performance_validstreamedhours",
				Threshold: 20,
			},
			MockFunc: func() {
				s.SetupTest()
			},
		},
		{
			Desc:        "should return level [5] metric type [MetricTypeValidStreamedHours] when rising level is [999]",
			Type:        int32(missionModel.Type_StreamerLiveXSeconds),
			RisingLevel: 999,
			ExpConfig: MetricConfig{
				ID:        "f4b94347-2674-4f3d-aec3-cd8ee83062dc",
				Type:      missionModel.Type_StreamerLiveXSeconds,
				Title:     "streamer_loyalty_streaming_performance_validstreamedhours",
				Threshold: 40,
			},
			MockFunc: func() {
				s.SetupTest()
			},
		},
	}

	for _, test := range tests {
		test.MockFunc()

		config := ComputeRisingMetric("TW", test.Type, test.RisingLevel)

		s.Require().Equal(test.ExpConfig, config, test.Desc)
	}
}

func (s *configSuite) TestComputeMetricStatus() {

	tests := []struct {
		Desc      string
		Type      int32
		Value     int32
		ExpLevel  int32
		ExpConfig MetricConfig
		MockFunc  func()
	}{
		{
			Desc:     "Valid Streamed Hours Level 1",
			Type:     int32(missionModel.Type_StreamerLiveXSeconds),
			Value:    5,
			ExpLevel: 0,
			ExpConfig: MetricConfig{
				ID:        "f4b94347-2674-4f3d-aec3-cd8ee83062dc",
				Type:      missionModel.Type_StreamerLiveXSeconds,
				Title:     "streamer_loyalty_streaming_performance_validstreamedhours",
				Threshold: 20,
			},
			MockFunc: func() {
				s.SetupTest()
			},
		},
		{
			Desc:     "New Post Count Level 5",
			Type:     int32(missionModel.Type_RisingStarNewPostCount),
			Value:    20,
			ExpLevel: 5,
			ExpConfig: MetricConfig{
				ID:        "e84d2bb0-195e-43f7-b706-02f15e7ca57b",
				Type:      missionModel.Type_RisingStarNewPostCount,
				Title:     "streamer_loyalty_streaming_performance_newpost",
				Threshold: 20,
			},
			MockFunc: func() {
				s.SetupTest()
			},
		},
		{
			Desc:     "Valid Streamed Hours Level 0",
			Type:     int32(missionModel.Type_StreamerLiveXSeconds),
			Value:    0,
			ExpLevel: 0,
			ExpConfig: MetricConfig{
				ID:        "f4b94347-2674-4f3d-aec3-cd8ee83062dc",
				Type:      missionModel.Type_StreamerLiveXSeconds,
				Title:     "streamer_loyalty_streaming_performance_validstreamedhours",
				Threshold: 20,
			},
			MockFunc: func() {
				s.SetupTest()
			},
		},
		{
			Desc:     "should return the undefined mission type",
			Type:     int32(missionModel.Type_CreateAvatar),
			Value:    999,
			ExpLevel: 0,
			ExpConfig: MetricConfig{
				Type: missionModel.Type_CreateAvatar,
			},
			MockFunc: func() {
				s.SetupTest()
			},
		},
	}

	for _, test := range tests {
		test.MockFunc()

		level, config := ComputeMetricStatus("TW", test.Type, test.Value)

		s.Require().Equal(test.ExpLevel, level, test.Desc)
		s.Require().Equal(test.ExpConfig, config, test.Desc)
	}
}

func (s *configSuite) TestStreamerGrowthYaml() {
	tests := []struct {
		Desc       string
		InputBytes []byte
		ExpErr     error
		ExpConf    StreamerGrowthConfig
	}{
		{
			Desc:       "empty case",
			InputBytes: []byte(``),
			ExpErr:     nil,
			ExpConf:    StreamerGrowthConfig{},
		},
		{
			Desc: "normal case",
			InputBytes: []byte(`
risingStar:
  title: streamer_growth_risingstar
  rewards:
    title: streamer_growth_info_risingstar_rewards
    level:
    - title: LV.1
      icon: https://cdn.17app.co/fa7eccea-deb9-4a82-baff-d362265eb0f8.png
      prizes:
      - itemName: streamer_growth_info_risingstar_rewarddefinition1
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/d8c7d13b-1f0e-4e6c-bb62-a02fcea681b7.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition2
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/e6433a00-7e9e-4c12-9a07-4aeacd9740e9.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition3
        itemNameParams: '100'
        itemImageURL: https://cdn.17app.co/29db0869-eb57-437e-9e84-60e72bd68dd1.png
    - title: LV.2
      icon: https://cdn.17app.co/ea777545-1c7c-4ed4-b859-e4aebcf47732.png
      prizes:
      - itemName: streamer_growth_info_risingstar_rewarddefinition1
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/d8c7d13b-1f0e-4e6c-bb62-a02fcea681b7.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition2
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/e6433a00-7e9e-4c12-9a07-4aeacd9740e9.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition3
        itemNameParams: '100'
        itemImageURL: https://cdn.17app.co/29db0869-eb57-437e-9e84-60e72bd68dd1.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition4
        itemNameParams: '100'
        itemImageURL: https://cdn.17app.co/a0f3299f-2b24-44ee-b920-38c8d47edd47.png
    - title: LV.3
      icon: https://cdn.17app.co/21e1a886-c9b6-4c9d-8b76-454678d7dbdf.png
      prizes:
      - itemName: streamer_growth_info_risingstar_rewarddefinition1
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/d8c7d13b-1f0e-4e6c-bb62-a02fcea681b7.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition2
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/e6433a00-7e9e-4c12-9a07-4aeacd9740e9.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition3
        itemNameParams: '100'
        itemImageURL: https://cdn.17app.co/29db0869-eb57-437e-9e84-60e72bd68dd1.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition4
        itemNameParams: '100'
        itemImageURL: https://cdn.17app.co/a0f3299f-2b24-44ee-b920-38c8d47edd47.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition5
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/bf1adf4a-d668-40ca-b878-b8f546da8296.png
    - title: LV.4
      icon: https://cdn.17app.co/4b6683c8-393c-4f14-8ffc-95c7f28aca9c.png
      prizes:
      - itemName: streamer_growth_info_risingstar_rewarddefinition1
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/d8c7d13b-1f0e-4e6c-bb62-a02fcea681b7.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition2
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/e6433a00-7e9e-4c12-9a07-4aeacd9740e9.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition3
        itemNameParams: '100'
        itemImageURL: https://cdn.17app.co/29db0869-eb57-437e-9e84-60e72bd68dd1.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition4
        itemNameParams: '100'
        itemImageURL: https://cdn.17app.co/a0f3299f-2b24-44ee-b920-38c8d47edd47.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition5
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/bf1adf4a-d668-40ca-b878-b8f546da8296.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition6
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/0657fa4f-f9c9-4a38-9620-bf2420fd2d37.png
    - title: LV.5
      icon: https://cdn.17app.co/7ebc9118-5529-4211-9db9-d4a9beb74c1f.png
      prizes:
      - itemName: streamer_growth_info_risingstar_rewarddefinition1
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/d8c7d13b-1f0e-4e6c-bb62-a02fcea681b7.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition2
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/e6433a00-7e9e-4c12-9a07-4aeacd9740e9.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition3
        itemNameParams: '100'
        itemImageURL: https://cdn.17app.co/29db0869-eb57-437e-9e84-60e72bd68dd1.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition4
        itemNameParams: '100'
        itemImageURL: https://cdn.17app.co/a0f3299f-2b24-44ee-b920-38c8d47edd47.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition5
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/bf1adf4a-d668-40ca-b878-b8f546da8296.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition6
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/0657fa4f-f9c9-4a38-9620-bf2420fd2d37.png
      - itemName: streamer_growth_info_risingstar_rewarddefinition7
        itemNameParams: ''
        itemImageURL: https://cdn.17app.co/bf55d9ff-3bfa-44e1-9c16-4bbb587f94b5.png
  metrics:
    title: streamer_growth_info_risingstar_metrics
    displayName:
      validStreamedHours: streamer_loyalty_streaming_performance_validstreamedhours
      timeSchedule: streamer_growth_risingstar_metrics_scheduletime
      uniqueGiftSenders: streamer_loyalty_streaming_performance_uniquegiftsender
      accumulatedFollowers: streamer_growth_risingstar_metrics_accumfollowers
      receivedBabyCoins: streamer_loyalty_streaming_performance_points
      newPostCount: streamer_loyalty_streaming_performance_newpost
    level:
    - title: LV.1
      metrics:
        validStreamedHours: '20'
        timeSchedule: '2'
        uniqueGiftSenders: '50'
        accumulatedFollowers: 1K
        receivedBabyCoins: 30K
        newPostCount: '2'
    - title: LV.2
      metrics:
        validStreamedHours: '25'
        timeSchedule: '5'
        uniqueGiftSenders: '200'
        accumulatedFollowers: 30K
        receivedBabyCoins: 300K
        newPostCount: '6'
    - title: LV.3
      metrics:
        validStreamedHours: '30'
        timeSchedule: '10'
        uniqueGiftSenders: '300'
        accumulatedFollowers: 300K
        receivedBabyCoins: 1000K
        newPostCount: '10'
    - title: LV.4
      metrics:
        validStreamedHours: '35'
        timeSchedule: '15'
        uniqueGiftSenders: '400'
        accumulatedFollowers: 800K
        receivedBabyCoins: 20M
        newPostCount: '15'
    - title: LV.5
      metrics:
        validStreamedHours: '40'
        timeSchedule: '20'
        uniqueGiftSenders: '500'
        accumulatedFollowers: 1200K
        receivedBabyCoins: 60M
        newPostCount: '20'
  rules:
  - title: streamer_growth_info_risingstar_rules
    description:
    - streamer_growth_info_risingstar_rules1
    - streamer_growth_info_risingstar_rules2
    - streamer_growth_info_risingstar_rules3
    - streamer_growth_info_risingstar_rules4
  - title: streamer_growth_info_risingstar_metricsdefinition
    description:
    - streamer_growth_info_risingstar_metrics1
    - streamer_growth_info_risingstar_metrics2
    - streamer_growth_info_risingstar_metrics3
    - streamer_growth_info_risingstar_metrics4
    - streamer_growth_info_risingstar_metrics5
    - streamer_growth_info_risingstar_metrics6
  - title: streamer_growth_info_risingstar_rewarddefinition
    description:
    - streamer_growth_info_risingstar_rewarddefinition1
    - streamer_growth_info_risingstar_rewarddefinition2
    - streamer_growth_info_risingstar_rewarddefinition3
    - streamer_growth_info_risingstar_rewarddefinition4
    - streamer_growth_info_risingstar_rewarddefinition5
    - streamer_growth_info_risingstar_rewarddefinition6
    - streamer_growth_info_risingstar_rewarddefinition7
    - streamer_growth_info_risingstar_rewarddefinition8
dailyTask:
  title: streamer_growth_dailytask
  rules:
  - title: streamer_growth_info_dailytask_rules
    description:
    - streamer_growth_info_dailytask_rules1
    - streamer_growth_info_dailytask_rules2
    - streamer_growth_info_dailytask_rules3
`),
			ExpErr: nil,
			ExpConf: StreamerGrowthConfig{
				RisingStar: streamergrowthModel.RisingStar{
					Title: "streamer_growth_risingstar",
					Rewards: streamergrowthModel.RisingStarRewards{
						Title: "streamer_growth_info_risingstar_rewards",
						Level: []streamergrowthModel.RisingStarRewardsLevel{
							{
								Title: "LV.1",
								Icon:  "https://cdn.17app.co/fa7eccea-deb9-4a82-baff-d362265eb0f8.png",
								Prizes: []streamergrowthModel.RisingStarRewardsPrizes{
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition1",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/d8c7d13b-1f0e-4e6c-bb62-a02fcea681b7.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition2",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/e6433a00-7e9e-4c12-9a07-4aeacd9740e9.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition3",
										ItemNameParams: "100",
										ItemImageURL:   "https://cdn.17app.co/29db0869-eb57-437e-9e84-60e72bd68dd1.png",
									},
								},
							},
							{
								Title: "LV.2",
								Icon:  "https://cdn.17app.co/ea777545-1c7c-4ed4-b859-e4aebcf47732.png",
								Prizes: []streamergrowthModel.RisingStarRewardsPrizes{
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition1",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/d8c7d13b-1f0e-4e6c-bb62-a02fcea681b7.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition2",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/e6433a00-7e9e-4c12-9a07-4aeacd9740e9.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition3",
										ItemNameParams: "100",
										ItemImageURL:   "https://cdn.17app.co/29db0869-eb57-437e-9e84-60e72bd68dd1.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition4",
										ItemNameParams: "100",
										ItemImageURL:   "https://cdn.17app.co/a0f3299f-2b24-44ee-b920-38c8d47edd47.png",
									},
								},
							},
							{
								Title: "LV.3",
								Icon:  "https://cdn.17app.co/21e1a886-c9b6-4c9d-8b76-454678d7dbdf.png",
								Prizes: []streamergrowthModel.RisingStarRewardsPrizes{
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition1",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/d8c7d13b-1f0e-4e6c-bb62-a02fcea681b7.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition2",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/e6433a00-7e9e-4c12-9a07-4aeacd9740e9.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition3",
										ItemNameParams: "100",
										ItemImageURL:   "https://cdn.17app.co/29db0869-eb57-437e-9e84-60e72bd68dd1.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition4",
										ItemNameParams: "100",
										ItemImageURL:   "https://cdn.17app.co/a0f3299f-2b24-44ee-b920-38c8d47edd47.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition5",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/bf1adf4a-d668-40ca-b878-b8f546da8296.png",
									},
								},
							},
							{
								Title: "LV.4",
								Icon:  "https://cdn.17app.co/4b6683c8-393c-4f14-8ffc-95c7f28aca9c.png",
								Prizes: []streamergrowthModel.RisingStarRewardsPrizes{
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition1",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/d8c7d13b-1f0e-4e6c-bb62-a02fcea681b7.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition2",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/e6433a00-7e9e-4c12-9a07-4aeacd9740e9.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition3",
										ItemNameParams: "100",
										ItemImageURL:   "https://cdn.17app.co/29db0869-eb57-437e-9e84-60e72bd68dd1.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition4",
										ItemNameParams: "100",
										ItemImageURL:   "https://cdn.17app.co/a0f3299f-2b24-44ee-b920-38c8d47edd47.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition5",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/bf1adf4a-d668-40ca-b878-b8f546da8296.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition6",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/0657fa4f-f9c9-4a38-9620-bf2420fd2d37.png",
									},
								},
							},
							{
								Title: "LV.5",
								Icon:  "https://cdn.17app.co/7ebc9118-5529-4211-9db9-d4a9beb74c1f.png",
								Prizes: []streamergrowthModel.RisingStarRewardsPrizes{
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition1",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/d8c7d13b-1f0e-4e6c-bb62-a02fcea681b7.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition2",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/e6433a00-7e9e-4c12-9a07-4aeacd9740e9.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition3",
										ItemNameParams: "100",
										ItemImageURL:   "https://cdn.17app.co/29db0869-eb57-437e-9e84-60e72bd68dd1.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition4",
										ItemNameParams: "100",
										ItemImageURL:   "https://cdn.17app.co/a0f3299f-2b24-44ee-b920-38c8d47edd47.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition5",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/bf1adf4a-d668-40ca-b878-b8f546da8296.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition6",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/0657fa4f-f9c9-4a38-9620-bf2420fd2d37.png",
									},
									{
										ItemName:       "streamer_growth_info_risingstar_rewarddefinition7",
										ItemNameParams: "",
										ItemImageURL:   "https://cdn.17app.co/bf55d9ff-3bfa-44e1-9c16-4bbb587f94b5.png",
									},
								},
							},
						},
					},
					Metrics: streamergrowthModel.RisingStarMetrics{
						Title: "streamer_growth_info_risingstar_metrics",
						DisplayName: streamergrowthModel.RisingStarMetricsDisplayName{
							ValidStreamedHours:   "streamer_loyalty_streaming_performance_validstreamedhours",
							TimeSchedule:         "streamer_growth_risingstar_metrics_scheduletime",
							UniqueGiftSenders:    "streamer_loyalty_streaming_performance_uniquegiftsender",
							AccumulatedFollowers: "streamer_growth_risingstar_metrics_accumfollowers",
							ReceivedBabyCoins:    "streamer_loyalty_streaming_performance_points",
							NewPostCount:         "streamer_loyalty_streaming_performance_newpost",
						},
						Level: []streamergrowthModel.RisingStarMetricsLevel{
							{
								Title: "LV.1",
								Metrics: streamergrowthModel.RisingStarMetricsDetail{
									ValidStreamedHours:   "20",
									TimeSchedule:         "2",
									UniqueGiftSenders:    "50",
									AccumulatedFollowers: "1K",
									ReceivedBabyCoins:    "30K",
									NewPostCount:         "2",
								},
							},
							{
								Title: "LV.2",
								Metrics: streamergrowthModel.RisingStarMetricsDetail{
									ValidStreamedHours:   "25",
									TimeSchedule:         "5",
									UniqueGiftSenders:    "200",
									AccumulatedFollowers: "30K",
									ReceivedBabyCoins:    "300K",
									NewPostCount:         "6",
								},
							},
							{
								Title: "LV.3",
								Metrics: streamergrowthModel.RisingStarMetricsDetail{
									ValidStreamedHours:   "30",
									TimeSchedule:         "10",
									UniqueGiftSenders:    "300",
									AccumulatedFollowers: "300K",
									ReceivedBabyCoins:    "1000K",
									NewPostCount:         "10",
								},
							},
							{
								Title: "LV.4",
								Metrics: streamergrowthModel.RisingStarMetricsDetail{
									ValidStreamedHours:   "35",
									TimeSchedule:         "15",
									UniqueGiftSenders:    "400",
									AccumulatedFollowers: "800K",
									ReceivedBabyCoins:    "20M",
									NewPostCount:         "15",
								},
							},
							{
								Title: "LV.5",
								Metrics: streamergrowthModel.RisingStarMetricsDetail{
									ValidStreamedHours:   "40",
									TimeSchedule:         "20",
									UniqueGiftSenders:    "500",
									AccumulatedFollowers: "1200K",
									ReceivedBabyCoins:    "60M",
									NewPostCount:         "20",
								},
							},
						},
					},
					Rule: []streamergrowthModel.Rules{
						{
							Title: "streamer_growth_info_risingstar_rules",
							Description: []string{
								"streamer_growth_info_risingstar_rules1",
								"streamer_growth_info_risingstar_rules2",
								"streamer_growth_info_risingstar_rules3",
								"streamer_growth_info_risingstar_rules4",
							},
						},
						{
							Title: "streamer_growth_info_risingstar_metricsdefinition",
							Description: []string{
								"streamer_growth_info_risingstar_metrics1",
								"streamer_growth_info_risingstar_metrics2",
								"streamer_growth_info_risingstar_metrics3",
								"streamer_growth_info_risingstar_metrics4",
								"streamer_growth_info_risingstar_metrics5",
								"streamer_growth_info_risingstar_metrics6",
							},
						},
						{
							Title: "streamer_growth_info_risingstar_rewarddefinition",
							Description: []string{
								"streamer_growth_info_risingstar_rewarddefinition1",
								"streamer_growth_info_risingstar_rewarddefinition2",
								"streamer_growth_info_risingstar_rewarddefinition3",
								"streamer_growth_info_risingstar_rewarddefinition4",
								"streamer_growth_info_risingstar_rewarddefinition5",
								"streamer_growth_info_risingstar_rewarddefinition6",
								"streamer_growth_info_risingstar_rewarddefinition7",
								"streamer_growth_info_risingstar_rewarddefinition8",
							},
						},
					},
				},
				DailyTask: streamergrowthModel.DailyTask{
					Title: "streamer_growth_dailytask",
					Rule: []streamergrowthModel.Rules{
						{
							Title: "streamer_growth_info_dailytask_rules",
							Description: []string{
								"streamer_growth_info_dailytask_rules1",
								"streamer_growth_info_dailytask_rules2",
								"streamer_growth_info_dailytask_rules3",
							},
						},
					},
				},
			},
		},
	}

	for _, t := range tests {
		fmt.Println("Cases: ", t.Desc)

		conf := StreamerGrowthConfig{}
		intf, _, err := conf.Check(t.InputBytes)
		s.Require().Equal(t.ExpErr, err, t.Desc)

		if err == nil {
			conf.Apply(intf)
			s.Require().Equal(t.ExpConf, conf, t.Desc)
		}

		fmt.Printf("\n\n")
	}
}

func (s *configSuite) TestGetThresholdsByMissionType() {
	tests := []struct {
		Desc     string
		Type     missionModel.Type
		ExpRes   []int
		MockFunc func()
	}{
		{
			Desc:   "should return [empty] threshold when type is undefined",
			Type:   missionModel.Type_CreateAvatar, // undefined mission type
			ExpRes: []int{},
			MockFunc: func() {
				s.SetupTest()
			},
		},
		{
			Desc:   "should return [20, 25, 30, 35, 40] threshold when type is [TypeStreamerLiveXSeconds]",
			Type:   missionModel.Type_StreamerLiveXSeconds,
			ExpRes: []int{20, 25, 30, 35, 40},
			MockFunc: func() {
				s.SetupTest()
			},
		},
	}

	for _, test := range tests {
		test.MockFunc()

		thresholds := GetThresholdsByMissionType(test.Type, mockTWRegion)

		s.Require().Equal(test.ExpRes, thresholds, test.Desc)
	}
}

func (s *configSuite) TestReachThreshold() {
	tests := []struct {
		Desc     string
		Value    int32
		Inc      int32
		ExpRes   bool
		ExpLevel int32
	}{
		{
			// 0 -> 19, level 0
			Desc:     "should return [false] and level [0]",
			Value:    19,
			Inc:      19,
			ExpRes:   false,
			ExpLevel: 0,
		},
		{
			// 0 -> 21, level 1
			Desc:     "should return [true] and level [1]",
			Value:    21,
			Inc:      21,
			ExpRes:   true,
			ExpLevel: 1,
		},
		{
			// 0 -> 41, level 5
			Desc:     "should return [true] and level [5]",
			Value:    41,
			Inc:      41,
			ExpRes:   true,
			ExpLevel: 5,
		},
		{
			// 41 -> 51, level 5
			Desc:     "should return [false] and level [5]",
			Value:    51,
			Inc:      10,
			ExpRes:   false,
			ExpLevel: 5,
		},
		{
			// 0 -> 1, level 0
			Desc:     "should return [false] and level [0]",
			Value:    1,
			Inc:      1,
			ExpRes:   false,
			ExpLevel: 0,
		},
		{
			// 19 -> 19, level 0
			Desc:     "should return [false] and level [0]",
			Value:    19,
			Inc:      0,
			ExpRes:   false,
			ExpLevel: 0,
		},
		{
			// 19 -> 20, level 1
			Desc:     "should return [true] and level [1]",
			Value:    20,
			Inc:      1,
			ExpRes:   true,
			ExpLevel: 1,
		},
		{
			// 19 -> 25, level 2
			Desc:     "should return [true] and level [2]",
			Value:    25,
			Inc:      6,
			ExpRes:   true,
			ExpLevel: 2,
		},
		{
			// 40 -> 41, level 5
			Desc:     "should return [true] and level [5]",
			Value:    41,
			Inc:      1,
			ExpRes:   false,
			ExpLevel: 5,
		},
	}

	mockThresholds := []int{20, 25, 30, 35, 40}
	for _, test := range tests {
		s.SetupTest()
		isReach, level := ReachThreshold(test.Value, test.Inc, mockThresholds)
		s.Require().Equal(test.ExpRes, isReach, test.Desc)
		s.Require().Equal(test.ExpLevel, level, test.Desc)
	}
}

func (s *configSuite) TestIsAvailable() {
	locTW, _ := btime.LocationTaipei()
	timeNow = func() time.Time { return time.Date(2021, 03, 01, 0, 5, 0, 0, locTW) }
	tests := []struct {
		Desc string
		Conf StreamerRisingStarConfig
		Exp  bool
	}{
		{
			Desc: "streamer mission not available for the region",
			Conf: StreamerRisingStarConfig{
				RisingStarEnable: false,
			},
			Exp: false,
		},
		{
			Desc: "streamer mission not available for the region, but user is in white list",
			Conf: StreamerRisingStarConfig{
				RisingStarEnable: false,
				WhiteList: []string{
					mockUserID,
				},
			},
			Exp: true,
		},
		{
			Desc: "streamer mission available for the region (config startTime not set)",
			Conf: StreamerRisingStarConfig{
				RisingStarEnable: true,
			},
			Exp: true,
		},
		{
			Desc: "streamer mission available for the region (config startTime set at 2021/03/01)",
			Conf: StreamerRisingStarConfig{
				RisingStarEnable: true,
				StartTimestamp:   time.Date(2021, 03, 01, 0, 0, 0, 0, locTW).Unix(),
			},
			Exp: true,
		},
		{
			Desc: "streamer mission not available yet for the region (config startTime set at 2021/04/01)",
			Conf: StreamerRisingStarConfig{
				RisingStarEnable: true,
				StartTimestamp:   time.Date(2021, 03, 01, 0, 5, 0, 0, locTW).Unix(),
			},
			Exp: false,
		},
	}

	for _, test := range tests {
		getStreamerRisingstarConfig = func(region string) StreamerRisingStarConfig {
			if region == mockTWRegion {
				return test.Conf
			}
			return StreamerRisingStarConfig{}
		}
		isAvailable := IsRisingStarEnable(mockUserID, mockTWRegion)
		s.Require().Equal(test.Exp, isAvailable, test.Desc)
	}
}

func (s *configSuite) TestIsRisingStarMissionOn() {
	tests := []struct {
		Desc     string
		Type     missionModel.Type
		ExpRes   bool
		MockFunc func()
	}{
		{
			Desc:   "rising star level array is empty",
			Type:   missionModel.Type_StreamerLiveXSeconds,
			ExpRes: false,
			MockFunc: func() {
				getStreamerRisingstarConfig = func(region string) StreamerRisingStarConfig {
					conf := StreamerRisingStarConfig{}
					intf, _, _ := conf.Check(mockRisingStarYamlFailCase)
					conf.Apply(intf)
					return conf
				}
			},
		},
		{
			Desc:   "rising star mismatch mission type",
			Type:   missionModel.Type_Dummy,
			ExpRes: false,
			MockFunc: func() {
				s.SetupTest()
			},
		},
		{
			Desc:   "rising star match mission type",
			Type:   missionModel.Type_StreamerLiveXSeconds,
			ExpRes: true,
			MockFunc: func() {
				s.SetupTest()
			},
		},
	}

	for _, test := range tests {
		test.MockFunc()

		isRisingStarMissionOn := IsRisingStarMissionOn(test.Type, mockTWRegion)

		s.Require().Equal(test.ExpRes, isRisingStarMissionOn, test.Desc)
	}
}

func (s *configSuite) TestCommonCfg() {
	locTW, _ := btime.LocationTaipei()
	tests := []struct {
		Desc         string
		Yaml         string
		ExpCommonCfg CommonCfg
		ExpErr       error
		ExpWarn      []string
	}{
		{
			Desc: "normal case",
			Yaml: `
risingStarRewardPinMinutes: 10
dailyStreamLimitSec: 7200 # 2 hours a day
animationList:
  - animationID: streamer_growth_rising_star_rank_up
    url: https://cdn.17app.co/4641d7e7-c2ba-41df-a8f9-c729095eed14.zip
    webpUrl: https://cdn.17app.co/36b7360b-dd07-4cd8-969b-366c42ffae7a.webp
    MD5: a7a6182652148b9bf83a978f9d6975ce
    webpMD5: 2a7e0b1ae1ab0c790fb693b8819bae8b
    androidType: 0
    iOSType: 2
  - animationID: redenvelope_streamer_growth_rising_star
    url: https://cdn.17app.co/3e44080f-27fd-42e7-83ef-c5fd0af7d578.zip
    webpUrl: https://cdn.17app.co/7b3d08fb-d6e9-46fd-a5f5-08ae4645a505.webp
    MD5: 4841aba2427be2b9ad6ce56b0a021139
    webpMD5: acd43abd3e29262245f03ab0abd67716
    androidType: 0
    iOSType: 2
cacheMetricsTimeSwitch:
  enable: true
  startTime: 2022-07-20 00:00:00 (GMT+0800)
`,
			ExpCommonCfg: CommonCfg{
				AnimationList: []models.AnimationLoader{
					{
						URL:         "https://cdn.17app.co/4641d7e7-c2ba-41df-a8f9-c729095eed14.zip",
						WebpURL:     "https://cdn.17app.co/36b7360b-dd07-4cd8-969b-366c42ffae7a.webp",
						AnimationID: "streamer_growth_rising_star_rank_up",
						MD5:         "a7a6182652148b9bf83a978f9d6975ce",
						WebpMD5:     "2a7e0b1ae1ab0c790fb693b8819bae8b",
						AndroidType: models.AnimationTypeDefault,
						IOSType:     models.AnimationTypeWebp,
					},
					{
						URL:         "https://cdn.17app.co/3e44080f-27fd-42e7-83ef-c5fd0af7d578.zip",
						WebpURL:     "https://cdn.17app.co/7b3d08fb-d6e9-46fd-a5f5-08ae4645a505.webp",
						AnimationID: "redenvelope_streamer_growth_rising_star",
						MD5:         "4841aba2427be2b9ad6ce56b0a021139",
						WebpMD5:     "acd43abd3e29262245f03ab0abd67716",
						AndroidType: models.AnimationTypeDefault,
						IOSType:     models.AnimationTypeWebp,
					},
				},
				DailyStreamLimitSec:        7200,
				RisingStarRewardPinMinutes: 10,
				CacheMetricsTimeSwitch: timeswitch.TimeSwitch{
					StartTimestamp: time.Date(2022, 7, 20, 0, 0, 0, 0, locTW).Unix(),
					Enable:         true,
				},
			},
		},
		{
			Desc: "natagive cacheRate is set to 0",
			Yaml: `
risingStarRewardPinMinutes: 10
dailyStreamLimitSec: 7200 # 2 hours a day
animationList:
  - animationID: streamer_growth_rising_star_rank_up
    url: https://cdn.17app.co/4641d7e7-c2ba-41df-a8f9-c729095eed14.zip
    webpUrl: https://cdn.17app.co/36b7360b-dd07-4cd8-969b-366c42ffae7a.webp
    MD5: a7a6182652148b9bf83a978f9d6975ce
    webpMD5: 2a7e0b1ae1ab0c790fb693b8819bae8b
    androidType: 0
    iOSType: 2
  - animationID: redenvelope_streamer_growth_rising_star
    url: https://cdn.17app.co/3e44080f-27fd-42e7-83ef-c5fd0af7d578.zip
    webpUrl: https://cdn.17app.co/7b3d08fb-d6e9-46fd-a5f5-08ae4645a505.webp
    MD5: 4841aba2427be2b9ad6ce56b0a021139
    webpMD5: acd43abd3e29262245f03ab0abd67716
    androidType: 0
    iOSType: 2
`,
			ExpCommonCfg: CommonCfg{
				AnimationList: []models.AnimationLoader{
					{
						URL:         "https://cdn.17app.co/4641d7e7-c2ba-41df-a8f9-c729095eed14.zip",
						WebpURL:     "https://cdn.17app.co/36b7360b-dd07-4cd8-969b-366c42ffae7a.webp",
						AnimationID: "streamer_growth_rising_star_rank_up",
						MD5:         "a7a6182652148b9bf83a978f9d6975ce",
						WebpMD5:     "2a7e0b1ae1ab0c790fb693b8819bae8b",
						AndroidType: models.AnimationTypeDefault,
						IOSType:     models.AnimationTypeWebp,
					},
					{
						URL:         "https://cdn.17app.co/3e44080f-27fd-42e7-83ef-c5fd0af7d578.zip",
						WebpURL:     "https://cdn.17app.co/7b3d08fb-d6e9-46fd-a5f5-08ae4645a505.webp",
						AnimationID: "redenvelope_streamer_growth_rising_star",
						MD5:         "4841aba2427be2b9ad6ce56b0a021139",
						WebpMD5:     "acd43abd3e29262245f03ab0abd67716",
						AndroidType: models.AnimationTypeDefault,
						IOSType:     models.AnimationTypeWebp,
					},
				},
				DailyStreamLimitSec:        7200,
				RisingStarRewardPinMinutes: 10,
			},
		},
		{
			Desc: "muiltiple animations, same animation should not show twice, return error",
			Yaml: `
risingStarRewardPinMinutes: 10
dailyStreamLimitSec: 7200 # 2 hours a day
animationList:
  - animationID: streamer_growth_rising_star_rank_up
    url: https://cdn.17app.co/4641d7e7-c2ba-41df-a8f9-c729095eed14.zip
    webpUrl: https://cdn.17app.co/36b7360b-dd07-4cd8-969b-366c42ffae7a.webp
    MD5: a7a6182652148b9bf83a978f9d6975ce
    webpMD5: 2a7e0b1ae1ab0c790fb693b8819bae8b
    androidType: 0
    iOSType: 2
  - animationID: streamer_growth_rising_star_rank_up
    url: https://cdn.17app.co/4641d7e7-c2ba-41df-a8f9-c729095eed14.zip
    webpUrl: https://cdn.17app.co/36b7360b-dd07-4cd8-969b-366c42ffae7a.webp
    MD5: a7a6182652148b9bf83a978f9d6975ce
    webpMD5: 2a7e0b1ae1ab0c790fb693b8819bae8b
    androidType: 0
    iOSType: 2
  - animationID: redenvelope_streamer_growth_rising_star
    url: https://cdn.17app.co/3e44080f-27fd-42e7-83ef-c5fd0af7d578.zip
    webpUrl: https://cdn.17app.co/7b3d08fb-d6e9-46fd-a5f5-08ae4645a505.webp
    MD5: 4841aba2427be2b9ad6ce56b0a021139
    webpMD5: acd43abd3e29262245f03ab0abd67716
    androidType: 0
    iOSType: 2
`,
			ExpErr: fmt.Errorf("duplicated animationID: streamer_growth_rising_star_rank_up"),
		},
		{
			Desc: "empty animationID, return error",
			Yaml: `
risingStarRewardPinMinutes: 10
dailyStreamLimitSec: 7200 # 2 hours a day
animationList:
  - animationID:
    url: https://cdn.17app.co/3e44080f-27fd-42e7-83ef-c5fd0af7d578.zip
    webpUrl: https://cdn.17app.co/7b3d08fb-d6e9-46fd-a5f5-08ae4645a505.webp
    MD5: 4841aba2427be2b9ad6ce56b0a021139
    webpMD5: acd43abd3e29262245f03ab0abd67716
    androidType: 0
    iOSType: 2
`,
			ExpErr: fmt.Errorf("empty animationID"),
		},
		{
			Desc: "emptyAnimationList",
			Yaml: `
risingStarRewardPinMinutes: 10
dailyStreamLimitSec: 7200 # 2 hours a day
animationList:
`,
			ExpCommonCfg: CommonCfg{
				DailyStreamLimitSec:        7200,
				RisingStarRewardPinMinutes: 10,
			},
		},
		{
			Desc: "dailyStreamLimitSec not set, return warning",
			Yaml: `
`,
			ExpCommonCfg: CommonCfg{},
			ExpWarn:      []string{"dailyStreamLimitSec: 0 should be positive"},
		},
	}

	for _, test := range tests {
		data, _, err := commonCfg.Check([]byte(test.Yaml))
		s.Require().Equal(test.ExpErr, err, test.Desc)
		if err == nil {
			commonCfg.Apply(data)
			s.Require().Equal(test.ExpCommonCfg, commonCfg)
		}
	}
}

func (s *configSuite) TestGetAnimationList() {
	tests := []struct {
		Desc             string
		CommonCfg        CommonCfg
		ExpAnimationList []models.AnimationLoader
	}{
		{
			Desc: "normal case",
			CommonCfg: CommonCfg{
				AnimationList: []models.AnimationLoader{
					{
						URL:         "https://cdn.17app.co/4641d7e7-c2ba-41df-a8f9-c729095eed14.zip",
						WebpURL:     "https://cdn.17app.co/36b7360b-dd07-4cd8-969b-366c42ffae7a.webp",
						AnimationID: "streamer_growth_rising_star_rank_up",
						MD5:         "a7a6182652148b9bf83a978f9d6975ce",
						WebpMD5:     "acd43abd3e29262245f03ab0abd67716",
						AndroidType: models.AnimationTypeDefault,
						IOSType:     models.AnimationTypeWebp,
					},
				},
			},
			ExpAnimationList: []models.AnimationLoader{
				{
					URL:         "https://cdn.17app.co/4641d7e7-c2ba-41df-a8f9-c729095eed14.zip",
					WebpURL:     "https://cdn.17app.co/36b7360b-dd07-4cd8-969b-366c42ffae7a.webp",
					AnimationID: "streamer_growth_rising_star_rank_up",
					MD5:         "a7a6182652148b9bf83a978f9d6975ce",
					WebpMD5:     "acd43abd3e29262245f03ab0abd67716",
					AndroidType: models.AnimationTypeDefault,
					IOSType:     models.AnimationTypeWebp,
				},
			},
		},
		{
			Desc:             "Animation not set, should not export empty animation",
			CommonCfg:        CommonCfg{},
			ExpAnimationList: []models.AnimationLoader{},
		},
	}

	for _, test := range tests {
		commonCfg = test.CommonCfg
		animationList := GetAnimationList()
		s.Require().Equal(test.ExpAnimationList, animationList, test.Desc)
	}
}

func (s *configSuite) TestGetRisingStarRewardPinMinutes() {
	tests := []struct {
		Desc      string
		CommonCfg CommonCfg
		ExpValue  int64
	}{
		{
			Desc: "normal case",
			CommonCfg: CommonCfg{
				RisingStarRewardPinMinutes: 10,
			},
			ExpValue: 10,
		},
		{
			Desc:      "abnormal case - not exist key",
			CommonCfg: CommonCfg{},
			ExpValue:  1,
		},
	}

	for _, test := range tests {
		commonCfg = test.CommonCfg
		minutes := GetRisingStarRewardPinMinutes()
		s.Require().Equal(test.ExpValue, minutes, test.Desc)
	}
}

func (s *configSuite) TestIsBabyCoinAlignLeaderboard() {
	tests := []struct {
		Desc      string
		CommonCfg CommonCfg
		ExpValue  bool
	}{
		{
			Desc:      "config not set (false)",
			CommonCfg: CommonCfg{},
			ExpValue:  false,
		},
		{
			Desc: "config set to true",
			CommonCfg: CommonCfg{
				BabyCoinAlignLeaderboard: true,
			},
			ExpValue: true,
		},
	}

	for _, test := range tests {
		commonCfg = test.CommonCfg
		babyCoinAlignLeaderboard := IsBabyCoinAlignLeaderboard()
		s.Require().Equal(test.ExpValue, babyCoinAlignLeaderboard, test.Desc)
	}
}

func (s *configSuite) TestGetRewardByLevel() {
	tests := []struct {
		Desc       string
		Level      int32
		RewardName string
		ExpRes     risingstarModel.RisingReward
	}{
		{
			Desc:       "normal case - score",
			Level:      1,
			RewardName: "score",
			ExpRes: risingstarModel.RisingReward{
				RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_score"},
				RewardType:        5,
				RewardTypeIconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png",
				Count:             10000,
				Level:             1,
			},
		},
		{
			Desc:       "normal case - score",
			Level:      2,
			RewardName: "score",
			ExpRes: risingstarModel.RisingReward{
				RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_score"},
				RewardType:        5,
				RewardTypeIconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png",
				Count:             20000,
				Level:             2,
			},
		},
		{
			Desc:       "normal case - score",
			Level:      3,
			RewardName: "score",
			ExpRes: risingstarModel.RisingReward{
				RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_score"},
				RewardType:        5,
				RewardTypeIconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png",
				Count:             30000,
				Level:             3,
			},
		},
		{
			Desc:       "normal case - score",
			Level:      4,
			RewardName: "score",
			ExpRes: risingstarModel.RisingReward{
				RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_score"},
				RewardType:        5,
				RewardTypeIconURL: "https://cdn.17app.co/83c8256f-0e37-422f-b416-3ccb1071343f.png",
				Count:             40000,
				Level:             4,
			},
		},
		{
			Desc:       "normal case - coin",
			Level:      5,
			RewardName: "coin",
			ExpRes: risingstarModel.RisingReward{
				RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_babycoins"},
				RewardType:        2,
				RewardTypeIconURL: "https://cdn.17app.co/afe8165f-8e88-4dfc-af8d-7ab7cbb2aba7.png",
				Count:             9000,
				Level:             5,
			},
		},
		{
			Desc:       "normal case - redEnvelope",
			Level:      5,
			RewardName: "redEnvelope",
			ExpRes: risingstarModel.RisingReward{
				RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_redenvelope"},
				RewardType:        11,
				RewardTypeIconURL: "https://cdn.17app.co/923b96d6-92e0-4e60-a7a9-fe8230c10234.png",
				Count:             1,
				RewardID:          "RedEnvelopeID",
				Level:             5,
			},
		},
		{
			Desc:       "normal case - directGift",
			Level:      5,
			RewardName: "directGift",
			ExpRes: risingstarModel.RisingReward{
				RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_gift"},
				RewardType:        7,
				RewardTypeIconURL: "https://cdn.17app.co/31328f42-0409-4b2b-a186-24b2b5125c5d.png",
				Count:             1,
				RewardID:          "DirectGiftID",
				Level:             5,
			},
		},
		{
			Desc:       "normal case - marquee",
			Level:      5,
			RewardName: "marquee",
			ExpRes: risingstarModel.RisingReward{
				RewardName:        &i18nModel.Token{Key: "streamer_growth_risingstar_rewards_marquee"},
				RewardType:        12,
				RewardTypeIconURL: "https://cdn.17app.co/0a70d730-d578-44b4-aaff-5a37fe48ba45.png",
				Count:             1,
				RewardDescription: &i18nModel.Token{Key: "streamer_growth_risingstar_marquee_wording"},
				Level:             5,
			},
		},
	}

	for _, test := range tests {
		randInt31n = func(int32) int32 {
			return 150
		}
		risingReward := GetRewardByLevel(mockTWRegion, test.Level, test.RewardName)
		s.Require().Equal(test.ExpRes, risingReward, test.Desc)
	}
}

func (s *configSuite) TestIsMetricLevelUp() {
	type metricStatus struct {
		LevelUp bool
		Level   int32
	}
	tests := []struct {
		Desc   string
		Value  int32
		Params *missionModel.MissionParams
		ExpRes metricStatus
	}{
		{
			Desc:  "should return level [0] with level up [false]",
			Value: 10,
			Params: &missionModel.MissionParams{
				Value:  10,
				Region: mockTWRegion,
				Type:   missionModel.Type_StreamerLiveXSeconds,
			},
			ExpRes: metricStatus{
				LevelUp: false,
				Level:   0,
			},
		},
		{
			Desc:  "should return level [1] with level up [true]",
			Value: 20,
			Params: &missionModel.MissionParams{
				Value:  20,
				Region: mockTWRegion,
				Type:   missionModel.Type_StreamerLiveXSeconds,
			},
			ExpRes: metricStatus{
				LevelUp: true,
				Level:   1,
			},
		},
	}

	for _, test := range tests {
		s.SetupTest()
		metricLevelUp, metricLevel := IsMetricLevelUp(test.Value, test.Params.Region, test.Params.Type, test.Params.Value)
		s.Require().Equal(test.ExpRes.Level, metricLevel, test.Desc)
		s.Require().Equal(test.ExpRes.LevelUp, metricLevelUp, test.Desc)
	}
}

func (s *configSuite) TestIsRisingLevelUp() {
	type risingStatus struct {
		LevelUp bool
		Level   int32
	}
	tests := []struct {
		Desc   string
		Metric risingstarModel.Metric
		Params *missionModel.MissionParams
		ExpRes risingStatus
	}{
		{
			Desc: "should return level [1] with level up [false]",
			Metric: risingstarModel.Metric{
				StreamedTime: 10,
			},
			Params: &missionModel.MissionParams{
				Value:  10,
				Region: mockTWRegion,
				Type:   missionModel.Type_StreamerLiveXSeconds,
			},
			ExpRes: risingStatus{
				LevelUp: false,
				Level:   0,
			},
		},
		{
			Desc: "should return level [1] with level up [true]",
			Metric: risingstarModel.Metric{
				StreamedTime:         20,
				ScheduleCount:        2,
				GiftSenderCount:      50,
				AccumulatedFollowers: 1000,
				ReceivedPoints:       30000,
				PostCount:            2,
			},
			Params: &missionModel.MissionParams{
				Value:  20,
				Region: mockTWRegion,
				Type:   missionModel.Type_StreamerLiveXSeconds,
			},
			ExpRes: risingStatus{
				LevelUp: true,
				Level:   1,
			},
		},
		{
			Desc: "should return level [1] with level up [false] when mission type is incorrect",
			Metric: risingstarModel.Metric{
				StreamedTime: 10,
			},
			Params: &missionModel.MissionParams{
				Value:  10,
				Region: mockTWRegion,
				Type:   missionModel.Type_CreateAvatar,
			},
			ExpRes: risingStatus{
				LevelUp: false,
				Level:   0,
			},
		},
	}

	for _, test := range tests {
		s.SetupTest()
		risingLevelUp, risingLevel := IsRisingLevelUp(&test.Metric, test.Params)
		s.Require().Equal(test.ExpRes.Level, risingLevel, test.Desc)
		s.Require().Equal(test.ExpRes.LevelUp, risingLevelUp, test.Desc)
	}
}

func (s *configSuite) TestGetLevelMetric() {
	tests := []struct {
		Desc   string
		Level  int32
		ExpRes *risingstarModel.Metric
	}{
		{
			Desc:  "should return level 1 config",
			Level: 1,
			ExpRes: &risingstarModel.Metric{
				StreamedTime:         20,
				ScheduleCount:        2,
				GiftSenderCount:      50,
				AccumulatedFollowers: 1000,
				ReceivedPoints:       30000,
				PostCount:            2,
			},
		},
		{
			Desc:   "should return an empty config",
			Level:  999,
			ExpRes: &risingstarModel.Metric{},
		},
	}

	s.SetupTest()
	for _, t := range tests {
		s.SetupTest()
		metric := GetLevelMetric(t.Level, mockTWRegion)
		s.Require().Equal(t.ExpRes, metric, t.Desc)
	}
}

func TestConfigSuite(t *testing.T) {
	suite.Run(t, new(configSuite))
}

func (s *configSuite) TestIsCacheMetricsEnabled() {
	tests := []struct {
		Desc      string
		CommonCfg CommonCfg
		ExpValue  bool
	}{
		{
			Desc:      "config not set (false)",
			CommonCfg: CommonCfg{},
			ExpValue:  false,
		},
		{
			Desc: "config set to true, but startTime is 1 hour later",
			CommonCfg: CommonCfg{
				CacheMetricsTimeSwitch: timeswitch.TimeSwitch{
					Enable:         true,
					StartTimestamp: time.Now().Add(time.Hour).Unix(),
				},
			},
			ExpValue: false,
		},
		{
			Desc: "config set to true and has started",
			CommonCfg: CommonCfg{
				CacheMetricsTimeSwitch: timeswitch.TimeSwitch{
					Enable:         true,
					StartTimestamp: time.Now().Add(-time.Hour).Unix(),
				},
			},
			ExpValue: true,
		},
	}

	for _, test := range tests {
		commonCfg = test.CommonCfg
		enabled := IsCacheMetricsEnabled()
		s.Require().Equal(test.ExpValue, enabled, test.Desc)
	}
}
