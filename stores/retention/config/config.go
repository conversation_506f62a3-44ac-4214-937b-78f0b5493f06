package config

import (
	"fmt"

	"github.com/17media/logrus"
	yaml "gopkg.in/yaml.v2"

	"github.com/17media/api/base/ctx"
	rmodel "github.com/17media/api/models/region"
	regionSrv "github.com/17media/api/service/region"
)

const (
	configID     = "config:comebackretention"
	configBase   = "17app/comebackretention/"
	configSuffix = "comebackretention.yaml"

	RewardTypeIAP   = "iap"
	RewardTypeStamp = "stamp"
)

var (
	Regions = []string{"TW", "JP", "OTHERS"}
	getCfg  = regionSrv.GetConfig
)

// Reward is infomation for single reward
type Reward struct {
	TitleI18nKey     string `yaml:"titleI18nKey"`
	DescI18nKey      string `yaml:"descI18nKey"`
	HighlightI18nKey string `yaml:"highlightI18nKey"`
	ImgURL           string `yaml:"imgURL"`
	RewardType       string `yaml:"rewardType"`
	RewardIdentity   string `yaml:"rewardIdentity"`
	Days             int32  `yaml:"days"`
}

// ComebackRetentionConfig contains config data of comeback retention
type ComebackRetentionConfig struct {
	Enabled         bool   `yaml:"enabled"`
	SendNotifHour   int32  `yaml:"sendNotifHour"`
	NotifTemplateID string `yaml:"notifTemplateID"`
	// ChurnDays defines which day to trigger retention notif after churn
	ChurnDays int32 `yaml:"churnDays"`
	// MinBaggageSent defines min Baggage sent condition to trigger retention rewards
	MinBaggageSent int32 `yaml:"minBaggageSent"`
	// MaxRewardLimit defines max times users will get retention reward
	MaxRewardLimit int32    `yaml:"maxRewardLimit"`
	Rewards        []Reward `yaml:"rewards"`
}

// Check checks config data
func (c *ComebackRetentionConfig) Check(data []byte) (interface{}, []string, error) {
	conf := ComebackRetentionConfig{}
	if err := yaml.Unmarshal(data, &conf); nil != err {
		return nil, nil, err
	}

	var warnings []string
	if conf.ChurnDays < 0 {
		return nil, nil, fmt.Errorf("ChurnDays must be equal to or greater than 0")
	}
	if conf.SendNotifHour < 0 || conf.SendNotifHour > 23 {
		return nil, nil, fmt.Errorf("SendNotifHour must between 0~23")
	}
	if conf.Enabled && len(conf.Rewards) == 0 {
		return nil, nil, fmt.Errorf("Rewards must not be empty when Enabled=true")
	}
	for _, reward := range conf.Rewards {
		if reward.RewardType != RewardTypeIAP && reward.RewardType != RewardTypeStamp {
			return nil, nil, fmt.Errorf("RewardType must be stamp or iap")
		}
	}
	return conf, warnings, nil
}

// Apply applies data to this config
func (c *ComebackRetentionConfig) Apply(v interface{}) {
	*c = v.(ComebackRetentionConfig)
}

// Get get the config instance
func (c *ComebackRetentionConfig) Get() interface{} {
	return *c
}

// GetCfg returns dailyquest config
func GetCfg(context ctx.CTX, region string) ComebackRetentionConfig {
	regionInfo := rmodel.RegionInfo{
		Region: region,
	}

	intf, err := getCfg(configID, &regionInfo)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "region": region}).Error("region.GetConfig failed")
		return ComebackRetentionConfig{}
	}
	c := intf.(ComebackRetentionConfig)
	return c
}

func init() {
	regionSrv.Register(configID, configBase, configSuffix, &ComebackRetentionConfig{}, Regions...)
}
