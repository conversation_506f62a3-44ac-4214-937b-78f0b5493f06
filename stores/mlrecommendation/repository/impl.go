package repository

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/17media/logrus"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/metrics"
	btime "github.com/17media/api/base/time"
	"github.com/17media/api/models/keys"
	mlModel "github.com/17media/api/models/mlrecommendation"
	"github.com/17media/api/service/cache"
	"github.com/17media/api/service/queryv2"
)

var (
	met = metrics.New("mlrepository")

	timeNow                     = btime.TimeNow
	sectionsStreamersExpireTime = 7 * 24 * time.Hour // 7 days
)

const (
	pfxSectionsStreamers = "mlSectionsStreamers"
)

type sectionsStreamersImpl struct {
	query queryv2.Mongo
	cache cache.Cache
}

func NewSectionsStreamersRepository(
	query queryv2.Mongo,
	cacheSvc cache.Service,
) SectionsStreamersRepository {
	im := &sectionsStreamersImpl{
		query: query,
	}
	im.cache = cacheSvc.Create(
		[]cache.Setting{
			{
				// key: <streamerID>:<region>
				Pfx:    pfxSectionsStreamers,
				TTL:    2 * time.Minute,
				Getter: im.getter,
				DBType: cache.RedisCache,
			},
		},
	)
	return im
}

func encodeSectionsStreamsCacheKey(userID, region string) string {
	return fmt.Sprintf("%s:%s", userID, region)
}

func decodeSectionsStreamsCacheKey(key string) (userID, region string, err error) {
	res := strings.Split(key, ":")
	if len(res) != 2 {
		return "", "", errInvalidCacheKey
	}
	return res[0], res[1], nil
}

func (im *sectionsStreamersImpl) Get(context ctx.CTX, userID, region string) (mlModel.RecommendSectionsStreamers, error) {
	defer met.BumpTime("time", "repo", "sectionsStreamers", "func", "Get").End()

	cacheKey := encodeSectionsStreamsCacheKey(userID, region)
	var recommendations mlModel.RecommendSectionsStreamers
	if err := im.cache.Get(context, pfxSectionsStreamers, cacheKey, &recommendations); errors.Is(err, ErrSectionsStreamersNotFound) {
		return mlModel.RecommendSectionsStreamers{}, ErrSectionsStreamersNotFound
	} else if err != nil {
		context.WithFields(logrus.Fields{"err": err, "userID": userID, "region": region}).Error("cache.Get failed")
		return mlModel.RecommendSectionsStreamers{}, err
	}

	return recommendations, nil
}

func (im *sectionsStreamersImpl) getter(context ctx.CTX, key string) (interface{}, error) {
	defer met.BumpTime("time", "repo", "sectionsStreamers", "func", "getter").End()

	userID, region, err := decodeSectionsStreamsCacheKey(key)
	if err != nil {
		context.WithFields(logrus.Fields{"err": err, "key": key}).Error("decodeSectionsStreamsCacheKey failed")
		return nil, err
	}

	var recommendations mlModel.RecommendSectionsStreamers
	selector := bson.M{
		"userID": userID,
		"region": region,
	}
	if err := im.query.FindOne(context, keys.TabMLRecommendSectionsStreamers, selector, &recommendations); errors.Is(err, queryv2.ErrNotFound) {
		return nil, ErrSectionsStreamersNotFound
	} else if err != nil {
		context.WithFields(logrus.Fields{"err": err, "userID": userID, "region": region}).Error("query.FindOne failed")
		return nil, err
	}

	return recommendations, nil
}

func (im *sectionsStreamersImpl) Upsert(context ctx.CTX, userID, region string, sections []mlModel.SectionStreamers) error {
	defer met.BumpTime("time", "repo", "sectionsStreamers", "func", "Upsert").End()

	selector := bson.M{
		"userID": userID,
		"region": region,
	}
	now := timeNow()
	recommendations := mlModel.RecommendSectionsStreamers{
		UserID:    userID,
		Region:    region,
		UpdatedAt: now,
		ExpireAt:  now.Add(sectionsStreamersExpireTime),
		Sections:  sections,
	}
	if err := im.query.Upsert(context, keys.TabMLRecommendSectionsStreamers, selector, recommendations); err != nil {
		context.WithFields(logrus.Fields{"err": err, "userID": userID, "region": region}).Error("query.Upsert failed")
		return err
	}

	cacheKey := encodeSectionsStreamsCacheKey(userID, region)
	if err := im.cache.Del(context, pfxSectionsStreamers, cacheKey); err != nil {
		context.WithFields(logrus.Fields{"err": err, "userID": userID, "region": region}).Warn("cache.Del failed")
	}

	return nil
}
