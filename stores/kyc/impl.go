package kyc

import (
	"bytes"
	"encoding/json"
	"fmt"

	"github.com/gocarina/gocsv"

	"github.com/17media/logrus"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/metrics"
	btime "github.com/17media/api/base/time"
	"github.com/17media/api/models/intra"
	kycModel "github.com/17media/api/models/kyc"
	ledgerModel "github.com/17media/api/models/ledger"
	progressModel "github.com/17media/api/models/taskprogress"
	"github.com/17media/api/service/kyc"
	"github.com/17media/api/service/region"
	"github.com/17media/api/service/storage"
	"github.com/17media/api/service/taskprogress"
	"github.com/17media/api/stores/intra/file"
	"github.com/17media/api/stores/user"
)

var (
	met     = metrics.New("kyc")
	timeNow = btime.TimeNow
)

type impl struct {
	// store instances
	us           user.Store
	taskProgress taskprogress.Service
	intraFile    file.Store
	// service instances
	kycService    kyc.Service
	regionService region.Service
}

func New(
	us user.Store,
	taskProgress taskprogress.Service,
	intraFile file.Store,
	kycService kyc.Service,
	regionService region.Service,
) Store {
	im := &impl{
		us:            us,
		taskProgress:  taskProgress,
		intraFile:     intraFile,
		kycService:    kycService,
		regionService: regionService,
	}

	return im
}

func (im *impl) CreateKycReportTask(context ctx.CTX, execRegion string, input kycModel.ListCond) (*progressModel.TaskProgress, error) {
	defer met.BumpTime("time", "func", "CreateKycReportTask").End()

	option := taskprogress.TaskOption{}
	task, err := im.taskProgress.Run(context, 100, option, im.genKycReport, execRegion, input)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err":            err,
			"executorRegion": execRegion,
			"input":          input,
		}).Error("taskProgress.Run failed")
		return nil, err
	}

	context.Info("CreateKycReportTask goroutine initialised")
	return task.GetProgress(), nil
}

type kycCsvRow struct {
	OpenID                    string                  `csv:"OpenID"`
	UserID                    string                  `csv:"UserID"`
	FirstName                 string                  `csv:"First name"`
	LastName                  string                  `csv:"Last name"`
	PayeeName                 string                  `csv:"Account Payee name"`
	Region                    string                  `csv:"Region"`
	Mail                      string                  `csv:"Mail"`
	ResidenceAddress          string                  `csv:"Residence address"`
	CreateTime                ledgerModel.CSVDateTime `csv:"Create time"`
	UpdateTime                ledgerModel.CSVDateTime `csv:"Update time"`
	EntityType                string                  `csv:"Entity type"`
	EntityRegistrationNumber  string                  `csv:"Entity registration number"`
	EntityStatusStartingMonth string                  `csv:"Entity status starting month"`
}

func (im *impl) genKycReport(context ctx.CTX, task *taskprogress.Task, params ...interface{}) (map[string]string, error) {
	if len(params) != 2 {
		return nil, fmt.Errorf("invalid length of the parameters in genKycReport")
	}
	execRegion := params[0].(string)
	conditions := params[1].(kycModel.ListCond)
	defer met.BumpTime("time", "func", "genKycReport").End()

	const (
		// applying the limited iteration on guarding the infinity loop
		maxLoopNumber = 100000
		// CSV file limitation but, currently, the number of our KYC streamers is less than this figure
		csvRowSize = 60000
	)
	var (
		cursor string       = ""
		files  []*file.File = make([]*file.File, 0)
	)

	// get time loc based on the command executor region
	loc, err := im.regionService.GetTimeLocation(context, execRegion)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err":        err,
			"execRegion": execRegion,
		}).Error("region.GetTimeLocation error")
		return nil, err
	}

	// index start from 1 because it will be used as the filename
	for i := 1; i < maxLoopNumber; i++ {
		// get kyc list
		resp, nextCursor, err := im.kycService.ListKYC(context, cursor, csvRowSize, conditions)
		if err != nil {
			context.WithFields(logrus.Fields{
				"err":        err,
				"conditions": conditions,
			}).Error("kycService.ListKYC error")
			return nil, err
		}
		respSize := len(resp)
		// there is no need to proceed as there is no data here
		if respSize == 0 {
			break
		}

		// get openIDs through user store(fetch data from mongoDB)
		userIDs := make([]string, respSize)
		for i, raw := range resp {
			userIDs[i] = raw.Detail.UserID
		}
		users, err := im.us.GetUsers(context, userIDs...)
		// FIXME: since, in the pre-prod env, our SQL and MongoDB are not synced,
		// we have to do the work around here to dodge the user not found issue
		if err != nil && err != user.ErrSomeUsersNotFound {
			context.WithField("err", err).Error("user.GetPlainUsers failed to get users")
			return nil, err
		}

		// building up the csv file based on the specified format, kycCsvRow
		csvFile := make([]kycCsvRow, 0, respSize)
		for i, raw := range resp {
			// theoretically, the empty payeename does not exist in our database
			// throwing out this warning to reflect the issue in our database
			if len(raw.Account.PayeeName) == 0 {
				context.WithFields(logrus.Fields{
					"account":        raw.Account,
					"executorRegion": execRegion,
				}).Warn("empty payeename warning")
				continue
			}
			// FIXME: since, in the pre-prod env, our SQL and MongoDB are not synced,
			// we will have the data proceed without user's openID
			if len(users[i].UserID) == 0 {
				context.WithField("userID", raw.Detail.UserID).Error("Can't find the user in kv")
			}

			csvRow := kycCsvRow{
				OpenID:           users[i].OpenID,
				UserID:           raw.Detail.UserID,
				FirstName:        raw.Detail.FirstName,
				LastName:         raw.Detail.LastName,
				PayeeName:        raw.Account.PayeeName,
				Region:           raw.Detail.Nationality,
				Mail:             raw.Detail.Email,
				ResidenceAddress: raw.Detail.Address,
				CreateTime:       ledgerModel.CSVDateTime{Loc: loc, T: raw.Detail.CreateTimeMs},
				UpdateTime:       ledgerModel.CSVDateTime{Loc: loc, T: raw.Detail.UpdateTimeMs},
			}

			if raw.EntityInfo != nil && raw.EntityInfo.Type != kycModel.EntityTypeUnknown {
				csvRow.EntityType = raw.EntityInfo.Type.String()
				if len(raw.EntityInfo.RegistrationNumber) == 13 {
					csvRow.EntityRegistrationNumber = fmt.Sprintf("T%s", raw.EntityInfo.RegistrationNumber)
				} else {
					csvRow.EntityRegistrationNumber = raw.EntityInfo.RegistrationNumber
				}
				csvRow.EntityStatusStartingMonth = raw.EntityInfo.StartingMonth
			}
			csvFile = append(csvFile, csvRow)
		}
		// convert csvStruct into csv file
		contentBuffer := bytes.NewBuffer([]byte("\xEF\xBB\xBF"))
		if err = gocsv.Marshal(&csvFile, contentBuffer); err != nil {
			context.WithField("err", err).Error("gocsv.Marshal data error")
			return nil, err
		}

		// upload file
		fileInstance := &file.File{
			Category:    intra.FileCategory_INTERNAL_ORDER_REPORT,
			Name:        fmt.Sprintf("KYC_report_%d_%s.csv", i, timeNow().In(loc).Format(btime.ParseFormatYYYYMMDD)),
			ContentType: storage.CSV,
			Reader:      bytes.NewReader(contentBuffer.Bytes()),
		}
		files = append(files, fileInstance)

		// break the infinity loop as we fetch all data
		if nextCursor == "" {
			break
		}
		cursor = nextCursor
		i++
	}

	// update the progress to 50%
	task.UpdateSteps(50)
	if err := im.taskProgress.Set(context, task); err != nil {
		context.WithField("err", err).Error("taskProgress.Set error")
		return nil, err
	}

	// if no file exists, we will return the empty key to the client-side
	if len(files) == 0 {
		context.WithFields(logrus.Fields{
			"execRegion": execRegion,
			"conditions": conditions,
		}).Info("there is no files")
		return map[string]string{"fileKeys": ""}, nil
	}

	// upload files
	fileKeys, err := im.intraFile.Upload(context, files)
	if err != nil {
		context.WithField("err", err).Error("intraFile.Upload error")
		return nil, err
	}

	// encode the fileKey array into jsonString
	jsonBytes, err := json.Marshal(fileKeys)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err": err,
			// TODO: here should be changed in the future if our KYC streamers grow significantly
			// currently, it is fine to log it because we will just receive few keys
			"fileKeys": fileKeys,
		}).Error("json.Marshal fileKeys error")
		return nil, err
	}

	result := map[string]string{
		"fileKeys": string(jsonBytes),
	}
	return result, nil
}
