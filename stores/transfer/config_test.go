package transfer

import (
	"testing"

	"github.com/stretchr/testify/suite"

	moneyModel "github.com/17media/api/models/money"
)

type configSuite struct {
	suite.Suite
}

var (
	mockTransferConfigRAW = `- from: 3
  fromFeeCoefficient: 0.00
  fromFeeConstant: 0
  to: 1
  toFeeCoefficient: 0
  toFeeConstant: 0
  exchangeRate: 100
  allowToSelf: true
  allowToOthers: false`
	mockTransferConfig = transferConfig{
		"REVENUEPOINT": &Setting{
			From:         moneyModel.Currency_REVENUE,
			To:           moneyModel.Currency_POINT,
			ExchangeRate: float64(100),
			AllowToSelf:  true,
		},
	}
)

func TestConfigSuite(t *testing.T) {
	suite.Run(t, new(configSuite))
}

func (s *configSuite) TestTransferConfig() {
	conf := transferConfig{}
	intf, _, err := conf.Check([]byte(mockTransferConfigRAW))
	s.NoError(err)
	conf.Apply(intf)
	s.Equal(mockTransferConfig, conf)

	m := `- from: 3
  fromFeeCoefficient: 0.00
  fromFeeConstant: 0
  to: 3
  toFeeCoefficient: 0
  toFeeConstant: 0
  exchangeRate: 100
  allowToSelf: true
  allowToOthers: false`
	conf = transferConfig{}
	intf, _, err = conf.Check([]byte(m))
	s.Error(err)

	// exchange cannot be zero
	m = `- from: 3
  fromFeeCoefficient: 0.00
  fromFeeConstant: 0
  to: 1
  toFeeCoefficient: 0
  toFeeConstant: 0
  exchangeRate: 0
  allowToSelf: true
  allowToOthers: false`
	conf = transferConfig{}
	intf, _, err = conf.Check([]byte(m))
	s.Error(err)
}
