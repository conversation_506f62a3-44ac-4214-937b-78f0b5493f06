package live

import (
	"fmt"

	"gopkg.in/yaml.v2"

	"github.com/17media/api/service/config"
)

var (
	// verNoCampaign is the app versions that skips campaign info
	verNoCampaign = campaignConf{}
)

const (
	forceUpdatePicture = "update_message_en_zh.png"

	// config path for app versions to skip campaign info
	noCampaignPath = "17app/campaign/skip.yaml"
)

type campaignConf []string

func (o *campaignConf) Check(data []byte) (interface{}, []string, error) {
	tmp := campaignConf{}
	err := yaml.Unmarshal(data, &tmp)
	if err != nil {
		return nil, nil, err
	}
	return tmp, nil, nil
}

func (o *campaignConf) Apply(v interface{}) {
	// Since it's a init config, let it panic as soon as possible when thgins go wrong.
	*o = v.(campaignConf)
}

func init() {
	if err := config.Register(noCampaignPath, &verNoCampaign); err != nil {
		panic(fmt.Sprintf("unable to register watcher %v, err %v", noCampaignPath, err))
	}
}
