package notif

import (
	"github.com/17media/api/service/queue"
	"github.com/17media/api/service/region"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/stores/follow"
	"github.com/17media/api/stores/live"
	"github.com/17media/api/stores/user"
	"github.com/17media/dig"
	subTopicModel "github.com/17media/gomodel/models/subTopic"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of liveNotif object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In

		Region region.Service `name:"region"`
		User   user.Store     `name:"user"`
		Follow follow.Store   `name:"follow"`
		Live   live.Live      `name:"live"`
	}
	fn := func(p params) Store {
		return New(
			p.Region,
			p.User,
			p.Follow,
			p.Live,
			queue.NewPubsubPublisher(subTopicModel.SubTopics[subTopicModel.KeyMultiOfficialMsgV2].Topic),
		)
	}
	m.ProvideConstructor(fn, `liveNotif`)
}

// GetLiveNotif returns the notification worker object
func GetLiveNotif(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"liveNotif"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
