package betting

import (
	"database/sql"

	"github.com/17media/dig"

	"github.com/17media/api/service/kv"
	"github.com/17media/api/service/localcache"
	"github.com/17media/api/service/pagingv2"
	"github.com/17media/api/service/queryv2"
	"github.com/17media/api/service/redis"
	"github.com/17media/api/service/region"
	"github.com/17media/api/service/relation"
	"github.com/17media/api/service/sequence"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/stores/leaderboard"
	"github.com/17media/api/stores/user"

	// trigger init
	_ "github.com/17media/api/service/localcache/compound"
	_ "github.com/17media/api/service/redis/redispersistent"
	_ "github.com/17media/api/setup/mysql"
	_ "github.com/17media/api/setup/mysql/mysqlgiftwriter"
)

func init() {
	Register(dimanager.DefaultManager)
}

// Register registers the constructor of betting object to the manager
func Register(m *dimanager.Manager) {
	type params struct {
		dig.In
		MysqlGiftWriter *sql.DB                 `name:"mysqlGiftWriter"`
		KV              kv.KV                   `name:"kv"`
		Query           queryv2.Mongo           `name:"queryv2"`
		Leaderboard     leaderboard.Leaderboard `name:"leaderboard"`
		RedisPersistent redis.Service           `name:"redisPersistent"`
		PagingFactory   pagingv2.Factory        `name:"pagingFactory"`
		Sequence        sequence.Service        `name:"sequence"`
		Relation        relation.Service        `name:"relation"`
		Region          region.Service          `name:"region"`
		User            user.Store              `name:"user"`
		LocalCache      localcache.Service      `name:"localcacheCache"`
		LocalPrimitive  localcache.Service      `name:"localcachePrimitive"`
	}

	fn := func(p params) Store {
		return New(
			p.MysqlGiftWriter,
			p.KV,
			p.Query,
			p.Leaderboard,
			p.RedisPersistent,
			p.PagingFactory,
			p.Sequence,
			p.Relation,
			p.Region,
			p.User,
			p.LocalCache,
			p.LocalPrimitive,
		)
	}
	m.ProvideConstructor(fn, `betting`)
}

// GetBetting returns the betting object
func GetBetting(m *dimanager.Manager) Store {
	var output Store
	type params struct {
		dig.In
		Output Store `name:"betting"`
	}
	fn := func(p params) {
		output = p.Output
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
