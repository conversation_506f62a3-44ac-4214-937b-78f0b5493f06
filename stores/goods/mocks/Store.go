// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import apimodels "github.com/17media/api/models"
import ctx "github.com/17media/api/base/ctx"
import goods "github.com/17media/api/stores/goods"
import mock "github.com/stretchr/testify/mock"
import models "github.com/17media/api/models/queue"
import modelsgoods "github.com/17media/api/models/goods"

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// AfterTransfer provides a mock function with given fields: context, data, option
func (_m *Store) AfterTransfer(context ctx.CTX, data []byte, option models.CallbackOption) error {
	ret := _m.Called(context, data, option)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []byte, models.CallbackOption) error); ok {
		r0 = rf(context, data, option)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Buy provides a mock function with given fields: context, user, goodsID, goodstype, amount
func (_m *Store) Buy(context ctx.CTX, user *apimodels.User, goodsID string, goodstype modelsgoods.GoodsType, amount int) error {
	ret := _m.Called(context, user, goodsID, goodstype, amount)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *apimodels.User, string, modelsgoods.GoodsType, int) error); ok {
		r0 = rf(context, user, goodsID, goodstype, amount)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CancelAllOrders provides a mock function with given fields: context, goodsID, region
func (_m *Store) CancelAllOrders(context ctx.CTX, goodsID string, region string) (*modelsgoods.ResDeleteAll, error) {
	ret := _m.Called(context, goodsID, region)

	var r0 *modelsgoods.ResDeleteAll
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) *modelsgoods.ResDeleteAll); ok {
		r0 = rf(context, goodsID, region)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsgoods.ResDeleteAll)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string) error); ok {
		r1 = rf(context, goodsID, region)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CancelOrder provides a mock function with given fields: context, userID, orderID
func (_m *Store) CancelOrder(context ctx.CTX, userID string, orderID string) error {
	ret := _m.Called(context, userID, orderID)

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) error); ok {
		r0 = rf(context, userID, orderID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetList provides a mock function with given fields: context, region, language, tradeStatus, options
func (_m *Store) GetList(context ctx.CTX, region string, language string, tradeStatus modelsgoods.TradeStatus, options ...goods.ListOption) []*modelsgoods.Goods {
	_va := make([]interface{}, len(options))
	for _i := range options {
		_va[_i] = options[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, region, language, tradeStatus)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	var r0 []*modelsgoods.Goods
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, modelsgoods.TradeStatus, ...goods.ListOption) []*modelsgoods.Goods); ok {
		r0 = rf(context, region, language, tradeStatus, options...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsgoods.Goods)
		}
	}

	return r0
}

// GetLogs provides a mock function with given fields: context, tradeIDs, language
func (_m *Store) GetLogs(context ctx.CTX, tradeIDs []string, language string) ([]modelsgoods.GoodsPurchaseLog, error) {
	ret := _m.Called(context, tradeIDs, language)

	var r0 []modelsgoods.GoodsPurchaseLog
	if rf, ok := ret.Get(0).(func(ctx.CTX, []string, string) []modelsgoods.GoodsPurchaseLog); ok {
		r0 = rf(context, tradeIDs, language)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modelsgoods.GoodsPurchaseLog)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, []string, string) error); ok {
		r1 = rf(context, tradeIDs, language)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOrderBook provides a mock function with given fields: context, goodsID, limit
func (_m *Store) GetOrderBook(context ctx.CTX, goodsID string, limit int64) (*modelsgoods.ResGetOrderBook, error) {
	ret := _m.Called(context, goodsID, limit)

	var r0 *modelsgoods.ResGetOrderBook
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, int64) *modelsgoods.ResGetOrderBook); ok {
		r0 = rf(context, goodsID, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsgoods.ResGetOrderBook)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, int64) error); ok {
		r1 = rf(context, goodsID, limit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOrderDetail provides a mock function with given fields: context, orderID
func (_m *Store) GetOrderDetail(context ctx.CTX, orderID string) ([]*modelsgoods.Deal, error) {
	ret := _m.Called(context, orderID)

	var r0 []*modelsgoods.Deal
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) []*modelsgoods.Deal); ok {
		r0 = rf(context, orderID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsgoods.Deal)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, orderID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOrderList provides a mock function with given fields: context, userID, goodsID, count, cursor, orderStatus
func (_m *Store) GetOrderList(context ctx.CTX, userID string, goodsID string, count int64, cursor string, orderStatus modelsgoods.OrderStatus) ([]*modelsgoods.Order, string, error) {
	ret := _m.Called(context, userID, goodsID, count, cursor, orderStatus)

	var r0 []*modelsgoods.Order
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, int64, string, modelsgoods.OrderStatus) []*modelsgoods.Order); ok {
		r0 = rf(context, userID, goodsID, count, cursor, orderStatus)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsgoods.Order)
		}
	}

	var r1 string
	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, int64, string, modelsgoods.OrderStatus) string); ok {
		r1 = rf(context, userID, goodsID, count, cursor, orderStatus)
	} else {
		r1 = ret.Get(1).(string)
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(ctx.CTX, string, string, int64, string, modelsgoods.OrderStatus) error); ok {
		r2 = rf(context, userID, goodsID, count, cursor, orderStatus)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// PlaceOrder provides a mock function with given fields: context, user, goodsID, amount, price, orderType
func (_m *Store) PlaceOrder(context ctx.CTX, user *apimodels.User, goodsID string, amount int64, price int64, orderType modelsgoods.OrderType) (string, error) {
	ret := _m.Called(context, user, goodsID, amount, price, orderType)

	var r0 string
	if rf, ok := ret.Get(0).(func(ctx.CTX, *apimodels.User, string, int64, int64, modelsgoods.OrderType) string); ok {
		r0 = rf(context, user, goodsID, amount, price, orderType)
	} else {
		r0 = ret.Get(0).(string)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *apimodels.User, string, int64, int64, modelsgoods.OrderType) error); ok {
		r1 = rf(context, user, goodsID, amount, price, orderType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}
