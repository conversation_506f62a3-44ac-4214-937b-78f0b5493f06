// Code generated by mockery v2.50.0. DO NOT EDIT.

package mocks

import (
	bbfu "github.com/17media/api/stores/bbfu"
	bbfuremittance "github.com/17media/api/stores/bbfu/remittance"

	ctx "github.com/17media/api/base/ctx"

	mock "github.com/stretchr/testify/mock"

	modelsbbfu "github.com/17media/api/models/bbfu"

	remittance "github.com/17media/api/models/bbfu/remittance"

	time "time"
)

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// AddCompanyPayUser provides a mock function with given fields: context, executorUserID, targetUserID, activeTime
func (_m *Store) AddCompanyPayUser(context ctx.CTX, executorUserID string, targetUserID string, activeTime time.Time) error {
	ret := _m.Called(context, executorUserID, targetUserID, activeTime)

	if len(ret) == 0 {
		panic("no return value specified for AddCompanyPayUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, time.Time) error); ok {
		r0 = rf(context, executorUserID, targetUserID, activeTime)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BillCreatedTask provides a mock function with given fields: context, data, publishTime
func (_m *Store) BillCreatedTask(context ctx.CTX, data []byte, publishTime time.Time) error {
	ret := _m.Called(context, data, publishTime)

	if len(ret) == 0 {
		panic("no return value specified for BillCreatedTask")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []byte, time.Time) error); ok {
		r0 = rf(context, data, publishTime)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ClaimRemittance provides a mock function with given fields: context, userID, remittanceID, withFee
func (_m *Store) ClaimRemittance(context ctx.CTX, userID string, remittanceID string, withFee bool) error {
	ret := _m.Called(context, userID, remittanceID, withFee)

	if len(ret) == 0 {
		panic("no return value specified for ClaimRemittance")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, bool) error); ok {
		r0 = rf(context, userID, remittanceID, withFee)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ClaimRemittancePreview provides a mock function with given fields: context, userID, remittanceID, withFee
func (_m *Store) ClaimRemittancePreview(context ctx.CTX, userID string, remittanceID string, withFee bool) (*modelsbbfu.ClaimResult, error) {
	ret := _m.Called(context, userID, remittanceID, withFee)

	if len(ret) == 0 {
		panic("no return value specified for ClaimRemittancePreview")
	}

	var r0 *modelsbbfu.ClaimResult
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, bool) (*modelsbbfu.ClaimResult, error)); ok {
		return rf(context, userID, remittanceID, withFee)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, bool) *modelsbbfu.ClaimResult); ok {
		r0 = rf(context, userID, remittanceID, withFee)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsbbfu.ClaimResult)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, bool) error); ok {
		r1 = rf(context, userID, remittanceID, withFee)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateRealTimeRemainingReport provides a mock function with given fields: context, types
func (_m *Store) CreateRealTimeRemainingReport(context ctx.CTX, types []modelsbbfu.WalletType) (string, error) {
	ret := _m.Called(context, types)

	if len(ret) == 0 {
		panic("no return value specified for CreateRealTimeRemainingReport")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []modelsbbfu.WalletType) (string, error)); ok {
		return rf(context, types)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, []modelsbbfu.WalletType) string); ok {
		r0 = rf(context, types)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, []modelsbbfu.WalletType) error); ok {
		r1 = rf(context, types)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateRemittances provides a mock function with given fields: context, remittances, opts
func (_m *Store) CreateRemittances(context ctx.CTX, remittances []*remittance.Remittance, opts ...bbfu.RemittanceOptFunc) ([]*remittance.Remittance, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, remittances)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateRemittances")
	}

	var r0 []*remittance.Remittance
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []*remittance.Remittance, ...bbfu.RemittanceOptFunc) ([]*remittance.Remittance, error)); ok {
		return rf(context, remittances, opts...)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, []*remittance.Remittance, ...bbfu.RemittanceOptFunc) []*remittance.Remittance); ok {
		r0 = rf(context, remittances, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*remittance.Remittance)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, []*remittance.Remittance, ...bbfu.RemittanceOptFunc) error); ok {
		r1 = rf(context, remittances, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Deposit provides a mock function with given fields: context, userID, withFee, nanoAmount
func (_m *Store) Deposit(context ctx.CTX, userID string, withFee bool, nanoAmount int64) error {
	ret := _m.Called(context, userID, withFee, nanoAmount)

	if len(ret) == 0 {
		panic("no return value specified for Deposit")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, bool, int64) error); ok {
		r0 = rf(context, userID, withFee, nanoAmount)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetActiveVerifiedNanoUSD provides a mock function with given fields: context, userID
func (_m *Store) GetActiveVerifiedNanoUSD(context ctx.CTX, userID string) (int64, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetActiveVerifiedNanoUSD")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (int64, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) int64); ok {
		r0 = rf(context, userID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBBFuInfo provides a mock function with given fields: context, userID
func (_m *Store) GetBBFuInfo(context ctx.CTX, userID string) (*modelsbbfu.BBFuInfo, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetBBFuInfo")
	}

	var r0 *modelsbbfu.BBFuInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (*modelsbbfu.BBFuInfo, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) *modelsbbfu.BBFuInfo); ok {
		r0 = rf(context, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsbbfu.BBFuInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetFeeType provides a mock function with given fields: context, userID
func (_m *Store) GetFeeType(context ctx.CTX, userID string) (modelsbbfu.FeeType, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetFeeType")
	}

	var r0 modelsbbfu.FeeType
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (modelsbbfu.FeeType, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) modelsbbfu.FeeType); ok {
		r0 = rf(context, userID)
	} else {
		r0 = ret.Get(0).(modelsbbfu.FeeType)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInProgressNanoUSD provides a mock function with given fields: context, userID
func (_m *Store) GetInProgressNanoUSD(context ctx.CTX, userID string) (int64, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetInProgressNanoUSD")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (int64, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) int64); ok {
		r0 = rf(context, userID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLogs provides a mock function with given fields: context, userID, cursor, limit, opts
func (_m *Store) GetLogs(context ctx.CTX, userID string, cursor string, limit int, opts ...bbfu.GetLogsOption) ([]*modelsbbfu.TransactionLog, string, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, userID, cursor, limit)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetLogs")
	}

	var r0 []*modelsbbfu.TransactionLog
	var r1 string
	var r2 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, int, ...bbfu.GetLogsOption) ([]*modelsbbfu.TransactionLog, string, error)); ok {
		return rf(context, userID, cursor, limit, opts...)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, int, ...bbfu.GetLogsOption) []*modelsbbfu.TransactionLog); ok {
		r0 = rf(context, userID, cursor, limit, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsbbfu.TransactionLog)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, int, ...bbfu.GetLogsOption) string); ok {
		r1 = rf(context, userID, cursor, limit, opts...)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(ctx.CTX, string, string, int, ...bbfu.GetLogsOption) error); ok {
		r2 = rf(context, userID, cursor, limit, opts...)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GetPayoutAgentInfo provides a mock function with given fields: context, userID, types
func (_m *Store) GetPayoutAgentInfo(context ctx.CTX, userID string, types []modelsbbfu.WalletType) ([]*modelsbbfu.PayoutAgentInfo, error) {
	ret := _m.Called(context, userID, types)

	if len(ret) == 0 {
		panic("no return value specified for GetPayoutAgentInfo")
	}

	var r0 []*modelsbbfu.PayoutAgentInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, []modelsbbfu.WalletType) ([]*modelsbbfu.PayoutAgentInfo, error)); ok {
		return rf(context, userID, types)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, []modelsbbfu.WalletType) []*modelsbbfu.PayoutAgentInfo); ok {
		r0 = rf(context, userID, types)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsbbfu.PayoutAgentInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, []modelsbbfu.WalletType) error); ok {
		r1 = rf(context, userID, types)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTotalRemainingBalance provides a mock function with given fields: context, types
func (_m *Store) GetTotalRemainingBalance(context ctx.CTX, types []modelsbbfu.WalletType) ([]int64, error) {
	ret := _m.Called(context, types)

	if len(ret) == 0 {
		panic("no return value specified for GetTotalRemainingBalance")
	}

	var r0 []int64
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []modelsbbfu.WalletType) ([]int64, error)); ok {
		return rf(context, types)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, []modelsbbfu.WalletType) []int64); ok {
		r0 = rf(context, types)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, []modelsbbfu.WalletType) error); ok {
		r1 = rf(context, types)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserWallet provides a mock function with given fields: context, userID
func (_m *Store) GetUserWallet(context ctx.CTX, userID string) (bbfu.UserWallet, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserWallet")
	}

	var r0 bbfu.UserWallet
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (bbfu.UserWallet, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) bbfu.UserWallet); ok {
		r0 = rf(context, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(bbfu.UserWallet)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetWalletSetting provides a mock function with given fields: context
func (_m *Store) GetWalletSetting(context ctx.CTX) bbfu.WalletSetting {
	ret := _m.Called(context)

	if len(ret) == 0 {
		panic("no return value specified for GetWalletSetting")
	}

	var r0 bbfu.WalletSetting
	if rf, ok := ret.Get(0).(func(ctx.CTX) bbfu.WalletSetting); ok {
		r0 = rf(context)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(bbfu.WalletSetting)
		}
	}

	return r0
}

// IsAvailableForWallet provides a mock function with given fields: context, userID
func (_m *Store) IsAvailableForWallet(context ctx.CTX, userID string) (bool, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for IsAvailableForWallet")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (bool, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) bool); ok {
		r0 = rf(context, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IsEntryEnable provides a mock function with given fields: context, userID
func (_m *Store) IsEntryEnable(context ctx.CTX, userID string) bool {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for IsEntryEnable")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) bool); ok {
		r0 = rf(context, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsHideTransferToRichBB provides a mock function with given fields: region, hasContract
func (_m *Store) IsHideTransferToRichBB(region string, hasContract bool) bool {
	ret := _m.Called(region, hasContract)

	if len(ret) == 0 {
		panic("no return value specified for IsHideTransferToRichBB")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(string, bool) bool); ok {
		r0 = rf(region, hasContract)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// ListCompanyPayUsers provides a mock function with given fields: context
func (_m *Store) ListCompanyPayUsers(context ctx.CTX) ([]*bbfu.CompanyPayUser, error) {
	ret := _m.Called(context)

	if len(ret) == 0 {
		panic("no return value specified for ListCompanyPayUsers")
	}

	var r0 []*bbfu.CompanyPayUser
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX) ([]*bbfu.CompanyPayUser, error)); ok {
		return rf(context)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX) []*bbfu.CompanyPayUser); ok {
		r0 = rf(context)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bbfu.CompanyPayUser)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX) error); ok {
		r1 = rf(context)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListRemittances provides a mock function with given fields: context, query, page, limit, snapshot
func (_m *Store) ListRemittances(context ctx.CTX, query bbfuremittance.Query, page int, limit int, snapshot int64) ([]*remittance.Remittance, int64, int64, error) {
	ret := _m.Called(context, query, page, limit, snapshot)

	if len(ret) == 0 {
		panic("no return value specified for ListRemittances")
	}

	var r0 []*remittance.Remittance
	var r1 int64
	var r2 int64
	var r3 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, bbfuremittance.Query, int, int, int64) ([]*remittance.Remittance, int64, int64, error)); ok {
		return rf(context, query, page, limit, snapshot)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, bbfuremittance.Query, int, int, int64) []*remittance.Remittance); ok {
		r0 = rf(context, query, page, limit, snapshot)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*remittance.Remittance)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, bbfuremittance.Query, int, int, int64) int64); ok {
		r1 = rf(context, query, page, limit, snapshot)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(ctx.CTX, bbfuremittance.Query, int, int, int64) int64); ok {
		r2 = rf(context, query, page, limit, snapshot)
	} else {
		r2 = ret.Get(2).(int64)
	}

	if rf, ok := ret.Get(3).(func(ctx.CTX, bbfuremittance.Query, int, int, int64) error); ok {
		r3 = rf(context, query, page, limit, snapshot)
	} else {
		r3 = ret.Error(3)
	}

	return r0, r1, r2, r3
}

// ListTransferRecords provides a mock function with given fields: context, query, opts
func (_m *Store) ListTransferRecords(context ctx.CTX, query bbfu.TransferRecordQuery, opts ...bbfu.TransferOptFunc) ([]*modelsbbfu.TransferRow, error) {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, query)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ListTransferRecords")
	}

	var r0 []*modelsbbfu.TransferRow
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, bbfu.TransferRecordQuery, ...bbfu.TransferOptFunc) ([]*modelsbbfu.TransferRow, error)); ok {
		return rf(context, query, opts...)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, bbfu.TransferRecordQuery, ...bbfu.TransferOptFunc) []*modelsbbfu.TransferRow); ok {
		r0 = rf(context, query, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*modelsbbfu.TransferRow)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, bbfu.TransferRecordQuery, ...bbfu.TransferOptFunc) error); ok {
		r1 = rf(context, query, opts...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PreviewWithdrawal provides a mock function with given fields: context, userID, typ, amount, optFuncs
func (_m *Store) PreviewWithdrawal(context ctx.CTX, userID string, typ modelsbbfu.WalletType, amount int64, optFuncs ...bbfu.WithdrawOptionFunc) (*modelsbbfu.PreviewResult, error) {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, userID, typ, amount)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for PreviewWithdrawal")
	}

	var r0 *modelsbbfu.PreviewResult
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, modelsbbfu.WalletType, int64, ...bbfu.WithdrawOptionFunc) (*modelsbbfu.PreviewResult, error)); ok {
		return rf(context, userID, typ, amount, optFuncs...)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, modelsbbfu.WalletType, int64, ...bbfu.WithdrawOptionFunc) *modelsbbfu.PreviewResult); ok {
		r0 = rf(context, userID, typ, amount, optFuncs...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsbbfu.PreviewResult)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, modelsbbfu.WalletType, int64, ...bbfu.WithdrawOptionFunc) error); ok {
		r1 = rf(context, userID, typ, amount, optFuncs...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RemoveCompanyPayUser provides a mock function with given fields: context, executorUserID, targetUserID
func (_m *Store) RemoveCompanyPayUser(context ctx.CTX, executorUserID string, targetUserID string) error {
	ret := _m.Called(context, executorUserID, targetUserID)

	if len(ret) == 0 {
		panic("no return value specified for RemoveCompanyPayUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) error); ok {
		r0 = rf(context, executorUserID, targetUserID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SyncWithdrawStatus provides a mock function with given fields: context
func (_m *Store) SyncWithdrawStatus(context ctx.CTX) (int, int, error) {
	ret := _m.Called(context)

	if len(ret) == 0 {
		panic("no return value specified for SyncWithdrawStatus")
	}

	var r0 int
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(ctx.CTX) (int, int, error)); ok {
		return rf(context)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX) int); ok {
		r0 = rf(context)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX) int); ok {
		r1 = rf(context)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(ctx.CTX) error); ok {
		r2 = rf(context)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// Transfer provides a mock function with given fields: context, userID, fromWallet, toWallet, amount, withFee, previewResult
func (_m *Store) Transfer(context ctx.CTX, userID string, fromWallet modelsbbfu.WalletType, toWallet modelsbbfu.WalletType, amount bbfu.TransferAmount, withFee bool, previewResult *modelsbbfu.TransferResult) (*modelsbbfu.TransferResult, error) {
	ret := _m.Called(context, userID, fromWallet, toWallet, amount, withFee, previewResult)

	if len(ret) == 0 {
		panic("no return value specified for Transfer")
	}

	var r0 *modelsbbfu.TransferResult
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, modelsbbfu.WalletType, modelsbbfu.WalletType, bbfu.TransferAmount, bool, *modelsbbfu.TransferResult) (*modelsbbfu.TransferResult, error)); ok {
		return rf(context, userID, fromWallet, toWallet, amount, withFee, previewResult)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, modelsbbfu.WalletType, modelsbbfu.WalletType, bbfu.TransferAmount, bool, *modelsbbfu.TransferResult) *modelsbbfu.TransferResult); ok {
		r0 = rf(context, userID, fromWallet, toWallet, amount, withFee, previewResult)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsbbfu.TransferResult)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, modelsbbfu.WalletType, modelsbbfu.WalletType, bbfu.TransferAmount, bool, *modelsbbfu.TransferResult) error); ok {
		r1 = rf(context, userID, fromWallet, toWallet, amount, withFee, previewResult)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TransferPreview provides a mock function with given fields: context, userID, fromWallet, toWallet, amount, withFee
func (_m *Store) TransferPreview(context ctx.CTX, userID string, fromWallet modelsbbfu.WalletType, toWallet modelsbbfu.WalletType, amount bbfu.TransferAmount, withFee bool) (*modelsbbfu.TransferResult, error) {
	ret := _m.Called(context, userID, fromWallet, toWallet, amount, withFee)

	if len(ret) == 0 {
		panic("no return value specified for TransferPreview")
	}

	var r0 *modelsbbfu.TransferResult
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, modelsbbfu.WalletType, modelsbbfu.WalletType, bbfu.TransferAmount, bool) (*modelsbbfu.TransferResult, error)); ok {
		return rf(context, userID, fromWallet, toWallet, amount, withFee)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, modelsbbfu.WalletType, modelsbbfu.WalletType, bbfu.TransferAmount, bool) *modelsbbfu.TransferResult); ok {
		r0 = rf(context, userID, fromWallet, toWallet, amount, withFee)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelsbbfu.TransferResult)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, modelsbbfu.WalletType, modelsbbfu.WalletType, bbfu.TransferAmount, bool) error); ok {
		r1 = rf(context, userID, fromWallet, toWallet, amount, withFee)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Withdraw provides a mock function with given fields: context, userID, typ, amount, feeType, payoutAgentType, optFuncs
func (_m *Store) Withdraw(context ctx.CTX, userID string, typ modelsbbfu.WalletType, amount int64, feeType modelsbbfu.FeeType, payoutAgentType modelsbbfu.PayoutAgent, optFuncs ...bbfu.WithdrawOptionFunc) error {
	_va := make([]interface{}, len(optFuncs))
	for _i := range optFuncs {
		_va[_i] = optFuncs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, context, userID, typ, amount, feeType, payoutAgentType)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Withdraw")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, modelsbbfu.WalletType, int64, modelsbbfu.FeeType, modelsbbfu.PayoutAgent, ...bbfu.WithdrawOptionFunc) error); ok {
		r0 = rf(context, userID, typ, amount, feeType, payoutAgentType, optFuncs...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewStore creates a new instance of Store. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *Store {
	mock := &Store{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
