package history

import (
	"errors"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models"
)

var (
	// ErrTimeIncorrect means timestamp in redis is incorrect
	ErrTimeIncorrect = errors.New("time incorrect")
)

// Store defines interface handling histories related to what happened in the past.
// i.e. watching history related to who (viewer) watched the show hosted by whom (anchor)
type Store interface {
	// Watching create the relationship from `viewer` to `anchor` at current timestamp
	Watching(context ctx.CTX, viewerID, anchorID, region string) error
	// GetWatchingHistoryByCursor returns history related to specified viewerID by cursor and count
	GetWatchingHistoryByCursor(context ctx.CTX, viewerID, cursor string, count int) ([]models.WatchHistory, string, error)
	// GetWatchFollowingHistoryByCursor returns history of viewers watching
	// streamers followed by themselves
	GetWatchFollowingHistoryByCursor(context ctx.CTX, viewerID, cursor string, count int) ([]models.WatchFollowingHistory, string, error)
}
