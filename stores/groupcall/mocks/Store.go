// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	ctx "github.com/17media/api/base/ctx"
	groupcall "github.com/17media/api/models/groupcall"

	mock "github.com/stretchr/testify/mock"

	models "github.com/17media/api/models"

	queue "github.com/17media/api/models/queue"
)

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

// Accept provides a mock function with given fields: context, groupCallID, userInfo, isStreaming
func (_m *Store) Accept(context ctx.CTX, groupCallID string, userInfo *models.User, isStreaming bool) (*groupcall.RespAccept, error) {
	ret := _m.Called(context, groupCallID, userInfo, isStreaming)

	if len(ret) == 0 {
		panic("no return value specified for Accept")
	}

	var r0 *groupcall.RespAccept
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User, bool) (*groupcall.RespAccept, error)); ok {
		return rf(context, groupCallID, userInfo, isStreaming)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User, bool) *groupcall.RespAccept); ok {
		r0 = rf(context, groupCallID, userInfo, isStreaming)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*groupcall.RespAccept)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, *models.User, bool) error); ok {
		r1 = rf(context, groupCallID, userInfo, isStreaming)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Add provides a mock function with given fields: context, groupCallID, hostUserID, toUserID
func (_m *Store) Add(context ctx.CTX, groupCallID string, hostUserID string, toUserID string) error {
	ret := _m.Called(context, groupCallID, hostUserID, toUserID)

	if len(ret) == 0 {
		panic("no return value specified for Add")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string) error); ok {
		r0 = rf(context, groupCallID, hostUserID, toUserID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Approve provides a mock function with given fields: context, groupCallID, hostUserID, toUserID
func (_m *Store) Approve(context ctx.CTX, groupCallID string, hostUserID string, toUserID string) error {
	ret := _m.Called(context, groupCallID, hostUserID, toUserID)

	if len(ret) == 0 {
		panic("no return value specified for Approve")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string) error); ok {
		r0 = rf(context, groupCallID, hostUserID, toUserID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CancelRaiseHand provides a mock function with given fields: context, groupCallID, userInfo
func (_m *Store) CancelRaiseHand(context ctx.CTX, groupCallID string, userInfo *models.User) error {
	ret := _m.Called(context, groupCallID, userInfo)

	if len(ret) == 0 {
		panic("no return value specified for CancelRaiseHand")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User) error); ok {
		r0 = rf(context, groupCallID, userInfo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Close provides a mock function with given fields: context, groupCallID, nextHostUserID, userInfo
func (_m *Store) Close(context ctx.CTX, groupCallID string, nextHostUserID string, userInfo *models.User) (*groupcall.RespNextRoomInfo, error) {
	ret := _m.Called(context, groupCallID, nextHostUserID, userInfo)

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 *groupcall.RespNextRoomInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, *models.User) (*groupcall.RespNextRoomInfo, error)); ok {
		return rf(context, groupCallID, nextHostUserID, userInfo)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, *models.User) *groupcall.RespNextRoomInfo); ok {
		r0 = rf(context, groupCallID, nextHostUserID, userInfo)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*groupcall.RespNextRoomInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, *models.User) error); ok {
		r1 = rf(context, groupCallID, nextHostUserID, userInfo)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Confirm provides a mock function with given fields: context, groupCallID, userInfo
func (_m *Store) Confirm(context ctx.CTX, groupCallID string, userInfo *models.User) (*groupcall.RespConfirm, error) {
	ret := _m.Called(context, groupCallID, userInfo)

	if len(ret) == 0 {
		panic("no return value specified for Confirm")
	}

	var r0 *groupcall.RespConfirm
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User) (*groupcall.RespConfirm, error)); ok {
		return rf(context, groupCallID, userInfo)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User) *groupcall.RespConfirm); ok {
		r0 = rf(context, groupCallID, userInfo)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*groupcall.RespConfirm)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, *models.User) error); ok {
		r1 = rf(context, groupCallID, userInfo)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Create provides a mock function with given fields: context, userInfo, topics, subtabID, isPrivate
func (_m *Store) Create(context ctx.CTX, userInfo *models.User, topics []string, subtabID string, isPrivate bool) (*groupcall.Info, error) {
	ret := _m.Called(context, userInfo, topics, subtabID, isPrivate)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *groupcall.Info
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, []string, string, bool) (*groupcall.Info, error)); ok {
		return rf(context, userInfo, topics, subtabID, isPrivate)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, []string, string, bool) *groupcall.Info); ok {
		r0 = rf(context, userInfo, topics, subtabID, isPrivate)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*groupcall.Info)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, *models.User, []string, string, bool) error); ok {
		r1 = rf(context, userInfo, topics, subtabID, isPrivate)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateOBSAgoraTranscoder provides a mock function with given fields: context, groupCallID, userID, roomID
func (_m *Store) CreateOBSAgoraTranscoder(context ctx.CTX, groupCallID string, userID string, roomID int32) error {
	ret := _m.Called(context, groupCallID, userID, roomID)

	if len(ret) == 0 {
		panic("no return value specified for CreateOBSAgoraTranscoder")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, int32) error); ok {
		r0 = rf(context, groupCallID, userID, roomID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Decline provides a mock function with given fields: context, groupCallID, userInfo, reason
func (_m *Store) Decline(context ctx.CTX, groupCallID string, userInfo *models.User, reason string) error {
	ret := _m.Called(context, groupCallID, userInfo, reason)

	if len(ret) == 0 {
		panic("no return value specified for Decline")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User, string) error); ok {
		r0 = rf(context, groupCallID, userInfo, reason)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Dequeue provides a mock function with given fields: context, groupCallID, userInfo
func (_m *Store) Dequeue(context ctx.CTX, groupCallID string, userInfo *models.User) error {
	ret := _m.Called(context, groupCallID, userInfo)

	if len(ret) == 0 {
		panic("no return value specified for Dequeue")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User) error); ok {
		r0 = rf(context, groupCallID, userInfo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ForceDisconnect provides a mock function with given fields: context, groupCallID, userInfo
func (_m *Store) ForceDisconnect(context ctx.CTX, groupCallID string, userInfo *models.User) error {
	ret := _m.Called(context, groupCallID, userInfo)

	if len(ret) == 0 {
		panic("no return value specified for ForceDisconnect")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User) error); ok {
		r0 = rf(context, groupCallID, userInfo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GenerateStreamerChannelToken provides a mock function with given fields: context, groupcallID, streamInfo, userInfo
func (_m *Store) GenerateStreamerChannelToken(context ctx.CTX, groupcallID string, streamInfo *models.Stream, userInfo *models.User) (string, error) {
	ret := _m.Called(context, groupcallID, streamInfo, userInfo)

	if len(ret) == 0 {
		panic("no return value specified for GenerateStreamerChannelToken")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.Stream, *models.User) (string, error)); ok {
		return rf(context, groupcallID, streamInfo, userInfo)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.Stream, *models.User) string); ok {
		r0 = rf(context, groupcallID, streamInfo, userInfo)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, *models.Stream, *models.User) error); ok {
		r1 = rf(context, groupcallID, streamInfo, userInfo)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetGroupCallInfoByStreamerID provides a mock function with given fields: context, streamerID
func (_m *Store) GetGroupCallInfoByStreamerID(context ctx.CTX, streamerID string) (*groupcall.Info, error) {
	ret := _m.Called(context, streamerID)

	if len(ret) == 0 {
		panic("no return value specified for GetGroupCallInfoByStreamerID")
	}

	var r0 *groupcall.Info
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (*groupcall.Info, error)); ok {
		return rf(context, streamerID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) *groupcall.Info); ok {
		r0 = rf(context, streamerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*groupcall.Info)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, streamerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInvitationInfo provides a mock function with given fields: context, userID
func (_m *Store) GetInvitationInfo(context ctx.CTX, userID string) (*groupcall.RespInvitationInfo, error) {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for GetInvitationInfo")
	}

	var r0 *groupcall.RespInvitationInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (*groupcall.RespInvitationInfo, error)); ok {
		return rf(context, userID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) *groupcall.RespInvitationInfo); ok {
		r0 = rf(context, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*groupcall.RespInvitationInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetInvitationList provides a mock function with given fields: context, groupCallID, reqUserID, memberType
func (_m *Store) GetInvitationList(context ctx.CTX, groupCallID string, reqUserID string, memberType groupcall.MemberType) (*groupcall.RespMemberList, error) {
	ret := _m.Called(context, groupCallID, reqUserID, memberType)

	if len(ret) == 0 {
		panic("no return value specified for GetInvitationList")
	}

	var r0 *groupcall.RespMemberList
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, groupcall.MemberType) (*groupcall.RespMemberList, error)); ok {
		return rf(context, groupCallID, reqUserID, memberType)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, groupcall.MemberType) *groupcall.RespMemberList); ok {
		r0 = rf(context, groupCallID, reqUserID, memberType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*groupcall.RespMemberList)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, groupcall.MemberType) error); ok {
		r1 = rf(context, groupCallID, reqUserID, memberType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetMembersStatus provides a mock function with given fields: context, groupCallID
func (_m *Store) GetMembersStatus(context ctx.CTX, groupCallID string) (map[groupcall.MemberStatus][]string, error) {
	ret := _m.Called(context, groupCallID)

	if len(ret) == 0 {
		panic("no return value specified for GetMembersStatus")
	}

	var r0 map[groupcall.MemberStatus][]string
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) (map[groupcall.MemberStatus][]string, error)); ok {
		return rf(context, groupCallID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) map[groupcall.MemberStatus][]string); ok {
		r0 = rf(context, groupCallID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[groupcall.MemberStatus][]string)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string) error); ok {
		r1 = rf(context, groupCallID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetViewers provides a mock function with given fields: context, groupcallID, userID, excludeMyViewers
func (_m *Store) GetViewers(context ctx.CTX, groupcallID string, userID string, excludeMyViewers bool) (*groupcall.RespViewerList, error) {
	ret := _m.Called(context, groupcallID, userID, excludeMyViewers)

	if len(ret) == 0 {
		panic("no return value specified for GetViewers")
	}

	var r0 *groupcall.RespViewerList
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, bool) (*groupcall.RespViewerList, error)); ok {
		return rf(context, groupcallID, userID, excludeMyViewers)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, bool) *groupcall.RespViewerList); ok {
		r0 = rf(context, groupcallID, userID, excludeMyViewers)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*groupcall.RespViewerList)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, bool) error); ok {
		r1 = rf(context, groupcallID, userID, excludeMyViewers)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Invite provides a mock function with given fields: context, groupCallID, host, toUserID, inviteMsg
func (_m *Store) Invite(context ctx.CTX, groupCallID string, host *models.User, toUserID string, inviteMsg string) error {
	ret := _m.Called(context, groupCallID, host, toUserID, inviteMsg)

	if len(ret) == 0 {
		panic("no return value specified for Invite")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User, string, string) error); ok {
		r0 = rf(context, groupCallID, host, toUserID, inviteMsg)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// InviteByReferralCode provides a mock function with given fields: context, toUserID, referralCode
func (_m *Store) InviteByReferralCode(context ctx.CTX, toUserID string, referralCode string) error {
	ret := _m.Called(context, toUserID, referralCode)

	if len(ret) == 0 {
		panic("no return value specified for InviteByReferralCode")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) error); ok {
		r0 = rf(context, toUserID, referralCode)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// IsAvailable provides a mock function with given fields: context, userID, region, hasContract, level
func (_m *Store) IsAvailable(context ctx.CTX, userID string, region string, hasContract bool, level int32) bool {
	ret := _m.Called(context, userID, region, hasContract, level)

	if len(ret) == 0 {
		panic("no return value specified for IsAvailable")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, bool, int32) bool); ok {
		r0 = rf(context, userID, region, hasContract, level)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsHost provides a mock function with given fields: context, groupcallID, userID
func (_m *Store) IsHost(context ctx.CTX, groupcallID string, userID string) bool {
	ret := _m.Called(context, groupcallID, userID)

	if len(ret) == 0 {
		panic("no return value specified for IsHost")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) bool); ok {
		r0 = rf(context, groupcallID, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// IsInGroupCall provides a mock function with given fields: context, groupcallID, userID
func (_m *Store) IsInGroupCall(context ctx.CTX, groupcallID string, userID string) bool {
	ret := _m.Called(context, groupcallID, userID)

	if len(ret) == 0 {
		panic("no return value specified for IsInGroupCall")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) bool); ok {
		r0 = rf(context, groupcallID, userID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// KeepChannelAlive provides a mock function with given fields: context, groupCallID, userID, roomIDs
func (_m *Store) KeepChannelAlive(context ctx.CTX, groupCallID string, userID string, roomIDs []int32) error {
	ret := _m.Called(context, groupCallID, userID, roomIDs)

	if len(ret) == 0 {
		panic("no return value specified for KeepChannelAlive")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, []int32) error); ok {
		r0 = rf(context, groupCallID, userID, roomIDs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// KeepView provides a mock function with given fields: context, data, option
func (_m *Store) KeepView(context ctx.CTX, data []byte, option queue.CallbackOption) error {
	ret := _m.Called(context, data, option)

	if len(ret) == 0 {
		panic("no return value specified for KeepView")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, []byte, queue.CallbackOption) error); ok {
		r0 = rf(context, data, option)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Kickout provides a mock function with given fields: context, groupCallID, hostUserID, targetUserID
func (_m *Store) Kickout(context ctx.CTX, groupCallID string, hostUserID string, targetUserID string) error {
	ret := _m.Called(context, groupCallID, hostUserID, targetUserID)

	if len(ret) == 0 {
		panic("no return value specified for Kickout")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string) error); ok {
		r0 = rf(context, groupCallID, hostUserID, targetUserID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Leave provides a mock function with given fields: context, groupCallID, nextHostUserID, userInfo
func (_m *Store) Leave(context ctx.CTX, groupCallID string, nextHostUserID string, userInfo *models.User) error {
	ret := _m.Called(context, groupCallID, nextHostUserID, userInfo)

	if len(ret) == 0 {
		panic("no return value specified for Leave")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, *models.User) error); ok {
		r0 = rf(context, groupCallID, nextHostUserID, userInfo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Mute provides a mock function with given fields: context, groupCallID, fromUserID, targetUserID
func (_m *Store) Mute(context ctx.CTX, groupCallID string, fromUserID string, targetUserID string) error {
	ret := _m.Called(context, groupCallID, fromUserID, targetUserID)

	if len(ret) == 0 {
		panic("no return value specified for Mute")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string) error); ok {
		r0 = rf(context, groupCallID, fromUserID, targetUserID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RaiseHand provides a mock function with given fields: context, groupCallID, userInfo
func (_m *Store) RaiseHand(context ctx.CTX, groupCallID string, userInfo *models.User) error {
	ret := _m.Called(context, groupCallID, userInfo)

	if len(ret) == 0 {
		panic("no return value specified for RaiseHand")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User) error); ok {
		r0 = rf(context, groupCallID, userInfo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Ready provides a mock function with given fields: context, groupCallID, userInfo
func (_m *Store) Ready(context ctx.CTX, groupCallID string, userInfo *models.User) error {
	ret := _m.Called(context, groupCallID, userInfo)

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User) error); ok {
		r0 = rf(context, groupCallID, userInfo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Reject provides a mock function with given fields: context, groupCallID, userInfo
func (_m *Store) Reject(context ctx.CTX, groupCallID string, userInfo *models.User) error {
	ret := _m.Called(context, groupCallID, userInfo)

	if len(ret) == 0 {
		panic("no return value specified for Reject")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, *models.User) error); ok {
		r0 = rf(context, groupCallID, userInfo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SearchUsers provides a mock function with given fields: context, groupCallID, reqUserID, targetOpenID
func (_m *Store) SearchUsers(context ctx.CTX, groupCallID string, reqUserID string, targetOpenID string) (*groupcall.RespMemberList, error) {
	ret := _m.Called(context, groupCallID, reqUserID, targetOpenID)

	if len(ret) == 0 {
		panic("no return value specified for SearchUsers")
	}

	var r0 *groupcall.RespMemberList
	var r1 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string) (*groupcall.RespMemberList, error)); ok {
		return rf(context, groupCallID, reqUserID, targetOpenID)
	}
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string, string) *groupcall.RespMemberList); ok {
		r0 = rf(context, groupCallID, reqUserID, targetOpenID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*groupcall.RespMemberList)
		}
	}

	if rf, ok := ret.Get(1).(func(ctx.CTX, string, string, string) error); ok {
		r1 = rf(context, groupCallID, reqUserID, targetOpenID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// StartMonitor provides a mock function with given fields: context, workerSize
func (_m *Store) StartMonitor(context ctx.CTX, workerSize int) error {
	ret := _m.Called(context, workerSize)

	if len(ret) == 0 {
		panic("no return value specified for StartMonitor")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, int) error); ok {
		r0 = rf(context, workerSize)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UnbindUserGroupCallBindingKey provides a mock function with given fields: context, userID
func (_m *Store) UnbindUserGroupCallBindingKey(context ctx.CTX, userID string) error {
	ret := _m.Called(context, userID)

	if len(ret) == 0 {
		panic("no return value specified for UnbindUserGroupCallBindingKey")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string) error); ok {
		r0 = rf(context, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UnmuteSelf provides a mock function with given fields: context, groupCallID, userID
func (_m *Store) UnmuteSelf(context ctx.CTX, groupCallID string, userID string) error {
	ret := _m.Called(context, groupCallID, userID)

	if len(ret) == 0 {
		panic("no return value specified for UnmuteSelf")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ctx.CTX, string, string) error); ok {
		r0 = rf(context, groupCallID, userID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewStore creates a new instance of Store. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *Store {
	mock := &Store{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
