// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import ctx "github.com/17media/api/base/ctx"
import mock "github.com/stretchr/testify/mock"
import models "github.com/17media/api/models"
import modelspersonalprize "github.com/17media/api/models/personalprize"

import time "time"

// Prize is an autogenerated mock type for the Prize type
type Prize struct {
	mock.Mock
}

// Applicable provides a mock function with given fields: context, user, region, _a3
func (_m *Prize) Applicable(context ctx.CTX, user *models.User, region string, _a3 time.Time) (bool, error) {
	ret := _m.Called(context, user, region, _a3)

	var r0 bool
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, string, time.Time) bool); ok {
		r0 = rf(context, user, region, _a3)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *models.User, string, time.Time) error); ok {
		r1 = rf(context, user, region, _a3)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExpireDate provides a mock function with given fields:
func (_m *Prize) ExpireDate() time.Time {
	ret := _m.Called()

	var r0 time.Time
	if rf, ok := ret.Get(0).(func() time.Time); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(time.Time)
	}

	return r0
}

// GetID provides a mock function with given fields:
func (_m *Prize) GetID() string {
	ret := _m.Called()

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// GetInfo provides a mock function with given fields:
func (_m *Prize) GetInfo() *modelspersonalprize.PrizeInfo {
	ret := _m.Called()

	var r0 *modelspersonalprize.PrizeInfo
	if rf, ok := ret.Get(0).(func() *modelspersonalprize.PrizeInfo); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelspersonalprize.PrizeInfo)
		}
	}

	return r0
}

// Pay provides a mock function with given fields: context, user, region, prizeID, _a4
func (_m *Prize) Pay(context ctx.CTX, user *models.User, region string, prizeID string, _a4 time.Time) (*modelspersonalprize.PayInfo, error) {
	ret := _m.Called(context, user, region, prizeID, _a4)

	var r0 *modelspersonalprize.PayInfo
	if rf, ok := ret.Get(0).(func(ctx.CTX, *models.User, string, string, time.Time) *modelspersonalprize.PayInfo); ok {
		r0 = rf(context, user, region, prizeID, _a4)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modelspersonalprize.PayInfo)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(ctx.CTX, *models.User, string, string, time.Time) error); ok {
		r1 = rf(context, user, region, prizeID, _a4)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}
