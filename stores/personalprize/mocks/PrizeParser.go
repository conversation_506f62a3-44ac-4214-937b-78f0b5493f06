// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"
import personalprize "github.com/17media/api/stores/personalprize"

// PrizeParser is an autogenerated mock type for the PrizeParser type
type PrizeParser struct {
	mock.Mock
}

// Parse provides a mock function with given fields: _a0
func (_m *PrizeParser) Parse(_a0 personalprize.RawConf) (personalprize.Prize, []string, error) {
	ret := _m.Called(_a0)

	var r0 personalprize.Prize
	if rf, ok := ret.Get(0).(func(personalprize.RawConf) personalprize.Prize); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(personalprize.Prize)
		}
	}

	var r1 []string
	if rf, ok := ret.Get(1).(func(personalprize.RawConf) []string); ok {
		r1 = rf(_a0)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]string)
		}
	}

	var r2 error
	if rf, ok := ret.Get(2).(func(personalprize.RawConf) error); ok {
		r2 = rf(_a0)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}
