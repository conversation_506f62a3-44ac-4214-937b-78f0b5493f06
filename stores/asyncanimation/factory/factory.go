package factory

import (
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/models"
	"github.com/17media/api/stores/army"
	"github.com/17media/api/stores/mlevel"
	"github.com/17media/api/stores/user"
)

type Product interface {
	// IsValidSendMsg checks if can send the message to the streamer
	IsValidSendMsg(context ctx.CTX, viewerID, streamerID string) (bool, error)

	// GenPubnubMsg generates the pubnub message
	GenPubnubMsg(context ctx.CTX, displayUserMsg *models.DisplayUserMsg, viewerUser, streamerUser *models.User, message string) (*models.Message, error)
}

// Resources is collections of services and stores used by factories
type Resources struct {
	Army   army.Store
	User   user.Store
	Mlevel mlevel.Store
}

// Factory returns a new AsyncAnimation struct
type Factory interface {
	GenerateProduct(resources Resources) Product
}
