package queue

import (
	"crypto/sha512"
	"crypto/tls"
	"encoding/json"
	"flag"
	"fmt"
	"hash"
	"reflect"
	"time"

	"github.com/Shopify/sarama"
	kafka "github.com/Shopify/sarama"
	"github.com/xdg/scram"

	"github.com/17media/logrus"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/metrics"
)

var (
	kafkaMet = metrics.New("base.kafka")

	// KafkaVersionStr specified the version kafka cluster
	KafkaVersionStr = flag.String("kafka_version", "2.5.0", "kafka cluster version")
	// KafkaAuthID specified the SASL user id
	KafkaAuthID = flag.String("kafka_auth_id", "", "kafka user id")
	// KafkaAuthPWD specified the SASL password
	KafkaAuthPWD = flag.String("kafka_auth_pwd", "", "kafka password")
	// KafkaConnectToExternalTestDocker set to true when connecting to test docker
	KafkaConnectToExternalTestDocker = flag.Bool("kafka_connect_to_external_test_docker", false, "connect to kafka external test docker")
	// KafkaMetadataRefreshFrequency specified the broker metadata refresh frequency
	KafkaMetadataRefreshFrequency = time.Minute * 10
)

// KafkaImpl implements queue.Publisher by kafka
type KafkaImpl struct {
	TopicName string
	Producer  kafka.SyncProducer
	UUID      string
}

// Publish the msg via kafka
// TODO: add trace for debug purpose
func (im *KafkaImpl) Publish(cont ctx.CTX, msg interface{}, optFuncs ...OptionFunc) error {
	defer kafkaMet.BumpTime("time", "func", "Publish").End()
	// prepare options
	opt, err := parseOptions(optFuncs...)
	if err != nil {
		cont.WithFields(logrus.Fields{
			"err":      err,
			"optFuncs": optFuncs,
		}).Error("kafka parseOptions failed")
		return err
	}
	cont = ctx.WithValues(cont, map[string]interface{}{
		"msg": msg,
	})

	msgBytes := []byte{}
	msgValue := reflect.ValueOf(msg)
	switch msgValue.Kind() {
	case reflect.String:
		msgBytes = []byte(msg.(string))
	default:
		msgBytes, err = json.Marshal(msg)
		if err != nil {
			cont.WithFields(logrus.Fields{
				"err": err,
			}).Error("kafka json.Marshal failed")
			return err
		}
	}

	if len(msgBytes) == 0 {
		return fmt.Errorf("kafka msg to publish is empty")
	}

	producerMsg := &kafka.ProducerMessage{
		Topic: im.TopicName,
		Value: kafka.ByteEncoder(msgBytes),
	}
	if opt.Key != "" {
		producerMsg.Key = kafka.StringEncoder(opt.Key)
	}
	_, _, err = im.Producer.SendMessage(producerMsg)
	if err != nil {
		kafkaMet.BumpSum("publish.err", 1, "topic", producerMsg.Topic)
		cont.WithFields(logrus.Fields{
			"err":   err,
			"Topic": im.TopicName,
			"key":   opt.Key,
		}).Error("kafka SendMessage failed")
		return err
	}
	kafkaMet.BumpSum("publish", 1, "topic", producerMsg.Topic)
	return nil
}

// KafkaClient defines the kafka client structure
type KafkaClient struct {
	// Producer is kafka producer
	Producer kafka.SyncProducer
}

// GetKafkaClient returns kafka client
func GetKafkaClient(cont ctx.CTX, brokerURIs []string, opts ...NewOptionFunc) (*KafkaClient, error) {
	defer kafkaMet.BumpTime("time", "func", "GetKafkaClient").End()
	// prepare options
	options := NewOption{}
	for _, optFunc := range opts {
		optFunc(&options)
	}

	kafkaVersion, err := kafka.ParseKafkaVersion(*KafkaVersionStr)
	if err != nil {
		cont.WithFields(logrus.Fields{
			"err":             err,
			"kafkaVersionStr": *KafkaVersionStr,
		}).Error("ParseKafkaVersion failed")
		return nil, err
	}

	// get kafka client
	config := kafka.NewConfig()
	config.Version = kafkaVersion
	config.Producer.RequiredAcks = kafka.WaitForAll
	// use sarama defaul retry/backoff mechanism
	config.Producer.Retry.Max = publishRetryLimit
	config.Producer.Retry.BackoffFunc = func(retries, maxRetries int) time.Duration {
		return publishRetryInterval * (1 << retries)
	}
	config.Producer.Return.Successes = true
	// use FNV-1a to encode the message key for partition
	// choose random partion if message key is nil
	config.Producer.Partitioner = sarama.NewReferenceHashPartitioner
	// refresh broker metadata every n minutes to avoid the connection being closed by kafka server
	// https://kafka.apache.org/25/documentation.html#connections.max.idle.ms
	config.Metadata.RefreshFrequency = KafkaMetadataRefreshFrequency

	if *KafkaConnectToExternalTestDocker == false {
		if *KafkaAuthID == "" || *KafkaAuthPWD == "" {
			return nil, fmt.Errorf("kafka authID or authPWD is empty")
		}
		// use SASL scram with TLS
		config.Net.TLS.Enable = true
		config.Net.TLS.Config = &tls.Config{InsecureSkipVerify: false}
		config.Net.SASL.Enable = true
		config.Net.SASL.Mechanism = kafka.SASLTypeSCRAMSHA512
		config.Net.SASL.User = *KafkaAuthID
		config.Net.SASL.Password = *KafkaAuthPWD
		config.Net.SASL.SCRAMClientGeneratorFunc = func() kafka.SCRAMClient {
			return &XDGSCRAMClient{HashGeneratorFcn: SHA512}
		}
	}

	producer, err := kafka.NewSyncProducer(brokerURIs, config)
	if err != nil {
		return nil, err
	}

	kafkaClient := &KafkaClient{
		Producer: producer,
	}
	cont.Info("kafka client initiated")
	return kafkaClient, nil
}

// SHA512 hash generator function for SCRAM conversation
var SHA512 scram.HashGeneratorFcn = func() hash.Hash { return sha512.New() }

// XDGSCRAMClient struct to perform SCRAM conversation
type XDGSCRAMClient struct {
	*scram.Client
	*scram.ClientConversation
	scram.HashGeneratorFcn
}

// Begin starts SCRAM conversation
func (x *XDGSCRAMClient) Begin(userName, password, authzID string) (err error) {
	x.Client, err = x.HashGeneratorFcn.NewClient(userName, password, authzID)
	if err != nil {
		return err
	}
	x.ClientConversation = x.Client.NewConversation()
	return nil
}

// Step performs step in SCRAM conversation
func (x *XDGSCRAMClient) Step(challenge string) (response string, err error) {
	response, err = x.ClientConversation.Step(challenge)
	return
}

// Done completes SCRAM conversation
func (x *XDGSCRAMClient) Done() bool {
	return x.ClientConversation.Done()
}
