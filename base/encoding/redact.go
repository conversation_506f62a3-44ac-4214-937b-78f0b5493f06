package encoding

/* redact.go redacts fields with specified tags and remove them before sending to
* DB or REST API */

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"gopkg.in/go-playground/validator.v9"
)

const (
	tag17Root       = "17media"
	tagPrivate      = "private"
	tagSkipDB       = "skip_db"
	tagPrefix       = "prefix"
	tagDefaultNum   = "default_num"
	tagDefaultStr   = "default_str"
	tagDefaultValue = "default_value"
	tagGuestIgnore  = "guest_ignore"
	tagAdmin        = "admin"
	// tagJSON for api response json field name
	// the value of this tag could override name set in json tag
	// for example: 17media:"json",field:test would rename output field name to test
	tagJSON = "json"
)

var (
	validate = validator.New()
)

type (
	// OptionFunc offers functional arguments
	OptionFunc func(*option)
	option     struct {
		skipFillNil bool
	}
)

func parseOptions(optionFuncs ...OptionFunc) *option {
	o := &option{}
	for _, optFunc := range optionFuncs {
		optFunc(o)
	}
	return o
}

// BindJSON wraps gin context's BindJSON or Bind method according different HTTP method and inserts nil fields by default values.
// if o.skipFillNil is false, FillNil will be applied but will ignore tags <`binding:"required"`>
// if o.skipFillNil is true, FillNil won't be applied and tags <`binding:"required"`> will be effective
// o.skipFillNil's default value is false
func BindJSON(ctx *gin.Context, obj interface{}, optionFuncs ...OptionFunc) error {

	o := parseOptions(optionFuncs...)

	if binding.Default(ctx.Request.Method, ctx.ContentType()) == binding.Form {
		// TODO need to check how to do this with FillNil
		if err := ctx.Bind(obj); err != nil {
			// NOTE if ctx.Bind fails, this request will be aborted with HTTP status code 400
			return err
		}
		return validateStruct(obj)
	}

	if o.skipFillNil {
		if err := ctx.ShouldBindWith(obj, binding.JSON); err != nil {
			return err
		}
	} else {
		var intf interface{}
		// ctx.BindJSON will send status code 400 and overwrites our status code if error occurs
		// which is not the desired behaviour
		if err := ctx.ShouldBindWith(&intf, binding.JSON); err != nil {
			return err
		} else if err := FillNil(intf, obj); err != nil {
			return err
		}
	}
	return validateStruct(obj)
}

// WithoutFillNil set skipFillNil true
func WithoutFillNil() OptionFunc {
	return func(o *option) {
		o.skipFillNil = true
	}
}

func validateStruct(obj interface{}) error {
	val := reflect.ValueOf(obj)
	if val.Kind() == reflect.Ptr {
		val = Indirect(val)
	}

	typ := val.Type()
	switch typ.Kind() {
	case reflect.Struct:
		return validate.Struct(obj)
	}
	// other case we simply ignore validation now
	return nil
}

// StripForDB strips unnecessary fields before DB writes. It should be used right before
// database writes.
func StripForDB(value interface{}) (interface{}, error) {
	return StripNil(redact(value, tagSkipDB, nil))
}

func getInsertValue(in reflect.Value, defVal reflect.Value) reflect.Value {
	t := in.Type().Kind()
	if nilVal, ok := NilValue[t]; ok {
		// convert int, int64 to int32 and float64 to float32 for avoiding type inconsistent
		if in.Convert(reflect.TypeOf(nilVal)).Interface() == nilVal {
			return reflect.Zero(in.Type())
		}
		return in
	}

	switch t {
	case reflect.Interface:
		if in.IsNil() {
			return defVal
		}
		return getInsertValue(in.Elem(), defVal)
	default:
		return in
	}
}

// RedactPrivate filters incoming structure and removes private fields
func RedactPrivate(obj interface{}, ignoreTag ...bool) interface{} {
	ignore := false
	if len(ignoreTag) > 0 && ignoreTag[0] == true {
		ignore = true
	}
	tag := tagPrivate
	if ignore {
		tag = ""
	}
	return redact(obj, tag, nil)
}

// RedactPrefix prepend prefix to fields
func RedactPrefix(obj interface{}, prefix map[string]string) interface{} {
	return redact(obj, "", prefix)
}

func redact(obj interface{}, tagToRedact string, prefix map[string]string) interface{} {
	// Duplicate obj to another one
	in := reflect.ValueOf(obj)
	out := reflect.New(in.Type()).Elem()
	// Save return value for later use
	ret := out

	// Incoming object can be pointer. Deference if necessary
	if in.Kind() == reflect.Ptr {
		in = in.Elem()
		// Initialize pointer value and dereference it.
		out.Set(reflect.New(in.Type()))
		out = out.Elem()
	}
	typ := in.Type()

	switch typ.Kind() {
	case reflect.Struct:
		redactStruct(in, out, tagToRedact, prefix)
	case reflect.Slice:
		redactSlice(in, out, tagToRedact, prefix)
	case reflect.Ptr:
		panic(fmt.Sprintf("only support one level of pointer indirection, obj %v", in.Interface()))
	default:
		// If it's not struct (either map or plain value), return directly
		return obj
	}

	// Convert out from refelct value to interface and return
	return ret.Interface()
}

func redactSlice(in, out reflect.Value, tagToRedact string, prefix map[string]string) {
	// In case it's a nil slice, there's nothing to be done.
	if in.IsNil() {
		return
	}
	out.Set(reflect.MakeSlice(in.Type(), in.Len(), in.Len()))
	for i := 0; i < in.Len(); i++ {
		inIdx := in.Index(i)
		outIdx := out.Index(i)
		if inIdx.Kind() == reflect.Ptr && inIdx.Elem().Kind() == reflect.Struct {
			outIdx.Set(reflect.New(inIdx.Type().Elem()))
			inIdx = inIdx.Elem()
			outIdx = outIdx.Elem()
		}
		redactStruct(inIdx, outIdx, tagToRedact, prefix)
	}
}

func redactStruct(in, out reflect.Value, tagToRedact string, prefix map[string]string) {
	typ := in.Type()
	if typ.Kind() != reflect.Struct {
		out.Set(in)
		return
	}

	// Iterate through fields to see if need to add fields or set default value
	for i := 0; i < typ.NumField(); i++ {
		// ignore unexported fields (ex. `state` in protobuf message)
		t := typ.FieldByIndex([]int{i})
		if t.PkgPath != "" {
			continue
		}
		f := typ.Field(i)
		outField := out.Field(i)
		// Construct tags
		tags := map[string]bool{}
		if f.Tag.Get(tag17Root) != "" {
			for _, t := range strings.Split(f.Tag.Get(tag17Root), ",") {
				tags[t] = true
			}
		}

		nilVal, hasNilVal := NilValue[f.Type.Kind()]
		inf := in.Field(i)
		if tags[tagToRedact] {
			switch {
			case hasNilVal:
				outField.Set(reflect.ValueOf(nilVal).Convert(f.Type))
			case tags[tagDefaultNum]:
				outField.Set(reflect.ValueOf(NilInt))
			case tags[tagDefaultStr]:
				outField.Set(reflect.ValueOf(NilString))
			}
			continue
		}

		switch {
		case tags[tagDefaultNum]:
			outField.Set(getInsertValue(inf, reflect.ValueOf(0)))
		case tags[tagDefaultStr]:
			outField.Set(getInsertValue(inf, reflect.ValueOf("")))
		case inf.Kind() == reflect.Slice:
			redactSlice(inf, outField, tagToRedact, prefix)
		case inf.Kind() == reflect.Ptr && inf.Elem().Kind() == reflect.Struct:
			outField.Set(reflect.New(inf.Type().Elem()))
			redactStruct(inf.Elem(), outField.Elem(), tagToRedact, prefix)
		case inf.Kind() == reflect.Struct:
			// If the field is another struct, need to dig into it
			redactStruct(inf, outField, tagToRedact, prefix)
		case tags[tagDefaultValue]:
			for k := range tags {
				if strings.HasPrefix(k, "str:") {
					if EmptyOrNilString(inf.String()) {
						outField.Set(reflect.ValueOf(strings.TrimPrefix(k, "str:")))
					} else {
						outField.Set(inf)
					}
					break
				} else if strings.HasPrefix(k, "int:") {
					if Zero64OrNilInt(inf.Int()) {
						if outInt, err := strconv.ParseInt(strings.TrimPrefix(k, "int:"), 10, 64); err == nil {
							outField.Set(reflect.ValueOf(outInt).Convert(inf.Type()))
						}
					} else {
						outField.Set(inf)
					}
					break
				}
			}
		case inf.Kind() == reflect.String:
			match := false
			for k := range tags {
				if strings.HasPrefix(k, tagPrefix+":") && !EmptyOrNilString(inf.String()) {
					prefixFor := strings.TrimPrefix(k, tagPrefix+":")
					if _, ok := prefix[prefixFor]; ok {
						match = true
						outField.Set(reflect.ValueOf(prefix[prefixFor] + inf.String()))
						break
					}
				}
			}
			if !match {
				outField.Set(inf)
			}
		default:
			outField.Set(inf)
		}
	}
}
