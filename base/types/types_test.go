package types

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

type testType int

const (
	type0 testType = iota
	type1
	type2
)

type stringType string

func TestNewNumeric(t *testing.T) {
	tests := []struct {
		desc  string
		value interface{}
		exp   Numeric
		err   error
	}{
		{
			desc:  "int8",
			value: int8(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "int16",
			value: int16(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "int",
			value: int(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "int32",
			value: int32(1),
			exp:   Numeric(1),
			err:   nil,
		},

		{
			desc:  "int64",
			value: int64(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "float32",
			value: float32(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "float64",
			value: float64(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "uint",
			value: uint(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "uint32",
			value: uint32(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "uint64",
			value: uint64(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "uint8",
			value: uint8(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "uint16",
			value: uint16(1),
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "float in string",
			value: "12.3",
			exp:   Numeric(12.3),
			err:   nil,
		},
		{
			desc:  "int in string",
			value: "1",
			exp:   Numeric(1),
			err:   nil,
		},
		{
			desc:  "string",
			value: "fail",
			exp:   Numeric(0),
			err:   ErrNotNumeric,
		},
		{
			desc:  "bool",
			value: true,
			exp:   Numeric(0),
			err:   ErrNotNumeric,
		},
		{
			desc:  "nil",
			value: nil,
			exp:   Numeric(0),
			err:   ErrNilValue,
		},
		{
			desc:  "indirect type",
			value: type2,
			exp:   Numeric(2),
			err:   nil,
		},
		{
			desc:  "hidden string type",
			value: stringType("123"),
			exp:   Numeric(123),
			err:   nil,
		},
	}

	for _, test := range tests {
		v, err := NewNumeric(test.value)
		assert.Equal(t, test.err, err)
		assert.Equal(t, test.exp, v)
	}
}

func TestAdd(t *testing.T) {
	a := Numeric(1)
	b := Numeric(2)
	assert.Equal(t, Numeric(3), a.Add(b))
}

func TestSub(t *testing.T) {
	a := Numeric(1)
	b := Numeric(2)
	assert.Equal(t, Numeric(-1), a.Sub(b))
}

func TestMul(t *testing.T) {
	a := Numeric(3)
	b := Numeric(2)
	assert.Equal(t, Numeric(6), a.Mul(b))
}

func TestDivBy(t *testing.T) {
	a := Numeric(1)
	b := Numeric(2)
	assert.Equal(t, Numeric(0.5), a.DivBy(b))
}

func TestToInt(t *testing.T) {
	assert.Equal(t, -1, Numeric(-1.5).ToInt())
}

func TestToInt32(t *testing.T) {
	assert.Equal(t, int32(-1), Numeric(-1.5).ToInt32())
}

func TestToInt64(t *testing.T) {
	assert.Equal(t, int64(-1), Numeric(-1.5).ToInt64())
}

func TestToFloat32(t *testing.T) {
	assert.Equal(t, float32(-1.5), Numeric(-1.5).ToFloat32())
}

func TestToFloat64(t *testing.T) {
	assert.Equal(t, float64(-1.5), Numeric(-1.5).ToFloat64())
}

func TestConvertInt(t *testing.T) {
	assert.Equal(t, int64(115), (Numeric(1.15) * Numeric(100)).ToInt64())
}
