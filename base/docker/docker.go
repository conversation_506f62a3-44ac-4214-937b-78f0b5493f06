/*Package docker wraps common used docker utilities*/
package docker

import (
	"os/exec"
	"runtime"
	"strings"

	log "github.com/17media/logrus"
	docker "github.com/fsouza/go-dockerclient"
)

const (
	pullImageRetryCount = 3
)

var (
	clientPullImage = func(c *docker.Client, opts docker.PullImageOptions, auth docker.AuthConfiguration) error {
		return c.PullImage(opts, auth)
	}
)

// NewClient creates a docker client based on current OS
func NewClient() (*docker.Client, error) {
	if runtime.GOOS == "darwin" {
		return docker.NewClientFromEnv()
	}

	// For linux, get docker from docker.sock
	endpoint := "unix:///Users/<USER>/.docker/run/docker.sock"
	return docker.NewClient(endpoint)
}

// HostIP returns docker host ip address
func HostIP() (string, error) {
	// For linux it's simply localhost
	if runtime.GOOS == "linux" {
		return "localhost", nil
	}

	// Otherwise, assume it's darwin
	ip, err := exec.Command("docker-machine", "ip").Output()
	if err == nil {
		return strings.Trim(string(ip), "\n"), nil
	}

	// In case of mac docker, there's no docker-machine, use localhost instead
	return "localhost", nil
}

// CheckNPullImage checks whether image exists. If not pulls images
func CheckNPullImage(client *docker.Client, repo, tag string) error {
	filterName := repo + ":" + tag
	filters := map[string][]string{
		// "ListImagesOptions.Filter" field has been deprecated after docker API version >= 1.41
		// Ref: https://docs.docker.com/engine/release-notes/#deprecation--removal
		// `reference` is for the replacement
		"reference": []string{filterName},
	}
	img, err := client.ListImages(docker.ListImagesOptions{Filter: filterName, Filters: filters})
	if err != nil {
		return err
	}

	// We have specified image. we're done.
	if len(img) > 0 {
		return nil
	}

	// Image is there here. Pull it
	return pullImage(client, repo, tag)
}

func logDockerAuthErr(err error, repo, tag string) {
	log.WithFields(log.Fields{
		"err":  err,
		"repo": repo,
		"tag":  tag,
	}).Error("cannot authenticate docker with dockercfg")
}

func getDockerAuthConfiguration(repo, tag string) docker.AuthConfiguration {
	isGCR := strings.HasPrefix(repo, "gcr.io")
	auths, err := docker.NewAuthConfigurationsFromDockerCfg()
	if err != nil {
		logDockerAuthErr(err, repo, tag)
	}
	if isGCR {
		if auth, ok := auths.Configs["gcr.io"]; ok {
			return auth
		}
		auth, err := docker.NewAuthConfigurationsFromCredsHelpers("gcr.io")
		if err != nil {
			logDockerAuthErr(err, repo, tag)
		}
		return *auth
	}
	auth := auths.Configs["https://index.docker.io/v1/"]
	return auth
}

func pullImage(client *docker.Client, repo, tag string) (err error) {
	log.WithFields(log.Fields{"repo": repo, "tag": tag}).Info("pulling docker image")
	for i := 0; i < pullImageRetryCount; i++ {
		dockerAuthConfig := getDockerAuthConfiguration(repo, tag)
		err = clientPullImage(
			client,
			docker.PullImageOptions{Repository: repo, Tag: tag},
			dockerAuthConfig,
		)
		if err != nil {
			log.WithFields(log.Fields{
				"err":        err,
				"retryCount": i,
				"repo":       repo,
				"tag":        tag,
			}).Error("pulling docker image error")
			continue
		}
		log.WithFields(log.Fields{
			"repo": repo,
			"tag":  tag,
		}).Info("pulling docker image done")
		break
	}
	return
}

// RunContainer runs container with specified docker option
func RunContainer(client *docker.Client, option docker.CreateContainerOptions) (*docker.Container, error) {
	container, err := client.CreateContainer(option)
	if err != nil {
		log.WithFields(log.Fields{"err": err, "image": option.Config.Image}).Error("Cannot create container")
		return nil, err
	}

	if err := client.StartContainer(container.ID, nil); err != nil {
		log.WithFields(log.Fields{"err": err, "containerID": container.ID}).Error("Cannot start container")
		return nil, err
	}
	return container, nil
}

// RemoveContainer removes and kills running containers
func RemoveContainer(client *docker.Client, containers ...*docker.Container) error {
	for _, c := range containers {
		if err := client.KillContainer(docker.KillContainerOptions{ID: c.ID, Signal: docker.Signal(9)}); err != nil {
			return err
		}
		// Set RemoveVolumes to true, 2017/04/10 Abner
		// Otherwise, volumes need to be deleted manually after containers deleted.
		// There will be an error 'No space left on device' if too many volumes left.
		// Use `docker volume ls` to check volumes.
		// Use `docker volume ls -q | xargs docker volume rm` to delete all volumes.
		// Use `docker ps -aq | xargs docker rm -fv` to delete volumes along with containers.
		if err := client.RemoveContainer(docker.RemoveContainerOptions{ID: c.ID, RemoveVolumes: true, Force: true}); err != nil {
			// FIXME: circle CI can't remove intermediate container. Do not return error even if
			// remove failed. See https://discuss.circleci.com/t/docker-error-removing-intermediate-container/70
			log.WithFields(log.Fields{"err": err, "containerID": c.ID}).Error("unable to remove container")
		}
	}
	return nil
}

// RestartContainer restart running containers
func RestartContainer(client *docker.Client, timeout uint, containers ...*docker.Container) error {
	for _, c := range containers {
		if err := client.RestartContainer(c.ID, timeout); err != nil {
			log.WithFields(log.Fields{"err": err, "containerID": c.ID}).Error("unable to restart container")
			return err
		}
	}
	return nil
}
