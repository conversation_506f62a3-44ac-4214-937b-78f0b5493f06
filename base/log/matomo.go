package log

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"flag"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"go.opencensus.io/trace"

	btime "github.com/17media/api/base/time"

	"github.com/google/go-querystring/query"
	yaml "gopkg.in/yaml.v2"

	"github.com/17media/logrus"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/goroutine"
	"github.com/17media/api/base/rand"
)

const (
	packageID = "com.media17.backend"
)

var (
	MatomoConfigPath = "17app/matomo/matomo.yaml"
	MatomoConfigs    MatomoConfig

	siteID = flag.Int("matomo_site_id", 1, "matomo site_id")
	api    = flag.String("matomo_api", "", "matomo api")
	token  = flag.String("matomo_token", "", "matomo token")

	goroutinePool *goroutine.Pool

	timeNow     = btime.TimeNow
	sendRequest = apiRequest
	activeFlag  = true
)

func SetGoroutinePool(pool *goroutine.Pool) {
	goroutinePool = pool
}

type MatomoConfig struct {
	Enable bool `yaml:"enable"`
}

func (c *MatomoConfig) Check(data []byte) (interface{}, []string, error) {
	conf := MatomoConfig{}
	if err := yaml.Unmarshal(data, &conf); err != nil {
		return nil, nil, err
	}

	return conf, nil, nil
}

func (c *MatomoConfig) Apply(v interface{}) {
	*c = v.(MatomoConfig)
}

type MatomoEvent struct {
	ActionName string `url:"action_name" json:"action_name"`
	// Path used to build param `url` send to matomo,
	// url: "https://<packageID>/<path>"
	Path         string `url:"path" json:"path"`
	EC           string `url:"e_c" json:"e_c"`
	EA           string `url:"e_a" json:"e_a"`
	EV           int32  `url:"e_v" json:"e_v"`
	EN           string `url:"e_n" json:"e_n"`
	TradeID      string `url:"dimension14" json:"dimension14"`
	ProductID    string `url:"dimension3" json:"dimension3"`
	ReceiverID   string `url:"dimension4" json:"dimension4"`
	SendPoint    int32  `url:"dimension9" json:"dimension9"`
	ReceivePoint int32  `url:"dimension10" json:"dimension10"`
	ContentID    string `url:"dimension1" json:"dimension1"`
	ContentTYPE  string `url:"dimension2" json:"dimension2"`
	GenericText  string `url:"dimension12" json:"dimension12"`
	PULID        int64  `url:"dimension18" json:"dimension18"`
	PGLID        int64  `url:"dimension19" json:"dimension19"`
}

type matomoEventPrivate struct {
	TraceID        string `url:"dimension13" json:"dimension13"`
	IDSite         int    `url:"idsite" json:"idsite"`
	Rec            int    `url:"rec" json:"rec"`
	URL            string `url:"url" json:"url"`
	ID             string `url:"_id" json:"_id"`
	Rand           int    `url:"rand" json:"rand"`
	R              int    `url:"r" json:"r"`
	APIV           int    `url:"apiv" json:"apiv"`
	QueuedTracking int    `url:"queuedtracking" json:"queuedtracking"`
	UserID         string `url:"uid" json:"uid"`
	CID            string `url:"cid" json:"cid"`
	PageViewID     string `url:"pv_id" json:"pv_id"`
	Timestamp      int64  `url:"cdt" json:"cdt"`
	TimestampMs    int64  `url:"dimension17" json:"dimension17"`
	DurationMs     int64  `url:"gt_ms" json:"gt_ms"`
	MatomoEvent    `url:",inline" json:",inline"`
}

type MatomoEvents struct {
	Context   ctx.CTX
	UserID    string
	StartTime int64
	Events    []*MatomoEvent
}

func StartMatomoEvent(context ctx.CTX, userID string, events ...MatomoEvent) *MatomoEvents {
	output := MatomoEvents{
		Context:   context,
		UserID:    userID,
		StartTime: timeNow().UnixNano(),
	}
	for _, event := range events {
		e := event
		output.Events = append(output.Events, &e)
	}
	return &output
}

func (m *MatomoEvents) Append(events ...MatomoEvent) *MatomoEvents {
	for _, event := range events {
		e := event
		m.Events = append(m.Events, &e)
	}
	return m
}

func (m *MatomoEvents) End() {
	if !MatomoConfigs.Enable {
		return
	}

	now := timeNow()
	durationNs := now.UnixNano() - m.StartTime
	requests := []string{}
	pageViewID := rand.String(6)
	requestID := m.Context.Value("requestID")
	span := trace.FromContext(m.Context)
	spanContext := span.SpanContext()

	if requestID != nil {
		pageViewID = shortedUUID(requestID.(string), 6)
	}

	requestTime := timeNow().Unix()
	// if event trigger time exists, use it instead of server time
	if t := m.Context.Value("requestTime"); t != nil {
		requestTime = t.(int64)
	}

	for _, event := range m.Events {
		data := matomoEventPrivate{
			TraceID:        spanContext.TraceID.String(),
			IDSite:         *siteID,
			Rec:            1,
			URL:            "https://" + packageID + "/" + event.Path,
			ID:             shortedUUID(m.UserID, 16),
			CID:            shortedUUID(m.UserID, 16),
			Rand:           rand.Int(),
			R:              rand.Int(),
			APIV:           1,
			QueuedTracking: 1,
			UserID:         m.UserID,
			PageViewID:     pageViewID,
			Timestamp:      requestTime,
			TimestampMs:    requestTime * 1e9 / time.Millisecond.Nanoseconds(),
			DurationMs:     durationNs / 1e6,
			MatomoEvent:    *event,
		}
		queryString, err := query.Values(data)
		if err != nil {
			m.Context.WithField("err", err).Warn("query.Values failed")
			continue
		}
		requests = append(requests, "?"+queryString.Encode())
	}
	sendRequest(m.Context, requests)
}

func shortedUUID(uuid string, count int) string {
	uuid = strings.Replace(uuid, "-", "", -1)
	if len(uuid) <= count {
		return uuid
	}
	return uuid[0:count]
}

func apiRequest(context ctx.CTX, requests []string) error {
	if goroutinePool == nil {
		if activeFlag {
			context.WithField("reason", "goroutine pool is nil").Warn("matomo disabled")
			activeFlag = false
		}
		return nil
	}
	if len(*api) == 0 {
		if activeFlag {
			context.WithField("reason", "api len = 0").Warn("matomo disabled")
			activeFlag = false
		}
		return nil
	}
	body := struct {
		Requests  []string `json:"requests"`
		TokenAuth string   `json:"token_auth"`
	}{
		Requests:  requests,
		TokenAuth: *token,
	}

	b, err := json.Marshal(body)
	if err != nil {
		context.WithField("err", err).Warn("json.Marshal failed")
		return err
	}

	goroutinePool.ScheduleTimeout(3*time.Second, func() {
		defer met.BumpTime("matomo.time").End()
		tr := &http.Transport{
			TLSClientConfig:    &tls.Config{InsecureSkipVerify: true},
			DisableCompression: true,
		}
		client := &http.Client{Transport: tr}

		req, err := http.NewRequest("POST", *api, bytes.NewBuffer(b))
		if err != nil {
			context.WithField("err", err).Error("Failed to create request")
			return
		}

		resp, err := client.Do(req)
		if err != nil {
			context.WithField("err", err).Error("Failed to do request")
			return
		}
		defer resp.Body.Close()

		respBody, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			context.WithField("err", err).Error("ioutil.ReadAll failed")
			return
		}

		if resp.StatusCode < 200 || resp.StatusCode >= 300 {
			context.WithFields(logrus.Fields{
				"httpResponseCode": resp.StatusCode,
				"httpResponseBody": string(respBody),
			}).Warn("encounter error when sending requests to matomo")
		}
		return
	})
	return nil
}
