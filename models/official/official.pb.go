// Code generated by protoc-gen-go. DO NOT EDIT.
// source: models/official/official.proto

/*
Package official is a generated protocol buffer package.

It is generated from these files:

	models/official/official.proto

It has these top-level messages:

	SystemMessage
	VersionStatus
	ButlerMsg
	ButlerMsgInfo
	TagInfo
	SystemMsgParam
*/
package official

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import user "github.com/17media/api/models/user"
import i18n "github.com/17media/api/models/i18n"
import chat "github.com/17media/api/models/chat"
import "github.com/17media/api/models/contractapplication"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Type is used to change the tag on client butler UI
// | Type | Value |
// | --- | --- |
// | Streamer_Notif | 1 |
// | Feature_Update | 2 |
// | Event_News | 3 |
// | System_Announce | 4 |
// | GroupCall_Invite | 5 |
// | VIP | 6 |
// | Onlive | 7 |
// | Non_Realtime | 8 |
// | ReferralV2_Invite | 9 |
// | ReferralV2_Invitee | 10 |
// | Streamer_Revenue | 11 |
// | ApplySeller | 12 |
// | OrderCreated | 13 |
// | Violation | 14 |
// | Shop_Order_Cancel | 15 |
// | Loyalty_Gift | 16 |
// | Event_Rewards | 17 |
// | Payment | 18 |
// | Form | 19 |
// | Tailored_News | 20 |
// | Special_Activity | 21 |
// | Reject_Paid_Post | 22 |
// | EVENT_CARD_REWARD | 23 |
// | CONTRACT_APPLICATION | 24 |
// | RECEIVE_USER_STYLE | 25 |

type Type int32

const (
	Type_DUMMYTYPE            Type = 0
	Type_STREAMER_NOTIF       Type = 1
	Type_FEATURE_UPDATE       Type = 2
	Type_EVENT_NEWS           Type = 3
	Type_SYSTEM_ANNOUNCE      Type = 4
	Type_GROUPCALL_INVITE     Type = 5
	Type_VIP                  Type = 6
	Type_ONLIVE               Type = 7
	Type_NON_REALTIME         Type = 8
	Type_REFERRALV2_INVITER   Type = 9
	Type_REFERRALV2_INVITEE   Type = 10
	Type_STREAMER_REVENUE     Type = 11
	Type_SHOP_APPLY_SELLER    Type = 12
	Type_SHOP_ORDER_CREATED   Type = 13
	Type_VIOLATION            Type = 14
	Type_SHOP_ORDER_CANCEL    Type = 15
	Type_LOYALTY_GIFT         Type = 16
	Type_EVENT_REWARDS        Type = 17
	Type_PAYMENT              Type = 18
	Type_FORM                 Type = 19
	Type_TAILORED_NEWS        Type = 20
	Type_SPECIAL_ACTIVITY     Type = 21
	Type_REJECT_PAID_POST     Type = 22
	Type_EVENT_CARD_REWARD    Type = 23
	Type_CONTRACT_APPLICATION Type = 24
	Type_RECEIVE_USER_STYLE   Type = 25
)

var Type_name = map[int32]string{
	0:  "DUMMYTYPE",
	1:  "STREAMER_NOTIF",
	2:  "FEATURE_UPDATE",
	3:  "EVENT_NEWS",
	4:  "SYSTEM_ANNOUNCE",
	5:  "GROUPCALL_INVITE",
	6:  "VIP",
	7:  "ONLIVE",
	8:  "NON_REALTIME",
	9:  "REFERRALV2_INVITER",
	10: "REFERRALV2_INVITEE",
	11: "STREAMER_REVENUE",
	12: "APPLY_SELLER",
	13: "ORDER_CREATED",
	14: "VIOLATION",
	15: "ORDER_CANCEL",
	16: "LOYALTY_GIFT",
	17: "EVENT_REWARDS",
	18: "PAYMENT",
	19: "FORM",
	20: "TAILORED_NEWS",
	21: "SPECIAL_ACTIVITY",
	22: "REJECT_PAID_POST",
	23: "EVENT_CARD_REWARD",
	24: "CONTRACT_APPLICATION",
	25: "RECEIVE_USER_STYLE",
}
var Type_value = map[string]int32{
	"DUMMYTYPE":            0,
	"STREAMER_NOTIF":       1,
	"FEATURE_UPDATE":       2,
	"EVENT_NEWS":           3,
	"SYSTEM_ANNOUNCE":      4,
	"GROUPCALL_INVITE":     5,
	"VIP":                  6,
	"ONLIVE":               7,
	"NON_REALTIME":         8,
	"REFERRALV2_INVITER":   9,
	"REFERRALV2_INVITEE":   10,
	"STREAMER_REVENUE":     11,
	"APPLY_SELLER":         12,
	"ORDER_CREATED":        13,
	"VIOLATION":            14,
	"ORDER_CANCEL":         15,
	"LOYALTY_GIFT":         16,
	"EVENT_REWARDS":        17,
	"PAYMENT":              18,
	"FORM":                 19,
	"TAILORED_NEWS":        20,
	"SPECIAL_ACTIVITY":     21,
	"REJECT_PAID_POST":     22,
	"EVENT_CARD_REWARD":    23,
	"CONTRACT_APPLICATION": 24,
	"RECEIVE_USER_STYLE":   25,
}

func (x Type) String() string {
	return proto.EnumName(Type_name, int32(x))
}
func (Type) EnumDescriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

// | Type | Value |
// | --- | --- |
// | All_User | 1 |
// | Newbie_D0 | 2 |
//
// TargetAudience defines who is the TA user
// ALL_USER means everyone can receive the bulletinboard
// NEWBIE_D0 means only new users in the day of the register day
type TargetAudience int32

const (
	TargetAudience_DUMMYUSER TargetAudience = 0
	TargetAudience_ALL_USER  TargetAudience = 1
	TargetAudience_NEWBIE_D0 TargetAudience = 2
)

var TargetAudience_name = map[int32]string{
	0: "DUMMYUSER",
	1: "ALL_USER",
	2: "NEWBIE_D0",
}
var TargetAudience_value = map[string]int32{
	"DUMMYUSER": 0,
	"ALL_USER":  1,
	"NEWBIE_D0": 2,
}

func (x TargetAudience) String() string {
	return proto.EnumName(TargetAudience_name, int32(x))
}
func (TargetAudience) EnumDescriptor() ([]byte, []int) { return fileDescriptor0, []int{1} }

type SystemMessage struct {
	Text  string `protobuf:"bytes,1,opt,name=text" json:"text"`
	Color string `protobuf:"bytes,2,opt,name=color" json:"color"`
}

func (m *SystemMessage) Reset()                    { *m = SystemMessage{} }
func (m *SystemMessage) String() string            { return proto.CompactTextString(m) }
func (*SystemMessage) ProtoMessage()               {}
func (*SystemMessage) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

func (m *SystemMessage) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SystemMessage) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

// VersionNotification table
type VersionStatus struct {
	Type            string `protobuf:"bytes,1,opt,name=type" json:"type"`
	Text            string `protobuf:"bytes,2,opt,name=text" json:"text"`
	Img             string `protobuf:"bytes,3,opt,name=img" json:"img"`
	Link            string `protobuf:"bytes,4,opt,name=link" json:"link"`
	UpdateTimeStamp int64  `protobuf:"varint,5,opt,name=updateTimeStamp" json:"updateTimeStamp"`
	RenewTimeStamp  int64  `protobuf:"varint,6,opt,name=renewTimeStamp" json:"renewTimeStamp"`
}

func (m *VersionStatus) Reset()                    { *m = VersionStatus{} }
func (m *VersionStatus) String() string            { return proto.CompactTextString(m) }
func (*VersionStatus) ProtoMessage()               {}
func (*VersionStatus) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1} }

func (m *VersionStatus) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *VersionStatus) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *VersionStatus) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *VersionStatus) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *VersionStatus) GetUpdateTimeStamp() int64 {
	if m != nil {
		return m.UpdateTimeStamp
	}
	return 0
}

func (m *VersionStatus) GetRenewTimeStamp() int64 {
	if m != nil {
		return m.RenewTimeStamp
	}
	return 0
}

// swagger:model ButlerMsg
type ButlerMsg struct {
	Text          string            `protobuf:"bytes,1,opt,name=text" json:"text"`
	Picture       string            `protobuf:"bytes,2,opt,name=picture" json:"picture"`
	Timestamp     int64             `protobuf:"varint,3,opt,name=timestamp" json:"timestamp"`
	Sender        *user.DisplayInfo `protobuf:"bytes,4,opt,name=sender" json:"sender"`
	Tag           *TagInfo          `protobuf:"bytes,5,opt,name=tag" json:"tag"`
	GroupCallID   string            `json:"groupCallID"`
	ID            string            `json:"id"`
	Category      MsgCategoryType   `json:"category"`
	Type          Type              `json:"type"`
	LayoutType    int               `json:"layoutType"`
	ComponentID   string            `json:"componentID"`
	PostID        string            `json:"postID"`
	ActionURL     string            `json:"actionURL,omitempty"`
	TutorialURL   string            `json:"tutorialURL,omitempty"`
	// ContractApplicationInfo is only used for contract application
	ContractApplicationInfo *contractapplication.ContractApplicationInfoForIAM `json:"contractApplicationInfo,omitempty"`
	UserStyleType string            `json:"userStyleType"`
}

func (m *ButlerMsg) Reset()                    { *m = ButlerMsg{} }
func (m *ButlerMsg) String() string            { return proto.CompactTextString(m) }
func (*ButlerMsg) ProtoMessage()               {}
func (*ButlerMsg) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{2} }

func (m *ButlerMsg) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ButlerMsg) GetPicture() string {
	if m != nil {
		return m.Picture
	}
	return ""
}

func (m *ButlerMsg) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *ButlerMsg) GetSender() *user.DisplayInfo {
	if m != nil {
		return m.Sender
	}
	return nil
}

func (m *ButlerMsg) GetTag() *TagInfo {
	if m != nil {
		return m.Tag
	}
	return nil
}

// swagger:model ButlerMsgInfo
type ButlerMsgInfo struct {
	ButlerMsgs []*ButlerMsg `protobuf:"bytes,1,rep,name=butlerMsgs" json:"butlerMsgs"`
	Cursor     string       `protobuf:"bytes,2,opt,name=cursor" json:"cursor"`
}

func (m *ButlerMsgInfo) Reset()                    { *m = ButlerMsgInfo{} }
func (m *ButlerMsgInfo) String() string            { return proto.CompactTextString(m) }
func (*ButlerMsgInfo) ProtoMessage()               {}
func (*ButlerMsgInfo) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{3} }

func (m *ButlerMsgInfo) GetButlerMsgs() []*ButlerMsg {
	if m != nil {
		return m.ButlerMsgs
	}
	return nil
}

func (m *ButlerMsgInfo) GetCursor() string {
	if m != nil {
		return m.Cursor
	}
	return ""
}

type TagInfo struct {
	Token *i18n.Token `protobuf:"bytes,1,opt,name=token" json:"token"`
	Color string      `protobuf:"bytes,2,opt,name=color" json:"color"`
}

func (m *TagInfo) Reset()                    { *m = TagInfo{} }
func (m *TagInfo) String() string            { return proto.CompactTextString(m) }
func (*TagInfo) ProtoMessage()               {}
func (*TagInfo) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{4} }

func (m *TagInfo) GetToken() *i18n.Token {
	if m != nil {
		return m.Token
	}
	return nil
}

func (m *TagInfo) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

type SystemMsgParam struct {
	ReceiveUserID string              `protobuf:"bytes,1,opt,name=ReceiveUserID" json:"ReceiveUserID"`
	Message       string              `protobuf:"bytes,2,opt,name=Message" json:"Message"`
	Picture       string              `protobuf:"bytes,5,opt,name=Picture" json:"Picture"`
	ContentType   chat.MessageMsgType `protobuf:"varint,6,opt,name=ContentType,enum=chat.MessageMsgType" json:"ContentType"`
}

func (m *SystemMsgParam) Reset()                    { *m = SystemMsgParam{} }
func (m *SystemMsgParam) String() string            { return proto.CompactTextString(m) }
func (*SystemMsgParam) ProtoMessage()               {}
func (*SystemMsgParam) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{5} }

func (m *SystemMsgParam) GetReceiveUserID() string {
	if m != nil {
		return m.ReceiveUserID
	}
	return ""
}

func (m *SystemMsgParam) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *SystemMsgParam) GetPicture() string {
	if m != nil {
		return m.Picture
	}
	return ""
}

func (m *SystemMsgParam) GetContentType() chat.MessageMsgType {
	if m != nil {
		return m.ContentType
	}
	return chat.Message_TEXT
}

func init() {
	proto.RegisterType((*SystemMessage)(nil), "official.systemMessage")
	proto.RegisterType((*VersionStatus)(nil), "official.versionStatus")
	proto.RegisterType((*ButlerMsg)(nil), "official.ButlerMsg")
	proto.RegisterType((*ButlerMsgInfo)(nil), "official.ButlerMsgInfo")
	proto.RegisterType((*TagInfo)(nil), "official.TagInfo")
	proto.RegisterType((*SystemMsgParam)(nil), "official.SystemMsgParam")
	proto.RegisterEnum("official.Type", Type_name, Type_value)
	proto.RegisterEnum("official.TargetAudience", TargetAudience_name, TargetAudience_value)
}

func init() { proto.RegisterFile("models/official/official.proto", fileDescriptor0) }

var fileDescriptor0 = []byte{
	// 589 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x93, 0x4d, 0x6f, 0xd3, 0x4c,
	0x10, 0xc7, 0xeb, 0x38, 0x49, 0x9b, 0xc9, 0x93, 0xd4, 0xcf, 0x16, 0x2a, 0x0b, 0x21, 0x54, 0x02,
	0x42, 0xa1, 0x87, 0x00, 0xe9, 0x01, 0x90, 0xb8, 0xa4, 0xcd, 0x56, 0x8a, 0xd4, 0xb8, 0x91, 0xed,
	0x50, 0x55, 0x42, 0x8a, 0x5c, 0x67, 0x6a, 0xac, 0xfa, 0x4d, 0xbb, 0xeb, 0x42, 0x3e, 0x0f, 0x07,
	0x8e, 0x7c, 0x45, 0xb4, 0x6b, 0xc7, 0x29, 0x11, 0x5c, 0x36, 0x33, 0xbf, 0xf9, 0xcf, 0x78, 0x77,
	0x66, 0x02, 0xcf, 0xe2, 0x74, 0x89, 0x11, 0x7f, 0x93, 0xde, 0xde, 0x86, 0x7e, 0xe8, 0x45, 0x95,
	0x31, 0xc8, 0x58, 0x2a, 0x52, 0xb2, 0xb7, 0xf6, 0x9f, 0x1c, 0x96, 0xca, 0x9c, 0x23, 0x53, 0x47,
	0xa1, 0xa8, 0x78, 0xf8, 0xee, 0x43, 0xa2, 0x8e, 0x2d, 0xee, 0x7f, 0xf5, 0x84, 0x3a, 0x0a, 0xde,
	0xfb, 0x08, 0x1d, 0xbe, 0xe2, 0x02, 0xe3, 0x29, 0x72, 0xee, 0x05, 0x48, 0x08, 0xd4, 0x05, 0x7e,
	0x17, 0xa6, 0x76, 0xa4, 0xf5, 0x5b, 0xb6, 0xb2, 0xc9, 0x23, 0x68, 0xf8, 0x69, 0x94, 0x32, 0xb3,
	0xa6, 0x60, 0xe1, 0xf4, 0x7e, 0x69, 0xd0, 0xb9, 0x47, 0xc6, 0xc3, 0x34, 0x71, 0x84, 0x27, 0x72,
	0xae, 0x72, 0x57, 0x19, 0x56, 0xb9, 0xab, 0x6c, 0x53, 0xaf, 0xf6, 0xa0, 0x9e, 0x01, 0x7a, 0x18,
	0x07, 0xa6, 0xae, 0x90, 0x34, 0xa5, 0x2a, 0x0a, 0x93, 0x3b, 0xb3, 0x5e, 0xa8, 0xa4, 0x4d, 0xfa,
	0xb0, 0x9f, 0x67, 0x4b, 0x4f, 0xa0, 0x1b, 0xc6, 0xe8, 0x08, 0x2f, 0xce, 0xcc, 0xc6, 0x91, 0xd6,
	0xd7, 0xed, 0x6d, 0x4c, 0x5e, 0x41, 0x97, 0x61, 0x82, 0xdf, 0x36, 0xc2, 0xa6, 0x12, 0x6e, 0xd1,
	0xde, 0x4f, 0x0d, 0x5a, 0xa7, 0xb9, 0x88, 0x90, 0x4d, 0x79, 0xf0, 0xd7, 0x97, 0x9a, 0xb0, 0x9b,
	0x85, 0xbe, 0xc8, 0x19, 0x96, 0x17, 0x5e, 0xbb, 0xe4, 0x29, 0xb4, 0x44, 0x18, 0x23, 0x57, 0xe5,
	0x75, 0x55, 0x7e, 0x03, 0xc8, 0x6b, 0x68, 0x72, 0x4c, 0x96, 0xc8, 0xd4, 0x0b, 0xda, 0xc3, 0xff,
	0x07, 0x6a, 0x26, 0xe3, 0x90, 0x67, 0x91, 0xb7, 0x9a, 0x24, 0xb7, 0xa9, 0x5d, 0x0a, 0xc8, 0x0b,
	0xd0, 0x85, 0x17, 0xa8, 0xa7, 0x48, 0x5d, 0x35, 0x61, 0xd7, 0x0b, 0x94, 0x4e, 0x46, 0x7b, 0x5f,
	0xa0, 0x53, 0x5d, 0x54, 0x52, 0x72, 0x02, 0x70, 0xb3, 0x06, 0xdc, 0xd4, 0x8e, 0xf4, 0x7e, 0x7b,
	0x78, 0xb0, 0x49, 0xae, 0xc4, 0xf6, 0x03, 0x19, 0x39, 0x84, 0xa6, 0x9f, 0x33, 0x5e, 0x0d, 0xae,
	0xf4, 0x7a, 0xa7, 0xb0, 0x5b, 0x7e, 0x8d, 0x3c, 0x87, 0x86, 0x48, 0xef, 0x30, 0x51, 0x5d, 0x68,
	0x0f, 0xdb, 0x03, 0xb5, 0x33, 0xae, 0x44, 0x76, 0x11, 0xf9, 0xc7, 0xf4, 0x7f, 0x68, 0xd0, 0x75,
	0x8a, 0xcd, 0xe1, 0xc1, 0xcc, 0x63, 0x5e, 0x4c, 0x5e, 0x42, 0xc7, 0x46, 0x1f, 0xc3, 0x7b, 0x9c,
	0x73, 0x64, 0x93, 0x71, 0xd9, 0xd9, 0x3f, 0xa1, 0x6c, 0x71, 0xb9, 0x6b, 0xeb, 0x16, 0xaf, 0x57,
	0xcf, 0x84, 0xdd, 0x59, 0xd9, 0xfc, 0x46, 0x11, 0x29, 0x5d, 0xf2, 0x1e, 0xda, 0x67, 0x69, 0x22,
	0x30, 0x11, 0xae, 0xdc, 0x2f, 0x39, 0xdd, 0xee, 0xf0, 0xf1, 0x40, 0xed, 0x71, 0x99, 0x3d, 0x88,
	0x79, 0x20, 0x83, 0xf6, 0x43, 0xe5, 0xf1, 0x0d, 0xd4, 0xe5, 0x2f, 0xe9, 0x40, 0x6b, 0x3c, 0x9f,
	0x4e, 0xaf, 0xdd, 0xeb, 0x19, 0x35, 0x76, 0x08, 0x81, 0xae, 0xe3, 0xda, 0x74, 0x34, 0xa5, 0xf6,
	0xc2, 0xba, 0x74, 0x27, 0xe7, 0x86, 0x26, 0xd9, 0x39, 0x1d, 0xb9, 0x73, 0x9b, 0x2e, 0xe6, 0xb3,
	0xf1, 0xc8, 0xa5, 0x46, 0x8d, 0x74, 0x01, 0xe8, 0x67, 0x6a, 0xb9, 0x0b, 0x8b, 0x5e, 0x39, 0x86,
	0x4e, 0x0e, 0x60, 0xdf, 0xb9, 0x76, 0x5c, 0x3a, 0x5d, 0x8c, 0x2c, 0xeb, 0x72, 0x6e, 0x9d, 0x51,
	0xa3, 0x7e, 0xfc, 0x09, 0xba, 0xae, 0xc7, 0x02, 0x14, 0xa3, 0x7c, 0x19, 0x62, 0xe2, 0x6f, 0xbe,
	0x36, 0x77, 0xa8, 0x6d, 0xec, 0x90, 0xff, 0x60, 0x6f, 0x74, 0x71, 0xb1, 0x50, 0x9e, 0x26, 0x83,
	0x16, 0xbd, 0x3a, 0x9d, 0xd0, 0xc5, 0xf8, 0xad, 0x51, 0xbb, 0x69, 0xaa, 0xff, 0xe1, 0xc9, 0xef,
	0x00, 0x00, 0x00, 0xff, 0xff, 0xe3, 0x77, 0x57, 0x4a, 0xfb, 0x03, 0x00, 0x00,
}
