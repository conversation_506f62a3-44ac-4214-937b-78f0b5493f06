package style

import (
	"testing"
	"time"

	"github.com/stretchr/testify/suite"

	"github.com/17media/api/base/testutil"
)

type mockFuncs struct {
	testutil.MockFuncs
}

type styleSuite struct {
	suite.Suite

	mockFuncs *mockFuncs
}

func (s *styleSuite) SetupSubTest() {
	s.mockFuncs = &mockFuncs{}
	TimeNow = s.mockFuncs.TimeNow
}

func TestStyleSuite(t *testing.T) {
	suite.Run(t, new(styleSuite))
}

func (s *styleSuite) TestComputeAvailableTime() {
	tests := []struct {
		desc     string
		slots    []TimeSlot
		expStart int64
		expEnd   int64
	}{
		{
			desc: "case 1",
			slots: []TimeSlot{
				{StartTime: 10, EndTime: 20},
				{StartTime: 50, EndTime: 120},
				{StartTime: 100, EndTime: 180},
			},
			expStart: 50,
			expEnd:   180,
		},
		{
			desc: "case 2",
			slots: []TimeSlot{
				{StartTime: 10, EndTime: 120},
			},
			expStart: 10,
			expEnd:   120,
		},
		{
			desc: "case 3",
			slots: []TimeSlot{
				{StartTime: 10, EndTime: 0},
				{StartTime: 1000, EndTime: 1001},
			},
			expStart: 10,
			expEnd:   0,
		},
		{
			desc: "case 4",
			slots: []TimeSlot{
				{StartTime: 10, EndTime: 80},
				{StartTime: 100, EndTime: 150},
				{StartTime: 200, EndTime: 220},
			},
			expStart: 100,
			expEnd:   150,
		},
		{
			desc: "case 5",
			slots: []TimeSlot{
				{StartTime: 10, EndTime: 70},
				{StartTime: 90, EndTime: 120},
				{StartTime: 120, EndTime: 150},
			},
			expStart: 90,
			expEnd:   150,
		},
		{
			desc: "case 6",
			slots: []TimeSlot{
				{StartTime: 10, EndTime: 60},
				{StartTime: 50, EndTime: 120},
				{StartTime: 110, EndTime: 170},
				{StartTime: 150, EndTime: 160},
			},
			expStart: 10,
			expEnd:   170,
		},
	}

	for _, t := range tests {
		s.Run(t.desc, func() {
			s.mockFuncs.On("TimeNow").Return(time.Unix(int64(100), 0)).Once()
			start, end := computeAvailableTime(t.slots)
			s.Require().Equal(t.expStart, start)
			s.Require().Equal(t.expEnd, end)
		})
	}
}
