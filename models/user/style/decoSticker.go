package style

import (
	"fmt"
	"strconv"
	"time"

	"github.com/slack-go/slack"

	"github.com/17media/api/models"
	liveCfg "github.com/17media/api/stores/live/config"
)

// DecoSticker is the decorative sticker detail
type DecoSticker struct {
	Type    int32  `json:"type" bson:"type"`
	StyleID string `json:"styleID" bson:"styleID"`
	URL     string `json:"url" bson:"url"`
	// DefaultDisplayName is used only when the translation cannot be gotton from lokalise
	DefaultDisplayName string      `json:"defaultDisplayName" bson:"defaultDisplayName"`
	TextColor          string      `json:"textColor" bson:"textColor"`
	AnimationID        string      `json:"animationID" bson:"animationID"`
	StartTime          int64       `json:"startTime" bson:"startTime"`
	EndTime            int64       `json:"endTime" bson:"endTime"`
	Deleted            interface{} `json:"deleted,omitempty" bson:"deleted,omitempty"`
}

func (c *DecoSticker) GetStyleType() StyleType {
	return StyleTypeDecoSticker
}

func (c *DecoSticker) GetStyleID() string {
	return c.StyleID
}

func (c *DecoSticker) GetEndTime() int64 {
	return c.EndTime
}

func (c *DecoSticker) GetDefaultDisplayName() string {
	return c.DefaultDisplayName
}

func (c *DecoSticker) SetDefaultDisplayName(s string) {
	c.DefaultDisplayName = s
	return
}

func (c *DecoSticker) ConvertToSetting(opt DisplayI18nOptions) VirtualRewardSetting {
	return &DecoStickerSettings{
		StyleID:      c.StyleID,
		DisplayNames: opt.DisplayNames,
		URL:          c.URL,
		TextColor:    c.TextColor,
		StartTime:    c.StartTime,
		EndTime:      c.EndTime,
		AnimationID:  c.AnimationID,
	}
}

func (c *DecoSticker) ConvertToRewardRes(userStyle *UserStyle, opt DefaultDisplayOptions) interface{} {
	_, endTime := computeAvailableTime(userStyle.TimeSlots)
	styleID := int32(-1)
	if i64, err := strconv.ParseInt(c.StyleID, 10, 32); err != nil {
		return nil
	} else if styleID = int32(i64); styleID < 0 {
		return nil
	}
	return liveCfg.DecoStickerCore{
		Style:         styleID,
		Name:          opt.DisplayName,
		Type:          models.DecoStickerTypeEditable,
		ImageURL:      c.URL,
		PreviewURL:    c.URL,
		BackgroundURL: c.URL,
		TextColor:     c.TextColor,
		AnimationID:   c.AnimationID,
		End:           endTime,
	}
}

// DecoStickerSettings is the settings for decorative sticker
type DecoStickerSettings struct {
	StyleID      string          `json:"styleID"`
	DisplayNames []LanguageValue `json:"displayNames"`
	URL          string          `json:"url"`
	TextColor    string          `json:"textColor"`
	StartTime    int64           `json:"startTime"`
	EndTime      int64           `json:"endTime"`
	AnimationID  string          `json:"animationID"`
}

func (c *DecoStickerSettings) GetStyleID() string {
	return c.StyleID
}

func (c *DecoStickerSettings) GetDisplayNames() []LanguageValue {
	return c.DisplayNames
}

func (c *DecoStickerSettings) GetStyleType() StyleType {
	return StyleTypeDecoSticker
}

func (c *DecoStickerSettings) Validate() error {
	if c.StyleID == "" {
		return ErrMissingStyleID
	}
	if !reDecoStickerID.MatchString(c.StyleID) {
		return ErrInvalidStyleID
	}
	return validateTimeRange(c.StartTime, c.EndTime)
}

func (c *DecoStickerSettings) ConvertToReward(opt DefaultDisplayOptions) (VirtualReward, error) {
	return &DecoSticker{
		Type:               StyleTypeDecoSticker.Int32(),
		StyleID:            c.StyleID,
		URL:                c.URL,
		DefaultDisplayName: opt.DisplayName,
		TextColor:          c.TextColor,
		AnimationID:        c.AnimationID,
		StartTime:          c.StartTime,
		EndTime:            c.EndTime,
	}, nil
}

type typeDecoSticker int32

const (
	minDecoStickerEquipment = 0
	maxDecoStickerEquipment = 1
)

func (t typeDecoSticker) String() string {
	return "decoSticker"
}

func (t typeDecoSticker) Int32() int32 {
	return int32(StyleTypeDecoSticker)
}

func (t typeDecoSticker) NewRewardEntity() VirtualReward {
	return &DecoSticker{}
}

func (t typeDecoSticker) NewRewardSliceEntity() interface{} {
	return []*DecoSticker{}
}

func (t typeDecoSticker) ConvertToListUserReward(styles []interface{}) interface{} {
	res := []liveCfg.DecoStickerCore{}
	for _, style := range styles {
		style, ok := style.(liveCfg.DecoStickerCore)
		if !ok {
			continue
		}
		res = append(res, style)
	}
	return res
}

func (t typeDecoSticker) ConvertToListUserRewardRes(styles interface{}) ListUserRewardRes {
	res := []liveCfg.DecoStickerCore{}
	if stickers, ok := styles.([]liveCfg.DecoStickerCore); ok {
		res = stickers
	}

	return &ListUserDecoStickerRes{
		Type:         t.Int32(),
		DecoStickers: res,
		EquipmentMin: minDecoStickerEquipment,
		EquipmentMax: maxDecoStickerEquipment,
	}
}

func (t typeDecoSticker) GetEquipmentCount() equipmentCount {
	return equipmentCount{
		Min: minDecoStickerEquipment,
		Max: maxDecoStickerEquipment,
	}
}

type ListUserDecoStickerRes struct {
	Type         int32                     `json:"type"`
	DecoStickers []liveCfg.DecoStickerCore `json:"decoStickers"`
	EquipmentMin int                       `json:"equipmentMin"`
	EquipmentMax int                       `json:"equipmentMax"`
}

func (l *ListUserDecoStickerRes) SetSampleComment(s *models.CommentMsg) {
	return
}

func (l *ListUserDecoStickerRes) SetValidEventURL(url string) {
	return
}

func (l *ListUserDecoStickerRes) Len() int {
	return len(l.DecoStickers)
}

func (l *ListUserDecoStickerRes) ParseToCSVRows() any {
	rows := make([]ListUserOwnedDecoStickerRow, l.Len())
	for i := range rows {
		et := l.DecoStickers[i].End
		endTimeText := permanentEndTimeMsg
		if et > 0 {
			endTimeText = time.Unix(et, 0).Format(time.RFC3339)
		}
		rows[i] = ListUserOwnedDecoStickerRow{
			StyleID:            l.DecoStickers[i].Style,
			DefaultDisplayName: l.DecoStickers[i].Name,
			URL:                l.DecoStickers[i].ImageURL,
			EndTime:            endTimeText,
		}
	}
	return rows
}

func (l *ListUserDecoStickerRes) ParseToSlackBlocks() []*slack.ContextBlock {
	blocks := []*slack.ContextBlock{}
	for _, s := range l.DecoStickers {
		et := permanentEndTimeMsg
		if s.End != 0 {
			et = time.Unix(s.End, 0).Format(time.RFC3339)
		}

		blocks = append(blocks, slack.NewContextBlock("",
			slack.NewImageBlockElement(s.ImageURL, fmt.Sprintf("%v", s.Style)),
			slack.NewTextBlockObject(ElementMarkdown, fmt.Sprintf(
				"*%v* %s %s", s.Style, s.Name, et,
			), false, false),
		))
	}

	return blocks
}
