package award

// UserStyleRewardSetting defines the configuration for a userstyle reward.
// This includes receiver info, style attributes, and optional time constraints.
type UserStyleRewardSetting struct {
	ReceiverID string `json:"receiverID" bson:"receiverID"`
	StyleType  string `json:"styleType" bson:"styleType"`
	StyleID    string `json:"styleID" bson:"styleID"`

	TimeMode    TimeMode `json:"timeMode" bson:"timeMode"`                       // "fixed" or "dynamic"
	StartTime   int64    `json:"startTime,omitempty" bson:"startTime,omitempty"` // For fixed mode; UNIX timestamp (0 = permanent)
	EndTime     int64    `json:"endTime,omitempty" bson:"endTime,omitempty"`     // For fixed mode; UNIX timestamp
	DurationSec int64    `json:"duration,omitempty" bson:"duration,omitempty"`   // For dynamic mode; valid duration in seconds
}

// TimeMode specifies how the reward's active period is determined.
type TimeMode string

const (
	TimeModeFixed   TimeMode = "fixed"   // Use predefined StartTime and EndTime.
	TimeModeDynamic TimeMode = "dynamic" // Compute StartTime and EndTime at delivery time using Duration.
)

// UserStyleDeliveryResult contains post-delivery metadata for userstyle rewards.
type UserStyleDeliveryResult struct {
	SendNotifyNow   []string // Notify these users immediately
	SendNotifyLater []string // Notify these users after certain conditions
}

// RewardType returns the reward type associated with this delivery result.
func (r *UserStyleDeliveryResult) RewardType() RewardType {
	return RewardUserStyle
}
