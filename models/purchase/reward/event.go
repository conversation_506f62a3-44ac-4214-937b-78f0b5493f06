package reward

import (
	"time"
)

// Rule ...
type Rule struct {
	<PERSON>ward       Reward            `yaml:"reward"`
	Requirements []RuleRequirement `yaml:"requirements"`
}

// IsValid ...
func (c Rule) IsValid() bool {
	if c.Reward.IsValid() == false ||
		len(c.Requirements) == 0 {
		return false
	}

	for _, r := range c.Requirements {
		if r.<PERSON>ali<PERSON>() == false {
			return false
		}
	}

	return true
}

type Condition struct {
	DailySignInValidDays []int32 `yaml:"dailySignInValidDays"`
	RetentionUserOnly    bool    `yaml:"retentionUserOnly"`
}

type SecretIAPCondition struct {
	ID           string    `yaml:"ID"`
	Condition    Condition `yaml:"condition"`
	DisplayHours int32     `yaml:"displayHours"`
}

// RegisterSecretIDParam is the model thats defines parameters to check if user is qualified for some secretIDs
type RegisterSecretIDParam struct {
	CurrentSignInDay int32
}

// Event ...
type Event struct {
	EventID           string             `yaml:"eventID"`
	StartTime         *time.Time         `yaml:"startTime"`
	EndTime           *time.Time         `yaml:"endTime"`
	EventRequirements []EventRequirement `yaml:"eventRequirements"`
	Rules             []Rule             `yaml:"rules"`
}

// IsValid ...
func (c Event) IsValid() bool {
	if len(c.Rules) == 0 ||
		len(c.EventID) == 0 ||
		len(c.EventRequirements) == 0 {
		return false
	}

	for _, e := range c.EventRequirements {
		if e.IsValid() == false {
			return false
		}
	}

	for _, e := range c.Rules {
		if e.IsValid() == false {
			return false
		}
	}

	return true
}

// EventInfo contains event basic information like ID, start timestamp, end timestamp
//
//	the start/end timestamp will be zero if event didn't define start time or end time
type EventInfo struct {
	ID             string
	StartTimestamp int64
	EndTimestamp   int64
}
