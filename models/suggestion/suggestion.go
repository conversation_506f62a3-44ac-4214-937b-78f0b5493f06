package suggestion

import (
	"strconv"

	ctModel "github.com/17media/api/models/contract"
	"github.com/17media/api/models/keys"
)

// UserSuggestion is a model corresponding to mongo table UserSuggestion
type UserSuggestion struct {
	UserID                 string   `json:"userID"`
	LastActive             int64    `json:"lastActive"`
	Timestamp              int64    `json:"timestamp"`
	FilteredSuggestionList []string `json:"filteredSuggestionList"`
}

// SkipInfoRel is a model corresponding to mongo table SuggestionSkips
type SkipInfoRel struct {
	UserID       string `json:"userID" rel:"from,name=userID"`
	TargetUserID string `json:"targetUserID" rel:"to,name=targetUserID"`
	Timestamp    int64  `json:"timestamp" rel:"sorted,name=timestamp"`
}

// ForwardTableName indicates the forward table name
func (r *SkipInfoRel) ForwardTableName() string {
	return keys.TabSuggestionSkipsRel.ForwardTableName()
}

// BackwardTableName indicates the forward table name
func (r *SkipInfoRel) BackwardTableName() string {
	return keys.TabSuggestionSkipsRel.BackwardTableName()
}

// Category ...
type Category int32

const (
	CategoryDummy  Category = 0
	CategoryFemale Category = 1
	CategoryMale   Category = 2
	CategoryTW     Category = 3
	CategoryJP     Category = 4
	CategoryHK     Category = 5
	CategoryMENA   Category = 6
	CategoryUS     Category = 7
	CategorySG     Category = 8
	CategoryMY     Category = 9
	CategoryIN     Category = 10
)

var CategoryValue = map[string]int32{
	"DUMMY":  0,
	"FEMALE": 1,
	"MALE":   2,
	"TW":     3,
	"JP":     4,
	"HK":     5,
	"MENA":   6,
	"US":     7,
	"SG":     8,
	"MY":     9,
	"IN":     10,
}

var categoryName = map[int32]string{
	0:  "DUMMY",
	1:  "FEMALE",
	2:  "MALE",
	3:  "TW",
	4:  "JP",
	5:  "HK",
	6:  "MENA",
	7:  "US",
	8:  "SG",
	9:  "MY",
	10: "IN",
}

func (x Category) IsGender() bool {
	return x == CategoryFemale || x == CategoryMale
}

func (x Category) String() string {
	if x.IsGender() {
		if x == CategoryFemale {
			return ctModel.Gender_FEMALE.String()
		}
		if x == CategoryMale {
			return ctModel.Gender_MALE.String()
		}
	}

	s, ok := categoryName[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
