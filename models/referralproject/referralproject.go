package referralproject

import (
	i18nModel "github.com/17media/api/models/i18n"
	"github.com/17media/api/models/keys"
	missionModel "github.com/17media/api/models/mission"
	userModel "github.com/17media/api/models/user"
)

// Mode defines what group of users is valid for the referralproject
//
// | Mode | Value |
// | --- | --- |
// | ModeNoOne | 0 |
// | ModeWhiteList | 1 |
// | ModeContractedStreamers | 2 |
type Mode int32

const (
	// ModeNoOne means no one can join the project
	ModeNoOne Mode = 0
	// ModeWhiteList means only users in whiteList can join the project
	ModeWhiteList Mode = 1
	// ModeContractedStreamers means all contracted streamers can join the project
	ModeContractedStreamers Mode = 2
)

// LayoutType defines how layout would display depending on each user's situation
//
// | LayoutType | Value |
// | --- | --- |
// | LayoutTypeHidden | 0 |
// | LayoutTypeWithoutReferral | 1 |
// | LayoutTypeWithReferral | 2 |
type LayoutType int32

const (
	// LayoutTypeHidden means it will be hidden
	LayoutTypeHidden LayoutType = 0
	// LayoutTypeWithoutReferral means the user needs to input referralCode
	LayoutTypeWithoutReferral LayoutType = 1
	// LayoutTypeWithReferral means the user has been referred, he/she can do the missions
	LayoutTypeWithReferral LayoutType = 2
)

// CompleteStatus defines the progress rate of referral missions
// ex: has he/she completed all mission ?
//
// | CompleteStatus | Value |
// | --- | --- |
// | CompleteStatusNone | 0 |
// | CompleteStatusInProgress | 1 |
// | CompleteStatusAllComplete | 2 |
type CompleteStatus int32

const (
	// CompleteStatusNone is a dummy value
	CompleteStatusNone CompleteStatus = 0
	// CompleteStatusInProgress means user is still in progress
	CompleteStatusInProgress CompleteStatus = 1
	// CompleteStatusAllComplete means user has completed all referral missions
	CompleteStatusAllComplete CompleteStatus = 2
)

// ResMissionInfo will give all information that users need in referralMission page
type ResMissionInfo struct {
	LayoutType  LayoutType                 `json:"layoutType"`
	Duetime     int64                      `json:"duetime"`
	ReferOpenID string                     `json:"referOpenID"`
	Missions    []*missionModel.ResMission `json:"missions"`
}

// ResRewardInfo is a defined model that users need after pressing the `recevie` button
type ResRewardInfo struct {
	CompleteStatus     CompleteStatus                 `json:"completeStatus"`
	CompleteToastTitle *i18nModel.Token               `json:"completeToastTitle"`
	CompleteToastDesc  *i18nModel.Token               `json:"completeToastDesc"`
	Reward             *missionModel.ResReceiveReward `json:"reward"`
}

// UserProgress records the progress of each user
type UserProgress struct {
	UserInfo            *userModel.DisplayInfo `json:"userInfo"`
	NumMissionCompleted int32                  `json:"numMissionCompleted"`
}

// ResProgress ...
// swagger:model ResProgress
type ResProgress struct {
	Cursor   string          `json:"cursor"`
	Progress []*UserProgress `json:"progress"`
}

// swagger:model referralInfo
type Info struct {
	EndTime      int64    `json:"endTime"`
	ReferralCode string   `json:"referralCode"`
	ReferralMsg  string   `json:"referralMsg"`
	ReferralURL  string   `json:"referralURL"`
	Month        int32    `json:"month"`
	MonthlyAward float64  `json:"monthlyAward"`
	RuleTitle    string   `json:"ruleTitle"`
	Rules        []string `json:"rules"`
}

// swagger:model referralAwardLog
type AwardLog struct {
	Date      string  `json:"date"`
	NumInvite int32   `json:"numInvite"`
	Award     float64 `json:"award"`
}

// Info includes all required info that streamer should know in the "referral info" page
// swagger:model resReferralInfo
type ResInfo struct {
	Currency  string     `json:"currency"`
	Info      Info       `json:"info"`
	AwardLogs []AwardLog `json:"awardLogs"`
}

// Log is the model that will be stored in `ReferralProjectLogs` relations table
// it logs the relation info between referrer and referral
type Log struct {
	ReferrerID string `json:"referrerID" rel:"from,name=referrerID"`
	ReferralID string `json:"referralID" rel:"to,name=referralID"`
	Code       string `json:"code" rel:"name=code"`
	// time that Referral binds the code
	Timestamp           int64   `json:"timestamp" rel:"sorted,name=timestamp"`
	NumMissionCompleted int32   `json:"numMissionCompleted" rel:"name=numMissionCompleted"`
	Award               float64 `json:"award" rel:"name=award"`
	Currency            string  `json:"currency" rel:"name=currency"`
	ReferralDeviceID    string  `json:"referralDeviceID" rel:"name=referralDeviceID"`
}

// ForwardTableName indicates the forward table name
func (log *Log) ForwardTableName() string {
	return keys.TabReferralProjectLogs.ForwardTableName()
}

// BackwardTableName indicates the backward table name
func (log *Log) BackwardTableName() string {
	return keys.TabReferralProjectLogs.BackwardTableName()
}
