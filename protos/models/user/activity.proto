// To build the proto files, use `make proto`
syntax = "proto3";
package user;

import "models/bbfu/bbfu.proto";

message Income {
    int64 point = 1;
    int64 streamDuration = 2;
    double revenue = 3;
}

message ResOverviewIncome {
    repeated Income recentDays = 1;
    Income thisMonth = 2;
    double lastMonthRevenue = 3;
    // lastMonthPaidRevenue: 結算至上月累積應出帳金額
    double lastMonthPaidRevenue = 4;
    string lastMonthRank = 5;
    bool bbfuEntryEnable = 6;
    bbfu.BBFuInfo bbfuInfo = 7;
    int64 lastMonthPoint = 8;
}

message ResIncome {
    repeated Income income = 1;
}
