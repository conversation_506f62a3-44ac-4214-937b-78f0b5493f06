// To build the proto files, use `make proto`
syntax = "proto3";
package pay;

// *********************************************************
// Definition of Subscription Period
// *********************************************************
// | SubscriptionPeriod | Value |
// | --- | --- |
// | ThreeMonth | 1 |
// swagger:model SubscriptionPeriod
enum SubscriptionPeriod {
    UNKNOWN_PERIOD = 0;
    THREE_MONTH = 1;
}

// *********************************************************
// Definition of Subscription Status
// *********************************************************
// | SubscriptionStatus | Value | Description |
// | --- | --- | --- |
// | Subscribing | 1 | Regular Subscription |
// | Renewed  | 2 | The subscription is renewed by a new one |
// | User Canceled | 3 | The subscription is canceled by user |
// | System Canceled | 4 | The subscription is expired |
// | Renew fail | 5 | Renew the subscription failed |
// | Renew fail and expired | 6 | The subscription can't be renewed and the subscription is expired |
// | User canceled and expired | 7 | User canceled the subscription and the subscription is expired |
// | UPGRADED  | 8 | The subscription is upgraded by a new one |
// swagger:model SubscriptionStatus
enum SubscriptionStatus {
    UNKNOWN_STATUS = 0;
    SUBSCRIBING = 1;
    RENEWED = 2;
    USER_CANCEL = 3;
    SYSTEM_CANCEL = 4;
    RENEW_FAIL = 5;
    RENEW_FAIL_AND_EXPIRE = 6;
    USER_CANCEL_AND_EXPIRE = 7;
    UPGRADED = 8;
}

// ParamCancelSubscription defines the parameter of cancel subscription
// swagger:parameters ParamCancelSubscription
message ParamCancelSubscription {
    // The subscriptionID lists in the subscription log
    // in: path
    string subscriptionID = 1;
}

enum TerminateReason {
    NONE = 0;
    UPGRADE = 1;
    EXPIRED = 2;
}
