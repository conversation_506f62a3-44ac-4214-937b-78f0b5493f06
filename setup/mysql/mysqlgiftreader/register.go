package mysqlgiftreader

import (
	"database/sql"

	"github.com/go-xorm/xorm"

	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/setup/mysql"
	"github.com/17media/dig"
)

func init() {
	RegisterGiftReader(dimanager.DefaultManager)
}

func RegisterGiftReader(m *dimanager.Manager) {
	m.RegisterString("rds_gift_reader_uri", "", "RDSGift reader DB URI")
	type regParams struct {
		dig.In

		ReaderURI *string `name:"rds_gift_reader_uri"`
	}
	m.ProvideConstructor(
		func(p regParams) (xorm.EngineInterface, error) {
			// initialize the mysql driver
			mysql.InitDriver()

			return mysql.GetORM(`gift-reader`, *p.ReaderURI)
		}, `mysqlGiftReaderORM`,
	)

	type dbParams struct {
		dig.In
		ORM xorm.EngineInterface `name:"mysqlGiftReaderORM"`
	}
	m.ProvideConstructor(
		func(p dbParams) *sql.DB {
			// it is design bug in the xorm, =.="
			if eng, ok := p.ORM.(*xorm.Engine); ok {
				return eng.DB().DB
			}
			return nil
		}, `mysqlGiftReader`,
	)
}

// GetMySQLGiftReaderORM returns the orm of MySQLGiftReader object
func GetMySQLGiftReaderORM(m *dimanager.Manager) xorm.EngineInterface {
	var output xorm.EngineInterface
	type params struct {
		dig.In
		MysqlGiftReaderORM xorm.EngineInterface `name:"mysqlGiftReaderORM"`
	}
	fn := func(p params) {
		output = p.MysqlGiftReaderORM
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}

// GetMySQLGiftReader returns the MySQLGiftReader object
func GetMySQLGiftReader(m *dimanager.Manager) *sql.DB {
	var output *sql.DB
	type params struct {
		dig.In
		MysqlGiftReader *sql.DB `name:"mysqlGiftReader"`
	}
	fn := func(p params) {
		output = p.MysqlGiftReader
	}
	if err := m.Invoke(fn); err != nil {
		panic(err)
	}
	return output
}
