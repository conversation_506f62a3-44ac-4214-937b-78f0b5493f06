FROM alpine:edge

RUN \
  echo http://dl-4.alpinelinux.org/alpine/edge/testing >> /etc/apk/repositories &&\
  apk add --no-cache mongodb-tools py2-pip pigz &&\
  pip install pymongo awscli &&\
  mkdir /backup

ENV S3_PATH=17database-backup
ENV AWS_DEFAULT_REGION=us-west-2

COPY entrypoint.sh /usr/local/bin/entrypoint.sh
COPY restore.sh /usr/local/bin/restore.sh
COPY mongouri.py /usr/local/bin/mongouri.py

VOLUME /backup

CMD /usr/local/bin/entrypoint.sh
