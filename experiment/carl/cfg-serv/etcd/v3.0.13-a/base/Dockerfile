# etcd base container.
#
# we'd like to lock the version of binary files.
# this inhouse etcd is larger than csi_run/etcd:latest (81M > 20M )
# due to i'd like to combine with docker-compose to make recreate easier,
# so i rewrite this, won't replace csi_run/etcd, only for tricorder.
#
FROM golang:1.7-alpine

ENV ETCD_DOWNLOAD_URL https://github.com/coreos/etcd/releases/download
ENV ETCD_VERSION v3.0.13

RUN apk add --update --no-cache ca-certificates openssl tar \
    bash curl supervisor git openssh &&\
    wget ${ETCD_DOWNLOAD_URL}/${ETCD_VERSION}/etcd-${ETCD_VERSION}-linux-amd64.tar.gz &&\
    tar xzvf etcd-${ETCD_VERSION}-linux-amd64.tar.gz &&\
    mv etcd-${ETCD_VERSION}-linux-amd64/etcd* /bin/ &&\
    go get github.com/BurntSushi/cmd &&\
    go get github.com/csigo/config &&\
    apk del --purge tar openssl &&\
    rm -rf etcd-${ETCD_VERSION}-linux-amd64* /var/cache/apk/*

# TODO:
