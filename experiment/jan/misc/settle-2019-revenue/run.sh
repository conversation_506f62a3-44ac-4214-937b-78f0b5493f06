#!/bin/bash

env=$1
if [ $env == "-internal" ]
then
  echo "========== use env prod-internal =========="
  go run main.go \
    -disable_fd_monitor=true \
    -etcd_hosts="http://************:2379" \
    -etcd_root="/configs/envs/prod" \
    -mongo_uri="?:?@m17prod-shard-05-02-8rrni.gcp.mongodb.net:27016" \
    -mongo_dst_uri="?:?@m17prod-shard-05-02-8rrni.gcp.mongodb.net:27016" \
    -mongo_bak_uri="?:?@m17prod-shard-05-02-8rrni.gcp.mongodb.net:27016" \
    -mongo_db="17media" \
    -mongo_enableSSL=1 \
    -rds_gift_uri="?:?@tcp(***********:3306)/media17?charset=utf8&&&?:?@tcp(***********:3306)/media17?charset=utf8&&&?:?@tcp(***********:3306)/media17?charset=utf8" \
    -rds_report_uri="?:?@tcp(***********:3306)/media17report?charset=utf8mb4&&&?:?@tcp(***********:3306)/media17report?charset=utf8mb4&&&?:?@tcp(***********:3306)/media17report?charset=utf8mb4" \
    -rds_admin_uri="?:?@tcp(***********:3306)/media17admin?charset=utf8mb4&&&?:?@tcp(***********:3306)/media17admin?charset=utf8mb4&&&?:?@tcp(***********:3306)/media17admin?charset=utf8mb4" \
    -rds_gift_reader_uri="?:?@tcp(***********:3306)/media17?charset=utf8&&&?:?@tcp(***********:3306)/media17?charset=utf8&&&?:?@tcp(***********:3306)/media17?charset=utf8" \
    -redis_cache_uri="redis-11665.internal.c1.us-west1-mz.gce.cloud.redislabs.com:11665" \
    -redis_cache_pwd="IYhH3RNMmOtq4ri6FrvLQXbaj81me54oK" \
    -redis_cache_dst_uri="redis-11665.internal.c1.us-west1-mz.gce.cloud.redislabs.com:11665" \
    -redis_cache_dst_pwd="IYhH3RNMmOtq4ri6FrvLQXbaj81me54oK" \
    -redis_persist_uri="redis-13527.internal.c1.us-west1-mz.gce.cloud.redislabs.com:13527" \
    -redis_persist_pwd="uvqKHV2GtBZegAnFmqSQiPnfJRkdZwOI" \
    -s3_region="us-west-2" \
    -s3_bucket="17app-prod" \
    -awsLegacyAccessKeyID="********************" \
    -awsLegacySecretAccessKey="+ohGShQfLSVTEJgkwZ1tLlyhfUtgUAf8RVyHhT5+" \
    -private_bucket="private.17.media" \
    -awsAccessKeyID="********************" \
    -awsSecretAccessKey="Sx4MfxnrFltCjoM21hMPlSJlJXUZNpwXWb3aETmN" \
    -jwt_private_key="MIIEowIBAAKCAQEAwNzbxt6CaM/CK6Sk06ouQIiVcqWIe6cvyuXtI+cSXxDMuLPp\
       s1azHKod969/rmt3oeZtP+l14QlQ6XDtmLRn17eZ2HWPI/WsZUVUJaNb6TPywV2z\
       5AhbER31TW9Fsmd7aCZo8fMbzUGDmsrerdYvjxy7SVj6C3IhO8KpaF3cSzHXWoZY\
       9wf1zm6lbsxuwg2H+wo0vEIe2gSkS6RvSqD73MFdMY4toLqFYgJEOeNYsg1xXfHa\
       ya0AkIVzfxqr+jzH6rRPZ0DbvclLztxg74TNtOSxLadA0ACAPPspEZpHQYOHlaU6\
       ccnFYK/Nq77oh4QqEkMubvGQyat0OmiyKPPJKQIDAQABAoIBABjj6BFQYjA4r+yG\
       ASe4DOQTPIZhSq36s7ZR2jsJ9VsDhghIX97fT5phciCnK6lNAVGJtYvMC4SReTnH\
       c0KD9ernLRm8gk63xBoFyohY/uGn7Kp79110npwk66WkuK//nvOgEWc4Ytwoml19\
       1SNGQzkLEjizHQIhJpzZ0JdBlc62VEMpiFkH/aKCcJZHHNFHDe5cR7cRC4QPx0V8\
       fYUwkglNy/2E8w8vpEdnYRtIygihD3vCW3uljRD8crppQO8gsa6BG4COkBHCTSb3\
       7VVDDZpjonPe9aXjA7WEyM6+3ssrMdthLH0hM43Tu4ibCT+849XE0Sz9j3j49fGk\
       qZXgoikCgYEA+UFxSisn0NpDCXHLqTEtgL1kB8pzPV5WyGiCZfml5f5elEyRt/bh\
       TKIp/Sg7j5osyZZzGoKncMl9cXG+cF+uun15+hzKOqN8SMYbPfRaFm0q+zPq9ZcH\
       9Kczh4hzBMJnsSBFI2dw9Qiq5j52kdLJu1EhjKVN1VOsH18zuR3Vz3sCgYEAxhTK\
       X4g/lkJRaP8hfNovp6y5LltL7V/5yg1FqSMITitkBEynNaysI8XZHBHHljORtxHW\
       EIvB7FF8+Bk5v38k4TNFxiMdM3cWvtWCeXLkaMiv0XkeIcqYu0UiDz0CxX3l/dlY\
       1HGBxmKS4GUIW7T3V9o7wRb68sXzQeQGzvSm9qsCgYA3dKT1AHcCU504m7XZNdTO\
       4NRBm3xaOgQK4J2qfIm5iweV7Rc6m9Xyi93Vsj7WK1Ito6iGTG1efaXIOKtLKy6f\
       1UzvJbqkoz0Cn/jtptDoTm+SpjFvZKPjfdPMIsKcW3hggzJ+twP9sGj9gwY0WXaC\
       k4FoXlrbzJrt++txEz8/wwKBgBL2dpFCt+OooaaNliUftiQ1Mt0vPEuTPpSLRLn2\
       cpsZKFAJDpDi4O/jxu8a/KJyeWavPHEFECGRg4c00wR0JZGxmOJpBiihI/pM8lqy\
       Fw+49qv1Dl9px8lVyvbufk9tyBDPN+POof/d+/WjdnNhFbTBXasUih2nuav7KnOx\
       lDJVAoGBALTrRWdhAxBidvpJJH4XDrzKidyb2yDF6b2mk1o6u0XCEukuSBZsZswn\
       +GNrW+cBHznG8PnQABUOpzRv/u/lQScJ9AunoczqXm8FQWej/jmMamvKKuinfTwG\
       dvL/5VEruXDRNOdTU5PzwtHrv9bnhArUcHxfwo7oE3yclbvAk6i3" \
    -jwt_public_key="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwNzbxt6CaM/CK6Sk06ou\
       QIiVcqWIe6cvyuXtI+cSXxDMuLPps1azHKod969/rmt3oeZtP+l14QlQ6XDtmLRn\
       17eZ2HWPI/WsZUVUJaNb6TPywV2z5AhbER31TW9Fsmd7aCZo8fMbzUGDmsrerdYv\
       jxy7SVj6C3IhO8KpaF3cSzHXWoZY9wf1zm6lbsxuwg2H+wo0vEIe2gSkS6RvSqD7\
       3MFdMY4toLqFYgJEOeNYsg1xXfHaya0AkIVzfxqr+jzH6rRPZ0DbvclLztxg74TN\
       tOSxLadA0ACAPPspEZpHQYOHlaU6ccnFYK/Nq77oh4QqEkMubvGQyat0OmiyKPPJ\
       KQIDAQAB" \
    -big_query_key="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" \
    -es_uri="https://17media:<EMAIL>" \
    -es_ping_uri="https://17media:<EMAIL>" \
    -es_index="17media" \
    -pub_key="******************************************" \
    -sub_key="******************************************" \
    -sec_key="sec-c-MmYwMWMzZTItZTk1MS00OTJhLWJkYTUtZTlmOTNhZGNiMGJk" 
elif [ $env == "-prod" ]
then
  echo "========== use env prod =========="
  go run main.go \
    -disable_fd_monitor=true \
    -etcd_hosts="http://************:2379" \
    -etcd_root="/configs/envs/prod" \
    -mongo_uri="?:?@m17prod-shard-05-02-8rrni.gcp.mongodb.net:27016" \
    -mongo_dst_uri="?:?@m17prod-shard-05-02-8rrni.gcp.mongodb.net:27016" \
    -mongo_bak_uri="?:?@m17prod-shard-05-02-8rrni.gcp.mongodb.net:27016" \
    -mongo_db="17media" \
  	-mongo_enableSSL=true \
    -redis_cache_uri="redis-11665.c1.us-west1-mz.gce.cloud.redislabs.com:11665" \
    -redis_cache_pwd="IYhH3RNMmOtq4ri6FrvLQXbaj81me54oK" \
    -redis_cache_dst_uri="redis-11665.c1.us-west1-mz.gce.cloud.redislabs.com:11665" \
    -redis_cache_dst_pwd="IYhH3RNMmOtq4ri6FrvLQXbaj81me54oK" \
    -redis_cache_bak_uri="redis-11640.c7.us-west-2-1.ec2.cloud.redislabs.com:11640" \
    -redis_cache_bak_pwd="MJxJmPf2fzhu6WYR" \
    -redis_persist_uri="redis-13527.c1.us-west1-mz.gce.cloud.redislabs.com:13527" \
    -redis_persist_pwd="uvqKHV2GtBZegAnFmqSQiPnfJRkdZwOI" \
    -rds_gift_uri="?:?@tcp(**************:3306)/media17?charset=utf8&&&?:?@tcp(**************:3306)/media17?charset=utf8&&&?:?@tcp(**************:3306)/media17?charset=utf8" \
    -rds_report_uri="?:?@tcp(**************:3306)/media17report?charset=utf8mb4&&&?:?@tcp(**************:3306)/media17report?charset=utf8mb4&&&?:?@tcp(**************:3306)/media17report?charset=utf8mb4" \
    -rds_admin_uri="?:?@tcp(**************:3306)/media17admin?charset=utf8mb4&&&?:?@tcp(**************:3306)/media17admin?charset=utf8mb4&&&?:?@tcp(**************:3306)/media17admin?charset=utf8mb4" \
    -rds_gift_reader_uri="?:?@tcp(*************:3306)/media17?charset=utf8&&&?:?@tcp(*************:3306)/media17?charset=utf8&&&?:?@tcp(*************:3306)/media17?charset=utf8" \
    -rds_high_priority_uri="?:?@tcp(**************:3306)/media17?charset=utf8&&&?:?@tcp(**************:3306)/media17?charset=utf8&&&?:?@tcp(**************:3306)/media17?charset=utf8" \
    -rds_money_uri="?:?@tcp(**************:3306)/media17?charset=utf8&&&?:?@tcp(**************:3306)/media17?charset=utf8&&&?:?@tcp(**************:3306)/media17?charset=utf8" \
    -s3_region="us-west-2" \
    -s3_bucket="17app-prod" \
    -awsAccessKeyID="********************" \
    -awsSecretAccessKey="Sx4MfxnrFltCjoM21hMPlSJlJXUZNpwXWb3aETmN" \
    -jwt_private_key="MIIEowIBAAKCAQEAwNzbxt6CaM/CK6Sk06ouQIiVcqWIe6cvyuXtI+cSXxDMuLPp\
       s1azHKod969/rmt3oeZtP+l14QlQ6XDtmLRn17eZ2HWPI/WsZUVUJaNb6TPywV2z\
       5AhbER31TW9Fsmd7aCZo8fMbzUGDmsrerdYvjxy7SVj6C3IhO8KpaF3cSzHXWoZY\
       9wf1zm6lbsxuwg2H+wo0vEIe2gSkS6RvSqD73MFdMY4toLqFYgJEOeNYsg1xXfHa\
       ya0AkIVzfxqr+jzH6rRPZ0DbvclLztxg74TNtOSxLadA0ACAPPspEZpHQYOHlaU6\
       ccnFYK/Nq77oh4QqEkMubvGQyat0OmiyKPPJKQIDAQABAoIBABjj6BFQYjA4r+yG\
       ASe4DOQTPIZhSq36s7ZR2jsJ9VsDhghIX97fT5phciCnK6lNAVGJtYvMC4SReTnH\
       c0KD9ernLRm8gk63xBoFyohY/uGn7Kp79110npwk66WkuK//nvOgEWc4Ytwoml19\
       1SNGQzkLEjizHQIhJpzZ0JdBlc62VEMpiFkH/aKCcJZHHNFHDe5cR7cRC4QPx0V8\
       fYUwkglNy/2E8w8vpEdnYRtIygihD3vCW3uljRD8crppQO8gsa6BG4COkBHCTSb3\
       7VVDDZpjonPe9aXjA7WEyM6+3ssrMdthLH0hM43Tu4ibCT+849XE0Sz9j3j49fGk\
       qZXgoikCgYEA+UFxSisn0NpDCXHLqTEtgL1kB8pzPV5WyGiCZfml5f5elEyRt/bh\
       TKIp/Sg7j5osyZZzGoKncMl9cXG+cF+uun15+hzKOqN8SMYbPfRaFm0q+zPq9ZcH\
       9Kczh4hzBMJnsSBFI2dw9Qiq5j52kdLJu1EhjKVN1VOsH18zuR3Vz3sCgYEAxhTK\
       X4g/lkJRaP8hfNovp6y5LltL7V/5yg1FqSMITitkBEynNaysI8XZHBHHljORtxHW\
       EIvB7FF8+Bk5v38k4TNFxiMdM3cWvtWCeXLkaMiv0XkeIcqYu0UiDz0CxX3l/dlY\
       1HGBxmKS4GUIW7T3V9o7wRb68sXzQeQGzvSm9qsCgYA3dKT1AHcCU504m7XZNdTO\
       4NRBm3xaOgQK4J2qfIm5iweV7Rc6m9Xyi93Vsj7WK1Ito6iGTG1efaXIOKtLKy6f\
       1UzvJbqkoz0Cn/jtptDoTm+SpjFvZKPjfdPMIsKcW3hggzJ+twP9sGj9gwY0WXaC\
       k4FoXlrbzJrt++txEz8/wwKBgBL2dpFCt+OooaaNliUftiQ1Mt0vPEuTPpSLRLn2\
       cpsZKFAJDpDi4O/jxu8a/KJyeWavPHEFECGRg4c00wR0JZGxmOJpBiihI/pM8lqy\
       Fw+49qv1Dl9px8lVyvbufk9tyBDPN+POof/d+/WjdnNhFbTBXasUih2nuav7KnOx\
       lDJVAoGBALTrRWdhAxBidvpJJH4XDrzKidyb2yDF6b2mk1o6u0XCEukuSBZsZswn\
       +GNrW+cBHznG8PnQABUOpzRv/u/lQScJ9AunoczqXm8FQWej/jmMamvKKuinfTwG\
       dvL/5VEruXDRNOdTU5PzwtHrv9bnhArUcHxfwo7oE3yclbvAk6i3" \
    -jwt_public_key="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwNzbxt6CaM/CK6Sk06ou\
       QIiVcqWIe6cvyuXtI+cSXxDMuLPps1azHKod969/rmt3oeZtP+l14QlQ6XDtmLRn\
       17eZ2HWPI/WsZUVUJaNb6TPywV2z5AhbER31TW9Fsmd7aCZo8fMbzUGDmsrerdYv\
       jxy7SVj6C3IhO8KpaF3cSzHXWoZY9wf1zm6lbsxuwg2H+wo0vEIe2gSkS6RvSqD7\
       3MFdMY4toLqFYgJEOeNYsg1xXfHaya0AkIVzfxqr+jzH6rRPZ0DbvclLztxg74TN\
       tOSxLadA0ACAPPspEZpHQYOHlaU6ccnFYK/Nq77oh4QqEkMubvGQyat0OmiyKPPJ\
       KQIDAQAB" \
    -es_uri="https://17media:<EMAIL>" \
    -es_ping_uri="https://17media:<EMAIL>" \
    -es_index="17media" \
    -big_query_key="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" \
    -pub_key="******************************************" \
    -sub_key="******************************************" \
    -sec_key="sec-c-MmYwMWMzZTItZTk1MS00OTJhLWJkYTUtZTlmOTNhZGNiMGJk" \
    -erp_url="http://34.80.177.7:8080" \
    -erp_account="" \
    -erp_password=""
elif [ $env == "-sta" ]
then
  echo "========== use env sta =========="
    go run main.go \
      -year=2019 \
      -month=3 \
      -disable_fd_monitor=true \
      -mongo_uri="?:?@sta-compass.17app.co:57017" \
      -mongo_dst_uri="?:?@sta-compass.17app.co:57017" \
      -mongo_bak_uri="?:?@sta-compass.17app.co:57017" \
      -mongo_db="17media" \
      -mongo_enableSSL=1 \
      -rds_admin_uri="?:?@tcp(sta-report.17app.co:3306)/media17admin?charset=utf8mb4" \
      -rds_gift_uri="?:?@tcp(sta-gift.17app.co:3306)/media17?charset=utf8mb4" \
      -rds_gift_reader_uri="?:?@tcp(sta-gift.17app.co:3306)/media17?charset=utf8mb4" \
      -redis_cache_uri="redis-13514.c1.asia-east1-1.gce.cloud.redislabs.com:13514" \
      -redis_cache_pwd="SucG9ycbYv3g3jmA" \
      -redis_persist_uri="sta-persist-redis.17app.co:14392" \
      -redis_persist_pwd="!HRnFb7Gew8ePkRW" \
      -etcd_hosts="http://localhost:65535" \
      -etcd_root="/configs/envs/dev" \
      -jwt_private_key="MIIEowIBAAKCAQEAwNzbxt6CaM/CK6Sk06ouQIiVcqWIe6cvyuXtI+cSXxDMuLPp\
       s1azHKod969/rmt3oeZtP+l14QlQ6XDtmLRn17eZ2HWPI/WsZUVUJaNb6TPywV2z\
       5AhbER31TW9Fsmd7aCZo8fMbzUGDmsrerdYvjxy7SVj6C3IhO8KpaF3cSzHXWoZY\
       9wf1zm6lbsxuwg2H+wo0vEIe2gSkS6RvSqD73MFdMY4toLqFYgJEOeNYsg1xXfHa\
       ya0AkIVzfxqr+jzH6rRPZ0DbvclLztxg74TNtOSxLadA0ACAPPspEZpHQYOHlaU6\
       ccnFYK/Nq77oh4QqEkMubvGQyat0OmiyKPPJKQIDAQABAoIBABjj6BFQYjA4r+yG\
       ASe4DOQTPIZhSq36s7ZR2jsJ9VsDhghIX97fT5phciCnK6lNAVGJtYvMC4SReTnH\
       c0KD9ernLRm8gk63xBoFyohY/uGn7Kp79110npwk66WkuK//nvOgEWc4Ytwoml19\
       1SNGQzkLEjizHQIhJpzZ0JdBlc62VEMpiFkH/aKCcJZHHNFHDe5cR7cRC4QPx0V8\
       fYUwkglNy/2E8w8vpEdnYRtIygihD3vCW3uljRD8crppQO8gsa6BG4COkBHCTSb3\
       7VVDDZpjonPe9aXjA7WEyM6+3ssrMdthLH0hM43Tu4ibCT+849XE0Sz9j3j49fGk\
       qZXgoikCgYEA+UFxSisn0NpDCXHLqTEtgL1kB8pzPV5WyGiCZfml5f5elEyRt/bh\
       TKIp/Sg7j5osyZZzGoKncMl9cXG+cF+uun15+hzKOqN8SMYbPfRaFm0q+zPq9ZcH\
       9Kczh4hzBMJnsSBFI2dw9Qiq5j52kdLJu1EhjKVN1VOsH18zuR3Vz3sCgYEAxhTK\
       X4g/lkJRaP8hfNovp6y5LltL7V/5yg1FqSMITitkBEynNaysI8XZHBHHljORtxHW\
       EIvB7FF8+Bk5v38k4TNFxiMdM3cWvtWCeXLkaMiv0XkeIcqYu0UiDz0CxX3l/dlY\
       1HGBxmKS4GUIW7T3V9o7wRb68sXzQeQGzvSm9qsCgYA3dKT1AHcCU504m7XZNdTO\
       4NRBm3xaOgQK4J2qfIm5iweV7Rc6m9Xyi93Vsj7WK1Ito6iGTG1efaXIOKtLKy6f\
       1UzvJbqkoz0Cn/jtptDoTm+SpjFvZKPjfdPMIsKcW3hggzJ+twP9sGj9gwY0WXaC\
       k4FoXlrbzJrt++txEz8/wwKBgBL2dpFCt+OooaaNliUftiQ1Mt0vPEuTPpSLRLn2\
       cpsZKFAJDpDi4O/jxu8a/KJyeWavPHEFECGRg4c00wR0JZGxmOJpBiihI/pM8lqy\
       Fw+49qv1Dl9px8lVyvbufk9tyBDPN+POof/d+/WjdnNhFbTBXasUih2nuav7KnOx\
       lDJVAoGBALTrRWdhAxBidvpJJH4XDrzKidyb2yDF6b2mk1o6u0XCEukuSBZsZswn\
       +GNrW+cBHznG8PnQABUOpzRv/u/lQScJ9AunoczqXm8FQWej/jmMamvKKuinfTwG\
       dvL/5VEruXDRNOdTU5PzwtHrv9bnhArUcHxfwo7oE3yclbvAk6i3" \
      -jwt_public_key="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwNzbxt6CaM/CK6Sk06ou\
       QIiVcqWIe6cvyuXtI+cSXxDMuLPps1azHKod969/rmt3oeZtP+l14QlQ6XDtmLRn\
       17eZ2HWPI/WsZUVUJaNb6TPywV2z5AhbER31TW9Fsmd7aCZo8fMbzUGDmsrerdYv\
       jxy7SVj6C3IhO8KpaF3cSzHXWoZY9wf1zm6lbsxuwg2H+wo0vEIe2gSkS6RvSqD7\
       3MFdMY4toLqFYgJEOeNYsg1xXfHaya0AkIVzfxqr+jzH6rRPZ0DbvclLztxg74TN\
       tOSxLadA0ACAPPspEZpHQYOHlaU6ccnFYK/Nq77oh4QqEkMubvGQyat0OmiyKPPJ\
       KQIDAQAB" \
      -pub_key="******************************************" \
      -sub_key="******************************************" \
      -sec_key="sec-c-NTQzZWU4ZDUtMTI4YS00NGUzLTg0MGUtOGJiMGE1OTY3MTBl"
else
  # default to use dev
  # use local etcd 
  echo "========== use env dev =========="
    go run main.go \
      -disable_fd_monitor=true \
      -mongo_uri="mongodb://17media:7r4**b*#GGNY*vd8@***************:57017" \
      -mongo_db="17media" \
      -rds_report_uri="media17report:Machipopo99@tcp(reportdevseed-cluster.cluster-c0h0xvusrkln.us-west-2.rds.amazonaws.com:3306)/media17report?charset=utf8mb4" \
      -rds_admin_uri="media17report:Machipopo99@tcp(reportdevseed-cluster.cluster-c0h0xvusrkln.us-west-2.rds.amazonaws.com:3306)/media17admin?charset=utf8mb4" \
      -rds_gift_uri="media17:edf60f1e-c0e8-4f5f-a9cb-659b0f1a8ae5@tcp(giftdevseed-cluster.cluster-c0h0xvusrkln.us-west-2.rds.amazonaws.com:3306)/media17?charset=utf8mb4" \
      -legacy_redis_uri="dev-legacycache-redis.17app.co:13089" \
      -legacy_redis_pwd="8CVFyHNSY7hmdcmyyk3SqF6tQUJ42ZaA" \
      -redis_cache_uri="dev-cache-redis.17app.co:13415" \
      -redis_cache_pwd="dFkmsUHp7KNhhKNrNSEB7K8revGqJ62x" \
      -redis_persist_uri="dev-persist-redis.17app.co:15102"\
      -redis_persist_pwd="" \
      -s3_region="us-west-2" \
      -s3_bucket="17app-dev" \
      -cdn_path_prefix="http://cdn.17app.co" \
      -real_path_prefix="https://s3-us-west-2.amazonaws.com" \
      -awsAccessKeyID="********************" \
      -awsSecretAccessKey="MVIW9AMNk8wuO30r9zMsGC+5P6qXkwmOo2XesG5B" \
      -etcd_hosts="http://localhost:2379" \
      -etcd_root="/configs/envs/dev" \
      -jwt_private_key="MIIEowIBAAKCAQEAwNzbxt6CaM/CK6Sk06ouQIiVcqWIe6cvyuXtI+cSXxDMuLPp\
       s1azHKod969/rmt3oeZtP+l14QlQ6XDtmLRn17eZ2HWPI/WsZUVUJaNb6TPywV2z\
       5AhbER31TW9Fsmd7aCZo8fMbzUGDmsrerdYvjxy7SVj6C3IhO8KpaF3cSzHXWoZY\
       9wf1zm6lbsxuwg2H+wo0vEIe2gSkS6RvSqD73MFdMY4toLqFYgJEOeNYsg1xXfHa\
       ya0AkIVzfxqr+jzH6rRPZ0DbvclLztxg74TNtOSxLadA0ACAPPspEZpHQYOHlaU6\
       ccnFYK/Nq77oh4QqEkMubvGQyat0OmiyKPPJKQIDAQABAoIBABjj6BFQYjA4r+yG\
       ASe4DOQTPIZhSq36s7ZR2jsJ9VsDhghIX97fT5phciCnK6lNAVGJtYvMC4SReTnH\
       c0KD9ernLRm8gk63xBoFyohY/uGn7Kp79110npwk66WkuK//nvOgEWc4Ytwoml19\
       1SNGQzkLEjizHQIhJpzZ0JdBlc62VEMpiFkH/aKCcJZHHNFHDe5cR7cRC4QPx0V8\
       fYUwkglNy/2E8w8vpEdnYRtIygihD3vCW3uljRD8crppQO8gsa6BG4COkBHCTSb3\
       7VVDDZpjonPe9aXjA7WEyM6+3ssrMdthLH0hM43Tu4ibCT+849XE0Sz9j3j49fGk\
       qZXgoikCgYEA+UFxSisn0NpDCXHLqTEtgL1kB8pzPV5WyGiCZfml5f5elEyRt/bh\
       TKIp/Sg7j5osyZZzGoKncMl9cXG+cF+uun15+hzKOqN8SMYbPfRaFm0q+zPq9ZcH\
       9Kczh4hzBMJnsSBFI2dw9Qiq5j52kdLJu1EhjKVN1VOsH18zuR3Vz3sCgYEAxhTK\
       X4g/lkJRaP8hfNovp6y5LltL7V/5yg1FqSMITitkBEynNaysI8XZHBHHljORtxHW\
       EIvB7FF8+Bk5v38k4TNFxiMdM3cWvtWCeXLkaMiv0XkeIcqYu0UiDz0CxX3l/dlY\
       1HGBxmKS4GUIW7T3V9o7wRb68sXzQeQGzvSm9qsCgYA3dKT1AHcCU504m7XZNdTO\
       4NRBm3xaOgQK4J2qfIm5iweV7Rc6m9Xyi93Vsj7WK1Ito6iGTG1efaXIOKtLKy6f\
       1UzvJbqkoz0Cn/jtptDoTm+SpjFvZKPjfdPMIsKcW3hggzJ+twP9sGj9gwY0WXaC\
       k4FoXlrbzJrt++txEz8/wwKBgBL2dpFCt+OooaaNliUftiQ1Mt0vPEuTPpSLRLn2\
       cpsZKFAJDpDi4O/jxu8a/KJyeWavPHEFECGRg4c00wR0JZGxmOJpBiihI/pM8lqy\
       Fw+49qv1Dl9px8lVyvbufk9tyBDPN+POof/d+/WjdnNhFbTBXasUih2nuav7KnOx\
       lDJVAoGBALTrRWdhAxBidvpJJH4XDrzKidyb2yDF6b2mk1o6u0XCEukuSBZsZswn\
       +GNrW+cBHznG8PnQABUOpzRv/u/lQScJ9AunoczqXm8FQWej/jmMamvKKuinfTwG\
       dvL/5VEruXDRNOdTU5PzwtHrv9bnhArUcHxfwo7oE3yclbvAk6i3" \
      -jwt_public_key="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwNzbxt6CaM/CK6Sk06ou\
       QIiVcqWIe6cvyuXtI+cSXxDMuLPps1azHKod969/rmt3oeZtP+l14QlQ6XDtmLRn\
       17eZ2HWPI/WsZUVUJaNb6TPywV2z5AhbER31TW9Fsmd7aCZo8fMbzUGDmsrerdYv\
       jxy7SVj6C3IhO8KpaF3cSzHXWoZY9wf1zm6lbsxuwg2H+wo0vEIe2gSkS6RvSqD7\
       3MFdMY4toLqFYgJEOeNYsg1xXfHaya0AkIVzfxqr+jzH6rRPZ0DbvclLztxg74TN\
       tOSxLadA0ACAPPspEZpHQYOHlaU6ccnFYK/Nq77oh4QqEkMubvGQyat0OmiyKPPJ\
       KQIDAQAB" \
      -pub_key="******************************************" \
      -sub_key="******************************************" \
      -sec_key="sec-c-NGM2ZTAxNjktOGI0Yy00YmYxLWJlM2QtMDQwY2YxZGNlNjQ3" \
      -dryrun=false
fi
