package main

import (
	"flag"
	"fmt"
	"io/ioutil"
	"os"
	"time"

	"github.com/gocarina/gocsv"
	"github.com/jmoiron/sqlx"

	"github.com/17media/api/base/ctx"
	"github.com/17media/api/base/env"
	mModel "github.com/17media/api/models/money"
	"github.com/17media/api/service/config"
	fcron "github.com/17media/api/service/cron/fake"
	"github.com/17media/api/service/kms"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/setup/etcd/etcdconfig"
	"github.com/17media/api/stores/money"
	"github.com/17media/logrus"
)

var (
	dryrun = flag.Bool("dryrun", true, "dry run")
)

type record struct {
	UserID  string `csv:"userID"`
	Point   int64  `csv:"point"`
	PGLID   int64  `csv:"maxID"`
	TradeID string `csv:"tradeID"`
}

type output struct {
	UserID            string `csv:"userID"`
	Balance           int64  `csv:"balance"`
	RecallPoint       int64  `csv:"recallPoint"`
	ActualRecallPoint int64  `csv:"actualRecallPoint"`
	AfterBalance      int64  `csv:"afterBalance"`
	RecallTradeID     string `csv:"recallTradeID"`
}

func writeCSV(context ctx.CTX, fileName string, contentBytes []byte) error {
	utf8Bom := "\xEF\xBB\xBF"
	csvBytes := []byte{}
	csvBytes = append(csvBytes, utf8Bom...)
	csvBytes = append(csvBytes, contentBytes...)

	f, err := os.OpenFile(fileName, os.O_WRONLY|os.O_CREATE, 0755)
	if err != nil {
		return err
	}
	_, err = f.Write(csvBytes)
	if err != nil {
		return err
	}
	return nil
}

func main() {
	m := dimanager.DefaultManager
	fcron.RegisterFake(m)
	m.Compile()

	flag.Parse()
	logrus.SetLevel(logrus.InfoLevel)
	logrus.SetFormatter(&logrus.TextFormatter{ForceColors: true})
	// f, err := os.OpenFile("fix-incorrect-returned-bill-amount.txt", os.O_WRONLY|os.O_CREATE, 0755)
	// if err != nil {
	// 	panic("Openfile")
	// }
	// logrus.SetOutput(f)

	// sync configuration
	cfg := etcdconfig.GetConfigClient(m)
	kmsServ := kms.GetKms(m)
	if err := config.StartWatcher(config.Params{
		Client: cfg,
		Kms:    kmsServ,
	}); err != nil {
		logrus.WithField("err", err).Panic("Fail to start watchers")
	}

	context := ctx.Background()
	bank := money.GetBank(m)

	// Parse csv file
	csvFile, err := ioutil.ReadFile("user_list.csv")
	if err != nil {
		context.WithField("err", err).Panic("read file error")
	}

	rows := []*record{}
	if err := gocsv.UnmarshalBytes(csvFile, &rows); err != nil {
		context.WithField("err", err).Panic("read file error")
	}
	if len(rows) == 0 {
		return
	}

	context.WithField("rows", len(rows)).Info("ct rows")

	exportRecords := []*output{}
	for _, r := range rows {
		if r.TradeID == "" {
			context.WithField("row", *r).Panic("empty tradeID")
		}

		balance, err := bank.GetUserBalanceFromDB(context, r.UserID)
		if err != nil {
			context.WithField("err", err).Panic("bank.GetUserBalanceFromDB error")
		}
		userPoint := balance[mModel.Currency_POINT].Amount
		if userPoint < r.Point {
			context.WithFields(logrus.Fields{
				"userID":  r.UserID,
				"balance": userPoint,
				"point":   r.Point,
				"tradeID": r.TradeID,
			}).Error("user point is not enough")
		}

		if *dryrun {
			context.Info("dryrun")
			exportRecords = append(exportRecords, &output{
				UserID:            r.UserID,
				Balance:           userPoint,
				RecallPoint:       r.Point,
				ActualRecallPoint: 0,
				AfterBalance:      0,
				RecallTradeID:     "",
			})
			continue
		}

		afterPoint := userPoint
		var refundAmount int64
		var refundTradeID string
		_, err = bank.Refund(context, "5a824222-5970-4335-5eca-e56d4f4b5c7b", r.TradeID, mModel.RefundPolicy_DEFICIT_ON_17, money.WithRefundCallback(func(tx *sqlx.Tx, rTradeID string, timeMs int64, dealings []*mModel.Dealing) error {
			var find bool
			for _, d := range dealings {
				// it's the recall dealing
				if d.FromUserID == r.UserID && d.ToUserID == env.OfficialPseudoUserID {
					find = true
					refundAmount = d.Amount
					break
				}
			}
			if !find {
				context.WithField("dealings", dealings).Error("failed to get refund amount")
				return fmt.Errorf("failed to get refund amount")
			}

			afterBalance, err := bank.GetUserBalanceFromDB(context, r.UserID, money.GetBalanceWithTx(tx))
			if err != nil {
				context.WithField("err", err).Error("failed to get user balance")
				return err
			}
			afterPoint = afterBalance[mModel.Currency_POINT].Amount

			refundTradeID = rTradeID

			return nil
		}))
		if err != nil {
			context.WithFields(logrus.Fields{
				"err": err,
				"row": *r,
			}).Panic("bank.Refund error")
		}

		exportRecords = append(exportRecords, &output{
			UserID:            r.UserID,
			Balance:           userPoint,
			RecallPoint:       r.Point,
			ActualRecallPoint: refundAmount,
			AfterBalance:      afterPoint,
			RecallTradeID:     refundTradeID,
		})
	}

	bs, err := gocsv.MarshalBytes(&exportRecords)
	if err != nil {
		context.WithField("err", err).Panic("gocsv.MarshalBytes error")
	}
	now := time.Now().Unix()
	if err := writeCSV(context, fmt.Sprintf("bank-refund_%d.csv", now), bs); err != nil {
		context.WithField("err", err).Panic("writeCSV error")
	}

	context.Info("done")
}
