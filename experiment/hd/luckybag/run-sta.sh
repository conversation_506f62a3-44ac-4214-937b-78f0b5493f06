#!/bin/bash

# Uncomment arguments below before running this program
go run main.go  \
  -redis_cache_uri="redis-proxy-cache.17app.co:13514" \
  -redis_cache_pwd="SucG9ycbYv3g3jmA" \
  -redis_persist_uri="redis-proxy-persistent.17app.co:12484" \
  -redis_persist_pwd="3P9hPFxFpukcTrLb" \
  -legacy_redis_uris="redis-proxy-legacycache.17app.co:14851" \
  -legacy_redis_pwd="M629rhvVgaUCcAMz" \
  -mongo_uri="mongodb://fakeUser:<EMAIL>:57017" \
  -mongo_db="17media" \
  -rds_admin_uri="media17report:Machipopo99@tcp(sta-report.17app.co:3306)/media17admin?charset=utf8mb4" \
  -rds_report_uri="media17report:Machipopo99@tcp(sta-report.17app.co:3306)/media17report?charset=utf8mb4" \
  -rds_gift_uri="media17:edf60f1e-c0e8-4f5f-a9cb-659b0f1a8ae5@tcp(sta-gift.17app.co:3306)/media17?charset=utf8mb4" \
  -pub_key="******************************************" \
  -sub_key="******************************************" \
  -sec_key="sec-c-NTQzZWU4ZDUtMTI4YS00NGUzLTg0MGUtOGJiMGE1OTY3MTBl" \
  -etcd_hosts="http://localhost:2379" \
  -etcd_root="/configs/envs/sta" \
  -disable_fd_monitor=true
