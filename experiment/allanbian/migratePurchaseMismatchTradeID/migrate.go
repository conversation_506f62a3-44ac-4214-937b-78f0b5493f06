package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/jmoiron/sqlx"

	"github.com/17media/logrus"

	"github.com/17media/api/base/ctx"
	mdb "github.com/17media/api/base/db"
	"github.com/17media/api/setup/dimanager"
	"github.com/17media/api/setup/mysql/mysqlgiftwriter"
)

var (
	dryrun           = flag.Bool("dryrun", true, "dry run")
	migratePlatforms = []string{
		"IOS",
		"iOS",
		"IOS_CN",
		"IOS_GoodNight",
		"ANDROID",
		"ANDROID_CN",
		"ANDROID_GoodNight",
	}
	migrateProductIDs = []string{
		"gn_media17_points_100",
		"gn_media17_points_1100",
		"gn_media17_points_14000",
		"gn_media17_points_2400",
		"gn_media17_points_500",
		"gn_media17_points_6500",
		"iapppay17_points_100",
		"iapppay17_points_500",
		"media17_points_100",
		"media17_points_1100",
		"media17_points_14000",
		"media17_points_2400",
		"media17_points_34000",
		"media17_points_500",
		"media17_points_6500",
		"moyo17_points_100",
		"moyo17_points_14000",
	}

	// startTimestamp indicates migration start timestamp in PGL
	startTimestamp = 1527811200
	// endTimestamp indicates migration end timestamp in PGL
	endTimestamp = 1530403200
)

const (
	// Migration version 3 means PGL with CT record and tradeID
	migrationVer = 3
	// db operation batch
	updateBatchCount = 500
	// output log filename
	outputFileName = "migrate.log"
	// pglType in PointGainLog
	pglType = "purchase"
)

func main() {
	logrus.SetLevel(logrus.InfoLevel)
	file, err := os.OpenFile(outputFileName, os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		logrus.Panic("failed to open log file")
	}
	logrus.SetOutput(file)
	logrus.SetFormatter(&logrus.TextFormatter{FullTimestamp: true})
	defer file.Close()

	m := dimanager.DefaultManager
	m.Compile()
	flag.Parse()

	context := ctx.Background()
	db := sqlx.NewDb(mysqlgiftwriter.GetMySQLGiftWriter(m), mdb.SQLDriver)

	userIDs := []string{}
	q, args, err := sqlx.In(`
		SELECT DISTINCT userID
		FROM PointGainLog
		WHERE
			type = ?
			AND platform IN (?)
			AND productID IN (?)
			AND internalOrderID IS NULL
			AND timestamp >= ? AND timestamp < ?
			AND tradeID=''
		`,
		pglType, migratePlatforms, migrateProductIDs, startTimestamp, endTimestamp)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err":              err,
			"type":             pglType,
			"migratePlatforms": migratePlatforms,
		}).Panic("sqlx.In failed")
	}

	err = db.Select(&userIDs, q, args...)
	if err != nil {
		context.WithFields(logrus.Fields{
			"err":  err,
			"q":    q,
			"args": args,
		}).Panic("db.Select failed")
	}

	type record struct {
		PglID        int64
		PglTimestamp int64
		PplID        int64
		PplTimestamp int64
		PplTradeID   string
	}

	dedupRecords := []record{}
	for i, userID := range userIDs {
		context.WithFields(logrus.Fields{
			"i":      i,
			"userID": userID,
			"len":    len(userIDs),
		}).Info("find user's ppl/pgl")

		type dbRow struct {
			ID        int64  `db:"id"`
			TradeID   string `db:"tradeID"`
			ProductID string `db:"productID"`
			Platform  string `db:"platform"`
			Timestamp int64  `db:"timestamp"`
		}

		pgls := []dbRow{}
		q, args, err = sqlx.In(`
		SELECT 
			id, 
			tradeID, 
			productID, 
			platform, 
			timestamp
		FROM PointGainLog
		WHERE
			type=?
			AND platform IN (?)
			AND productID IN (?)
			AND internalOrderID IS NULL
			AND timestamp >= ? AND timestamp <= ?
			AND userID=?
		ORDER BY timestamp`,
			pglType, migratePlatforms, migrateProductIDs, startTimestamp, endTimestamp, userID)
		if err != nil {
			context.WithFields(logrus.Fields{
				"err":               err,
				"type":              pglType,
				"migratePlatforms":  migratePlatforms,
				"migrateProductIDs": migrateProductIDs,
				"userID":            userID,
			}).Panic("sqlx.In failed")
		}

		err = db.Select(&pgls, q, args...)
		if err != nil {
			context.WithFields(logrus.Fields{
				"err":  err,
				"q":    q,
				"args": args,
			}).Panic("db.Select failed")
		}

		ppls := []dbRow{}
		q, args, err = sqlx.In(`
		SELECT 
			id, 
			tradeID, 
			productID, 
			platform,
			timestamp
		FROM ProductPurchaseLog
		WHERE
			platform IN (?)
			AND productID IN (?)
			AND timestamp >= ? AND timestamp <= ?
			AND userID=?
		ORDER BY timestamp`,
			migratePlatforms, migrateProductIDs, startTimestamp, endTimestamp, userID)
		if err != nil {
			context.WithFields(logrus.Fields{
				"err":               err,
				"type":              pglType,
				"migratePlatforms":  migratePlatforms,
				"migrateProductIDs": migrateProductIDs,
				"userID":            userID,
			}).Panic("sqlx.In failed")
		}

		err = db.Select(&ppls, q, args...)
		if err != nil {
			context.WithFields(logrus.Fields{
				"err":  err,
				"q":    q,
				"args": args,
			}).Panic("db.Select failed")
		}

		if len(pgls) != len(ppls) {
			context.WithFields(logrus.Fields{
				"ppls":   len(ppls),
				"pgls":   len(pgls),
				"userID": userID,
			}).Warn("length mismatch")
			continue
		}

		for i := range pgls {
			pgl := pgls[i]
			ppl := ppls[i]

			if pgl.TradeID != "" {
				if pgl.TradeID != ppl.TradeID {
					context.WithFields(logrus.Fields{
						"pgl":    pgl,
						"ppl":    ppl,
						"userID": userID,
					}).Warn("mismatch record")
					break
				}
				continue
			}

			if pgl.Platform != ppl.Platform ||
				pgl.ProductID != ppl.ProductID {
				context.WithFields(logrus.Fields{
					"pgl":    pgl,
					"ppl":    ppl,
					"userID": userID,
				}).Warn("unexpect record")
				break
			}

			dedupRecords = append(dedupRecords, record{
				PglID:        pgl.ID,
				PglTimestamp: pgl.Timestamp,
				PplID:        ppl.ID,
				PplTimestamp: ppl.Timestamp,
				PplTradeID:   ppl.TradeID,
			})
		}
	}

	context.Info("migrate start...")

	for i := 0; i < len(dedupRecords); i += updateBatchCount {
		end := i + updateBatchCount
		if end > len(dedupRecords) {
			end = len(dedupRecords)
		}

		if err := mdb.Transactx(context, db, func(tx *sqlx.Tx) error {
			for _, r := range dedupRecords[i:end] {
				context.WithFields(logrus.Fields{
					"PglID":        r.PglID,
					"PglTimestamp": r.PglTimestamp,
					"PplID":        r.PplID,
					"PplTimestamp": r.PplTimestamp,
					"tradeID":      r.PplTradeID,
				}).Info("ready to migrate")

				if *dryrun {
					continue
				}

				res, err := tx.Exec(`UPDATE PointGainLog SET tradeID=?, migration=? WHERE id=?`,
					r.PplTradeID, migrationVer, r.PglID)
				if err != nil {
					context.WithFields(logrus.Fields{
						"err":          err,
						"id":           r.PglID,
						"tradeID":      r.PplTradeID,
						"migrationVer": migrationVer,
					}).Error("tx.Exec failed")
					return err
				}

				ra, err := res.RowsAffected()
				if err != nil {
					return err
				}

				if ra != 1 {
					return fmt.Errorf("RowsAffected too less")
				}
			}

			return nil
		}); err != nil {
			context.WithFields(logrus.Fields{
				"err": err,
				"i":   i,
				"end": end,
			}).Panic("migrate failed")
		}

		context.WithFields(logrus.Fields{
			"i":   i,
			"end": end,
		}).Info("migrate continue")
	}

	context.Info("migrate finish")
}
