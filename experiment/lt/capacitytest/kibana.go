package capacitytest

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptrace"
	"strings"

	"github.com/17media/api/base/bhttp"
	"github.com/17media/api/base/ctx"
	"github.com/17media/api/experiment/lt/capacitytest/search"
)

var (
	apiPath = "https://log-suppresser.17app.co/elasticsearch/_msearch"
)

// Kibana for search by kibana API
type Kibana struct {
	Index     *search.Index
	HTTPTrace *httptrace.ClientTrace
	Client    *http.Client
	Username  string
	Password  string
}

// NewKibana returns a kibana searcher
func NewKibana(index *search.Index, trace *httptrace.ClientTrace, username string, password string) *Ki<PERSON> {
	return &Kibana{
		Index:     search.DefaultIndex(),
		Client:    bhttp.ReuseConnsPerHostClient,
		HTTPTrace: trace,
		Username:  username,
		Password:  password,
	}
}

// Run will execute a kibana search request
func (client Kibana) Run(context ctx.CTX, req *search.Request) (*search.Result, error) {
	indexBuf := &bytes.Buffer{}
	encoder := json.NewEncoder(indexBuf)
	if err := encoder.Encode(client.Index); err != nil {
		return nil, err
	}
	queryBuf := &bytes.Buffer{}
	encoder = json.NewEncoder(queryBuf)
	if err := encoder.Encode(req); err != nil {
		return nil, err
	}

	mReader := io.MultiReader(strings.NewReader(indexBuf.String()), strings.NewReader(queryBuf.String()))

	reqCtx := context
	if client.HTTPTrace != nil {
		reqCtx = httptrace.WithClientTrace(reqCtx, client.HTTPTrace).(ctx.CTX)
	}

	httpReq, err := http.NewRequestWithContext(reqCtx, "POST", apiPath, mReader)
	if err != nil {
		return nil, err
	}
	httpReq.SetBasicAuth(client.Username, client.Password)
	httpReq.Header.Add("kbn-version", "6.0.1")
	httpReq.Header.Add("content-type", "application/x-ndjson")
	resp, err := client.Client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	//fmt.Println(string(body))
	decoder := json.NewDecoder(resp.Body)
	decoder.UseNumber()
	res := search.Result{}
	if err := decoder.Decode(&res); err != nil {
		return nil, err
	}
	return &res, nil
}
