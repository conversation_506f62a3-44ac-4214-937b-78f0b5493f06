package capacitytest

import (
	"compress/gzip"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/http/httptrace"
	"strings"
	"time"

	"gopkg.in/kothar/brotli-go.v0/dec"

	"github.com/17media/logrus"

	"github.com/17media/api/base/bhttp"
	"github.com/17media/api/base/ctx"
)

var (
	// ErrMissingField represents some expected field not exist when parsing
	ErrMissingField = errors.New("missing field")

	host = "https://sta-api.17app.co"
)

// SendRequest represents a HTTP Request
type SendRequest struct {
	T      time.Time
	Header map[string][]string
	Method string
	Path   string
	Query  string
	Body   string
}

// ParseSource parses search result Source into SendRequest
func ParseSource(source map[string]interface{}) (*SendRequest, error) {
	req := SendRequest{}
	timestampStr, ok := source["@timestamp"]
	if !ok {
		return nil, fmt.Errorf("%w, missing timestamp", ErrMissingField)
	}
	t, err := time.Parse(time.RFC3339Nano, timestampStr.(string))
	if err != nil {
		return nil, err
	}
	headerStr, ok := source["s_header"]
	if !ok {
		return nil, fmt.Errorf("%w, missing header", ErrMissingField)
	}
	if err := json.Unmarshal([]byte(headerStr.(string)), &req.Header); err != nil {
		return nil, err
	}

	req.T = t
	req.Method = source["s_method"].(string)
	req.Path = source["s_path"].(string)
	req.Query = source["s_query"].(string)
	if bodyStr, ok := source["s_body"]; ok {
		req.Body = bodyStr.(string)
	}

	return &req, nil
}

// Sender for sending HTTP requests
type Sender struct {
	HTTPTrace *httptrace.ClientTrace
	Client    *http.Client
	reqChan   chan SendRequest
}

// NewSender creates a Sender
func NewSender(trace *httptrace.ClientTrace) *Sender {

	c := make(chan SendRequest)

	return &Sender{
		Client:    bhttp.ReuseConnsPerHostClient,
		HTTPTrace: trace,
		reqChan:   c,
	}
}

// Push a request into channel
func (s Sender) Push(context ctx.CTX, req SendRequest) {
	s.reqChan <- req
}

// Start a goroutine which will get request from channel and run it
func (s Sender) Start(context ctx.CTX) {
	go func() {
		for {
			r := <-s.reqChan
			go func() {
				if err := s.Run(context, r); err != nil {
					context.WithField("err", err).Error("Sender run failed")
				}
			}()
		}
	}()
}

// Run will send a request
func (s Sender) Run(context ctx.CTX, req SendRequest) error {
	reqCtx := context
	if s.HTTPTrace != nil {
		reqCtx = httptrace.WithClientTrace(reqCtx, s.HTTPTrace).(ctx.CTX)
	}

	body := strings.NewReader(req.Body)
	path := host + req.Path
	if req.Query != "" {
		path = path + "?" + req.Query
	}
	httpReq, err := http.NewRequestWithContext(reqCtx, req.Method, path, body)
	if err != nil {
		return err
	}
	httpReq.Header = http.Header(req.Header)
	resp, err := s.Client.Do(httpReq)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	encoding := resp.Header.Get("Content-Encoding")
	var decoder io.Reader
	if encoding == "br" {
		decoder = dec.NewBrotliReader(resp.Body)
	} else if encoding == "gzip" {
		decoder, err = gzip.NewReader(resp.Body)
		if err != nil {
			return err
		}
	} else {
		decoder = resp.Body
	}

	all, err := ioutil.ReadAll(decoder)
	if err != nil {
		return err
	}
	logLen := len(all)
	if logLen > 100 {
		logLen = 100
	}

	context.WithFields(logrus.Fields{
		"statusCode": resp.StatusCode,
		"req":        req.Path,
		"header":     resp.Header.Get("Content-Encoding"),
		"body":       string(all)[:logLen],
	}).Info("resp")

	return nil
}
