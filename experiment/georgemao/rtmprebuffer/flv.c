/*
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date:   2016/04/13
 * File:   flv.c
 * Desc:   FLV file module
 */

#include <arpa/inet.h>
#include <errno.h>
#include <fcntl.h>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

#include "common.h"
#include "flv.h"
#include "log.h"
#include "rtmp_amf.h"

static bool flv_get_file_size(int flvfd, size_t *flvsize, error_context_t *errctx);

uint8_t *
flv_open(int             *flvfd,
         size_t          *flvsize,
         const char      *flvpath,
         error_context_t *errctx)
{
    uint8_t *flvptr = NULL;
    bool     result = false;

    if (!flvfd || !flvpath || !errctx)
        error_save_return(errctx, EINVAL, NULL);

    /* Open FLV file source */
    *flvfd = open(flvpath, O_RDONLY);
    if (*flvfd < 0)
        error_save_return(errctx, errno, NULL);

    /* Get FLV file size */
    result = flv_get_file_size(*flvfd, flvsize, errctx);
    if (!result)
        error_save_jump(errctx, errno, FAILED);

    /* Map the FLV file */
    flvptr = mmap(NULL, *flvsize, PROT_READ | PROT_WRITE, MAP_PRIVATE, *flvfd, 0);
    if (flvptr == MAP_FAILED)
        error_save_jump(errctx, errno, FAILED);

    log_info("FLV file [%s] succesfully mapped at memory range [%p - %p]",
            flvpath, flvptr, flvptr + *flvsize);

    return flvptr;

FAILED:

    if (flvptr && flvsize && *flvsize > 0)
        munmap(flvptr, *flvsize);
    if (*flvfd >= 0)
        close(*flvfd);

    return NULL;
}

bool
flv_close(int              flvfd,
          uint8_t         *flvptr,
          size_t           flvsize,
          error_context_t *errctx)
{
    int ret = -1;

    if (flvfd < 0 || !flvptr || !errctx)
        error_save_return(errctx, EINVAL, false);

    ret = munmap(flvptr, flvsize);
    if (ret < 0)
        error_save_return(errctx, errno, false);
    ret = close(flvfd);
    if (ret < 0)
        error_save_return(errctx, errno, false);

    return true;
}

bool
flv_read_header(flv_header_t    **header,
                uint8_t         **flvptr,
                error_context_t  *errctx)
{
    if (!header || !flvptr || !*flvptr || !errctx)
        error_save_return(errctx, EINVAL, false);

    /* Read FLV header tag */
    *header = (flv_header_t *)(*flvptr);
    *flvptr += sizeof(flv_header_t);

    log_trace("Read FLV header:");
    log_trace("  Signature:   [%.3s]", (*header)->signature);
    log_trace("  Version:     [0x%02X]", (*header)->version);
    log_trace("  Flags:       [0x%02X]", (*header)->flags);
    log_trace("  Header size: [%u]", ntohl((*header)->header_size));

    return true;
}

bool
flv_read_tag(flv_tag_t       **flvtag,
             uint8_t         **flvptr,
             error_context_t  *errctx)
{
    size_t length = 0;

    if (!flvtag || !flvptr || !*flvptr || !errctx)
        error_save_return(errctx, EINVAL, false);

    /* Read FLV tag */
    *flvtag = (flv_tag_t *)(*flvptr);
    *flvptr += sizeof(flv_tag_t);

    /* Skip payload length to next FLV tag position */
    length = ntohu24((*flvtag)->length);
    *flvptr += length;

    return true;
}

bool
flv_parse_tag(uint32_t   *offset,
              uint8_t    *type,
              uint32_t   *length,
              uint32_t   *timestamp,
              uint32_t   *stream_id,
              uint8_t   **payload,
              flv_tag_t  *flvtag)
{
    if (offset)
        *offset = ntohl(flvtag->offset);
    if (type)
        *type = flvtag->type;
    if (length)
        *length = ntohu24(flvtag->length);
    if (timestamp)
        *timestamp = flvtag->timestamp_upper << 24 | ntohu24(flvtag->timestamp_lower);
    if (stream_id)
        *stream_id = flvtag->stream_id;
    if (payload)
        *payload = flvtag->payload;

    log_trace("Parsed FLV tag, offset: [%u], type: [0x%02X], length: [%u], "
            "timestamp: [%u], stream id: [%u], payload: [%p]",
            offset ? *offset : 0, type ? *type : 0, length ? *length : 0,
            timestamp ? *timestamp : 0, stream_id ? *stream_id : 0,
            payload ? *payload : 0);

    return true;
}

void
flv_print_tag(flv_tag_t *flvtag)
{
    uint32_t  offset    = 0; 
    uint8_t   type      = 0;
    uint32_t  length    = 0;
    uint32_t  timestamp = 0;
    uint32_t  stream_id = 0;
    uint8_t  *payload   = NULL;

    assert(flvtag != NULL);

    offset = ntohl(flvtag->offset);
    type = flvtag->type;
    length = ntohu24(flvtag->length);
    timestamp = flvtag->timestamp_upper << 24 | ntohu24(flvtag->timestamp_lower);
    stream_id = flvtag->stream_id;
    payload = flvtag->payload;

    log_trace("Parsed FLV tag, offset: [%u], type: [0x%02X], length: [%u], "
            "timestamp: [%u], stream id: [%u], payload: [%p]",
            offset, type, length, timestamp, stream_id, payload);
}

bool
flv_parse_metadata_framerate(double          *frame_rate,
                             flv_tag_t       *flvtag,
                             error_context_t *errctx)
{
    char      tag_name[32]  = {0};
    uint32_t  size          = 0;
    uint8_t  *position      = NULL;
    char     *property_name = NULL;
    uint16_t  length        = 0;
    uint8_t   amf_type      = 0;
    bool      result        = false;

    if (!frame_rate || !flvtag || !errctx)
        error_save_return(errctx, EINVAL, false);

    size = ntohu24(flvtag->length);
    position = flvtag->payload;
    result = rtmp_amf0_parse_string(tag_name, sizeof(tag_name), &position, errctx);
    if (unlikely(!result))
        error_save_return(errctx, EILSEQ, false);

    amf_type = *position;
    switch (amf_type)
    {
        case RTMP_AMF0_OBJECT:
            position += sizeof(uint8_t);
        break;
        case RTMP_AMF0_ECMA_ARRAY:
            position += sizeof(uint8_t) + sizeof(uint32_t);
        break;
    }

    for (result = false; position - flvtag->payload < size;)
    {
        length = ntohs(*(uint16_t *)(position));
        position += sizeof(uint16_t);
        property_name = (char *)(position);
        position += length;

        if (strncasecmp(property_name, "framerate", length) == 0)
        {
            result = rtmp_amf0_parse_number(frame_rate, &position, errctx);
            break;
        }

        amf_type = *position;
        position += sizeof(uint8_t);
        switch (amf_type)
        {
            case RTMP_AMF0_NUMBER:
                position += sizeof(double);
            break;
            case RTMP_AMF0_BOOLEAN:
                position += sizeof(bool);
            break;
            case RTMP_AMF0_STRING:
            {
                length = ntohs(*position);
                position += sizeof(uint16_t) + length;
            }
            break;
            default: position++;
        }
    }

    return result;
}

static bool
flv_get_file_size(int              flvfd,
                  size_t          *flvsize,
                  error_context_t *errctx)
{
    struct stat flvstat = {};
    int         ret     = -1;

    if (flvfd < 0 || !flvsize || !errctx)
        error_save_return(errctx, EINVAL, false);

    ret = fstat(flvfd, &flvstat);
    if (ret < 0)
        error_save_return(errctx, errno, false);

    *flvsize = (size_t)(flvstat.st_size);

    return true;
}


