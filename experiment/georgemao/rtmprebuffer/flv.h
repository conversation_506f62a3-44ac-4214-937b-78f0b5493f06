/*
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date:   2016/04/13
 * File:   flv.h
 * Desc:   FLV file module
 */

#ifndef _FLV_H_
#define _FLV_H_

#include <stdint.h>
#include <stdbool.h>

#include "flv.h"
#include "error.h"

#ifdef __cplusplus
extern "C"
{
#endif

    typedef struct flv_header_t
    {
        char     signature[3];
        uint8_t  version;
        uint8_t  flags;
        uint32_t header_size;

    } __attribute__ ((__packed__)) flv_header_t;

    typedef struct flv_tag_t
    {
        uint32_t offset:          32; /* relative offset from previous tag */
        uint32_t type:            8;
        uint32_t length:          24;
        uint32_t timestamp_lower: 24;
        uint32_t timestamp_upper: 8;
        uint32_t stream_id:       24;
        uint8_t  payload[];

    } __attribute__ ((__packed__)) flv_tag_t;

    typedef struct flv_audio_tag_t
    {
        uint8_t sound_type:   1; /* mono: 0, stereo: 1 */
        uint8_t sound_size:   1; /* 8-bit: 0, 16-bit: 1 */
        uint8_t sound_rate:   2; /* 5.5 KHz: 0, 11 KHz: 1, 22 KHz: 2, 44 KHz: 3 */
        uint8_t sound_format: 4; /* LPCM-BE: 0, MP3: 2, LPCM-LE: 3, G.711 A-LAW: 7, G.711 u-LAW: 8, AAC: 10 */
        uint8_t packet_type;     /* AAC Sequence Header: 0, AAC raw: 1 */
        uint8_t audio_frame[];

    } __attribute__ ((__packed__)) flv_audio_tag_t;

    typedef struct flv_video_tag_t
    {
        uint8_t  codec_id:         4;  /* 0x07 for H.264 */
        uint8_t  frame_type:       4;  /* intra-frame: 0x01, non-intra-frame: 0x02 */
        uint32_t packet_type:      8;  /* config parameter sets: 0x00, picture data: 0x01 */
        uint32_t composition_time: 24; /* for B-frames only, 0 for mainprofile */
        uint8_t  video_frame[];

    } __attribute__ ((__packed__)) flv_video_tag_t;

    typedef struct h264_sps_t
    {
        uint8_t nalu_header;
        uint8_t profile_idc;
        uint8_t profile_compatibility; /* constraint set 0~3 + reserved 4 bits */
        uint8_t level_idc;
        uint8_t remaining[];

    } __attribute__ ((__packed__)) h264_sps_t;

    typedef struct audio_specific_config_t
    {
        uint16_t ga_specific_config:       3;
        uint16_t channel_configuration:    4;
        uint16_t sampling_frequency_index: 4;
        uint16_t audio_object_type:        5;

    } __attribute__ ((__packed__)) audio_specific_config_t;

    uint8_t *flv_open(int *flvfd, size_t *flvsize, const char *flvpath, error_context_t *errctx);
    bool flv_close(int flvfd, uint8_t *flvptr, size_t flvsize, error_context_t *errctx);
    bool flv_read_header(flv_header_t **header, uint8_t **flvptr, error_context_t *errctx);
    bool flv_read_tag(flv_tag_t **metadata, uint8_t **flvptr, error_context_t *errctx);
    bool flv_parse_tag(uint32_t *offset, uint8_t *type, uint32_t *length, uint32_t *timestamp, uint32_t *stream_id, uint8_t **payload, flv_tag_t *tag);
    void flv_print_tag(flv_tag_t *flvtag);
    bool flv_parse_metadata_framerate(double *frame_rate, flv_tag_t *tag, error_context_t *errctx);

#ifdef __cplusplus
}
#endif

#endif

