// +build aaron

package etcdLock

// Run
// -server="http://*************:2379" -thread=5000 -optime=10 -ttl=100000 -queue-ttl=10000 -name="tetese34fsfet"

// import (
// 	"flag"
// 	"fmt"
// 	"sync"
// 	"time"

// 	"github.com/17media/go-etcd/etcd"
// 	"github.com/datawisesystems/etcdlock"
// )

// const (
//server              = "http://localhost:2379"
// numThread           = 100
//operationTimeMilliS = 100
//lockName = "testEtcdLock"
//lockTTL = 100
// )

// func main() {

// 	server := flag.String("server", "", "server url")
// 	numThread := flag.Int("thread", 10, "num of thread")
// 	opTimeMilliS := flag.Int64("optime", 100, "time of operation in milli second")
// 	lockName := flag.String("name", "testEtcdLock", "name of lock")
// 	lockTTL := flag.Uint64("ttl", 100000, "ttl of lock")
// 	//run := flag.Int("run", 1, "num of run")
// 	sameLock := flag.Bool("samelock", false, "use same lock")
// 	flag.Parse()

// 	//	fmt.Println(*server, *numThread, *opTimeMilliS, *lockName, *lockTTL)

// 	// x := server
// 	etcdlock.EtcdServer = server

// 	locks := make([]string, 0)
// 	for i := 0; i < *numThread; i++ {
// 		//fmt.Println(fmt.Sprintf("%v%v", *lockName, i))
// 		locks = append(locks, fmt.Sprintf("%v%v", *lockName, i))
// 	}
// 	//fmt.Println(locks)
// 	//	start := time.Now()
// 	ch := make(chan int64, 100000)
// 	gw := sync.WaitGroup{}
// 	gw.Add(*numThread)
// 	for i := 0; i < *numThread; i++ {
// 		if *sameLock {
// 			//fmt.Println("use same lock")
// 			go f(&gw, *lockName, *lockTTL, *opTimeMilliS, ch)
// 		} else {
// 			//fmt.Printf("use its own key %v\n", locks[i])
// 			go f(&gw, locks[i], *lockTTL, *opTimeMilliS, ch)
// 		}
// 	}
// 	ticker := time.NewTicker(time.Second * 3)
// 	for {
// 		var sum int64 = 0
// 		var count int64 = 0
// 		for {
// 			select {
// 			case <-ticker.C:
// 				fmt.Println(sum, count, time.Duration((float64(sum) / float64(count))))
// 				sum = 0
// 				count = 0
// 			case i := <-ch:
// 				sum += i
// 				count += 1
// 			}
// 		}
// 	}

// 	gw.Wait()
// 	//elapsed := time.Since(start)
// 	//fmt.Println(time.Duration(int(elapsed) / *run))
// }

// func f(gr *sync.WaitGroup, lockName string, ttl uint64, opTime int64, ch chan int64) {
// 	defer gr.Done()
// 	client := etcd.NewClient([]string{"http://*************:2379", "http://35.164.228.168:2379", "http://35.163.212.225:2379"})
// 	for {
// 		start := time.Now()
// 		//start := time.Now()
// 		h, err := etcdlock.WLock(client, lockName, ttl)
// 		if err != nil {
// 			fmt.Println(err)
// 			continue
// 		}
// 		//                elapsed := time.Since(start)
// 		//                ch <- int64(elapsed)
// 		time.Sleep(time.Duration(opTime) * time.Millisecond)
// 		//start := time.Now()
// 		err = etcdlock.WUnlock(client, lockName, h)
// 		if err != nil {
// 			fmt.Println(err)
// 			continue
// 		}
// 		elapsed := time.Since(start)
// 		ch <- elapsed.Nanoseconds()
// 	}
// 	//if err != nil {
// 	//	fmt.Println(err)
// 	//	return
// 	//}
// 	//fmt.Println("done")
// }
